import request from '/@/utils/request';
import type { ParamsFetch } from './index';
import { getApprovalParams } from './utils';

const projectName = '/admin';
export function approvalFetch(data: Parameters<typeof getApprovalParams>[0]) {
	return request({
		url: `${projectName}/bizIntegralAccount/approval`,
		method: 'post',
		data: getApprovalParams(data),
	});
}

export function couponAuditDetail(data: ParamsFetch) {
	return request({
		url: `${projectName}/auditFlow/couponAuditDetail`,
		method: 'post',
		data,
	});
}

export function goldCoinAuditDetail(data: ParamsFetch) {
	return request({
		url: `${projectName}/auditFlow/goldCoinAuditDetail`,
		method: 'post',
		data,
	});
}
