import dayjs from 'dayjs';
import { getTreeNode, getTreeItem } from '/@/utils/base';
import { Status, ConfigMap } from './constant';

interface Form<T extends keyof typeof ConfigMap> {
	id: string;
	approvalFlowKey: T;
	status: keyof typeof Status;
}
export function getApprovalParams(form: Form<keyof typeof ConfigMap>) {
	return {
		...form,
		paramMap: {
			url: `${window.location.origin}/#/approval?type=${form.approvalFlowKey}&id=${form.id}`,
		},
	};
}

export function isMobileDevice() {
	return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
}

export function formatTable(col: any[]) {
	return col.filter(({ prop }) => prop !== 'action');
}

export class FormToDetailCol {
	constructor() {}
	public formatter(formCol: any[], form: any, group?: string): any[] {
		return formCol.reduce((prev, curr) => {
			if (curr.show) {
				if (curr.show(form)) {
					return [...prev, ...this.deepItem(curr, form, group)];
				}
				return prev;
			}
			return [...prev, ...this.deepItem(curr, form, group)];
		}, []);
	}
	private deepItem(item: { groups?: any[]; [key: string]: any }, data: any, group?: string) {
		if (item.groups) {
			return this.formatter(item.groups, data, item.label);
		}
		return [this.formatCol(item, group)];
	}
	private formatCol(item: any, groupName?: string) {
		const { render, type, ...other } = item;
		const obj = {
			...other,
			prop: item.prop,
			label: item.label,
			group: groupName || undefined,
			span: item.colProps?.span >= 12 ? 3 : 1,
			itemLabel: item.itemLabel || undefined,
		};

		if (obj.formatter) {
			return obj;
		}
		if (['select', 'radio', 'checkbox'].includes(type)) {
			obj.type = 'enums';
			obj.attrs = {
				options: item.enums || item.attrs?.options || [],
			};
		}
		if (type === 'cascader') {
			const { props, options = [] } = item.attrs || {};
			const { label = 'label' } = props || {};
			if (item.attrs?.props?.emitPath === false) {
				obj.formatter = (row: any) => getTreeItem(options, row[item.prop], props)[label] || '--';
			} else {
				obj.formatter = (row: any) =>
					getTreeNode(options, row[item.prop], props)
						.map((item) => item[label])
						.join('/') || '--';
			}
		}
		if (type === 'date-picker') {
			obj.formatter = (row: any) => (row[item.prop] ? dayjs(row[item.prop]).format(row.attrs?.valueFormat || 'YYYY-MM-DD') || '--' : '--');
		}
		if (type === 'images') {
			obj.type = 'images';
			obj.attrs = {
				enabledPreview: true,
				viewMode: 'fold',
			};
			obj.formatter = (row: any) => (row[item.prop] || []).map((item: any) => item.url);
		}
		return obj;
	}
}

export class FormatterCol {
	#isH5;
	constructor(isH5: boolean) {
		this.#isH5 = isH5;
	}
	formatter(col: any[]) {
		return col.map(({ span, ...item }) => {
			return {
				...item,
				span: this.#isH5 ? 1 : span && span >= 2 ? 3 : 1,
			};
		});
	}
}
