<script setup lang="tsx">
import useConfig from './config';
const { form, config, _Columns, loading, component } = useConfig();
</script>
<template>
  <div class="page" v-loading="loading">
    <component :is="component" v-if="component"></component>
    <yun-pro-detail v-else :config="config" :detail="form" :columns="_Columns">
    </yun-pro-detail>
  </div>
</template>
<style lang="scss" scoped>
.page {
  overflow: auto;
  // height: 100%;
}
</style>
