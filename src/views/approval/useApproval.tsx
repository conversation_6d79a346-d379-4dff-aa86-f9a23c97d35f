import { ElMessage, ElMessageBox } from 'yun-design';
import { useRouter } from 'vue-router';
import { StatusKey, ConfigMap } from './constant';
import { approvalFetch } from './api';

export default () => {
	const router = useRouter();
	// 正常撤销
	function revokeApproval(id: string, approvalFlowKey: keyof typeof ConfigMap, fatch = approvalFetch) {
		return new Promise((resolve, reject) => {
			ElMessageBox.confirm('此操作撤回审批, 是否继续?', '确认', {
				confirmButtonText: '确定',
				cancelButtonText: '取消',
				type: 'error',
			})
				.then(async () => {
					const data = await fatch({
						id,
						approvalFlowKey,
						status: 'AUDIT_REVOCATION',
					});
					ElMessage.success('操作成功!');
					resolve(data);
				})
				.catch((err) => {
					reject(err);
				});
		});
	}
	// 审批详情
	function approvalDetail(processInstanceId: string) {
		router.push({
			path: '/flow/task/started',
			query: {
				id: processInstanceId,
			},
		});
	}
	return {
		StatusKey,
		revokeApproval,
		approvalDetail,
	};
};
