import { useRoute } from 'vue-router';
import type { UseFun } from './index';
import { ConfigMap } from './constant';
import { objectEntries } from '/@/utils/base';
import { isMobileDevice, FormatterCol } from './utils';

const files = import.meta.glob('./models/*.{ts,tsx}', { import: 'default', eager: true });

const Model = Object.fromEntries(
	objectEntries(files).map(([path, context]) => {
		const name = path.replace(/^\.\/\w+\/(\w+)\.\w+$/, '$1');
		return [name, context];
	})
) as {
	[key in `${ConfigMap}`]: UseFun;
};

export default () => {
	const { query } = useRoute();
	const { type, ...params } = query as {
		type: keyof typeof ConfigMap;
	};
	const isH5 = isMobileDevice();
	const Col = new FormatterCol(isH5);
	const config = ref({
		descriptions: {
			column: isH5 ? 1 : 3,
		},
	});
	const { form, columns, loading, component } = Model[ConfigMap[type]](params);
	const _Columns = computed(() => {
		return Col.formatter(columns.value);
	});
	return {
		config,
		form,
		_Columns,
		loading,
		component,
	};
};
