export enum StatusKey {
  AUDIT_ABNORMAL = 'AUDIT_ABNORMAL',
}

export type Status = {
  AUDITING: '审批中';
  AUDIT_FAIL: '审批不通过';
  AUDIT_FINISHED: '审批通过';
  AUDIT_WAIT: '待审批';
  AUDIT_REVOCATION: '撤销审批';
  AUDI_SUBMIT: '提交审批';
  [StatusKey.AUDIT_ABNORMAL]: '审批异常';
};

export enum ConfigMap {
  SRM_PROCUREMENT_PLAN = 'purchasingDetails',
  PROJECT_PROPOSAL = 'projectProposal',
	MY_SUPPLY = 'mySupply',
	BIDDING_PROCESS = 'biddingProcess', // 询价流程-审批详情
  TENDER_FAILED = 'tenderFailed', // 流标-审批详情
  CHANGE = 'change', // 变更-审批详情
  AWARD_RESULT_DETAIL = 'awardResultDetail', // 变更-审批详情
  CONSTRACT_DETAIl = 'constract', 
}
