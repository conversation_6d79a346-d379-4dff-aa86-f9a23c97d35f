import { defineComponent } from 'vue';
import type { UseFun } from '../index';
import TenderFailedDrawer from '@/views/procurementSourcing/biddingProcess/components/TenderFailed/TenderFailedDrawer.vue';
import ProjectInfo from '@/components/ProjectInfo/index.vue';

// 添加样式
import './tenderFailed.scss';
interface Params {
	failedNoticeId: string;
}

const useFun: UseFun = (data: Params) => {

	return {
		component: defineComponent({
			setup() {
				return () => (
					<div class="tender-failed-audit-container">
						<ProjectInfo />
						<TenderFailedDrawer contentOnly={true} failedNoticeId={data.failedNoticeId} isEdit={true} />
					</div>
				);
			},
		}),
	};
};

export default useFun;
