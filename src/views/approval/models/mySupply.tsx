import { defineComponent } from 'vue';
import useDetail from '@/views/lowcode/supply/components/detail/index';
import { MODULE_TYPE_ENUM } from '@/views/lowcode/supply/const';
import type { UseFun } from '../index';
import Detail from '@/components/Detail/index.vue';

interface Params {
	id: string;
	supplierCode: string;
}

const useFun: UseFun = (data: Params) => {
	const form = ref({});
	const { processedGroups, collections, initData } = useDetail({
		moduleType: MODULE_TYPE_ENUM.MY,
	});
	(async () => {
		await initData(data);
		form.value = collections.value || {};
	})();

	return {
		component: defineComponent({
			setup() {
				return () => (
					<Detail
						groups={processedGroups.value}
						data={form.value}
					/>
				);
			},
		}),
	};
};

export default useFun;
