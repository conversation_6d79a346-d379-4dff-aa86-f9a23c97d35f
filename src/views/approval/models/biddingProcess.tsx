import { defineComponent } from 'vue';
import ProjectInfo from '@/components/ProjectInfo/index.vue';
import BiddingProcess from '@/views/procurementSourcing/biddingProcess/index.vue';
import type { UseFun } from '../index';

import './biddingProcess.scss';

const useFun: UseFun = () => {
	return {
		component: defineComponent({
			setup() {
				return () => (
					<div class="bidding-process-audit-container">
						<ProjectInfo />
						<BiddingProcess />
					</div>
				);
			},
		}),
	};
};

export default useFun;