import { defineComponent } from 'vue';
import type { UseFun } from '../index';
import ChangeDrawer from '@/views/procurementSourcing/changeList/components/ChangeDrawer.vue'
import ProjectInfo from '@/components/ProjectInfo/index.vue';

// 添加样式
import './change.scss';

const useFun: UseFun = () => {

	return {
		component: defineComponent({
			setup() {
				return () => (
					<div class="change-audit-container">
						<ProjectInfo />
						<ChangeDrawer />
					</div>
				);
			},
		}),
	};
};

export default useFun;
