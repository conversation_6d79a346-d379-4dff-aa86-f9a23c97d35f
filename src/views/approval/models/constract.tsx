import { defineComponent } from 'vue';
import type { UseFun } from '../index';
import ContractForm from '@/views/contractMangement/components/ContractForm.vue';

// 添加样式
import './constract.scss';

const useFun: UseFun = (data: Params) => {

	return {
		component: defineComponent({
			setup() {
				return () => (
					<div class="change-audit-container">
						<ContractForm mode={'view'}
							contractId={data.contractCode}
							is-purchaser={true}
							is-supplier={false} />
					</div>
				);
			},
		}),
	};
};

export default useFun;
