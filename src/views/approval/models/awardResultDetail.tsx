import { defineComponent, onMounted, ref, h } from 'vue';
import { useRoute } from 'vue-router';
import { useBiddingStore } from '@/views/procurementSourcing/biddingProcess/stores/bidding-store';
import { useAwardStore } from '@/views/procurementSourcing/biddingProcess/stores/award-store';
import AwardResultDetail from '@/views/procurementSourcing/biddingProcess/components/award/AwardResult/detail.vue';
import ProjectInfo from '@/components/ProjectInfo/index.vue';
import type { UseFun } from '../index';
// 添加样式
import './awardResultDetail.scss';

interface Params {
  sectionId: string;
  projectCode?: string;
}

const useFun: UseFun = (data: Params) => {
  return {
    component: defineComponent({
      setup() {
        const route = useRoute();
        const biddingStore = useBiddingStore();
        const awardStore = useAwardStore();
        const isReady = ref(false);

        onMounted(async () => {
          // 1. 初始化 biddingStore
          const projectCode = data.projectCode || route.query.projectCode;
          if (!biddingStore.projectDetail && projectCode) {
            await biddingStore.getProjectDetailData({ projectCode });
          }
          if (!biddingStore.noticeInfo && biddingStore.projectDetail && biddingStore.projectDetail.id) {
            await biddingStore.getEffectNoticeData();
          }
          // 2. 初始化 awardStore
          const noticeId = route.query.noticeId;
          const projectId = route.query.projectId;
          if (noticeId && projectId) {
            await awardStore.loadForAwardResult({
              noticeId,
              projectId,
              sectionId: data.sectionId || route.query.sectionId,
            });
          }
          isReady.value = true;
        });

        return () => isReady.value
          ? h('div', { class: 'award-result-detail-audit-container' }, [
              h(ProjectInfo),
              h(AwardResultDetail, { isViewMode: false, sectionId: data.sectionId })
            ])
          : h('el-skeleton', { rows: 6, animated: true });
      }
    })
  }
};

export default useFun;
