<template>
  <yun-drawer v-model="visible" :title="title" destroy-on-close @confirm="handleConfirm" size="small">
    <yun-pro-form
      ref="formRef"
      :form="form"
      :columns="columns"
      :config="{
        colProps: { span: 24 }
      }"
      :form-props="{ labelPosition: 'top'}"
    />
  </yun-drawer>
</template>
<script setup>
import { ref, computed } from 'vue';
import { belongingSpaceOptions } from '../const'
import { saveProject, updateProject } from '@/api/service/index'
import { useUserInfo } from '/@/stores/userInfo';

const emits = defineEmits(['getData']);
const visible = ref(false);
const isEdit = ref(false);
const form = ref({});
const formRef = ref();
const userInfoStore = useUserInfo();
const { userInfos: userInfo } = storeToRefs(userInfoStore);
const title = computed(() => {
  return isEdit.value ? '编辑' : '新增';
});

async function handleConfirm(done, loading) {
  await formRef.value.elForm.validate();
  loading.value = true;
  const api = isEdit.value ? updateProject : saveProject;
  const parmas = {
    ...form.value,
    [!isEdit.value ? 'createBy' : 'updateBy']: userInfo.value.user?.name
  };
  api(parmas).then(() => {
    done();
    emits('getData', form.value);
  }).finally(() => {
    loading.value = false;
  })
}
function show(row) {
  isEdit.value = !!row;
  form.value = {
    ...row,
    status: row?.status || 'ENABLED'
  } || {};
  visible.value = true;
  nextTick(() => {
    formRef.value?.elForm?.clearValidate();
  });
}

const columns = computed(() => [
  {
    label: '项目名称',
    prop: 'projectName',
    type: 'input',
    attrs: {
      maxLength: 64,
    },
    rules: [{ required: true, message: '请输入项目名称', trigger: 'blur' }],
  },
  {
    label: '项目描述',
    prop: 'projectDesc',
    type: 'input',
    attrs: { type: 'textarea', maxLength: 200 },
    rules: [{ required: false, message: '请输入项目描述', trigger: 'blur'}],
  },
  {
    label: '所属空间',
    prop: 'spaceName',
    type: 'select',
    attrs: {
      clearable: true
    },
    enums: belongingSpaceOptions.value,
    rules: [{ required: true, message: '请输入项目名称', trigger: 'blur' }],
  },
  {
    label: '接口前缀',
    prop: 'serviceName',
    type: 'input',
    tip: '服务地址',
    attrs: {
      clearable: true
    },
    rules: [{ required: false, message: '请输入接口前缀', trigger: 'blur' }],
  },
  // {
  //   label: '状态',
  //   prop: 'status',
  //   type: 'radio',
  //   enums: status,
  //   attrs: {
  //     clearable: true
  //   },
  //   rules: [{ required: true, message: '请选择状态', trigger: 'blur' }],
  // },
])

defineExpose({
  show,
});
</script>
<style scoped lang="scss"></style>