<template>
  <yun-drawer v-model="visible" :show-cancel-button="false" :title="title" @confirm="handleConfirm" size="small">
    <yun-pro-detail
      :detail="form"
      :columns="columns"
      :config="{
        descriptions: { labelPosition: 'top', column: 1 }
      }"
    />
  </yun-drawer>
</template>
<script setup>
import { ref, computed } from 'vue';
import { belongingSpaceOptions, status } from '../const'

const visible = ref(false);
const form = ref({});
const title = computed(() => {
  return '详情';
});

async function handleConfirm(done) {
  done();
}
function show(row) {
  form.value = {...row} || {};
  visible.value = true;
}

const columns = computed(() => [
  {
    label: '项目名称',
    prop: 'projectName',
  },
  {
    label: '所属空间',
    prop: 'spaceName',
    type: 'enums',
    attrs: {
      options: belongingSpaceOptions.value,
    }
  },
  {
    label: '接口前缀',
    prop: 'serviceName',
  },
  {
    label: '状态',
    prop: 'status',
    type: 'enums',
    attrs: {
      options: status,
    }
  },
  {
    label: '创建时间',
    prop: 'createTime',
  },
  {
    label: '创建人',
    prop: 'createBy',
  },
  {
    label: '项目描述',
    prop: 'projectDesc',
  },
])

defineExpose({
  show,
});
</script>
<style scoped lang="scss">
:deep(.yun-descriptions) {
  padding: 0;
}
</style>