<template>
  <div class="table-content">
    <yun-pro-table
      ref="proTableRef"
      v-model:pagination="pagination"
      v-model:filter-data="filterTableData"
      v-model:searchData="searchData"
      :table-columns="columns"
      :search-fields="searchFields"
      :auto-height="true"
      :remote-method="remoteMethod"
      :table-props="tableProps"
      layout="whole"
    >
      <template #tableHeaderLeft>
        <el-button :icon="Plus" type="primary" @click="handleAdd()"> 新增 </el-button>
      </template>
      <template #t_action="{ row }">
        <el-button type="text" size="small" @click.prevent="handleEdit(row)"> 编辑 </el-button>
        <el-button type="text" size="small" @click.prevent="handleView(row)"> 查看 </el-button>
        <el-button type="text" size="small" @click.prevent="handleUpdate(row)"> {{ row.status === 'ENABLED' ? '禁用' : '启用' }} </el-button>
      </template>
    </yun-pro-table>
    <upsert ref="upsertRef" @getData="getData" />
    <detail ref="detailRef" @getData="getData" />
  </div>
</template>

<script setup>
import { ref, computed } from 'vue';
import { useProTable } from '@ylz-use/core';
import { ElMessage, ElMessageBox } from 'yun-design';
import upsert from './components/upsert.vue'
import detail from './components/detail.vue'
import { fetchProjectPage, updateProjectStatus } from '@/api/service/index'
import { belongingSpaceOptions, status } from './const'
import dayjs from 'dayjs';

const upsertRef = ref();
const detailRef = ref();
const columns = computed(() => [
  {
    label: '项目名称',
    prop: 'projectName',
    width: 200,
  },
  {
    label: '所属空间',
    prop: 'spaceName',
    width: 100,
    render({ row }) {
      return belongingSpaceOptions.value.find(e => e.value === row.spaceName)?.label || '--';
    },
  },
  {
    label: '接口前缀',
    prop: 'serviceName',
    width: 150,
  },
  {
    label: '描述',
    prop: 'projectDesc',
  },
  {
    label: '状态',
    prop: 'status',
    width: 80,
    render({ row }) {
      return status.find(e => e.value === row.status)?.label || '--';
    },
  },
  {
    label: '创建时间',
    prop: 'createTime',
    width: 160,
    render(row){
      return dayjs(row.createTime).format('YYYY-MM-DD HH:mm:ss');
    }
  },
  {
    label: '创建人',
    prop: 'createBy',
    width: 100,
  },
  {
    label: '操作',
    prop: 'action',
    fixed: 'right',
    width: 150,
  },
]);
const searchFields = computed(() => [
  {
    prop: 'projectName',
    label: '项目名称',
    component: 'el-input',
    componentAttrs: {
      placeholder: '请输入项目名称',
    },
  },
  {
    prop: 'spaceName',
    label: '所属空间',
    component: 'el-select',
    enums: belongingSpaceOptions.value,
    componentAttrs: {
      placeholder: '请输入项目名称',
      clearable: true,
    },
  },
  {
    prop: 'status',
    label: '状态',
    component: 'el-select',
    enums: status,
    componentAttrs: {
      placeholder: '请选择状态',
      clearable: true,
    },
  }
]);
const { pagination, remoteMethod, tableProps, proTableRef, filterTableData } = useProTable({
  apiFn: fetchProjectPage,
  paramsHandler(params) {
    return {
      ...params,
    };
  },
  querysHandler(query) {
    return {
      ...query,
      current: pagination.value.page
    };
  },
  responseHandler(result) {
    return result.data;
  },
  plugins: {
    config: {
      columns: columns.value,
      searchFields: searchFields.value,
    },
    list: ['SEARCH_PLUS'],
  },
});
function getData() {
  proTableRef.value?.getData();
}
function handleEdit(row) {
  upsertRef.value.show(row);
}
function handleAdd() {
  upsertRef.value.show();
}
function handleView(row) {
  detailRef.value.show(row);
}
function handleUpdate(row) {
  ElMessageBox.confirm('此操作将修改该状态, 是否继续?', '状态更新', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'error',
  }).then(async () => {
    await updateProjectStatus({
      id: row.id,
      status: row.status === 'ENABLED' ? 'DISABLED' : 'ENABLED'
    })
    ElMessage.success( row.status === 'ENABLED' ? '禁用成功!' : '启用成功!');
    getData();
  });
}
</script>

<style scoped lang='scss'>
.table-content {
  width: 100%;
  height: calc(100vh - 120px);
}
</style>