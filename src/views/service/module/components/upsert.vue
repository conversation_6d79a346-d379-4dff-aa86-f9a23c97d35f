<template>
  <yun-drawer v-model="visible" :title="title" :z-index="1000"  destroy-on-close @confirm="handleConfirm" size="small">
    <yun-pro-form
      ref="formRef"
      :form="form"
      :columns="columns"
      :config="{
        colProps: { span: 24 }
      }"
      :form-props="{ labelPosition: 'top'}"
    />
  </yun-drawer>
</template>
<script setup lang="jsx">
import { ref, computed } from 'vue';
import { saveModule, updateModule, bizRoute, getFeishuUser } from '@/api/service/index';
import { useUserInfo } from '/@/stores/userInfo';
import { ElMessage } from 'yun-design';
import { useProjects } from '../const'
import Select from '@/components/Select/index.vue'


const emits = defineEmits(['getData']);
const visible = ref(false);
const isEdit = ref(false);
const form = ref({});
const formRef = ref();
const userInfoStore = useUserInfo();
const { userInfos: userInfo } = storeToRefs(userInfoStore);
const title = computed(() => {
  return isEdit.value ? '编辑' : '新增';
});
async function fetchFeishuUser(arr) {
  if (!arr.length) return;
  const params = {
    appCode: 'robot_app_msg',
    accountType: 'MOBILE',
    accounts: arr,
  };
  const res = await getFeishuUser(params);
  return res.data || [];
}
const { projects, fetchList: fetchProjects } = useProjects();
async function handleConfirm(done, loading) {
  await formRef.value.elForm.validate();
  loading.value = true;
  const api = isEdit.value ? updateModule : saveModule;
  const parmas = {
    ...form.value,
    [!isEdit.value ? 'createBy' : 'updateBy']: userInfo.value.user?.name
  };
  api(parmas).then(() => {
    done();
    emits('getData', form.value);
  }).finally(() => {
    loading.value = false;
  })
}

const routeOpeions = ref([]);
async function getRouteOpeions() {
  const { data } = await bizRoute({});
  routeOpeions.value = data.map((item) => ({
    value: {
      routeId: item.routeId,
      routeName: item.routeName,
    },
    label: item.routeName,
	}));
}
function show(row) {
  isEdit.value = !!row;
  form.value = {...row, status: row?.status || 'ENABLED' } || {};
  visible.value = true;
  nextTick(() => {
    formRef.value?.elForm?.clearValidate();
    fetchProjects();
    getRouteOpeions();
  });
}
const columns = computed(() => [
  {
    label: '服务名称',
    prop: 'moduleName',
    type: 'input',
    attrs: {
      maxLength: 64,
    },
    rules: [{ required: true, message: '请输入服务名称', trigger: 'blur' }],
  },
  {
		label: '服务名',
		prop: 'routeIdName',
		type: 'select',
		attrs: {
      valueKey: 'routeId',
			placeholder: '请选择',
			filterable: true,
			clearable: true,
		},
		rules: [{ required: true, message: '请选择', trigger: 'change' }],
		enums: routeOpeions.value,
	},
  {
		label: '服务ID',
		prop: 'routeId',
    render(form){
      return form.routeIdName?.routeId || '--'
    }
	},
  {
    label: '所属项目',
    prop: 'projectId',
    type: 'select',
    enums: projects.value,
    attrs: {
      filterable: true,
      clearable: true,
      onChange(){
        form.value.projectName = projects.value.find(item => item.value === form.value.projectId)?.label
      }
    },
    rules: [{ required: true, message: '请输入所属项目', trigger: 'blur'}],
    render(){
      function onChange(){
        form.value.projectName = projects.value.find(item => item.value === form.value.projectId)?.label
      }
      return  (<Select onChange={onChange} filterable clearable v-model={form.value.projectId} options={projects.value} placeholder="请输入所属项目" />)
    }
  },
  // {
  //   label: '状态',
  //   prop: 'status',
  //   type: 'radio',
  //   attrs: {
  //     clearable: true
  //   },
  //   enums: status,
  //   rules: [{ required: true, message: '请选择状态', trigger: 'blur' }],
  // },
  {
    label: '维护人员',
    prop: 'ext.personPhones',
    tip: '该服务的维护人员手机号码',
    type: 'select',
    rules: [{ required: true, message: '该服务的维护人员手机号码', trigger: 'blur' }],
    attrs: {
      clearable: true,
      multiple: true,
      filterable: true,
      allowCreate: true
    },
    render(form){
      if (!form.ext) form.ext = {}
      function onChange(){
        const phones = form.ext.personPhones || []
        fetchFeishuUser(phones).then(res => {
          const map = res?.reduce((acc, crr) => {
            if (crr.userInfo) acc[crr.account] = crr.userInfo
            else ElMessage.warning(`未找到${crr.account}手机号的飞书用户`)
            return acc
          }, {})
          form.ext.personPhones = Object.keys(map)
        })
      }
      return <el-select clearable multiple filterable allowCreate onChange={onChange} placeholder="请输入联系人手机号" v-model={form.ext.personPhones} />
    }
  },
  {
    label: '简要介绍',
    prop: 'moduleBrief',
    type: 'input',
    attrs: { type: 'textarea', maxLength: 200 },
    rules: [{ required: true, message: '请输入简要介绍', trigger: 'blur'}],
  },
  {
    label: '详细介绍',
    prop: 'moduleDesc',
    type: 'input',
    attrs: { type: 'textarea', maxLength: 500 },
    rules: [{ required: false, message: '请输入详细介绍', trigger: 'blur'}],
  }, 
  {
    label: '缩略图',
    prop: 'icon',
    render(form){
      return <>
        <upload-img v-model:image-url={form.icon} uploadFileUrl="/admin/oss/upload" fileSize={10} v-slots={
          {
            tip: () => <div style="color: #999; text-align: left;">限1张，png/jpg/jpeg/gif格式，建议尺寸120*120px，10M以内。</div>
          }
        } />
      </>
    }
  }
])

defineExpose({
  show,
});
</script>
<style scoped lang="scss"></style>