<template>
  <yun-drawer v-model="visible" :show-cancel-button="false" :title="title" @confirm="handleConfirm" size="small">
    <yun-pro-detail
      :detail="form"
      :columns="columns"
      :config="{
        descriptions: { labelPosition: 'top', column: 1 }
      }"
    />
  </yun-drawer>
</template>
<script setup lang="jsx">
import { ref, computed } from 'vue';
import { status, useProjects } from '../const'

const visible = ref(false);
const form = ref({});
const { projects } = useProjects();
const title = computed(() => {
  return '详情';
});

async function handleConfirm(done) {
  done();
}
function show(row) {
  form.value = {...row} || {};
  visible.value = true;
}

const columns = computed(() => [
  {
    label: '服务名称',
    prop: 'moduleName',
  },
  {
    label: '所属项目',
    prop: 'projectId',
    type: 'enums',
    attrs: {
      options: projects.value,
    }
  },
  {
		label: '服务ID',
		prop: 'routeIdName.routeId',
	},
  {
		label: '服务名',
		prop: 'routeIdName.routeName',
	},
  {
    label: '状态',
    prop: 'status',
    type: 'enums',
    attrs: {
      options: status,
    }
  },
  {
    label: '维护人员',
    type: 'enums',
    prop: 'ext.personPhones',
  },
  {
    label: '创建时间',
    prop: 'createTime',
  },
  {
    label: '创建人',
    prop: 'createBy',
  },
  {
    label: '简要介绍',
    prop: 'moduleBrief',
  },
  {
    label: '详细介绍',
    prop: 'moduleDesc',
  },
  {
    label: '缩略图',
    prop: 'icon',
    render(form){
      return <>
        <upload-img v-model:image-url={form.icon} disabled />
      </>
    }
  }
])

defineExpose({
  show,
});
</script>
<style scoped lang="scss">
:deep(.yun-descriptions) {
  padding: 0;
}
</style>