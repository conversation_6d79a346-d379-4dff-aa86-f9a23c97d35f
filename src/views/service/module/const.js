import { ref } from 'vue';
import { useDict } from '/@/hooks/dict';
import { fetchProjectPage } from '@/api/service/index'

const { belongingSpace } = useDict(
	'belongingSpace',
);

export const belongingSpaceOptions = belongingSpace;
export const status = [
	{ label: '启用', value: 'ENABLED' },
	{ label: '禁用', value: 'DISABLED' },
];
export function useProjects() {
  const projects = ref([])
	const fetchList = async (params = {}) => {
		const result = await fetchProjectPage({
      ...params
    }, { page: 1, size: 1000 });
		projects.value = result.data.records.map(e => ({...e, label: e.projectName, value: e.id})) || [];
    return result.data.records || [];
	};
  fetchList();
  return { projects, fetchList };
}
