<template>
  <yun-dialog
    v-model="visible"
    :title="title"
    :confirm-button-handler="confirmHandler"
    :confirm-button-text="'确定'"
    :cancel-button-text="'取消'"
  >
    <yun-pro-form
      ref="formRef"
      :form="form"
      :columns="columns"
      :form-props="{ labelPosition: 'top' }"
      :config="{
        colProps: { span: 24 },
      }"
    />
  </yun-dialog>
</template>
<script setup>
import { getProjectInfo, getInterfaceMenus, getInterfaceList, getInterfaceByCat } from '@/api/service/index';
import { ref } from 'vue';

defineProps({
  title: {
    type: String,
    default: '拉取YAPI接口',
  },
});
const emit = defineEmits(['add']);

const visible = ref(false);
const form = ref({});
const formRef = ref();
const typeList = ref([]);
const interfaceList = ref([]);

async function getInterfacePage() {
  if (!form.value.token || !form.value.projectId) return;
  const api = form.value.catId ? getInterfaceByCat : getInterfaceList;
  const { data } = await api({
    catId: form.value.catId || '',
    token: form.value.token,
    projectId: form.value.projectId,
  }, {
    page: 1,
    size: 999
  })
  interfaceList.value = (data.list || data).map(e => {
    return {
      ...e,
      label: `${e.title}(${e.path})`,
      value: e.id,
    }
  })
}
async function getTypeList(reset = false){
  if (!form.value.token) return;
  if (!form.value.projectId || reset) {
    const { data: { projectId } } = await getProjectInfo({
      token: form.value.token,
    })
    form.value.projectId = projectId
    if (!projectId) {
      typeList.value = [];
      return;
    }
  }
  const { data } = await getInterfaceMenus({
    token: form.value.token,
    projectId: form.value.projectId,
  }, {
    page: 1,
    size: 999
  })
  typeList.value = data.map(e => {
    return {
      ...e,
      label: e.name,
      value: e.id,
    }
  })
  getInterfacePage();
}
const columns = computed(() => [
  {
    label: 'Token',
    prop: 'token',
    type: 'input',
    attrs: { 
      placeholder: '请输入YAPI的token',    
      onBlur: () => {
        form.value.catId = ''
        form.value.interfaceId = ''
        typeList.value = []
        interfaceList.value = []
        getTypeList(true)
      },
    },
    rules: [{ required: true, message: '请输入YAPI的token', trigger: 'blur' }],
  },
  {
    label: '分类',
    prop: 'catId',
    type: 'select',
    attrs: {
      placeholder: '请选择分类',
      filterable: true,
      onChange(){
        form.value.interfaceId = '';
        getInterfacePage();
      }
    },
    enums: typeList.value,
  },
  {
    label: '接口',
    prop: 'interfaceId',
    type: 'select',
    attrs: {
      placeholder: '请选择接口',
      filterable: true,
    },
    enums: interfaceList.value.filter(e => {
      return !form.value.catId || +e.catId === +form.value.catId
    }),
    rules: [{ required: true, message: '请选择接口', trigger: 'blur' }],
  },
])
const show = () => {
  visible.value = true;
};
const confirmHandler = async (done) => {
  await formRef.value.elForm.validate();
  done();
  emit('add', form.value);
};
defineExpose({
  show,
});
</script>
<style lang="scss" scoped>
.info {
  margin: 8px 0;
  font-size: 12px;
  line-height: 20px;
  color: var(--el-text-color-secondary);
}
</style>
