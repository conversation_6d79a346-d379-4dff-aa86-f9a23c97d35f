<template>
  <yun-drawer v-model="visible" :show-cancel-button="false" custom-class="interface-detail-drawer" :title="title" destroy-on-close @confirm="handleConfirm" size="X-large">
    <yun-pro-detail
      :detail="form"
      :columns="columns"
      :config="{
        descriptions: { labelPosition: 'top', column: 3 }
      }"
    />
  </yun-drawer>
</template>
<script setup lang=jsx>
import { ref, computed } from 'vue';
import {  useModules, status, methods, parseAttrs, inputAttrs, resBodyColumns, errorCodeColumns, capitalizeFirstLetter } from '../const'
import EditTable from './EditTable.vue'
import CodeEditor from '/@/components/MonacoEditor/index.vue';

const visible = ref(false);
const form = ref({});
const { modules } = useModules();
const title = computed(() => {
  return '详情';
});

async function handleConfirm(done) {
  done();
}
const body = ref(null)
function addEvent(){
  body.value = document.querySelector('.interface-detail-drawer .el-drawer__body');
}
function show(row) {
  form.value = {
    ...row,
    interfaceJson: parseAttrs(row.interfaceJson),
    requestModel: row.requestModel,
    requestSccuess: row.requestSccuess,
    requestFail: row.requestFail,
    errorCode: row.errorCode
  }
  visible.value = true;
  nextTick(() => {
    addEvent()
  });
}

const columns = computed(() => [
  {
    label: '接口名称',
    prop: 'interfaceName',
    group: '基本信息',
  },
  {
    label: '接口地址',
    prop: 'interfaceUrl',
    group: '基本信息',
  },
  {
    label: '所属服务',
    prop: 'moduleId',
    type: 'enums',
    group: '基本信息',
    attrs: {
      options: modules.value
    },
  },
  {
    label: '状态',
    prop: 'status',
    group: '基本信息',
    type: 'enums',
    attrs: {
      options: status
    },
  },
  {
    label: '创建时间',
    prop: 'createTime',
    group: '基本信息',
  },
  {
    label: '创建人',
    prop: 'createBy',
    group: '基本信息',
  },
  {
    label: '指令',
    prop: 'command',
    group: '基本信息',
  },
  {
    label: '请求方式',
    prop: 'interfaceJson.method',
    group: '文档信息',
    type: 'enums',
    attrs: {
      options: methods
    },
  },
  {
    label: '请求参数',
    group: '文档信息',
    span: 3,
    render(form){
      const activeName = ref('body')
      return (<el-tabs v-model={activeName.value}>
        {
          Object.keys(inputAttrs.value).map(e => {
            const key =  `req${capitalizeFirstLetter(e)}`
            if (!form.interfaceJson[key]) form.interfaceJson[key] = []
            return <el-tab-pane name={e} label={e}>
              <EditTable model-value={form.interfaceJson[key].filter(e => e.key || e.name)} disabled columns={inputAttrs.value[e]} />
            </el-tab-pane>
          })
        }
      </el-tabs>)
    }
  },
  {
    label: '返回参数',
    group: '文档信息',
    span: 3,
    render(form){
      if (!form.interfaceJson.resBody) {
        form.interfaceJson.resBody = []
      }
      return (<EditTable model-value={form.interfaceJson.resBody.filter(e => e.key)} disabled columns={resBodyColumns.value} />)
    }
  },
  {
    label: '请求示例',
    prop: 'requestModel',
    span: 3,
    group: '调用信息',
    render(form){
      if (!Object.keys(form.requestModel || {}).length) form.requestModel = {
        javascript: { code: '', type: 'javascript' },
        java: { code: '', type: 'java' },
        python: { code: '', type: 'python' },
      }
      const activeName = ref('javascript')
      return (<el-tabs v-model={activeName.value}>
        {
          Object.keys(form.requestModel).map(e => {
            return <el-tab-pane name={e} label={e}>
              <CodeEditor disabled v-model={form.requestModel[e].code} scrollDom={body.value} language={form.requestModel[e].type}></CodeEditor>
            </el-tab-pane>
          })
        }
      </el-tabs>)
    }
  },
  {
    label: '成功响应',
    prop: 'requestSccuess',
    group: '调用信息',
    span: 3,
    render(form){
      if (!form.requestSccuess) form.requestSccuess = { code: '' }
      return <CodeEditor disabled scrollDom={body.value} v-model={form.requestSccuess.code} ></CodeEditor>
    }
  },
  {
    label: '失败响应',
    prop: 'requestFail',
    group: '调用信息',
    span: 3,
    render(form){
      if (!form.requestFail) form.requestFail = { code: '' }
      return <CodeEditor disabled scrollDom={body.value} v-model={form.requestFail.code} ></CodeEditor>
    }
  },
  {
    label: '错误码',
    prop: 'errorCode',
    group: '调用信息',
    span: 3,
    render(form){
      return (<EditTable disabled model-value={form.errorCode?.codes?.filter(e => e.code)} columns={errorCodeColumns.value} />)
    }
  },
])

defineExpose({
  show,
});
</script>
<style scoped lang="scss"></style>