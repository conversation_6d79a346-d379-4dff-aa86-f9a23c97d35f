<template>
  <div class="table-container">
    <el-table
      :data="data"
      style="width: 100%; margin-bottom: 20px"
      row-key="id"
      border
      stripe
      :default-expand-all="defaultExpandAll"
      v-bind="$attrs"
    >
      <el-table-column v-for="(e, i) of columns" :key="i" v-bind="e">
        <template v-if="!disabled && e.render" #default="scope">
          <component style="flex: 1;" :is="e.render(scope)"></component>
        </template>
        <template v-else-if="disabled && e.formatter" #default="scope">
          <component style="flex: 1;" :is="e.formatter(scope)"></component>
        </template>
        <template v-else #default="scope">
          {{ scope.row[e.prop] }}
        </template>
      </el-table-column>
      <el-table-column v-if="!disabled" fixed="right" label="操作" width="120">
        <template #default="{ row }">
          <el-button type="action" @click="deleteRow(row)" v-if="data.length > 1">删除</el-button>
          <el-button type="action" @click="handleAddChild(row)" v-if="['object', 'array'].includes(row.type)">添加</el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-button style="width: 100%; margin-top: 5px;" :icon="Plus" v-if="!disabled" @click="addAttr">添加</el-button>
  </div>
</template>

<script setup>
import { Plus } from '@yun-design/icons-vue'
import { nanoid } from 'nanoid';

const props = defineProps({
  disabled: {
    type: Boolean,
    default: false
  },
  defaultExpandAll: {
    type: Boolean,
    default: false
  },
  columns: {
    type: Array,
    default: () => []
  },
})
const data = defineModel()

if (!props.disabled && !props.columns.find(e => e.prop === 'action')) {
  props.columns.forEach(e => {
    if (!e['show-overflow-tooltip']) e['show-overflow-tooltip'] = false;
  })
}
props.columns.forEach(e => {
  if (!e.id) e.id = nanoid();
})
function addAttr(){
  data.value.push({
    id: nanoid()
  })
}
function handleAddChild(row){
  if (row.children) {
    row.children.push({
      id: nanoid(),
      key: row.type === 'array' ? 'item' : '',
      disabled: row.type === 'array'
    })
  } else {
    row.children = [{
      id: nanoid(),
      key: row.type === 'array' ? 'item' : '',
      disabled: row.type === 'array'
    }]
  }
}
function deepDelete(arr, id){
  arr.forEach((e, index) => {
    if (e.id === id) {
      arr.splice(index, 1)
      return
    }
    if (e.children) {
      deepDelete(e.children, id)
    }
  })
}
function deleteRow(row){
  deepDelete(data.value, row.id)
}
watch(data, () => {
  if (!data.value?.length && !props.disabled) data.value = [{
    id: nanoid()
  }];
}, { immediate: true })
</script>

<style scoped lang='scss'>
  .table-container {
    position: sticky;
    top: 80px;
  }
  :deep(.el-table__row .cell) {
    display: flex;
    align-items: center;
  }
</style>