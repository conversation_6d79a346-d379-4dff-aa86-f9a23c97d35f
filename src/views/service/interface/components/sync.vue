<template>
	<yun-dialog v-model="visible" title="同步" :confirm-button-handler="confirmHandler" :confirm-button-text="'确定'" :cancel-button-text="'取消'">
		<yun-pro-form
			ref="formRef"
			:form="form"
			:columns="columns"
			:form-props="{ labelPosition: 'top' }"
			:config="{
				colProps: { span: 24 },
			}"
		/>
	</yun-dialog>
</template>
<script setup>
import { ref } from 'vue';
import { useUserInfo } from '/@/stores/userInfo';
import { fetchPage, opImportSettingPageSave, moduleItem } from '/@/api/openplatform/apply';

const emit = defineEmits(['getData']);

const visible = ref(false);
const form = ref({});
const formRef = ref();
const currentuserName = computed(() => {
	return useUserInfo().userInfos.user.name;
});
const appOptions = ref([]);
async function getAppOptions(val) {
	const { data } =
		(await fetchPage(
			{
				appName: val || undefined,
				status: 'ENABLED',
			},
			{ page: 1, size: 20 },
		)) || {};
	appOptions.value = (data?.records || []).map(({ appId, appName, enterpriseCode }) => ({ label: appName, value: appId, enterpriseCode }));
}
const columns = computed(() => [
	{
		label: '应用',
		prop: 'appId',
		type: 'select',
		attrs: {
			placeholder: '请选择',
			filterable: true,
			clearable: true,
			remote: true,
			'remote-method': getAppOptions,
			onChange: () => {
				const item = appOptions.value.find((e) => e.value === form.value.appId);
				if (item) {
					form.value.enterpriseCode = item.enterpriseCode + '';
				}
			},
		},
		enums: appOptions.value,
		rules: [{ required: true, message: '请选择', trigger: 'blur' }],
	},
	{
		label: 'appId',
		prop: 'appId',
	},
]);

async function setServiceName(id) {
	const { data } = await moduleItem(id);
	form.value.serviceName = data?.routeIdName?.routeId;
}
const show = (row) => {
	visible.value = true;
	form.value = {
		url: row.interfaceUrl,
		// outUri:row.interfaceUrl,
		invokeMethod: row.interfaceJson?.method,
		command: row.command,
		signatureMethod: 'default',
		isDirect: 0,
	};
	setServiceName(row.moduleId);
	getAppOptions();
};
const confirmHandler = async (done) => {
	await formRef.value.elForm.validate();
	opImportSettingPageSave({ ...form.value, updateBy: currentuserName.value });
	done();
	emit('getData');
};
defineExpose({
	show,
});
</script>
<style lang="scss" scoped>
.info {
	margin: 8px 0;
	font-size: 12px;
	line-height: 20px;
	color: var(--el-text-color-secondary);
}
</style>
