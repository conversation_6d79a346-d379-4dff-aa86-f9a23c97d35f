<template>
  <yun-drawer v-model="visible" custom-class="interface-drawer" :title="title" :close-on-click-modal="false" :before-close="beforeClose" destroy-on-close @confirm="handleConfirm" size="X-large">
    <yun-pro-form
      ref="formRef"
      :form="form"
      :columns="columns"
      :config="{
        colProps: { span: 8 }
      }"
      :form-props="{ labelPosition: 'top'}"
    />
  </yun-drawer>
</template>
<script setup lang="jsx">
import { ref, computed } from 'vue';
import { useProjects, useModules, status, methods, parseAttrs, stringifyAttrs, inputAttrs, resBodyColumns, errorCodeColumns, capitalizeFirstLetter, InOutTypeOptions } from '../const'
import { saveInterface, updateInterface, getInterfaceDoc } from '@/api/service/index';
import EditTable from './EditTable.vue'
import Select from '@/components/Select/index.vue'
import CodeEditor from '/@/components/MonacoEditor/index.vue';
import { nanoid } from 'nanoid';
import { useUserInfo } from '/@/stores/userInfo';

const emits = defineEmits(['getData']);
const visible = ref(false);
const isEdit = ref(false);
const form = ref({});
const drawerRef = ref();
const formRef = ref();
const docsRef = ref();
const userInfoStore = useUserInfo();
const { userInfos: userInfo } = storeToRefs(userInfoStore);
const { projects, fetchList: fetchProjects } = useProjects();
const { modules, fetchList: fetchModules } = useModules();
const title = computed(() => {
  return isEdit.value ? '编辑' : '新增';
});

function beforeClose(done){
  formRef.value?.elForm?.clearValidate();
  form.value = {};
  done();
}

async function handleConfirm(done, loading) {
  await formRef.value.elForm.validate();
  loading.value = true;
  const api = isEdit.value ? updateInterface : saveInterface;
  const parmas = {
    ...form.value,
    interfaceName: (form.value.interfaceName || '').replaceAll('<', '&lt;').replaceAll('>', '&gt;'),
    interfaceJson: stringifyAttrs(form.value.interfaceJson),
    requestModel: Object.fromEntries(Object.entries(form.value.requestModel).map(([key, item]) => {
      return [key, {
        ...item,
        code: (item.code || '').replaceAll('<', '&lt;').replaceAll('>', '&gt;')
      }]
    })),
    requestSccuess: form.value.requestSccuess,
    requestFail: form.value.requestFail,
    errorCode: form.value.errorCode,
    [!isEdit.value ? 'createBy' : 'updateBy']: userInfo.value.user?.name
  };
  api(parmas).then(() => {
    beforeClose(done);
    emits('getData', form.value);
  }).finally(() => {
    loading.value = false;
  })
}

function transform2tree(properties, requiredArr = [], type = ''){
  return Object.keys(properties).map(key => {
    const item = properties[key];
    let children = [];
    if (item.type === 'object') {
      children = item.properties || {};
    } else if (item.type === 'array') {
      children = {'item': {type: item.items?.type, properties: item.items?.properties || {} }};
    }
    return {
      key,
      ...item,
      disabled: type === 'array',
      id: item.id || nanoid(),
      required: requiredArr?.includes(key),
      children: transform2tree(children,item.required || [], item.type)
    }
  }) || []
}
function getInterfaceInfo(data){
  getInterfaceDoc({
    ...data
  }).then(({ data }) => {
    const bodyInfo = data.req_body_other ? JSON.parse(data.req_body_other) : {};
    const resInfo = data.res_body ? JSON.parse(data.res_body) : {};
    form.value = {
      interfaceName: data.title,
      interfaceUrl: data.path,
      status: status[0].value,
      interfaceJson: {
        method: data.method,
        reqBody: bodyInfo?.type === 'object' ? transform2tree(bodyInfo.properties, bodyInfo.required) : [],
        reqQuery: data.req_query.map(e => {
          return {
            ...e,
            required: e.required === '1'
          }
        }),
        reqHeaders: data.req_headers.map(e => {
          return {
            ...e,
            required: e.required === '1',
          }
        }).concat([
          {
            id: nanoid(),
            name: 'Authorization',
            value: '',
            required: true
          },
          {
            id: nanoid(),
            name: 'Tenant-Id',
            value: '',
            required: true
          },
        ]),
        resBody: resInfo?.type === 'object' ? transform2tree(resInfo.properties, resInfo.required) : [],
      },
      requestModel: {},
      requestSccuess: {
        code: '',
      },
      requestFail: {
        code: '',
      },
      errorCode: {

      }
    }
  })
}
const body = ref(null);
function addEvent(){
  body.value = document.querySelector('.interface-drawer .el-drawer__body');
}
function showByYapi(data){
  getInterfaceInfo(data);
  isEdit.value = false;
  visible.value = true;
  nextTick(() => {
    formRef.value?.elForm?.clearValidate();
    fetchModules();
    addEvent();
  });
}
function show(row) {
  isEdit.value = !!row;
  form.value = isEdit.value ? {
    ...row,
    status: row?.status || 'ENABLED',
    interfaceJson: parseAttrs(row.interfaceJson),
    requestModel: row.requestModel,
    requestSccuess: row.requestSccuess,
    requestFail: row.requestFail,
    errorCode: row.errorCode
  } : {
    status: row?.status || 'ENABLED',
    interfaceJson: {},
    requestModel: {},
    requestSccuess: {
      code: '',
    },
    requestFail: {
      code: '',
    },
    errorCode: {}
  };
  visible.value = true;
  nextTick(() => {
    formRef.value?.elForm?.clearValidate();
    fetchModules();
    addEvent();
  });
}
async function validateForm(proFormRef, rule, value, callback){
  try {
    await proFormRef.value?.elForm.validate();
    callback();
  } catch (error) {
    callback(new Error(' '))
  }
}

function extractParamsFromUrlPath(path) {
  // 使用正则表达式匹配 {} 或 : 后面的参数名
  // \{([^}]+)\} 匹配 {} 中的内容
  // :([^/]+) 匹配 : 后面的内容，直到遇到下一个 /
  const regex = /\{([^}]+)\}|:([^/]+)/g;
  const matches = [];
  let match;

  // 使用 exec 方法在字符串中查找所有匹配项
  while ((match = regex.exec(path)) !== null) {
    // match[0] 是整个匹配的字符串（包括 {} 或 :）
    // match[1] 是 {} 中的内容（如果存在）
    // match[2] 是 : 后面的内容（如果存在）
    // 由于我们使用了捕获组 ()，所以我们检查 match[1] 和 match[2] 哪个不为空
    if (match[1]) {
      matches.push(match[1]); // 添加 {} 中的参数名
    } else if (match[2]) {
      matches.push(match[2]); // 添加 : 后面的参数名
    }
  }

  return matches;
}

const columns = computed(() => [
  {
    label: '基本信息',
    colProps: { span: 24 },
    groups: [
      {
        label: '接口名称',
        prop: 'interfaceName',
        type: 'input',
        attrs: {
          maxLength: 64,
        },
        rules: [{ required: true, message: '请输入接口名称', trigger: 'blur' }],
      },
      {
        label: '接口地址',
        prop: 'interfaceUrl',
        type: 'input',
        attrs: {
          maxLength: 200,
        },
        rules: [{ required: true, message: '请输入接口地址', trigger: 'blur' }],
      },
      {
        label: '所属服务',
        prop: 'moduleId',
        type: 'select',
        enums: modules.value,
        attrs: {
          clearable: false,
          filterable: true,
          attrs: {
            onChange(){
              form.value.moduleName = modules.value.find(item => item.value === form.value.moduleId)?.label
            }
          },
        },
        rules: [{ required: true, message: '请选择所属服务', trigger: 'change'}],
        render(){
          function onChange(){
            form.value.moduleName = modules.value.find(item => item.value === form.value.moduleId)?.label
          }
          return  (<Select onChange={onChange} filterable clearable v-model={form.value.moduleId} options={modules.value} placeholder="请选择所属服务" />)
        }
      },
      {
        label: '调用方式',
        prop: 'inOutType',
        type: 'select',
        attrs: {
          clearable: false,
        },
        enums: InOutTypeOptions,
        rules: [{ required: true, message: '请选择', trigger: 'change'}],
      },
      // {
      //   label: '状态',
      //   prop: 'status',
      //   type: 'radio',
      //   attrs: {
      //     clearable: true
      //   },
      //   enums: status,
      //   rules: [{ required: true, message: '请选择状态', trigger: 'change' }],
      // },
    ],
  },
  {
    label: '文档信息',
    colProps: { span: 24 },
    groups: [
      {
        label: '文档信息',
        hideLabel: true,
        prop: 'interfaceJson',
        colProps: { span: 24 },
        type: 'input',
        rules: [{ required: true, validator: (...rest) => validateForm(docsRef, ...rest), trigger: 'change' }],
        render(form){
          const columns = [
            {
              label: '请求方式',
              prop: 'method',
              type: 'radio',
              colProps: { span: 24 },
              enums: methods,
              rules: [{ required: true, message: '请输入接口地址', trigger: 'change' }],
            },
            {
              label: '请求参数',
              colProps: { span: 24 },
              render(interfaceJson){
                const activeName = ref('body')
                return (<el-tabs v-model={activeName.value}>
                  {
                    Object.keys(inputAttrs.value).map(e => {
                      const key =  `req${capitalizeFirstLetter(e)}`
                      if (!interfaceJson.reqPath?.length) {
                        const url = form.interfaceUrl;
                        interfaceJson.reqPath = extractParamsFromUrlPath(url).map(e => ({
                          id: nanoid(),
                          name: e
                        }))
                      }
                      if (!interfaceJson.reqHeaders?.length) {
                        interfaceJson.reqHeaders = [
                          {
                            id: nanoid(),
                            name: 'Authorization',
                            value: '',
                            required: true
                          },
                          {
                            id: nanoid(),
                            name: 'Tenant-Id',
                            value: '',
                            required: true
                          },
                        ]
                      }
                      if (!interfaceJson[key]) interfaceJson[key] = []
                      return <el-tab-pane name={e} label={e}>
                        <EditTable v-model={interfaceJson[key]} columns={inputAttrs.value[e]} />
                      </el-tab-pane>
                    })
                  }
                </el-tabs>)
              }
            },
            {
              label: '返回参数',
              colProps: { span: 24 },
              render(form){
                if (!form.resBody) {
                  form.resBody = []
                }
                return (<EditTable v-model={form.resBody} columns={resBodyColumns.value} />)
              }
            },
          ]
          return  (<yun-pro-form
            ref={docsRef}
            form={form.interfaceJson}
            columns={columns}
            config={{
              colProps: { span: 24 }
            }}
            form-props={{ labelPosition: 'top'}}
          />)
        }
      },
    ],
  },
  {
    label: '调用信息',
    colProps: { span: 24 },
    groups: [
      {
        label: '请求示例',
        prop: 'requestModel',
        colProps: { span: 24 },
        render(form){
          if (!Object.keys(form.requestModel || {}).length) form.requestModel = {
            javascript: { code: '', type: 'javascript' },
            java: { code: '', type: 'java' },
            python: { code: '', type: 'python' },
          }
          const activeName = ref('javascript')
          return (<el-tabs v-model={activeName.value}>
            {
              Object.keys(form.requestModel).map(e => {
                return <el-tab-pane name={e} label={e}>
                  <CodeEditor scrollDom={body.value} v-model={form.requestModel[e].code} language={form.requestModel[e].type}></CodeEditor>
                </el-tab-pane>
              })
            }
          </el-tabs>)
        }
      },
      {
        label: '成功响应',
        prop: 'requestSccuess',
        colProps: { span: 24 },
        render(form){
          if (!form.requestSccuess) form.requestSccuess = { code: '' }
          return <CodeEditor scrollDom={body.value} v-model={form.requestSccuess.code} ></CodeEditor>
        }
      },
      {
        label: '失败响应',
        prop: 'requestFail',
        colProps: { span: 24 },
        render(form){
          if (!form.requestFail) form.requestFail = { code: '' }
          return <CodeEditor scrollDom={body.value} v-model={form.requestFail.code} ></CodeEditor>
        }
      },
      {
        label: '错误码',
        prop: 'errorCode',
        colProps: { span: 24 },
        render(form){
          if (!form.errorCode?.codes) {
            form.errorCode = {
              codes: []
            }
          }
          return (<EditTable v-model={form.errorCode.codes} columns={errorCodeColumns.value} />)
        }
      },
    ],
  },
])


defineExpose({
  show,
  showByYapi
});
</script>
<style scoped lang="scss"></style>