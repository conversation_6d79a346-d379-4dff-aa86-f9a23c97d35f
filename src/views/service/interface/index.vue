<template>
  <div class="table-content">
    <yun-pro-table
      ref="proTableRef"
      v-model:pagination="pagination"
      v-model:filter-data="filterTableData"
      v-model:searchData="searchData"
      :table-columns="columns"
      :search-fields="searchFields"
      :auto-height="true"
      :remote-method="remoteMethod"
      :table-props="tableProps1"
      layout="whole"
    >
      <template #tableHeaderLeft>
        <el-button :icon="Plus" type="primary" @click="handleAdd()"> 新增 </el-button>
        <el-button :icon="Plus" type="primary" @click="handleGetApi()"> 拉取YAPI接口 </el-button>
      </template>
      <template #t_command="{ row }">
				<div class="hidden">
					<span>{{ row.command }}</span>
					<div>
						<el-button v-if="row.command" type="action" @click="handleCopyText(row.command)"> 复制 </el-button>
					</div>
				</div>
			</template>
      <template #t_action="{ row }">
        <yun-rest limit="3">
          <el-button type="text" size="small" @click.prevent="handleEdit(row)" key="edit"> 编辑 </el-button>
          <el-button v-if="row.inOutType === 'IN'" type="text" size="small" @click="handleSync(row)"> 同步 </el-button>
          <el-button type="text" size="small" @click.prevent="handleView(row)" key="view"> 查看 </el-button>
          <el-button type="text" size="small" @click.prevent="handleUpdate(row)" key="update"> {{ row.status === 'ENABLED' ? '禁用' : '启用' }} </el-button>
        </yun-rest>
      </template>
    </yun-pro-table>
    <upsert ref="upsertRef" @getData="getData" />
    <detail ref="detailRef" @getData="getData" />
    <yapiDialog ref="yapiDialogRef" @add="handleAddYapi" />
    <Sync ref="syncRef" @getData="getData"/>
  </div>
</template>

<script setup lang="jsx">
import { ref, reactive, computed, onMounted } from 'vue';
import { useProTable } from '@ylz-use/core';
import { ElMessage, ElMessageBox } from 'yun-design';
import { fetchInterfacePage, removeInterface, updateInterfaceStatus } from '@/api/service/index'
import upsert from './components/upsert.vue'
import detail from './components/detail.vue'
import Select from '@/components/Select/index.vue'
import yapiDialog from './components/yapiDialog.vue'
import Sync from './components/sync.vue'
import { useModules, status, InOutType } from './const'
import dayjs from 'dayjs';

const upsertRef = ref();
const detailRef = ref();
const yapiDialogRef = ref();
const { modules } = useModules();
const columns = computed(() => [
  {
    label: '接口名称',
    prop: 'interfaceName',
  },
  {
    label: '接口地址',
    prop: 'interfaceUrl',
  },
  {
    label: '所属服务',
    prop: 'moduleId',
    render({ row }) {
      return modules.value.find(e => e.value === row.moduleId)?.label || row.moduleName || '--';
    },
  },
  {
    label: '指令',
    prop: 'command',
  },
  {
    label: '调用方式',
    prop: 'inOutType',
    formatter: ({inOutType}) => InOutType[inOutType] || '--'
  },
  {
    label: '状态',
    prop: 'status',
    width: 80,
    render({ row }) {
      return status.find(e => e.value === row.status)?.label || '--';
    },
  },
  {
    label: '已同步',
    prop: 'enterpriseAppList',
    width: 100,
    render: ({ row }) => {
       return row.enterpriseAppList?.length ? 
        <el-popover 
          placement="right" 
          width={400} 
          trigger="click"
          v-slots={{
            reference: () => <el-button type="text" size="small">{`已同步（${row.enterpriseAppList.length}）`}</el-button>,
            default: () => (<el-table data={row.enterpriseAppList}>
              <el-table-column width="150" property="appName" label="应用名称" />
              <el-table-column property="appId" label="APP ID" />
            </el-table>)
          }} /> : '--';
    },
  },
  {
    label: '创建时间',
    prop: 'createTime',
    width: 160,
    render({ row }) {
      return dayjs(row.createTime).format('YYYY-MM-DD HH:mm:ss');
    },
  },
  {
    label: '创建人',
    prop: 'createBy',
    width: 100,
  },
  {
    label: '操作',
    prop: 'action',
    fixed: 'right',
    width: 150,
  },
]);
const searchFields = computed(() => [
  {
    prop: 'interfaceName',
    label: '接口名称',
    component: 'el-input',
    componentAttrs: {
      placeholder: '我是输入框',
    },
  },
  {
    prop: 'moduleId',
    label: '所属服务',
    component: shallowRef(Select),
    componentAttrs: {
      placeholder: '请输入所属服务',
      clearable: true,
      filterable: true,
      options: modules.value
    },
  },
  {
    prop: 'status',
    label: '状态',
    component: 'el-select',
    enums: status,
    componentAttrs: {
      placeholder: '请选择状态',
      clearable: true,
      filterable: true
    },
  }
]);
const { pagination, remoteMethod, tableProps, proTableRef, filterTableData } = useProTable({
  apiFn: fetchInterfacePage,
  paramsHandler(params) {
    return {
      ...params,
    };
  },
  querysHandler(query) {
    return {
      ...query,
      current: pagination.value.page
    };
  },
  responseHandler(result) {
    return result.data;
  },
  plugins: {
    config: {
      columns: columns.value,
      searchFields: searchFields.value,
    },
    list: ['SEARCH_PLUS'],
  },
});
const tableProps1 = {
  ...tableProps,
  "current-row-key": 'id',
}
function getData() {
  proTableRef.value?.getData();
}
function handleEdit(row) {
  upsertRef.value.show(row);
}
function handleAdd() {
  upsertRef.value.show();
}

const syncRef = ref(null);
function handleSync(row) {
  syncRef.value?.show(row);
}

function handleGetApi(){
  yapiDialogRef.value?.show();
}
function handleAddYapi(data){
  upsertRef.value.showByYapi(data);
}
function handleView(row) {
  detailRef.value.show(row);
}
function handleDelete(row) {
  ElMessageBox.confirm('此操作将删除该项, 是否继续?', '删除确认', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'error',
  }).then(async () => {
    await removeInterface({
      ids: [row.id]
    })
    ElMessage.success('删除成功!');
    getData();
  });
}
function handleUpdate(row) {
  ElMessageBox.confirm('此操作将修改该状态, 是否继续?', '状态更新', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'error',
  }).then(async () => {
    await updateInterfaceStatus({
      id: row.id,
      status: row.status === 'ENABLED' ? 'DISABLED' : 'ENABLED'
    })
    ElMessage.success( row.status === 'ENABLED' ? '禁用成功!' : '启用成功!');
    getData();
  });
}
async function handleCopyText(text) {
	try {
		await navigator.clipboard.writeText(text);
		ElMessage.success('文本已复制到剪贴板');
	} catch (err) {
		ElMessage.success('复制到剪贴板失败');
	}
}
</script>

<style scoped lang='scss'>
.table-content {
  width: 100%;
  height: calc(100vh - 120px);
}
.hidden {
	display: grid;
	grid-template-columns: auto 1fr;
	span {
		overflow: hidden;
		white-space: nowrap;
		text-overflow: ellipsis;
	}
}
</style>