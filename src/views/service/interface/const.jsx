import { ref, computed } from 'vue';
import { useDict } from '/@/hooks/dict';
import { fetchProjectPage, fetchModulePage } from '@/api/service/index'

const { belongingSpace } = useDict(
	'belongingSpace',
);

export const belongingSpaceOptions = belongingSpace;
export const status = [
	{ label: '启用', value: 'ENABLED' },
	{ label: '禁用', value: 'DISABLED' },
];
export const methods = [
	{ label: 'GET', value: 'GET' },
	{ label: 'POST', value: 'POST' },
  { label: 'PUT', value: 'PUT' },
  { label: 'DELETE', value: 'DELETE' },
  { label: 'PATCH', value: 'PATCH' },
  { label: 'HEAD', value: 'HEAD' },
  { label: 'OPTIONS', value: 'OPTIONS' },
  { label: 'TRACE', value: 'TRACE' },
  { label: 'CONNECT', value: 'CONNECT' },
];

export const InOutType = {
	IN: '调入',
	OUT: '调出',
};
export const InOutTypeOptions = Object.entries(InOutType).map(([key, val]) => ({
	label: val,
	value: key,
}));
export function capitalizeFirstLetter(string) {
  if (!string) return string;
  return string.charAt(0).toUpperCase() + string.slice(1);
}
export function useProjects() {
  const projects = ref([])
	const fetchList = async (params = {}) => {
		const result = await fetchProjectPage({
      ...params
    }, { page: 1, size: 1000 });
		projects.value = result.data.records.map(e => ({...e, label: e.projectName, value: e.id})) || [];
    return result.data.records || [];
	};
  fetchList();
  return { projects, fetchList };
}
export function useModules() {
  const modules = ref([])
	const fetchList = async (params = {}) => {
		const result = await fetchModulePage({
      // status: 'ENABLED',
      ...params
    }, { page: 1, size: 1000 });
		modules.value = result.data.records.map(e => ({...e, label: e.moduleName, value: e.id})) || [];
    return result.data.records || [];
	};
  fetchList();
  return { modules, fetchList };
}

export function parseAttrs(obj){
  if (typeof obj !== 'object' || obj === null) return {};
  return Object.keys(obj).reduce((acc, key) => {
    const value = obj[key];
    try {
      acc[key] = JSON.parse(decodeURIComponent(value));
    } catch (error) {
      try {
        acc[key] = JSON.parse(value);
      } catch (error) {
        acc[key] = value;
      }
    }
    return acc;
  }, {});
}
export function stringifyAttrs(obj){
  if (typeof obj !== 'object' || obj === null) return {};
  return Object.keys(obj).reduce((acc, key) => {
    const value = obj[key];
    if (typeof value !== 'string'){
      acc[key] = encodeURIComponent(JSON.stringify(value));
    } else { 
      acc[key] = value;
    }
    return acc;
  }, {});
}

export const inputAttrs = computed(() => {
  return {
    body: [
      {
        label: '字段名称',
        prop: 'key',
        width: 250,
        render({ row }){
          return <el-input v-model={row.key} disabled={row.disabled} />
        }
      },
      {
        label: '字段类型',
        prop: 'type',
        width: 110,
        render({ row }){
          const types = [
            { label: 'string', value: 'string' },
            { label: 'number', value: 'number' },
            { label: 'boolean', value: 'boolean' },
            { label: 'object', value: 'object' },
            { label: 'array', value: 'array' },
            { label: 'integer', value: 'integer' },
          ]
          return <el-select v-model={row.type}>
            { types.map(e => <el-option key={e.value} label={e.label} value={e.value} />) }
          </el-select>
        }
      },
      {
        label: '是否必须',
        prop: 'required',
        width: 90,
        render({ row }) {
          return <el-switch v-model={row.required} inline-prompt active-text="是" inactive-text="否"></el-switch>
        }
      },
      {
        label: '默认值',
        prop: 'default',
        width: 250,
        render({ row }){
          return <el-input v-model={row.default} />
        }
      },
      {
        label: '备注',
        prop: 'description',
        render({ row }){
          return <el-input type="textarea" rows={1} v-model={row.description} />
        }
      },
    ],
    query: [
      {
        label: '参数名称',
        prop: 'name',
        render({ row }){
          return <el-input rows={1} v-model={row.name} />
        }
      },
      {
        label: '是否必须',
        prop: 'required',
        width: 90,
        render({ row }){
          return <el-switch v-model={row.required} inline-prompt active-text="是" inactive-text="否"></el-switch>
        }
      },
      {
        label: '示例',
        prop: 'example',
        render({ row }){
          return <el-input rows={1} v-model={row.example} />
        }
      },
      {
        label: '备注',
        prop: 'desc',
        render({ row }){
          return <el-input type="textarea" rows={1} v-model={row.desc} />
        }
      },
    ],
    headers: [
      {
        label: '参数名称',
        prop: 'name',
        render({ row }){
          return <el-input rows={1} v-model={row.name} />
        }
      },
      {
        label: '参数值',
        prop: 'value',
        render({ row }){
          return <el-input rows={1} v-model={row.value} />
        }
      },
      {
        label: '是否必须',
        prop: 'required',
        width: 90,
        render({ row }){
          return <el-switch v-model={row.required} inline-prompt active-text="是" inactive-text="否"></el-switch>
        }
      },
      {
        label: '示例',
        prop: 'example',
        render({ row }){
          return <el-input rows={1} v-model={row.example} />
        }
      },
      {
        label: '备注',
        prop: 'desc',
        render({ row }){
          return <el-input type="textarea" rows={1} v-model={row.desc} />
        }
      },
    ],
    path: [
      {
        label: '参数名称',
        prop: 'name',
        render({ row }){
          return <el-input rows={1} v-model={row.name} />
        }
      },
      {
        label: '示例',
        prop: 'example',
        render({ row }){
          return <el-input rows={1} v-model={row.example} />
        }
      },
      {
        label: '备注',
        prop: 'desc',
        render({ row }){
          return <el-input type="textarea" rows={1} v-model={row.desc} />
        }
      },
    ],
  }
});
export const resBodyColumns = computed(() => [
  {
    label: '字段名称',
    prop: 'key',
    width: 250,
    render({ row }){
      return <el-input v-model={row.key} disabled={row.disabled} />
    }
  },
  {
    label: '字段类型',
    prop: 'type',
    width: 110,
    render({ row }){
      const types = [
        { label: 'string', value: 'string' },
        { label: 'number', value: 'number' },
        { label: 'boolean', value: 'boolean' },
        { label: 'object', value: 'object' },
        { label: 'array', value: 'array' },
        { label: 'integer', value: 'integer' },
      ]
      return <el-select v-model={row.type}>
        { types.map(e => <el-option key={e.value} label={e.label} value={e.value} />) }
      </el-select>
    }
  },
  {
    label: '是否必须',
    prop: 'required',
    width: 90,
    render({ row }) {
      return <el-switch v-model={row.required} inline-prompt active-text="是" inactive-text="否"></el-switch>
    }
  },
  {
    label: '默认值',
    prop: 'default',
    width: 250,
    render({ row }){
      return <el-input v-model={row.default} />
    }
  },
  {
    label: '备注',
    prop: 'description',
    render({ row }){
      return <el-input type="textarea" rows={1} v-model={row.description} />
    }
  },
])
export const errorCodeColumns = computed(() => [
  {
    label: '错误码',
    prop: 'code',
    width: 120,
    render({ row }){
      return <el-input v-model={row.code} />
    }
  },
  {
    label: '错误信息',
    prop: 'info',
    render({ row }){
      return <el-input v-model={row.info} /> 
    }
  },
  {
    label: '描述',
    prop: 'desc',
    render({ row }) {
      return <el-input v-model={row.desc} />
    }
  },
])