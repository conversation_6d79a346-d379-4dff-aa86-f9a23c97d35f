import { ref, computed, onMounted } from 'vue';
import dayjs from 'dayjs';
import { StatusOptions } from '../const';
import useHandler from '@/views/workflow/hooks/useHandler';

export default () => {
	const { allCategoryList, getAllCategoryList } = useHandler();

	const searchFields = computed(() => [
		{
			label: '流程名称',
			prop: 'name',
			componentAttrs: {
				placeholder: '请输入流程名称',
				labelWidth: 80,
			},
			colAttrs: {
				// lg: 4,
				// md: 4,
				// sm: 8,
				// xl: 4,
				// xs: 8,
				span: 8,
			},
		},
		{
			label: '流程编码',
			prop: 'code',
			componentAttrs: {
				placeholder: '请输入流程编码',
				labelWidth: 80,
			},
			colAttrs: {
				// lg: 4,
				// md: 4,
				// sm: 8,
				// xl: 4,
				// xs: 8,
				span: 8,
			},
		},
		{
			label: '分类名称',
			prop: 'category',
			component: 'el-select',
			componentAttrs: {
				placeholder: '请选择分类',
				labelWidth: 80,
				filterable: true,
			},
			subComponent: 'el-option',
			options: allCategoryList.value,
			colAttrs: {
				// lg: 4,
				// md: 4,
				// sm: 8,
				// xl: 4,
				// xs: 8,
				span: 8,
			},
		},
		{
			label: '流程状态',
			prop: 'status',
			component: 'el-select',
			componentAttrs: {
				placeholder: '请选择流程状态',
				labelWidth: 80,
			},
			subComponent: 'el-option',
			options: StatusOptions,
			colAttrs: {
				// lg: 4,
				// md: 4,
				// sm: 8,
				// xl: 4,
				// xs: 8,
				span: 8,
			},
		},
	]);

	const tableColumns = ref([
		// {
		// 	type: 'selection',
		// 	width: 52,
		// 	fixed: 'left',
		// },
		{
			label: '流程名称',
			prop: 'name',
      minWidth: 240,
		},
		{
			label: '流程编码',
			prop: 'code',
			minWidth: 140,
		},
		{
			label: '分类名称',
			prop: 'categoryName',
			minWidth: 120,
		},
		// {
		// 	label: '流程ID',
		// 	prop: 'flowId',
		// },
		{
			label: '版本',
			prop: 'version',
			minWidth: 80,
			render: ({row}) => {
				return <el-tag type="primary">v{row.version}</el-tag>;
			},
		},
		// {
		// 	label: '备注信息',
		// 	prop: 'remark',
		// 	minWidth: 160,
		// },
		// {
		// 	label: '创建时间',
		// 	prop: 'createTime',
		// 	minWidth: 160,
		// },
    {
			label: '状态',
			prop: 'statusDesc',
			minWidth: 100,
		},
		{
			label: '更新时间',
			prop: 'updateTime',
			minWidth: 120,
      formatter: (row) => dayjs(row.updateTime).format('YYYY-MM-DD'),
		},
		{
			label: '操作',
			prop: 'action',
			width: 200,
			fixed: 'right',
		},
	]);

	onMounted(async () => {
		await getAllCategoryList();
	});

	return {
		searchFields,
		tableColumns,
	};
};
