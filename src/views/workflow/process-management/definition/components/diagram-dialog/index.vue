<template>
	<yun-dialog
		v-model="visible"
		title="流程图"
		:size="'large'"
		:hasFooter="!!diagramUrl"
	>
		<div
			class="workflow-process-definition-diagram-content"
			v-loading="loading"
		>
			<img
				v-if="diagramUrl"
				:src="diagramUrl"
				alt="流程图"
				class="diagram-image"
			/>
			<el-empty
				v-else
				description="暂无流程图"
			></el-empty>
		</div>

		<template #footer>
			<el-button
				v-if="diagramUrl"
				type="primary"
				@click="downloadImage"
				:loading="downloadLoading"
			>
				下载图片
			</el-button>
		</template>
	</yun-dialog>
</template>

<script setup>
import { ref, watch, onUnmounted } from 'vue';
import { diagramProcess } from '@/api/workflow/process-management';
import { ElMessage } from 'yun-design';

const loading = ref(false);
const downloadLoading = ref(false);
const visible = ref(false);
const currentRow = ref({});
const diagramBlob = ref(null);
const diagramUrl = ref(null);

// 清理 blob URL 的函数
function cleanup() {
	if (diagramUrl.value && diagramUrl.value.startsWith('blob:')) {
		URL.revokeObjectURL(diagramUrl.value);
	}
	diagramUrl.value = null;
}

// 完全清理所有数据的函数
function fullCleanup() {
	cleanup();
	diagramBlob.value = null;
}

// 将 blob 转换为可显示的 URL
function createImageUrl(blob) {
	// 只清理之前的 URL，不清理 blob 数据
	if (diagramUrl.value && diagramUrl.value.startsWith('blob:')) {
		URL.revokeObjectURL(diagramUrl.value);
	}

	if (!blob || !(blob instanceof Blob)) {
		return null;
	}

	try {
		return URL.createObjectURL(blob);
	} catch (error) {
		ElMessage.error('图片加载失败');
		return null;
	}
}

// 下载图片功能
async function downloadImage() {
	// 添加更详细的检查
	if (!currentRow.value.code) {
		ElMessage.warning('流程代码不能为空');
		return;
	}

	if (!diagramBlob.value) {
		ElMessage.warning('暂无可下载的图片，请先加载流程图');
		return;
	}

	if (!(diagramBlob.value instanceof Blob)) {
		ElMessage.warning('图片数据格式错误');
		return;
	}

	if (diagramBlob.value.size === 0) {
		ElMessage.warning('图片数据为空');
		return;
	}

	downloadLoading.value = true;
	try {
		// 创建下载链接
		const downloadUrl = URL.createObjectURL(diagramBlob.value);
		const link = document.createElement('a');
		link.href = downloadUrl;
		link.download = `流程图_${currentRow.value.code}_${new Date().getTime()}.png`;

		// 触发下载
		document.body.appendChild(link);
		link.click();
		document.body.removeChild(link);

		// 清理下载链接
		URL.revokeObjectURL(downloadUrl);

		ElMessage.success('图片下载成功');
	} catch (error) {
		ElMessage.error('图片下载失败');
	} finally {
		downloadLoading.value = false;
	}
}

// 监听 diagramBlob 变化，转换为可显示的 URL
watch(diagramBlob, (newBlob) => {
	if (newBlob) {
		diagramUrl.value = createImageUrl(newBlob);
	} else {
		cleanup();
	}
});

// 监听对话框关闭，清理资源
watch(visible, (newVisible) => {
	if (!newVisible) {
		fullCleanup();
	}
});

// 组件卸载时清理资源
onUnmounted(() => {
	fullCleanup();
});

async function show(row = {}) {
	visible.value = true;
	currentRow.value = row || {};

	if (!row.code) {
		ElMessage.warning('流程代码不能为空');
		return;
	}

	// 清理之前的数据
	cleanup();

	loading.value = true;
	try {
		// API 返回的是 blob 对象
		const blob = await diagramProcess({
			code: row.code,
		});

		// 验证返回的是否为有效的 blob
		if (blob && blob instanceof Blob && blob.size > 0) {
			diagramBlob.value = blob;
		} else {
			diagramBlob.value = null;
			ElMessage.warning('获取流程图失败，返回数据为空');
		}
	} catch (error) {
		diagramBlob.value = null;
		ElMessage.error('获取流程图失败');
	} finally {
		setTimeout(() => {
			loading.value = false;
		}, 500);
	}
}

defineExpose({
	show,
});
</script>

<style scoped>
.workflow-process-definition-diagram-content {
	height: 480px;
	display: flex;
	align-items: center;
	justify-content: center;
	overflow: hidden;
}

.diagram-image {
	width: 100%;
	/* max-height: 100%; */
	object-fit: contain;
	border-radius: 4px;
	box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}
</style>
