import { computed, onMounted } from 'vue';
import useHandler from '@/views/workflow/hooks/useHandler';

export function useColumns({codeDisabled } = {}) {
	const { allCategoryList, getAllCategoryList } = useHandler();

	onMounted(async () => {
		await getAllCategoryList();
	});

	const columns = computed(() => {
		return [
			{
				prop: 'name',
				label: '流程名称',
				type: 'input',
				attrs: {
					// disabled: false,
					maxlength: 64,
					placeholder: '请输入流程名称',
				},
				colProps: { span: 24 },
				rules: [
					{ trigger: 'blur', required: true, message: '请输入流程名称' },
					{ trigger: 'blur', max: 64, message: '长度在不能超过64个字符' },
					// { trigger: 'blur', max: 120, message: '长度在不能超过120个字符' },
				],
			},
			{
				prop: 'code',
				label: `流程编码`,
				type: 'input',
				attrs: {
					placeholder: `请输入流程编码`,
					disabled: !!codeDisabled.value,
					maxlength: 64,
				},
				colProps: { span: 24 },
				rules: [
					{ trigger: 'blur', required: true, message: '请输入流程编码' },
					// 分类编码必须以字母开头，后面可以跟字母、数字或下划线
					{ pattern: /^[A-Za-z][A-Za-z0-9_]*$/, trigger: 'blur', message: '流程编码必须以字母开头，只能包含字母、数字和下划线' },
					{ trigger: 'blur', max: 64, message: '长度在不能超过64个字符' },
					// { pattern: /^[A-Z0-9]{1,4}$/, trigger: 'blur', message: '请输入1-4位大写字母或数字' },
				],
			},
			{
				prop: 'category',
				label: '分类名称',
				type: 'select',
				colProps: { span: 24 },
				attrs: {
					disabled: false,
					placeholder: '请选择分类名称',
					// disabled: currentAction.value === ActionType.EDIT,
				},
				enums: allCategoryList.value,
				rules: [{ trigger: 'blur', required: true, message: '请选择分类名称' }],
			},
			{
				prop: 'remark',
				label: '备注信息',
				type: 'input',
				attrs: {
					type: 'textarea',
					rows: 4,
					maxlength: 200,
          disabled: false,
					showWordLimit: true,
					placeholder: '请输入备注信息',
					// disabled: currentAction.value === ActionType.EDIT,
				},
				colProps: { span: 24 },
				rules: [{ trigger: 'blur', required: false, message: '请输入备注信息' }],
			},
		];
	});
	return {
		columns,
	};
}
