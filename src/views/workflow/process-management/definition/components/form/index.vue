<template>
	<yun-drawer
		:show-footer="false"
		v-model="visible"
		:title="title"
		size="100vw"
		@close="handleClose"
		custom-class="custom-define-drawer-container"
	>
		<template #title>
			<div class="title-container">
				<div class="title-left">
					<div class="title-left-item">
						<div class="title-left-item-title">
							<span>{{title}}</span>
						</div>
					</div>
				</div>
				<div class="title-right" v-if="currentAction !== ActionType.VIEW">
          <template v-if="step === StepEnum.BASIC_INFO">
            <el-button
              :loading="loadingBasic"
              @click.stop="handleClose"
              >取消</el-button
            >
            <el-button
              type="primary"
              :loading="loadingBasic"
              @click.stop="() => confirmBasic()"
              >下一步</el-button
            >
          </template>
          <template v-if="step === StepEnum.PROCESS_DESIGN">
            <el-button
              :loading="loadingProcess"
              @click.stop="handleBack"
              v-if="![ActionType.CHANGE_VERSION].includes(currentAction)"
              >上一步</el-button
            >
            <el-button
              type="primary"
              :loading="loadingProcess"
              @click.stop="() => confirmProcess({ autoPublish: false })"
              >保存</el-button
            >
            <el-button
              type="primary"
              :loading="loadingProcess"
              @click.stop="() => confirmProcess({ autoPublish: true })"
              >保存并发布</el-button
            >
          </template>
        </div>
			</div>
		</template>
		<div class="define-form-containers">
			<!-- <div
				class="step-container"
				v-if="![ActionType.CHANGE_VERSION].includes(currentAction)"
			>
				<el-steps
					:active="step"
					align-center
					finish-status="success"
					process-status="success"
				>
					<el-step
						title="基本信息"
						description=""
						:icon="Edit"
					></el-step>
					<el-step
						title="流程设计"
						description=""
						:icon="Histogram"
					></el-step>
				</el-steps>
			</div> -->
			<div
				v-loading="loading"
				class="form-content"
				:class="{ 'form-container-change-version': currentAction === ActionType.CHANGE_VERSION }"
			>
				<div
					v-if="step === StepEnum.BASIC_INFO"
					class="form-container"
				>
					<yun-pro-form
						ref="formRef"
						:form="form"
						:columns="columns"
						:config="config"
						custom-class="common-pro-form"
						:form-props="{ labelPosition: 'top', labelWidth: '136px', disabled: currentAction === ActionType.VIEW }"
					>
					</yun-pro-form>
				</div>
				<div
					v-if="step === StepEnum.PROCESS_DESIGN"
					class="process-container"
				>
					<ProcessDesigner
						:key="`designer-${basicInfo?.id}`"
						ref="modelDesignerRef"
						v-loading="designerLoading"
						:designer-form="designerForm"
						:bpmn-xml="bpmnXml"
						:scene="[ActionType.EDIT, ActionType.CHANGE_VERSION].includes(currentAction) || !!basicInfo.id ? 'PROCESS_EDIT' : null"
					/>
				</div>
			</div>
		</div>
		<template #footer>
			<div></div>
		</template>
	</yun-drawer>
</template>

<script setup lang="jsx">
import { ref, nextTick, computed, reactive } from 'vue';
import { ElMessage } from 'yun-design';
import { useForm } from '@/hooks/useForm';
import { ActionType, StepEnum } from '../../const';
import { useColumns } from './useColumns';
import { createProcess, updateProcess, designProcess, detailProcess } from '@/api/workflow/process-management';
import { Edit, Histogram } from '@element-plus/icons-vue';
import ProcessDesigner from '@/components/ProcessDesigner/index.vue';

const designerForm = reactive({
	modelId: '',
	form: {
		processName: '',
		processKey: '',
	},
});
const modelDesignerRef = ref(null);
const bpmnXml = ref('');

const step = ref(StepEnum.BASIC_INFO);
const emits = defineEmits(['refresh']);
const currentAction = ref(ActionType.ADD);
const { config, resetForm, form, formRef, clearValidate, setForm } = useForm();
const currentRow = ref({});
const visible = ref(false);
const loading = ref(false);
const loadingBasic = ref(false);
const loadingProcess = ref(false);
const basicInfo = ref(null);
const designerLoading = ref(false);
const codeDisabled = computed(() => {
	return currentAction.value === ActionType.EDIT || basicInfo.value?.id ? true : false;
});
const editInfo = computed(() => {
	const obj = { ...(currentRow.value || {}), ...(basicInfo.value || {}) };
	return {
		code: obj?.code,
		name: obj?.name,
		category: obj?.category,
		remark: obj?.remark,
	};
});

const isSameData = computed(() => {
	return (
		editInfo.value?.name === form.value?.name &&
		editInfo.value?.code === form.value?.code &&
		editInfo.value?.category === form.value?.category &&
		String(editInfo.value?.remark || null) === String(form.value?.remark || null)
	);
});

const { columns } = useColumns({
	codeDisabled,
});
const title = computed(() => {
	if (currentAction.value === ActionType.VIEW) {
		return '查看流程定义';
	}
	if (currentAction.value === ActionType.EDIT) {
		return '编辑流程定义';
	}
	if (currentAction.value === ActionType.CHANGE_VERSION) {
		return '切换版本';
	}
	return '新增流程定义';
});

function handleBack() {
	step.value = StepEnum.BASIC_INFO;
}

async function confirmBasic() {
	if (loadingBasic.value) return;
	await formRef.value?.elForm?.validate();

	if (isSameData.value) {
		Object.assign(designerForm.form, {
			processName: form.value?.name || null,
			processKey: form.value?.code || null,
			// modelId: currentRow.value?.modelId || null,
		});
		setTimeout(() => {
			step.value = StepEnum.PROCESS_DESIGN;
		}, 200);
		return;
	}

	loadingBasic.value = true;
	try {
		if (currentAction.value === ActionType.ADD) {
			bpmnXml.value = null;
			const data = await createProcess(form.value);
			basicInfo.value = data?.data;
		} else if (currentAction.value === ActionType.EDIT) {
			const data = await updateProcess(form.value);
			basicInfo.value = data?.data;
		}
		Object.assign(designerForm.form, {
			processName: basicInfo.value?.name || null,
			processKey: basicInfo.value?.code || null,
			// modelId: currentRow.value?.modelId || null,
		});
		setTimeout(() => {
			ElMessage.success('操作成功');
			step.value = StepEnum.PROCESS_DESIGN;
		}, 200);
	} catch (error) {
		ElMessage.error('操作失败');
	}
	loadingBasic.value = false;
}

async function confirmProcess(extParams = {}) {
	// console.log(modelDesignerRef.value.xmlString);
	if (loadingProcess.value) return;
	loadingProcess.value = true;
	try {
		await designProcess({ ...editInfo.value, ...extParams, content: modelDesignerRef.value?.getXmlString() || null });
		ElMessage.success('操作成功');
		handleClose();
		emits('refresh');
	} catch (error) {
		// ElMessage.error('操作失败');
	}
	loadingProcess.value = false;
}

async function loadDetail() {
	try {
		const res = await detailProcess({ code: currentRow.value.code });
		if (res?.data) {
			currentRow.value = res?.data || {};
		}
		// eslint-disable-next-line no-empty
	} catch (error) {}
}

async function show({ row = null, action = ActionType.ADD } = {}) {
	basicInfo.value = null;
	form.value = {};

	currentRow.value = row;
	currentAction.value = action;
	visible.value = true;
	loading.value = true;
	if ([ActionType.EDIT, ActionType.VIEW].includes(action)) {
		await loadDetail();
		setForm({
			code: currentRow.value?.code,
			name: currentRow.value?.name,
			category: currentRow.value?.category,
			remark: currentRow.value?.remark,
			id: currentRow.value?.id,
			content: currentRow.value?.content,
		});

		// 流程相关
		bpmnXml.value = currentRow.value?.content || null;
		Object.assign(designerForm.form, {
			processName: currentRow.value?.name || null,
			processKey: currentRow.value?.code || null,
			// modelId: currentRow.value?.modelId || null,
		});
	}
	if (currentAction.value === ActionType.CHANGE_VERSION) {
		step.value = null;
		// 流程相关
		bpmnXml.value = currentRow.value?.content || null;
		Object.assign(designerForm.form, {
			processName: currentRow.value?.name || null,
			processKey: currentRow.value?.code || null,
			// modelId: currentRow.value?.modelId || null,
		});
		setTimeout(() => {
			step.value = StepEnum.PROCESS_DESIGN;
		}, 200);
	}
	loading.value = false;
}

function handleClose() {
	step.value = StepEnum.BASIC_INFO;
	visible.value = false;
	resetForm();
	nextTick(() => {
		clearValidate();
	});
}

defineExpose({
	show,
});
</script>
<style lang="scss">
.custom-define-drawer-container.yun-drawer .el-drawer__body {
	padding: 0 !important;
}
</style>
<style scoped lang="scss">


.title-container {
	display: flex;
	justify-content: space-between;
	align-items: center;
	width: 100%;

	.title-left {
		display: flex;
		align-items: center;
		flex: 1;

		.title-left-item {
			.title-left-item-title {
				span {
					font-size: 16px;
					font-weight: 500;
					color: var(--el-text-color-primary);
				}
			}
		}
	}

	.title-right {
		display: flex;
		align-items: center;
		justify-content: flex-end;
		gap: 8px;
		flex: 1;
		box-sizing: border-box;
		padding-right: 24px;
	}
}
.define-form-containers {
	// padding-bottom: 24px;
	box-sizing: border-box;
	height: 100%;
	width: 100%;
	overflow: hidden;
	display: flex;
	flex-direction: column;
}
.step-container {
	width: 400px;
	margin: 0 auto;
	flex-shrink: 0;
}
.form-content {
	flex: 1;
	overflow: auto;
	.form-container {
		width: 520px;
		margin: 0 auto;
	}
	&.form-container-change-version {
		height: 100%;
	}
}

.process-container {
	width: 100%;
	height: 100%;
}
</style>
