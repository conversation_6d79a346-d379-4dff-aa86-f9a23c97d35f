// BPMN 查看器主题配置
// 可以根据需要调整这些颜色变量

// 主色调配色方案
$primary-fill: #e3f2fd; // 主要填充色 - 浅蓝色
$primary-stroke: #1976d2; // 主要边框色 - 深蓝色
$primary-text: #424242; // 主要文本色 - 深灰色

// 次要配色方案
$secondary-fill: #fff3e0; // 网关填充色 - 浅橙色
$secondary-stroke: #f57c00; // 网关边框色 - 橙色

// 成功配色方案
$success-fill: #e8f5e8; // 事件填充色 - 浅绿色
$success-stroke: #4caf50; // 事件边框色 - 绿色

// 连接线配色
$connection-stroke: #666666; // 连接线颜色 - 深灰色

// 背景色
$canvas-background: #fff; // 画布背景色

@keyframes processing-dash {
	0% {
		stroke-dashoffset: 100% ;
	}
	to {
		stroke-dashoffset: 0;
	}
}


.node-popover {
	border-radius: 8px;
	box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
	.el-popper__arrow {
		z-index: -1;
		bottom: -5px;
		left: 50%;
		transform: translate(-5px, 0px);
		display: none;
	}

	.node-info {
		.node-title {
			margin: 0 0 12px 0;
			font-size: 16px;
			font-weight: 600;
			color: var(--el-text-color-primary);
			border-bottom: 1px solid var(--el-border-color-lighter);
			padding-bottom: 8px;
		}

		.node-details {
			.detail-item {
				display: flex;
				align-items: flex-start;
				margin-bottom: 8px;
				font-size: 14px;

				&:last-child {
					margin-bottom: 0;
				}

				.detail-label {
					min-width: 110px;
					color: var(--el-text-color-secondary);
					font-weight: 500;
				}

				.detail-value {
					color: var(--el-text-color-primary);
					word-break: break-all;
					flex: 1;
					max-width: 400px;
				}
			}
		}
	}
}

.process-view-container {
	width: 100%;
	height: 100%;
	position: relative;
	display: flex;
	padding: 6px 0;
	box-sizing: border-box;
	.djs-palette {
		display: none !important;
	}

	.process-panel-container {
		// position: absolute;
		// top: 0;
		// right: 0;
		width: 500px;
		height: 100%;
		// z-index: 10;
		background-color: #fff;
		flex-shrink: 0;
		transition: opacity 0.3s cubic-bezier(0.4, 0, 0.2, 1);
		opacity: 1;
	}
	.process-view-container-header {
		position: absolute;
		top: 50%;
		right: 486px;
		z-index: 10;
		font-size: 20px;
		cursor: pointer;
		background-color: #fff;
		padding: 4px;
		box-shadow: 0 0 8px #cccccc;
		display: flex;
		align-items: center;
		justify-content: center;
	}
	&.expand {
		.process-view-container-header {
			right: 6px;
		}
		.process-panel-container {
			width: 0 !important;
			padding: 0 !important;
			opacity: 0 !important;
		}
	}
}

// BPMN 元素默认样式
.bpmn-custom-theme {
	// width: 100%;
	height: 100%;
	flex: 1;
	/* height: v-bind(height); */
	/* height: calc(80vh - 120px) !important; */
	// border: 1px solid #e4e7ed;
	// border-radius: 6px;
	position: relative;
	overflow: hidden;
	background-color: #fff;
	border-left: 1px solid #eeeeee;
	box-shadow: 0 0 8px #cccccc;
	box-sizing: border-box;
	/* 画布背景 */
	.djs-container {
		background-color: $canvas-background;
	}

	// 默认连接线箭头颜色
	.djs-element.djs-connection > .djs-visual > path {
		marker-end: url(#sequenceflow-end-white-default) !important;
	}

	// 覆盖BPMN.js默认的箭头样式
	.djs-connection > .djs-visual > path[marker-end] {
		marker-end: url(#sequenceflow-end-white-default) !important;
	}

	// 流程线
	.highlight.djs-shape .djs-visual > :nth-child(1) {
		fill: #56bb56 !important;
		stroke: #56bb56 !important;
		fill-opacity: 0.2 !important;
	}

	.highlight.djs-shape .djs-visual > :nth-child(2) {
		fill: #56bb56 !important;
		stroke: #56bb56 !important;
	}

	.highlight.djs-shape .djs-visual > path {
		fill: #56bb56 !important;
		fill-opacity: 0.2 !important;
		stroke: #56bb56 !important;
	}

	.highlight.djs-connection > .djs-visual > path {
		stroke: #56bb56 !important;
		marker-end: url(#sequenceflow-end-white-success) !important;
	}

	.highlight-todo.djs-connection > .djs-visual > path {
		stroke: #eab24a !important;
		stroke-dasharray: 4px !important;
		fill-opacity: 0.2 !important;
		marker-end: url(#sequenceflow-end-white-warning) !important;
	}

	// // 成功状态的连接线
	// .success-connection.djs-connection > .djs-visual > path {
	// 	stroke: #56bb56 !important;
	// 	marker-end: url(#sequenceflow-end-white-success) !important;
	// }

	// // 警告状态的连接线
	// .warning-connection.djs-connection > .djs-visual > path {
	// 	stroke: #eab24a !important;
	// 	marker-end: url(#sequenceflow-end-white-warning) !important;
	// }

	// // 错误状态的连接线
	// .error-connection.djs-connection > .djs-visual > path {
	// 	stroke: #f56c6c !important;
	// 	marker-end: url(#sequenceflow-end-white-error) !important;
	// }

	.highlight-todo.djs-shape .djs-visual > :nth-child(1) {
		stroke-dasharray: 5, 5;
		stroke-dashoffset: 500;
		stroke: #eab24a !important;
		fill: rgba(252, 211, 127, 0.2) !important;
		animation: processing-dash 24s linear infinite;
	}
	.highlight-todo.djs-shape .djs-visual > :nth-child(2) {
		fill: #eab24a !important;
	}
	.highlight-todo.djs-shape .djs-visual > path {
		// fill: #eab24a !important;
		fill-opacity: 0.2 !important;
		stroke: #eab24a !important;
	}


	.error-tip.djs-shape .djs-visual > :nth-child(1) {
		stroke-dasharray: 5, 5;
		stroke-dashoffset: 500;
		stroke: red  !important;
		fill: rgb(252, 127, 127,0.2) !important;
		animation: processing-dash 24s linear infinite;
	}
	.error-tip.djs-shape .djs-visual > :nth-child(2) {
		fill: red  !important;
	}
	.error-tip.djs-shape .djs-visual > path {
		// fill: #eab24a !important;
		fill-opacity: 0.2 !important;
		stroke: red !important;
	}

	@keyframes draw {
		100% {
			stroke-dashoffset: 0;
		}
	}

	.process-status {
		position: absolute;
		width: auto;
		height: auto;

		display: flex;
		float: right;
		top: 10px;
		left: 10px;
		font-size: 12px;

		.intro {
			color: #303133;
			margin-top: 5px;
		}

		.finish {
			background-color: #e8ffea;
			padding: 4px;
			border: 1px solid rgba(0, 180, 42, 0.1);
			border-radius: 3px;
			color: #56bb56;
			margin-right: 8px;
		}

		.processing {
			background-color: #fcf5ea;
			padding: 4px;
			border: 1px solid #fce9c7;
			border-radius: 3px;
			color: #eab24a;
			margin-right: 8px;
		}

		.todo {
			padding: 4px;
			background: #ecedee;
			border: 1px solid rgba(204, 204, 204, 0.1);
			border-radius: 3px;
			color: #666666;
			margin-right: 5px;
		}
	}

	// /* 任务节点 (矩形) */
	// .djs-element.djs-shape .djs-visual rect {
	// 	fill: $primary-fill !important;
	// 	stroke: $primary-stroke !important;
	// 	stroke-width: 2px !important;
	// }

	// /* 网关节点 (菱形) */
	// .djs-element.djs-shape .djs-visual polygon {
	// 	fill: $secondary-fill !important;
	// 	stroke: $secondary-stroke !important;
	// 	stroke-width: 2px !important;
	// }

	// /* 事件节点 (圆形) */
	// .djs-element.djs-shape .djs-visual circle {
	// 	fill: $success-fill !important;
	// 	stroke: $success-stroke !important;
	// 	stroke-width: 2px !important;
	// }

	// /* 连接线 */
	// .djs-element.djs-connection .djs-visual path {
	// 	// stroke: $connection-stroke !important;
	// 	stroke: $success-stroke !important;
	// 	stroke-width: 2px !important;
	// }

	// /* 文本标签 */
	// .djs-element .djs-label text {
	// 	fill: $primary-text !important;
	// 	font-family: 'Segoe UI', Arial, sans-serif !important;
	// 	font-size: 12px !important;
	// 	font-weight: 500 !important;
	// }

	// /* 悬停效果 */
	// .djs-element:hover {
	// 	.djs-visual rect {
	// 		stroke-width: 3px !important;
	// 		filter: brightness(0.95);
	// 	}

	// 	.djs-visual circle {
	// 		stroke-width: 3px !important;
	// 		filter: brightness(0.95);
	// 	}

	// 	.djs-visual polygon {
	// 		stroke-width: 3px !important;
	// 		filter: brightness(0.95);
	// 	}
	// }

	// /* 选中状态 */
	// .djs-element.selected {
	// 	.djs-visual rect,
	// 	.djs-visual circle,
	// 	.djs-visual polygon {
	// 		stroke-width: 3px !important;
	// 		stroke-dasharray: 5, 5 !important;
	// 	}
	// }

	// .djs-element.djs-shape.on-passed {
	// 	opacity: 0.8;
	// }
	// .djs-element.djs-shape.on-processing {
	// 	animation: processing-dash 24s linear infinite;
	// }
	// .djs-element.djs-shape.on-processing .djs-visual rect,
	// .djs-element.djs-shape.on-processing .djs-visual circle,
	// .djs-element.djs-shape.on-processing .djs-visual polygon {
	// 	stroke-dasharray: 4, 4;
	// }
	// .djs-element.on-processing.djs-shape[class*='Gateway'] .djs-visual rect,
	// .djs-element.on-processing.djs-shape[class*='Gateway'] .djs-visual circle {
	// 	stroke-dasharray: 0;
	// 	animation: unset;
	// }
	// @keyframes processing-dash {
	// 	0% {
	// 		stroke-dashoffset: 100%;
	// 	}

	// 	to {
	// 		stroke-dashoffset: 0;
	// 	}
	// }
}

// 深色主题（可选）
// .bpmn-dark-theme {
// 	$dark-primary-fill: #263238;
// 	$dark-primary-stroke: #00bcd4;
// 	$dark-primary-text: #ffffff;
// 	$dark-canvas-background: #1e1e1e;

// 	.djs-container {
// 		background-color: $dark-canvas-background;
// 	}

// 	.djs-element.djs-shape .djs-visual rect {
// 		fill: $dark-primary-fill !important;
// 		stroke: $dark-primary-stroke !important;
// 		stroke-width: 2px !important;
// 	}

// 	.djs-element .djs-label text {
// 		fill: $dark-primary-text !important;
// 	}
// }

// // 高对比度主题（可选）
// .bpmn-high-contrast-theme {
// 	.djs-element.djs-shape .djs-visual rect {
// 		fill: #ffffff !important;
// 		stroke: #000000 !important;
// 		stroke-width: 3px !important;
// 	}

// 	.djs-element.djs-shape .djs-visual circle {
// 		fill: #ffffff !important;
// 		stroke: #000000 !important;
// 		stroke-width: 3px !important;
// 	}

// 	.djs-element .djs-label text {
// 		fill: #000000 !important;
// 		font-weight: bold !important;
// 	}
// }
