<!--
BPMN流程图查看器组件 - 支持自定义箭头颜色

使用示例：
1. 获取组件引用：
   const processViewRef = ref()

2. 设置单个连接线箭头颜色：
   processViewRef.value.setConnectionArrowColor('SequenceFlow_1', 'success')  // 绿色
   processViewRef.value.setConnectionArrowColor('SequenceFlow_2', 'warning')  // 橙色
   processViewRef.value.setConnectionArrowColor('SequenceFlow_3', 'error')    // 红色
   processViewRef.value.setConnectionArrowColor('SequenceFlow_4', 'default')  // 灰色

3. 批量设置多个连接线箭头颜色：
   processViewRef.value.setMultipleConnectionArrowColors([
     { id: 'SequenceFlow_1', color: 'success' },
     { id: 'SequenceFlow_2', color: 'warning' },
     { id: 'SequenceFlow_3', color: 'error' }
   ])

可用颜色：
- success: 绿色 (#56bb56)
- warning: 橙色 (#eab24a)
- error: 红色 (#f56c6c)
- default: 灰色 (#666666)
-->

<template>
	<!-- <yun-dialog
		v-model="visible"
		title="流程图"
		:hasFooter="false"
		size="X-large"
		custom-class="process-view-dialog"
		appendToBody
	> -->
	<div>
		<yun-drawer
			:show-footer="type === ExportPreviewType.HISTORY && !finalLoading"
			v-model="visible"
			title="流程图"
			:size="'100vw'"
			@close="closeDrawer"
			appendToBody
			destroy-on-close
			:show-cancel-button="false"
			confirm-button-text="切换版本"
			@confirm="handleSwitchVersion"
		>
			<div
				class="process-view-container"
				:class="{ expand: isExpand }"
				v-loading="finalLoading"
			>
				<!-- <template v-if="currentXML"> -->
					<div class="process-view-container-header">
						<el-icon
							v-if="isExpand"
							@click="toggleExpand(false)"
							><Fold
						/></el-icon>
						<el-icon
							v-else
							@click="toggleExpand(true)"
							><Expand
						/></el-icon>
					</div>
					<div
						ref="bpmnContainer"
						class="bpmn-custom-theme"
					></div>
					<MyProcessPenal
						:bpmn-modeler="bpmnViewer"
						:id-edit-disabled="true"
						prefix="flowable"
						:width="isExpand ? 0 : 500"
						class="process-panel-container"
						ref="processPenalRef"
						scene="DETAIL"
					/>
				<!-- </template>
				<template v-else>
					<el-empty style="margin: 0 auto" description="流程图不存在" />
				</template> -->
			</div>
		</yun-drawer>
		<FormDrawer
			ref="formDrawerRef"
			@refresh="refresh"
		/>

		<!-- 节点信息弹出框 -->
		<el-popover
			ref="nodePopoverRef"
			:visible="popoverVisible"
			v-if="visible"
			placement="bottom"
			:width="currentNodeData?.active ? 'max-content' : '300px'"
			trigger="manual"
			:offset="0"
			popper-class="node-popover"
		>
			<div class="node-info">
				<!-- <h4 class="node-title">{{ currentNodeData.name || '--' }}</h4> -->
				<div class="node-details">
					<div class="detail-item">
						<div class="detail-label">节点ID:</div>
						<div class="detail-value">{{ currentNodeData.activityId || '--' }}</div>
					</div>
					<div
						class="detail-item"
						v-if="currentNodeData.activityName"
					>
						<div class="detail-label">节点名称:</div>
						<div class="detail-value">{{ currentNodeData.activityName || '--' }}</div>
					</div>
					<div class="detail-item">
						<div class="detail-label">节点类型:</div>
						<div class="detail-value">{{ getNodeTypeText(currentNodeData.activityType) }}</div>
					</div>
					<template v-if="currentNodeData?.active">
						<div class="detail-item">
							<div class="detail-label">状态:</div>
							<div class="detail-value">
								<el-tag
									v-if="currentNodeData.completed"
									type="success"
								>
									已完成
								</el-tag>
								<el-tag
									v-else
									type="warning"
								>
									进行中
								</el-tag>
							</div>
						</div>
						<div class="detail-item">
							<div class="detail-label">持续时间(毫秒):</div>
							<div class="detail-value">{{ currentNodeData.durationInMillis || '--' }}</div>
						</div>
						<div class="detail-item">
							<div class="detail-label">开始时间:</div>
							<div class="detail-value">{{ currentNodeData.startTime || '--' }}</div>
						</div>
						<div class="detail-item">
							<div class="detail-label">结束时间:</div>
							<div class="detail-value">{{ currentNodeData.endTime || '--' }}</div>
						</div>
						<div class="detail-item" v-if="currentNodeData.hasError">
							<div class="detail-label">错误原因:</div>
							<div class="detail-value" :title="currentNodeData.errorReason">
							  {{currentNodeData.errorReason || '--'}}
							</div>
						</div>
						<div class="detail-item" v-if="currentNodeData.variables">
							<div class="detail-label">变量:</div>
							<div class="detail-value" v-if="Object.keys(currentNodeData.variables).length > 0">
								<JsonEditor
										readonly
										:modelValue="currentNodeData.variables"
										codec
									/>
							</div>
							<div class="detail-value" v-else>--</div>
						</div>
					</template>
				</div>
			</div>
		</el-popover>
		<!-- </yun-dialog> -->
    <flowViewWarp ref="flowViewWarpRef"></flowViewWarp>
	</div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, nextTick, computed } from 'vue';
import BpmnModeler from 'bpmn-js/lib/Modeler';
import { ElMessage } from 'yun-design';
import { ExportPreviewType, ActionType, type ExportPreviewTypeType, getNodeTypeText } from '../../const';
import FormDrawer from '@/views/workflow/process-management/definition/components/form/index.vue';
import MyProcessPenal from '@/package/penal/PropertiesPanel.vue';
import flowViewWarp from '/@/views/flow/task/components/flow-view-warp.vue';
// import { detailProcess, xmlHistory, xmlActivityProcessInstance } from '@/api/workflow/process-management/index.ts';
import { xmlHistory, xmlActivityProcessInstance } from '@/api/workflow/process-management/index';
import { Expand, Fold } from '@yun-design/icons-vue';
import JsonEditor from '@axolo/json-editor-vue';

const isExpand = ref(false);

// Popover 相关状态
const popoverVisible = ref(false);
const nodePopoverRef = ref();
const currentNodeData = ref<any>({});
let popoverTimer: NodeJS.Timeout | null = null;

interface Props {
	type?: ExportPreviewTypeType;
}

// 高亮状态枚举
enum HighlightType {
	HIGHLIGHT = 'highlight',
	HIGHLIGHT_TODO = 'highlight-todo',
	ERROR_TIP = 'error-tip'
}

const props = withDefaults(defineProps<Props>(), {
	type: ExportPreviewType.DETAIL,
});
const emits = defineEmits(['refresh']);
const formDrawerRef = ref<InstanceType<typeof FormDrawer>>();
const processPenalRef = ref<InstanceType<typeof MyProcessPenal>>();
const currentRow = ref<any>({});
const currentXML = ref<string>('');
const bpmnContainer = ref<HTMLElement>();
const visible = ref(false);
const loading = ref(false);
const internalLoading = ref(false);
let bpmnViewer: InstanceType<typeof BpmnModeler> | null = null;

const nodes = ref<any[]>([
	// {
	// 	key: 'startEvent',
	// 	completed: true,
	// },
]);

// 显示节点信息弹窗
function showNodePopover(element: any, mouseEvent?: MouseEvent) {
	if (!element) return;
	// 设置当前节点数据
	const info = nodes.value?.find((node) => node?.activityId === element?.id);
	if (info) {
		currentNodeData.value = { ...info, active: true };
	} else {
		currentNodeData.value = {
			activityId: element.id,
			activityName: element.businessObject?.name || '',
			activityType: element.type,
			businessObject: element.businessObject,
			active: false,
		};
	}
	// 清除之前的定时器
	if (popoverTimer) {
		clearTimeout(popoverTimer);
		popoverTimer = null;
	}

	// 显示弹窗
	popoverVisible.value = true;
	// 设置弹窗位置
	setTimeout(() => {
		if (nodePopoverRef.value) {
			try {
				// 直接访问 popover 实例的 popperRef
				const popoverEl: any = document.querySelector('.node-popover');
				if (popoverEl) {
					// 获取弹窗尺寸
					const popoverRect = popoverEl.getBoundingClientRect();
					const popoverWidth = popoverRect.width || 300;
					const popoverHeight = popoverRect.height || 150;

					// 获取视口尺寸
					const viewportWidth = window.innerWidth;
					const viewportHeight = window.innerHeight;

					let eventX = 0;
					let eventY = 0;

					// 优先使用 SVG 节点的位置信息
					try {
						// 查找对应的 SVG 元素
						const svgElement = document.querySelector(`[data-element-id="${element.id}"]`);
						if (svgElement) {
							const svgRect = svgElement.getBoundingClientRect();
							// 使用 SVG 节点的中心点作为弹窗定位基准
							eventX = svgRect.left + svgRect.width / 2;
							eventY = svgRect.top + svgRect.height / 2;
						} else if (mouseEvent) {
							// 降级方案：使用鼠标事件坐标
							eventX = mouseEvent.clientX;
							eventY = mouseEvent.clientY;
						} else {
							// 最后降级方案：使用屏幕中心
							eventX = viewportWidth / 2;
							eventY = viewportHeight / 2;
						}
					} catch (error) {
						// SVG 元素获取失败时的降级处理
						if (mouseEvent) {
							eventX = mouseEvent.clientX;
							eventY = mouseEvent.clientY;
						} else {
							eventX = viewportWidth / 2;
							eventY = viewportHeight / 2;
						}
					}

					// 弹窗间距
					const offset = 40;
					const margin = 10;

					// 计算弹窗位置（默认在事件点上方居中）
					let popoverX = eventX - popoverWidth / 2;
					let popoverY = eventY - popoverHeight - offset;

					// 垂直方向优先检测（上方 -> 下方 -> 左右方向）
					const canFitAbove = popoverY >= margin;
					const canFitBelow = eventY + offset + popoverHeight <= viewportHeight - margin;

					if (canFitAbove) {
						// 上方空间足够，使用上方位置
						popoverY = eventY - popoverHeight - offset;
					} else if (canFitBelow) {
						// 下方空间足够，使用下方位置
						popoverY = eventY + offset;
					} else {
						// 上下都不足，检查左右区域
						const canFitLeft = eventX - popoverWidth - offset >= margin;
						const canFitRight = eventX + offset + popoverWidth <= viewportWidth - margin;

						if (canFitRight) {
							// 右侧空间足够，显示在右侧
							popoverX = eventX + offset + 40;
							popoverY = eventY - popoverHeight / 2;
						} else if (canFitLeft) {
							// 左侧空间足够，显示在左侧
							popoverX = eventX - popoverWidth - offset;
							popoverY = eventY - popoverHeight / 2;
						} else {
							// 四个方向都不足，居中显示
							popoverX = (viewportWidth - popoverWidth) / 2;
							popoverY = (viewportHeight - popoverHeight) / 2;
						}
					}

					// 水平边界检测和调整（仅在垂直显示时进行）
					if (canFitAbove || canFitBelow) {
						if (popoverX < margin) {
							// 左边界
							popoverX = margin;
						} else if (popoverX + popoverWidth > viewportWidth - margin) {
							// 右边界
							popoverX = viewportWidth - popoverWidth - margin;
						}
					}

					// 垂直边界检测和调整（仅在水平显示时进行）
					if (!canFitAbove && !canFitBelow) {
						if (popoverY < margin) {
							// 上边界
							popoverY = margin;
						} else if (popoverY + popoverHeight > viewportHeight - margin) {
							// 下边界
							popoverY = viewportHeight - popoverHeight - margin;
						}
					}

					// 应用计算后的位置
					popoverEl.style.position = 'absolute';
					popoverEl.style.left = `${popoverX}px`;
					popoverEl.style.top = `${popoverY}px`;
					popoverEl.style.zIndex = '9999';
				}
			} catch (error) {
				// 如果设置位置失败，使用默认位置
				// 静默处理位置设置失败的情况
			}
		}
	}, 200);

	// 添加 Popover 事件监听器
	addPopoverEventListeners();
}

// 隐藏节点信息弹窗
function hideNodePopover() {
	// 添加延迟隐藏，给用户足够时间移入Popover
	popoverTimer = setTimeout(() => {
		popoverVisible.value = false;
		currentNodeData.value = {};
	}, 500);
}

// 处理 Popover 鼠标移入事件
function handlePopoverMouseEnter() {
	// 鼠标移入 Popover 时，清除定时器，保持显示
	if (popoverTimer) {
		clearTimeout(popoverTimer);
		popoverTimer = null;
	}
}

// 处理 Popover 鼠标移出事件
function handlePopoverMouseLeave() {
	// 鼠标移出 Popover 时，延迟隐藏
	hideNodePopover();
}

// 添加 Popover DOM 事件监听器
function addPopoverEventListeners() {
	// 延迟执行，确保 Popover DOM 已渲染
	setTimeout(() => {
		const popoverEl = document.querySelector('.node-popover');
		if (popoverEl) {
			// 移除可能存在的旧事件监听器
			popoverEl.removeEventListener('mouseenter', handlePopoverMouseEnter);
			popoverEl.removeEventListener('mouseleave', handlePopoverMouseLeave);

			// 添加新的事件监听器
			popoverEl.addEventListener('mouseenter', handlePopoverMouseEnter);
			popoverEl.addEventListener('mouseleave', handlePopoverMouseLeave);
		}
	}, 250);
}

function toggleExpand(bool: boolean) {
	isExpand.value = bool;
	setTimeout(() => {
		fitViewport();
		// const canvas = bpmnViewer.get('canvas');
		// canvas.zoom('fit-viewport');
	}, 200);
}

// 初始化 BPMN 查看器
function initViewer() {
	if (!bpmnContainer.value) return;

	try {
		// 创建只读的 BPMN Modeler，禁用所有编辑功能
		bpmnViewer = new BpmnModeler({
			container: bpmnContainer.value,
			keyboard: {
				bindTo: document,
			},
			// 禁用编辑相关的模块
			additionalModules: [],
			moddleExtensions: {},
		});

		// 禁用所有交互功能，使其变为只读模式
		disableInteractiveFeatures();

		// 添加元素点击事件监听
		setupElementClickListener();

		// 创建自定义箭头marker
		createCustomMarkers();

		// 初始化属性面板
		setTimeout(() => {
			if (processPenalRef.value && bpmnViewer) {
				// 手动调用属性面板的初始化方法，传入 modeler 实例
				processPenalRef.value.initModels();
			}
		}, 200);
	} catch (error) {
		ElMessage.error('BPMN 查看器初始化失败');
	}
}

// 禁用交互功能，使 Modeler 变为只读模式
function disableInteractiveFeatures() {
	if (!bpmnViewer) return;
	try {
		// 禁用上下文菜单
		const contextPad = bpmnViewer.get('contextPad');
		contextPad.close();
		contextPad.isOpen = () => false;
		contextPad.open = () => {};

		// 禁用调色板
		const palette = bpmnViewer.get('palette');
		palette.close();
		palette.isOpen = () => false;
		palette.open = () => {};

		// 禁用直接编辑
		const directEditing = bpmnViewer.get('directEditing');
		directEditing.isActive = () => false;
		directEditing.activate = () => {};

		// 禁用拖拽创建
		const create = bpmnViewer.get('create');
		create.isActive = () => false;
		create.start = () => {};

		// 禁用移动
		const move = bpmnViewer.get('move');
		move.isActive = () => false;
		move.start = () => {};

		// 禁用连接
		const connect = bpmnViewer.get('connect');
		connect.isActive = () => false;
		connect.start = () => {};

		// 禁用调整大小
		const resize = bpmnViewer.get('resize');
		resize.isActive = () => false;
		resize.activate = () => {};

		// 禁用建模功能
		const modeling = bpmnViewer.get('modeling');
		const originalMethods = [
			'moveElements',
			'updateProperties',
			'createElements',
			'removeElements',
			'distributeElements',
			'alignElements',
			'resizeShape',
			'createConnection',
			'reconnectStart',
			'reconnectEnd',
			'replaceShape',
		];

		originalMethods.forEach((method) => {
			if (modeling[method]) {
				modeling[method] = () => {
					// ElMessage.warning('当前为只读模式，无法进行编辑操作');
				};
			}
		});

		// 阻止键盘快捷键
		const keyboard = bpmnViewer.get('keyboard');
		keyboard.addListener(() => false);
	} catch (error) {
		// 静默处理禁用功能失败的情况
	}
}


const flowViewWarpRef = ref();

const cellClick = (row: any) => {
	const { processInstanceId } = row;
  if (!processInstanceId) return;
	flowViewWarpRef.value.deal({
		processInstanceId,
	});
};

// 设置元素点击事件监听器
function setupElementClickListener() {
	if (!bpmnViewer) return;
	try {
		const eventBus = bpmnViewer.get('eventBus');

		// 监听元素点击事件
		eventBus.on('element.click', (event: any) => {
			// 点击事件处理逻辑
			// 过滤掉根元素和画布点击
			// 在需要时可以通过 event.element 获取元素信息
      const {id, $type } = event?.element?.businessObject || {}
      if (!$type.includes('CallActivity')) {
        return;
      }
      const item = nodes.value.find((item: any) => item.activityId === id);
      if (!item || !item.calledProcessInstanceId) {
        return;
      }
      cellClick({
        processInstanceId: item.calledProcessInstanceId,
      })
		});

		// 监听元素鼠标移入事件
		eventBus.on('element.hover', (event: any) => {
			// 鼠标移入事件处理逻辑
			const { element, originalEvent } = event || {};
			const { type, id } = element || {};
			if (id && !['bpmn:Process', 'bpmn:Collaboration', 'label', 'bpmn:SequenceFlow'].includes(type)) {
				showNodePopover(element, originalEvent);
			}
		});

		// 监听元素鼠标移出事件
		eventBus.on('element.out', (event: any) => {
			// 鼠标移出事件处理逻辑
			const { element } = event || {};
			const { type, id } = element || {};
			if (id && !['bpmn:Process', 'bpmn:Collaboration', 'label', 'bpmn:SequenceFlow'].includes(type)) {
				// 隐藏弹窗
				hideNodePopover();
			}
		});
	} catch (error) {
		// 静默处理设置监听器失败的情况
		ElMessage.warning('元素事件监听器设置失败');
	}
}

function closeDrawer() {
	destroyViewer();
	visible.value = false;
}

// 加载 XML 数据
async function loadXML(xml: string) {
	if (!bpmnViewer || !xml) return;

	internalLoading.value = true;
	try {
		await bpmnViewer.importXML(xml);

		// 等待下一个 tick 确保 DOM 已更新
		await nextTick();

		// 自动适配画布大小 - 使用更强大的 fitViewport 方法
		fitViewport();

		// 应用节点状态高亮
		applyNodeStatus(nodes.value);
	} catch (error) {
		// console.log('error', error);
		ElMessage.error('流程图加载失败，请检查 XML 格式');
	} finally {
		internalLoading.value = false;
	}
}

function addMarker(canvas: any, nodeId: string, className: string) {
	if (canvas && nodeId && className) {
		try {
			canvas.addMarker(nodeId, className);
			canvas.addMarker(`${nodeId}_label`, className);
		} catch (error) {
			// 静默处理标记添加失败，避免影响用户体验
			// console.log('error', error);
		}
	}
}

function removeMarker(canvas: any, nodeId: string, className: string) {
	if (canvas && nodeId && className) {
		try {
			canvas.removeMarker(nodeId, className);
			// canvas.removeMarker(`${nodeId}_label`, className);
		} catch (error) {
			// 静默处理标记移除失败，避免影响用户体验
			// console.log('error', error);
		}
	}
}

// 应用节点状态高亮
function applyNodeStatus(flowData: any[]) {
	if (!bpmnViewer || props.type !== ExportPreviewType.PROCESS_DEFINITION) return;
	const elementRegistry = bpmnViewer.get('elementRegistry');
	const canvas = bpmnViewer.get('canvas');
	// 清除所有现有的标记
	clearAllMarkers();
	// 获取所有元素并清除标记
	elementRegistry.getAll().forEach((element: any) => {
		const id = element.id;
		const completeTask = flowData.find((m) => m.key === id);
		if (completeTask) {
			// addMarker(canvas, id, completeTask.completed ? HighlightType.HIGHLIGHT : HighlightType.HIGHLIGHT_TODO);
			if(completeTask.completed){
				addMarker(canvas, id, HighlightType.HIGHLIGHT);
			}else{
				if(completeTask.hasError){
					addMarker(canvas, id, HighlightType.ERROR_TIP);
				}else{
					addMarker(canvas, id, HighlightType.HIGHLIGHT_TODO);
				}
			}
		}
	});
}

// 清除所有标记
function clearAllMarkers() {
	if (!bpmnViewer) return;
	const canvas = bpmnViewer.get('canvas');
	const elementRegistry = bpmnViewer.get('elementRegistry');
	// 获取所有元素并清除标记
	elementRegistry.getAll().forEach((element: any) => {
		removeMarker(canvas, element.id, HighlightType.HIGHLIGHT);
		removeMarker(canvas, element.id, HighlightType.HIGHLIGHT_TODO);
		removeMarker(canvas, element.id, HighlightType.ERROR_TIP);
	});
}

// 清理查看器
function destroyViewer() {
	if (bpmnViewer) {
		try {
			bpmnViewer.destroy();
			bpmnViewer = null;
		} catch (error) {
			ElMessage.error('BPMN Viewer 销毁失败');
		}
	}
}

// 计算最终的 loading 状态
const finalLoading = computed(() => loading.value || internalLoading.value);

// 获取当前的 BPMN 查看器实例
function getViewer() {
	return bpmnViewer;
}

function refresh() {
	emits('refresh');
	visible.value = false;
}

// 缩放
function zoom(factor: number) {
	if (bpmnViewer) {
		const canvas = bpmnViewer.get('canvas');
		canvas.zoom(factor);
	}
}
// 新增：自适应画布大小的完整方法
function fitViewport() {
	if (!bpmnViewer) return;

	try {
		const canvas = bpmnViewer.get('canvas');
		const containerElement = bpmnContainer.value;

		if (!containerElement) return;

		// 获取当前容器的实际尺寸
		const containerWidth = containerElement.clientWidth;
		const containerHeight = containerElement.clientHeight;

		// 获取所有有效元素来计算内容边界
		const elementRegistry = bpmnViewer.get('elementRegistry');
		const elements = elementRegistry
			.getAll()
			.filter((element: any) => element.id && element.type !== 'bpmn:Process' && element.type !== 'bpmn:Collaboration' && element.type !== 'label');

		if (elements.length === 0) {
			canvas.zoom('fit-viewport');
			return;
		}

		// 计算所有元素的边界框
		let minX = Infinity,
			minY = Infinity,
			maxX = -Infinity,
			maxY = -Infinity;

		elements.forEach((element: any) => {
			if (element.waypoints) {
				element.waypoints.forEach((point: any) => {
					minX = Math.min(minX, point.x);
					minY = Math.min(minY, point.y);
					maxX = Math.max(maxX, point.x);
					maxY = Math.max(maxY, point.y);
				});
			} else if (element.x !== undefined && element.y !== undefined) {
				minX = Math.min(minX, element.x);
				minY = Math.min(minY, element.y);
				maxX = Math.max(maxX, element.x + (element.width || 0));
				maxY = Math.max(maxY, element.y + (element.height || 0));
			}
		});

		if (minX === Infinity) {
			canvas.zoom('fit-viewport');
			return;
		}

		// 计算内容的实际尺寸
		const contentWidth = maxX - minX;
		const contentHeight = maxY - minY;

		// 计算内容中心
		const contentCenterX = (minX + maxX) / 2;
		const contentCenterY = (minY + maxY) / 2;

		// 预留边距
		const margin = 60;
		const availableWidth = containerWidth - 2 * margin;
		const availableHeight = containerHeight - 2 * margin;

		// 根据当前容器尺寸计算最佳缩放比例
		const scaleX = availableWidth / contentWidth;
		const scaleY = availableHeight / contentHeight;
		const optimalScale = Math.min(scaleX, scaleY, 1.5); // 最大150%缩放

		// 应用缩放
		canvas.zoom(optimalScale);

		// 等待缩放完成后居中
		setTimeout(() => {
			try {
				// 计算容器中心在画布坐标系中的位置
				const containerCenterX = containerWidth / 2 / optimalScale;
				const containerCenterY = containerHeight / 2 / optimalScale;

				// 设置视口位置实现居中
				canvas.viewbox({
					x: contentCenterX - containerCenterX,
					y: contentCenterY - containerCenterY,
					width: containerWidth / optimalScale,
					height: containerHeight / optimalScale,
				});
			} catch (centerError) {
				// 如果精确居中失败，使用简单的滚动方式
				try {
					const scrollX = containerWidth / 2 - contentCenterX * optimalScale;
					const scrollY = containerHeight / 2 - contentCenterY * optimalScale;

					canvas.scroll({
						dx: scrollX,
						dy: scrollY,
					});
				} catch (scrollError) {
					// 最后的降级方案
					canvas.zoom('fit-viewport');
				}
			}
		}, 100);
	} catch (error) {
		// 如果整个过程失败，至少确保有基本的适配
		try {
			const canvas = bpmnViewer.get('canvas');
			canvas.zoom('fit-viewport');
		} catch (fallbackError) {
			// 静默处理
		}
	}
}

async function loadXmlHistory() {
	try {
		const res = await xmlHistory({ processDefinitionId: currentRow.value.flowId || currentRow.value.processDefinitionId });
		currentXML.value = res?.data || '';
	} catch (error) {
		ElMessage.error('流程图加载失败，请检查 XML 格式');
	}
}

// async function loadDetail() {
// 	try {
// 		const res = await detailProcess({ code: currentRow.value.code });
// 		currentXML.value = res?.data?.content || '';
// 	} catch (error) {
// 		ElMessage.error('流程图加载失败，请检查 XML 格式');
// 	}
// }

async function loadActivityProcessDetail() {
	try {
		const res = await xmlActivityProcessInstance({ processInstanceId: currentRow.value.processInstanceId });
		currentXML.value = res?.data?.xml || '';

		const activities = res?.data?.activities || [];
		nodes.value = activities.map((item: any) => ({
			...item,
			key: item.activityId,
			completed: item.completed,
		}));
	} catch (error) {
		ElMessage.error('流程图加载失败，请检查 XML 格式');
	}
}

async function loadXMLByType() {
	// 入口-流程定义-流程图
	if (props.type === ExportPreviewType.DETAIL) {
		// await loadDetail();
		await loadXmlHistory();
	// 入口-流程定义-历史版本-流程图
	} else if (props.type === ExportPreviewType.HISTORY) {
		await loadXmlHistory();
	// 入口-流程实例-流程图
	} else if (props.type === ExportPreviewType.PROCESS_DEFINITION) {
		await loadActivityProcessDetail();
	}
	if (currentXML.value) {
		await loadXML(currentXML.value);
	}
}

// function handleSwitchVersion(done, loadingRef) {
function handleSwitchVersion() {
	const params = {
		code: currentRow.value.processDefinitionKey,
		name: currentRow.value.processDefinitionName,
		id: currentRow.value.processDefinitionId,
		content: currentXML.value,
	};
	formDrawerRef.value.show({
		action: ActionType.CHANGE_VERSION,
		row: params,
	});
}

// 创建自定义SVG marker用于箭头颜色
function createCustomMarkers() {
	if (!bpmnViewer) return;

	try {
		const canvas = bpmnViewer.get('canvas');
		const svgElement = canvas._svg;

		// 获取或创建defs元素
		let defs = svgElement.querySelector('defs');
		if (!defs) {
			defs = document.createElementNS('http://www.w3.org/2000/svg', 'defs');
			svgElement.appendChild(defs);
		}

		// 创建成功状态的绿色箭头marker
		const successMarker = document.createElementNS('http://www.w3.org/2000/svg', 'marker');
		successMarker.setAttribute('id', 'sequenceflow-end-white-success');
		successMarker.setAttribute('viewBox', '0 0 20 20');
		successMarker.setAttribute('refX', '11');
		successMarker.setAttribute('refY', '10');
		successMarker.setAttribute('markerWidth', '10');
		successMarker.setAttribute('markerHeight', '10');
		successMarker.setAttribute('orient', 'auto');

		const successPath = document.createElementNS('http://www.w3.org/2000/svg', 'path');
		successPath.setAttribute('d', 'M 1 5 L 11 10 L 1 15 Z');
		successPath.setAttribute('fill', '#56bb56');
		successPath.setAttribute('stroke', '#56bb56');
		successPath.setAttribute('stroke-width', '1px');
		successMarker.appendChild(successPath);

		// 创建警告状态的橙色箭头marker
		const warningMarker = document.createElementNS('http://www.w3.org/2000/svg', 'marker');
		warningMarker.setAttribute('id', 'sequenceflow-end-white-warning');
		warningMarker.setAttribute('viewBox', '0 0 20 20');
		warningMarker.setAttribute('refX', '11');
		warningMarker.setAttribute('refY', '10');
		warningMarker.setAttribute('markerWidth', '10');
		warningMarker.setAttribute('markerHeight', '10');
		warningMarker.setAttribute('orient', 'auto');

		const warningPath = document.createElementNS('http://www.w3.org/2000/svg', 'path');
		warningPath.setAttribute('d', 'M 1 5 L 11 10 L 1 15 Z');
		warningPath.setAttribute('fill', '#eab24a');
		warningPath.setAttribute('stroke', '#eab24a');
		warningPath.setAttribute('stroke-width', '1px');
		warningMarker.appendChild(warningPath);

		// 创建默认状态的灰色箭头marker
		const defaultMarker = document.createElementNS('http://www.w3.org/2000/svg', 'marker');
		defaultMarker.setAttribute('id', 'sequenceflow-end-white-default');
		defaultMarker.setAttribute('viewBox', '0 0 20 20');
		defaultMarker.setAttribute('refX', '11');
		defaultMarker.setAttribute('refY', '10');
		defaultMarker.setAttribute('markerWidth', '10');
		defaultMarker.setAttribute('markerHeight', '10');
		defaultMarker.setAttribute('orient', 'auto');

		const defaultPath = document.createElementNS('http://www.w3.org/2000/svg', 'path');
		defaultPath.setAttribute('d', 'M 1 5 L 11 10 L 1 15 Z');
		defaultPath.setAttribute('fill', '#000');
		defaultPath.setAttribute('stroke', '#000');
		defaultPath.setAttribute('stroke-width', '1px');
		defaultMarker.appendChild(defaultPath);

		// 创建错误状态的红色箭头marker
		const errorMarker = document.createElementNS('http://www.w3.org/2000/svg', 'marker');
		errorMarker.setAttribute('id', 'sequenceflow-end-white-error');
		errorMarker.setAttribute('viewBox', '0 0 20 20');
		errorMarker.setAttribute('refX', '11');
		errorMarker.setAttribute('refY', '10');
		errorMarker.setAttribute('markerWidth', '10');
		errorMarker.setAttribute('markerHeight', '10');
		errorMarker.setAttribute('orient', 'auto');

		const errorPath = document.createElementNS('http://www.w3.org/2000/svg', 'path');
		errorPath.setAttribute('d', 'M 1 5 L 11 10 L 1 15 Z');
		errorPath.setAttribute('fill', '#f56c6c');
		errorPath.setAttribute('stroke', '#f56c6c');
		errorPath.setAttribute('stroke-width', '1px');
		errorMarker.appendChild(errorPath);

		// 将marker添加到defs中
		defs.appendChild(successMarker);
		defs.appendChild(warningMarker);
		defs.appendChild(defaultMarker);
		defs.appendChild(errorMarker);
	} catch (error) {
		// 静默处理创建自定义箭头marker失败的情况
	}
}

// 设置连接线箭头颜色
function setConnectionArrowColor(connectionId: string, color: 'success' | 'warning' | 'error' | 'default' = 'default') {
	if (!bpmnViewer) return;

	try {
		const canvas = bpmnViewer.get('canvas');
		const elementRegistry = bpmnViewer.get('elementRegistry');

		// 获取连接线元素
		const connection = elementRegistry.get(connectionId);
		if (!connection || connection.type !== 'bpmn:SequenceFlow') {
			return;
		}

		// 移除旧的状态类
		canvas.removeMarker(connectionId, 'success-connection');
		canvas.removeMarker(connectionId, 'warning-connection');
		canvas.removeMarker(connectionId, 'error-connection');

		// 添加新的状态类
		if (color !== 'default') {
			canvas.addMarker(connectionId, `${color}-connection`);
		}

		// 直接修改SVG元素的marker-end属性
		const pathElement = document.querySelector(`[data-element-id="${connectionId}"] path`);
		if (pathElement) {
			pathElement.setAttribute('marker-end', `url(#sequenceflow-end-white-${color})`);
		}
	} catch (error) {
		// 静默处理设置箭头颜色失败的情况
	}
}

// 批量设置多个连接线的箭头颜色
function setMultipleConnectionArrowColors(connections: Array<{ id: string; color: 'success' | 'warning' | 'error' | 'default' }>) {
	connections.forEach(({ id, color }) => {
		setConnectionArrowColor(id, color);
	});
}

onMounted(() => {});

function show(row = {}) {
	isExpand.value = false;
	loading.value = true;
	visible.value = true;
	currentRow.value = row || {};
	nextTick(async () => {
		await initViewer();
		await loadXMLByType();
		loading.value = false;
		// if (props.xml) {
		// 	loadXML(props.xml);
		// }
	});
}

onUnmounted(() => {
	destroyViewer();
});

// 暴露方法给父组件
defineExpose({
	getViewer,
	zoom,
	fitViewport,
	loadXML,
	show,
	clearAllMarkers,
	applyNodeStatus,
	showNodePopover,
	hideNodePopover,
	setConnectionArrowColor,
	setMultipleConnectionArrowColors,
});
</script>

<style lang="scss">
@import './bpmn-theme.scss';
</style>
