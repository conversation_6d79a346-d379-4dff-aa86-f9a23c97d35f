<template>
	<yun-dialog
		v-model="visible"
		title="启动【仅用于测试】"
		:size="'small'"
		:confirm-button-handler="confirmHandler"
		:confirm-button-text="'确定'"
		:cancel-button-text="'取消'"
		:before-close="handleClose"
	>
		<yun-pro-form
			ref="formRef"
			custom-class="batch-form"
			:form="form"
			:columns="schema"
			:config="config"
			:form-props="{ labelPosition: 'top', labelWidth: '240px' }"
		/>
	</yun-dialog>
</template>
<script setup>
import { ref, computed } from 'vue';
import { ElMessage } from 'yun-design';
import { useForm } from '@/hooks/useForm';
import { startProcessInstance } from '@/api/workflow/process-management';

const emit = defineEmits(['refresh']);
const visible = ref(false);
const { formRef, form, config, validateBasicForm, resetForm, setForm } = useForm();
const schema = computed(() => [
	{
		prop: 'key',
		type: 'input',
		label: '流程编码',
		attrs: {
			placeholder: '请输入KEY',
			// maxlength: 64,
		},
		colProps: { span: 24 },
		rules: [{ trigger: 'blur', required: true, message: '请输入流程名称' }],
	},
	{
		prop: 'businessKey',
		label: '业务Key',
		type: 'input',
		labelWidth: '0',
		attrs: {
			placeholder: '请输入业务Key',
			// maxlength: 64,
		},
	},
	{
		prop: 'variables',
		label: '变量对象 (JSON对象字符串格式)',
		type: 'input',
		labelWidth: '0',
		attrs: {
			type: 'textarea',
			placeholder: '请输入变量对象',
			// maxlength: 200,
			// rows: 4,
			// showWordLimit: true,
		},
	},
]);

// 			key: row.code,
// 			// variables: {
// 			// 	applicant: '张三',
// 			// 	days: 3,
// 			// 	reason: '身体不适',
// 			// },
// 			// businessKey: row.code,

const show = (row) => {
	setForm({ ...(row || {}), variables: JSON.stringify({}) });
	visible.value = true;
};
function handleClose() {
	resetForm();
	visible.value = false;
	emit('onClose');
}

const confirmHandler = async () => {
	await validateBasicForm();
	try {
		await startProcessInstance({ ...form.value, variables: JSON.parse(form.value.variables || '{}') });
		ElMessage.success('操作成功');
		handleClose();
		emit('refresh');
	} catch (e) {
		// eslint-disable-next-line no-console
		console.log(e);
	}
};

defineExpose({
	show,
});
</script>
<style lang="scss" scoped>
.close-title {
	margin: 0 0 8px 0;
}
</style>
