import { computed, onMounted, ref } from 'vue';
import RenderTable from '@/views/workflow/process-management/components/table.vue';
import JsonEditor from '@axolo/json-editor-vue';

export function useColumns() {
	const activeCollection = ref({});
	const setActiveCollection = (key, value) => {
		activeCollection.value[key] = value;
	};
	const getActiveCollection = (key) => {
		return activeCollection.value[key];
	};

	// 优化的 popover 组件
	const LazyPopover = ({ uniqueKey, data, renderContent, buttonText = '查看' }) => {
		return data && (data?.length > 0 || (typeof data === 'object' && Object.keys(data).length > 0)) ? (
			<el-popover
				placement="top-start"
				width="max-content"
				trigger="click"
				popper-class="json-popover"
				v-if={getActiveCollection(uniqueKey)}
				onHide={() => {
					setActiveCollection(uniqueKey, false);
				}}
				v-slots={{
					default: () => renderContent(data),
					reference: () => (
						<el-button
							onClick={async () => setActiveCollection(uniqueKey, true)}
							type="text"
						>
							{buttonText}
						</el-button>
					),
				}}
			/>
		) : (
			<span>--</span>
		);
	};

	onMounted(async () => {});

	const config = computed(() => {
		return {
			// title: '流程实例详情',
			labelWidth: 100,
			theme: 'simpleGreen',
			descriptions: {
				column: 2,
			},
		};
	});

	const columns = computed(() => {
		return [
			{
				label: '流程名称',
				prop: 'name',
				group: '基本信息',
				// tips: '客户的外部订单号',
			},
			{
				label: '流程编码',
				prop: 'code',
				group: '基本信息',
			},
			{
				label: '分类名称',
				prop: 'categoryName',
				group: '基本信息',
			},
			{
				label: '流程ID',
				prop: 'flowId',
				group: '基本信息',
			},
			{
				label: '状态',
				prop: 'statusDesc',
				group: '基本信息',
			},
			{
				label: '版本',
				prop: 'version',
				group: '基本信息',
				render: (row) => {
					return <el-tag type="primary">v{row.version}</el-tag>;
				},
			},
			{
				label: '创建时间',
				prop: 'createTime',
				group: '基本信息',
			},
			{
				label: '更新时间',
				prop: 'updateTime',
				group: '基本信息',
			},
			{
				label: '备注信息',
				prop: 'remark',
				group: '基本信息',
				span: 2,
			},
			{
				prop: 'processElements',
				group: '流程元素',
				span: 2,
				render: (row) => {
					return (
						<RenderTable
							columns={[
								{ label: '元素ID', prop: 'id', width: 100 },
								{ label: '元素名称', prop: 'name', width: 80 },
								{ label: '元素类型', prop: 'type', width: 80 },
								{ label: '元素分类', prop: 'category', minWidth: 80 },
								{
									label: '开始元素',
									prop: 'isStart',
									render: ({ isStart }) => {
										return <el-tag type="primary">{isStart ? '是' : '否'}</el-tag>;
									},
								},
								{
									label: '结束元素',
									prop: 'isEnd',
									render: ({ isEnd }) => {
										return <el-tag type="primary">{isEnd ? '是' : '否'}</el-tag>;
									},
								},
								{
									label: '输入',
									prop: 'incomingFlows',
									minWidth: 80,
									render: (row) => (
										<LazyPopover
											uniqueKey={`incomingFlows_${row.id}`}
											data={row.incomingFlows}
											renderContent={(data) => (
												<JsonEditor
													readonly={true}
													modelValue={data}
													codec
												/>
											)}
										/>
									),
								},
								{
									label: '输出',
									prop: 'outgoingFlows',
									minWidth: 80,
									render: (row) => (
										<LazyPopover
											uniqueKey={`outgoingFlows_${row.id}`}
											data={row.outgoingFlows}
											renderContent={(data) => (
												<JsonEditor
													readonly={true}
													modelValue={data}
													codec
												/>
											)}
										/>
									),
								},
								{ label: '文档说明', prop: 'documentation', minWidth: 80 },
								{
									prop: 'properties',
									label: '属性',
									render: (row) => (
										<LazyPopover
											uniqueKey={`properties_${row.id}`}
											data={row.properties}
											renderContent={(data) => (
												<RenderTable
													columns={[
														{ label: '属性名称', prop: 'columnName', minWidth: 140 },
														{ label: '属性描述', prop: 'columnDesc', minWidth: 140 },
														{ label: '属性值', prop: 'columnValue', minWidth: 140 },
														{ label: '属性分组', prop: 'propertyGroup', minWidth: 140 },
														{
															label: '关键属性',
															minWidth: 100,
															prop: 'isKey',
															render: ({ isKey }) => {
																return <el-tag type="primary">{isKey ? '是' : '否'}</el-tag>;
															},
														},
													]}
													tableData={data || []}
												/>
											)}
										/>
									),
									span: 2,
								},
								{
									label: '扩展元素',
									prop: 'extensionElements',
									minWidth: 80,
									render: (row) => (
										<LazyPopover
											uniqueKey={`extensionElements_${row.id}`}
											data={row.extensionElements}
											renderContent={(data) => (
												<JsonEditor
													readonly={true}
													modelValue={data}
													codec
												/>
											)}
										/>
									),
								},
							]}
							tableData={row?.processElements || []}
						/>
					);
				},
			},
			{
				prop: 'definitionItems',
				group: '流程定义项',
				render: (row) => {
					return (
						<RenderTable
							columns={[
								{ label: '元素ID', prop: 'elementId', width: 100 },
								{ label: '元素名称', prop: 'elementName', width: 80 },
								{ label: '元素类型', prop: 'elementType', width: 80 },
								{ label: '内容', prop: 'content', minWidth: 160 },
								{ label: '定义项名称', prop: 'title', minWidth: 80 },
								{ label: '定义项类型', prop: 'type', minWidth: 100 },
								{ label: '定义项描述', prop: 'description', minWidth: 160 },
								{
									label: '额外属性',
									prop: 'extraProperties',
									minWidth: 60,
									render: (row) => (
										<LazyPopover
											uniqueKey={`extraProperties_${row.elementId}`}
											data={row.extraProperties ? JSON.parse(row.extraProperties || '{}') : null}
											renderContent={(data) => (
												<JsonEditor
													readonly={true}
													modelValue={data}
													codec
												/>
											)}
										/>
									),
								},
							]}
							tableData={row?.definitionItems || []}
						/>
					);
				},
				span: 2,
			},
		];
	});
	return {
		columns,
		config,
	};
}
