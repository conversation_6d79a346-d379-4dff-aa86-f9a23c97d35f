import { ref, computed, onMounted } from 'vue';

export default () =>  {

	const searchFields = computed(() => []);

	const tableColumns = ref([
		{
			label: 'ID',
			prop: 'processDefinitionId',
			minWidth: 140,
		},
		{
			label: '流程Key',
			prop: 'processDefinitionKey',
			minWidth: 120,
		},
		{
			label: '分类名称',
			prop: 'categoryName',
			minWidth: 120,
		},
		{
			label: '流程名称',
			prop: 'processDefinitionName',
			minWidth: 120,
		},
		{
			label: '流程版本',
			prop: 'version',
			minWidth: 120,
			render: ({row}) => {
				return <el-tag type="primary">v{row.version}</el-tag>;
			},
		},
		{
			label: '部署ID',
			prop: 'deploymentId',
			minWidth: 120,
		},
		{
			label: '部署时间',
			prop: 'deploymentTime',
			minWidth: 180,
		},
		{
			label: '状态',
			prop: 'statusDesc',
			minWidth: 120,
		},
		{
			label: '资源名称',
			prop: 'resourceName',
			minWidth: 120,
		},
		{
			label: '操作',
			prop: 'action',
			width: 100,
			fixed: 'right',
		},
		// {
		// 	label: '流程图名称',
		// 	prop: 'diagramResourceName',
		// 	minWidth: 120,
		// },
	]);

	onMounted(async () => {});

	return {
		searchFields,
		tableColumns,
	};
};
