<template>
	<yun-drawer
		:show-footer="false"
		v-model="visible"
		title="历史版本"
		:size="'100vw'"
		@close="handleClose"
		custom-class="workflow-process-definition-table-drawer"
		appendToBody
		destroy-on-close
	>
		<div
			class="workflow-process-definition-table"
			v-loading="loading"
		>
			<yun-pro-table
				ref="proTableRef"
				v-model:pagination="pagination"
				:table-columns="tableColumns"
				:remote-method="remoteMethod"
				:default-fetch="false"
				:layout="'whole'"
				auto-height
				:table-props="{
					...tableProps,
					'show-table-setting': false,
					'show-summary': false,
				}"
			>
				<template #t_action="{ row }">
					<el-button
						type="text"
						size="small"
						@click.prevent.stop="handleDiagram(row)"
					>
						查看流程图
					</el-button>
					<!-- <el-button
						type="text"
						size="small"
						@click.prevent.stop="handleDiagram(row)"
					>
						切换版本
					</el-button> -->
				</template>
			</yun-pro-table>
		</div>
		<ProcessView
			ref="processViewRef"
			:type="ExportPreviewType.HISTORY"
			@refresh="refresh"
		/>
	</yun-drawer>
</template>

<script setup>
import { ref } from 'vue';
import useColumns from './useColumns';
import { historyProcess } from '@/api/workflow/process-management';
import ProcessView from '../process-view/index.vue';
import { useProTable } from '@ylz-use/core';
import { ExportPreviewType } from '@/views/workflow/process-management/definition/const';

const emits = defineEmits(['refresh']);
const currentRow = ref({});
const loading = ref(false);
const visible = ref(false);
const { tableColumns } = useColumns();
const processViewRef = ref();
const { pagination, remoteMethod, tableProps, proTableRef, reLoad } = useProTable({
	apiFn: historyProcess,
	paramsHandler(params) {
		return {
			code: currentRow.value?.code,
			current: pagination.value.page,
			size: pagination.value.size || pagination.value.pageSize,
			...params,
		};
	},
	customTotalHandler(result) {
		return result?.data?.total || 0;
	},
	querysHandler(query) {
		return {
			...query,
			code: currentRow.value?.code,
			current: pagination.value.page,
			size: pagination.value.size || pagination.value.pageSize,
		};
	},
	responseHandler(result) {
		return result?.data?.records || result?.data || [];
	},
	// plugins: {
	// 	// 挂载插件
	// 	config: {
	// 		columns: tableColumns.value, // 开启搜索增强需要传入列配置
	// 		// searchFields: searchFields.value, // 开启搜索增强需要传入搜索配置
	// 	},
	// 	list: ['SEARCH_PLUS'], // 开启插件列表
	// },
});

function refresh() {
	emits('refresh');
	visible.value = false;
}

function show(row = {}) {
	visible.value = true;
	currentRow.value = row || {};
	setTimeout(() => {
		reLoad();
	}, 500);
}

function handleDiagram(row) {
	processViewRef.value.show(row);
}

function handleClose() {
	visible.value = false;
}

defineExpose({
	show,
});
</script>

<style scoped lang="scss">
.workflow-process-definition-table {
	height: calc(100vh - 110px);
}
</style>
