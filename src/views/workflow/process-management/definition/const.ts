export const StatusEnum = {
	DRAFT: 'DRAFT',
	PENDING: 'PENDING',
	ACTIVE: 'ACTIVE',
} as const;

export const StatusOptions = [
	{
		label: '草稿',
		value: 'DRAFT',
	},
	{
		label: '待发布',
		value: 'PENDING',
	},
	{
		label: '已发布',
		value: 'ACTIVE',
	},
] as const;

// 操作类型
export const ActionType = {
	ADD: 'ADD',
	ADD_CHILD: 'ADD_CHILD',
	EDIT: 'EDIT',
	VIEW: 'VIEW',
	DELETE: 'DELETE',
	CHANGE_VERSION: 'CHANGE_VERSION',
} as const;

export const StepEnum = {
	BASIC_INFO: 0,
	PROCESS_DESIGN: 1,
} as const;

// 导出预览类型
export const ExportPreviewType = {
	DETAIL: 'DETAIL',
	HISTORY: 'HISTORY',
	// 流程定义
	PROCESS_DEFINITION: 'PROCESS_DEFINITION',
} as const;


// 节点类型映射
export const NodeTypeMap: Record<string, string> = {
	'bpmn:StartEvent': '开始事件',
	'bpmn:EndEvent': '结束事件',
	'bpmn:UserTask': '用户任务',
	'bpmn:ServiceTask': '服务任务',
	'bpmn:ScriptTask': '脚本任务',
	'bpmn:BusinessRuleTask': '业务规则任务',
	'bpmn:ManualTask': '手工任务',
	'bpmn:ReceiveTask': '接收任务',
	'bpmn:SendTask': '发送任务',
	'bpmn:ExclusiveGateway': '排他网关',
	'bpmn:InclusiveGateway': '包容网关',
	'bpmn:ParallelGateway': '并行网关',
	'bpmn:EventBasedGateway': '事件网关',
	'bpmn:ComplexGateway': '复杂网关',
	'bpmn:SequenceFlow': '顺序流',
	'bpmn:MessageFlow': '消息流',
	'bpmn:Association': '关联',
	'bpmn:DataObject': '数据对象',
	'bpmn:DataStore': '数据存储',
	'bpmn:SubProcess': '子流程',
	'bpmn:CallActivity': '调用活动',
	'bpmn:IntermediateThrowEvent': '触发事件',
} as const;

// 获取节点类型文本
export function getNodeTypeText(type: string): string {
	// 首先检查是否是完整的 BPMN 类型
	if (NodeTypeMap[type]) {
		return NodeTypeMap[type];
	}

	// 使用正则表达式将驼峰命名转换为 BPMN 类型格式
	// 例如：startEvent -> bpmn:StartEvent, userTask -> bpmn:UserTask, businessRuleTask -> bpmn:BusinessRuleTask
	const pascalCase = String(type).replace(/^[a-z]/, (match) => match.toUpperCase());
	const bpmnType = `bpmn:${pascalCase}`;

	// 查找转换后的类型
	if (NodeTypeMap[bpmnType]) {
		return NodeTypeMap[bpmnType];
	}

	// 如果都没找到，返回原始类型或默认值
	// return type || '未知类型';
	return type || '--';
}


// 类型定义
export type StatusEnumType = typeof StatusEnum[keyof typeof StatusEnum];
export type ActionTypeType = typeof ActionType[keyof typeof ActionType];
export type StepEnumType = typeof StepEnum[keyof typeof StepEnum];
export type ExportPreviewTypeType = typeof ExportPreviewType[keyof typeof ExportPreviewType];
export type NodeTypeMapType = typeof NodeTypeMap[keyof typeof NodeTypeMap];
