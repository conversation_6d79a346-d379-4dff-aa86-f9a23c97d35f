<template>
	<div class="workflow-task-process">
		<yun-pro-table
			ref="proTableRef"
			v-model:pagination="pagination"
			v-model:filter-data="filterTableData"
			:search-fields="searchFields"
			:table-columns="tableColumns"
			:remote-method="remoteMethod"
			:default-fetch="true"
			:layout="'whole'"
			auto-height
			:table-props="{
				...tableProps,
				'show-table-setting': true,
				'show-summary': false,
			}"
		>
			<template #tableHeaderLeft>
				<el-button
					:icon="Plus"
					type="primary"
					@click.prevent.stop="handleCreate"
				>
					新增
				</el-button>
			</template>
      <template #t_name="{ row }">
        <el-link type="primary" @click.prevent.stop="handleDetail(row)">
          {{ row.name }}
        </el-link>
      </template>
      <template #t_statusDesc="{ row }">
        <div class="status-display">
          <span 
            class="status-dot" 
            :class="{
              'status-draft': row.status === StatusEnum.DRAFT,
              'status-pending': row.status === StatusEnum.PENDING,
              'status-active': row.status === StatusEnum.ACTIVE
            }"
          ></span>
          <span class="status-text">{{ row.statusDesc }}</span>
        </div>
      </template>
			<template #t_action="{ row }">
        <yun-rest limit="3">
          <!-- <el-button
            type="text"
            size="small"
            @click.prevent.stop="handleDetail(row)"
          >
            详情
          </el-button> -->
          <el-button
            type="text"
            size="small"
            @click.prevent.stop="handleEdit(row)"
          >
            修改
          </el-button>
          <el-button
            type="text"
            size="small"
            @click.prevent.stop="handlePublish(row)"
            v-if="[StatusEnum.PENDING].includes(row.status)"
          >
            发布
          </el-button>
          <el-button
            type="text"
            size="small"
            @click.prevent.stop="handleUnpublish(row)"
            v-if="[StatusEnum.ACTIVE].includes(row.status)"
          >
            退回
          </el-button>
          <el-button
            type="text"
            size="small"
            @click.prevent.stop="handleHistory(row)"
            v-if="[StatusEnum.ACTIVE, StatusEnum.PENDING].includes(row.status)"
          >
            历史版本
          </el-button>
          <el-button
            type="text"
            size="small"
            @click.prevent.stop="handleDiagram(row)"
            v-if="row.flowId"
          >
            流程图
          </el-button>
          <el-button
            type="text"
            size="small"
            @click.prevent.stop="handleDelete(row)"
            v-if="[StatusEnum.DRAFT, StatusEnum.PENDING].includes(row.status)"
          >
            删除
          </el-button>
          <el-button
            type="text"
            size="small"
            v-if="[StatusEnum.ACTIVE].includes(row.status)"
            @click.prevent.stop="handleStart(row)"
          >
            启动【测试】
          </el-button>
        </yun-rest>
			</template>
		</yun-pro-table>
		<FormDrawer
			ref="formDrawerRef"
			@refresh="refresh"
		/>
		<HistoryDrawer
			ref="historyDrawerRef"
			@refresh="refresh"
		/>
		<!-- <DiagramDialog ref="diagramDialogRef" /> -->
		<ProcessView ref="processViewRef" />
		<DetailDrawer ref="detailDrawerRef" />

		<StartModal ref="startModalRef" @refresh="refresh" />
	</div>
</template>

<script setup>
import { ref } from 'vue';
import { useProTable } from '@ylz-use/core';
import { Plus } from '@yun-design/icons-vue';
import useColumns from './hooks/useColumns';
import FormDrawer from './components/form/index.vue';
import HistoryDrawer from './components/table/index.vue';
// import DiagramDialog from './components/diagram-dialog/index.vue';
import ProcessView from './components/process-view/index.vue';
import { useConfirm } from '@/views/workflow/hooks/useConfirm';
import { ActionType, StatusEnum } from './const';
import { deleteProcess, publishProcess, unpublishProcess, pageProcess } from '@/api/workflow/process-management';
import DetailDrawer from './components/detail/index.vue';
import StartModal from './components/startModal.vue';
// import { ElMessage } from 'yun-design';

const { tableColumns, searchFields } = useColumns();
const formDrawerRef = ref();
const historyDrawerRef = ref();
const processViewRef = ref();
const detailDrawerRef = ref();
const startModalRef = ref();
// const diagramDialogRef = ref();

const { pagination, remoteMethod, tableProps, proTableRef, filterTableData, reLoad } = useProTable({
	apiFn: pageProcess,
	paramsHandler(params) {
		return {
			current: pagination.value.page,
			size: pagination.value.size || pagination.value.pageSize,
			...params,
		};
	},
	customTotalHandler(result) {
		return result?.data?.total || 0;
	},
	querysHandler(query) {
		return {
			...query,
			current: pagination.value.page,
			size: pagination.value.size || pagination.value.pageSize,
		};
	},
	responseHandler(result) {
		return result?.data?.records || result?.data || [];
	},
	plugins: {
		// 挂载插件
		config: {
			columns: tableColumns.value, // 开启搜索增强需要传入列配置
			searchFields: searchFields.value, // 开启搜索增强需要传入搜索配置
		},
		list: ['SEARCH_PLUS'], // 开启插件列表
	},
});

function handleCreate() {
	formDrawerRef.value.show({
		action: ActionType.ADD,
	});
}

function handleEdit(row) {
	formDrawerRef.value.show({
		action: ActionType.EDIT,
		row,
	});
}

function handleDetail(row) {
	detailDrawerRef.value.show({ row });
}

async function handleStart(row) {
	startModalRef.value.show({ key: row.code });
	// await useConfirm(
	// 	{
	// 		title: '提示',
	// 		content: `是否确认手动启动编号为【${row.code}】的数据项？`,
	// 	},
	// 	() => {
	// 		startProcessInstance({
	// 			key: row.code,
	// 			// variables: {
	// 			// 	applicant: '张三',
	// 			// 	days: 3,
	// 			// 	reason: '身体不适',
	// 			// },
	// 			// businessKey: row.code,
	// 		});
	// 	}
	// );
	// refresh();
}

async function handlePublish(row) {
	await useConfirm(
		{
			title: '提示',
			content: `是否确认发布编号为【${row.code}】的数据项？`,
		},
		() => {
			publishProcess({
				code: row.code,
			});
		}
	);
	refresh();
}

async function handleUnpublish(row) {
	await useConfirm(
		{
			title: '提示',
			content: `是否确认退回编号为【${row.code}】的数据项？`,
		},
		() => {
			unpublishProcess({
				code: row.code,
			});
		}
	);
	refresh();
}

async function handleHistory(row) {
	historyDrawerRef.value.show(row);
}

async function handleDiagram(row) {
	processViewRef.value.show(row);
	// diagramDialogRef.value.show(row);
}

function refresh() {
	setTimeout(() => {
		reLoad();
	}, 200);
}

async function handleDelete(row) {
	await useConfirm(
		{
			title: '提示',
			content: `是否确认删除编号为【${row.code}】的数据项？`,
		},
		() => {
			deleteProcess({
				code: row.code,
			});
		}
	);
	refresh();
}
</script>

<style scoped lang="scss">
.workflow-task-process {
	width: 100%;
	height: calc(100vh - 88px);
	:deep(.el-form .el-form-item:last-of-type) {
		margin-bottom: 12px !important;
	}
}

.status-display {
  display: flex;
  align-items: center;
  gap: 8px;
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  display: inline-block;
  
  &.status-draft {
    background-color: #333333; // 黑色圆点
  }
  
  &.status-pending {
    background-color: #909399; // 灰色圆点
  }
  
  &.status-active {
    background-color: #67c23a; // 绿色圆点
  }
}

.status-text {
  font-size: 14px;
  color: var(--el-text-color-primary);
}
</style>
