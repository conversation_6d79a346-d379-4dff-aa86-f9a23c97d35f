import { ref, computed, onMounted } from 'vue';

export default () => {
	const searchFields = computed(() => [
		{
			label: '分类名称',
			prop: 'name',
			componentAttrs: {
				placeholder: '请输入分类名称',
				labelWidth: 80,
			},
			colAttrs: {
				// lg: 4,
				// md: 4,
				// sm: 8,
				// xl: 4,
				// xs: 8,
				span: 8,
			},
		},
		{
			label: '分类编码',
			prop: 'code',
			componentAttrs: {
				placeholder: '请输入分类编码',
				labelWidth: 80,
			},
			colAttrs: {
				// lg: 4,
				// md: 4,
				// sm: 8,
				// xl: 4,
				// xs: 8,
				span: 8,
			},
		},
	]);

	const tableColumns = ref([
		// {
		// 	type: 'selection',
		// 	width: 52,
		// 	fixed: 'left',
		// },
		{
			label: '分类名称',
			prop: 'name',
			minWidth: 140,
		},
		{
			label: '分类编码',
			prop: 'code',
			minWidth: 140,
		},
		{
			label: '备注信息',
			prop: 'remark',
			minWidth: 160,
		},
		{
			label: '创建时间',
			prop: 'createTime',
			minWidth: 160,
		},
		{
			label: '更新时间',
			prop: 'updateTime',
			minWidth: 160,
		},
		{
			label: '操作',
			prop: 'action',
			width: 180,
			fixed: 'right',
		},
	]);

	onMounted(async () => {});

	return {
		searchFields,
		tableColumns,
	};
};
