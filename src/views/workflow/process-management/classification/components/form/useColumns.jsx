import { computed } from 'vue';
import { ActionType } from '../../const';
export function useColumns({ currentAction } = {}) {

	const columns = computed(() => {
		return [
			{
				prop: 'name',
				label: '分类名称',
				type: 'input',
				attrs: {
					maxlength: 64,
          disabled: false,
					placeholder: '请输入分类名称',
					// disabled: currentAction.value === ActionType.EDIT,
				},
				colProps: { span: 24 },
				rules: [
					{ trigger: 'blur', required: true, message: '请输入分类名称' },
					{ trigger: 'blur', max: 64, message: '长度在不能超过64个字符' },
				],
			},
			{
				prop: 'code',
				label: '分类编码',
				type: 'input',
				attrs: {
          // disabled: false,
					maxlength: 64,
					placeholder: '请输入分类编码',
					disabled: [ActionType.EDIT, ActionType.VIEW].includes(currentAction.value),
				},
				colProps: { span: 24 },
				rules: [
					{ trigger: 'blur', required: true, message: '请输入分类编码' },
					{ trigger: 'blur', max: 64, message: '长度在不能超过64个字符' },
					// { pattern: /^[A-Z0-9]{1,4}$/, trigger: 'blur', message: '请输入1-4位大写字母或数字' },
				],
			},
			{
				prop: 'remark',
				label: '备注信息',
				type: 'input',
				attrs: {
					type: 'textarea',
					rows: 4,
					maxlength: 200,
          disabled: false,
					showWordLimit: true,
					placeholder: '请输入备注信息',
					// disabled: currentAction.value === ActionType.EDIT,
				},
				colProps: { span: 24 },
				rules: [
					{ trigger: 'blur', required: false, message: '请输入备注信息' },
					// { trigger: 'blur', max: 200, message: '长度在不能超过200个字符' },
				],
			},
		];
	});
	return {
		columns,
	};
}
