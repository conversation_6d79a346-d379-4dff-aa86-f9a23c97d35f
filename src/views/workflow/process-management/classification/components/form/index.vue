<template>
	<yun-drawer
		:show-footer="currentAction !== ActionType.VIEW"
		v-model="visible"
		:title="title"
		size="small"
		@close="handleClose"
		@confirm="confirmView"
	>
		<div
			v-loading="loading"
			class="form-content"
		>
			<yun-pro-form
				ref="formRef"
				:form="form"
				:columns="columns"
				:config="config"
				custom-class="common-pro-form"
				:form-props="{ labelPosition: 'top', labelWidth: '136px', disabled: currentAction === ActionType.VIEW }"
			>
			</yun-pro-form>
		</div>
	</yun-drawer>
</template>

<script setup lang="jsx">
import { ref, nextTick, computed } from 'vue';
import { ElMessage } from 'yun-design';
import { useForm } from '@/hooks/useForm';
import { ActionType } from '../../const';
import { useColumns } from './useColumns';
import { createCategory, updateCategory } from '@/api/workflow/process-management';

const currentAction = ref(ActionType.ADD);
const { columns } = useColumns({ currentAction });
const emits = defineEmits(['refresh']);
const { config, resetForm, form, formRef, clearValidate, setForm } = useForm();
const currentRow = ref({});
const visible = ref(false);
const loading = ref(false);
const title = computed(() => {
	if (currentAction.value === ActionType.VIEW) {
		return '查看流程分类';
	}
	if (currentAction.value === ActionType.EDIT) {
		return '编辑流程分类';
	}
	return '新增流程分类';
});

async function confirmView(done, loadingRef) {
	if (loading.value) return;
	await formRef.value?.elForm?.validate();
	loadingRef.value = true;
	loading.value = true;
	try {
		if (currentAction.value === ActionType.ADD) {
			await createCategory(form.value);
		} else if (currentAction.value === ActionType.EDIT) {
			await updateCategory(form.value);
		}
		ElMessage.success('操作成功');
		handleClose();
		done();
		emits('refresh');
	} catch (error) {
		ElMessage.error('操作失败');
	}
	loadingRef.value = false;
	loading.value = false;
}
async function show({row = null, action = ActionType.ADD} = {}) {
	currentRow.value = row;
	currentAction.value = action;
	visible.value = true;
	if ([ActionType.EDIT, ActionType.VIEW].includes(action)) {
		setForm({
			code: row.code,
			name: row.name,
			remark: row.remark,
			id: row.id,
		});
	}
}

function handleClose() {
	visible.value = false;
	resetForm();
	nextTick(() => {
		clearValidate();
	});
}

defineExpose({
	show,
});
</script>

<style scoped lang="scss"></style>
