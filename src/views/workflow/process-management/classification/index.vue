<template>
	<div class="workflow-task-process">
		<yun-pro-table
			ref="proTableRef"
			v-model:pagination="pagination"
			v-model:filter-data="filterTableData"
			:search-fields="searchFields"
			:table-columns="tableColumns"
			:remote-method="remoteMethod"
			:default-fetch="true"
			:layout="'whole'"
			auto-height
			:table-props="{
				...tableProps,
				'show-table-setting': true,
				'show-summary': false,
			}"
		>
			<template #tableHeaderLeft>
				<el-button
					:icon="Plus"
					type="primary"
					@click.prevent.stop="handleCreate"
				>
					新增
				</el-button>
			</template>
			<template #t_action="{ row }">
				<el-button
					type="text"
					size="small"
					@click.prevent.stop="handleEdit(row)"
				>
					修改
				</el-button>
				<el-button
					type="text"
					size="small"
					@click.prevent.stop="handleDelete(row)"
				>
					删除
				</el-button>
				<el-button
					type="text"
					size="small"
					@click.prevent.stop="handleDetail(row)"
				>
					详情
				</el-button>
			</template>
		</yun-pro-table>
		<FormDrawer
			ref="formDrawerRef"
			@refresh="reLoad"
		/>
	</div>
</template>

<script setup>
import { ref } from 'vue';
import { useProTable } from '@ylz-use/core';
import { Plus } from '@yun-design/icons-vue';
import useColumns from './hooks/useColumns';
import FormDrawer from './components/form/index.vue';
import { useConfirm } from '@/views/workflow/hooks/useConfirm';

import { ActionType } from './const';
import { deleteCategory, pageCategory } from '@/api/workflow/process-management';
// import { ElMessage } from 'yun-design';

const { tableColumns, searchFields } = useColumns();
const formDrawerRef = ref();
const { pagination, remoteMethod, tableProps, proTableRef, filterTableData, reLoad } = useProTable({
	apiFn: pageCategory,
	paramsHandler(params) {
		return {
			current: pagination.value.page,
			size: pagination.value.size || pagination.value.pageSize,
			...params,
		};
	},
	customTotalHandler(result) {
		return result?.data?.total || 0;
	},
	querysHandler(query) {
		return {
			...query,
			current: pagination.value.page,
			size: pagination.value.size || pagination.value.pageSize,
		};
	},
	responseHandler(result) {
		return result?.data?.records || result?.data || [];
	},
	plugins: {
		// 挂载插件
		config: {
			columns: tableColumns.value, // 开启搜索增强需要传入列配置
			searchFields: searchFields.value, // 开启搜索增强需要传入搜索配置
		},
		list: ['SEARCH_PLUS'], // 开启插件列表
	},
});

function handleCreate() {
	formDrawerRef.value.show({
		action: ActionType.ADD,
	});
}

function handleDetail(row) {
	formDrawerRef.value.show({
		action: ActionType.VIEW,
		row,
	});
}

function handleEdit(row) {
	formDrawerRef.value.show({
		action: ActionType.EDIT,
		row,
	});
}

async function handleDelete(row) {
	await useConfirm(
		{
			title: '提示',
			content: `是否确认删除编号为【${row.code}】的数据项？`,
		},
		() => {
			deleteCategory({
				code: row.code,
			});
		}
	);
	// proTableRef.value?.getData();
	setTimeout(() => {
		reLoad();
	}, 500);
}
</script>

<style scoped lang="scss">
.workflow-task-process {
	width: 100%;
	height: calc(100vh - 88px);
}
</style>
