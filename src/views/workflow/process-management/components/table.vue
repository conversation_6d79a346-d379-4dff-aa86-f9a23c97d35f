<template>
	<el-table
		:data="tableData"
		border
		size="small"
		max-height="300"
		style="width: 100%"
		:row-style="rowStyle"
	>
		<el-table-column
			v-for="column in columns"
			:key="column.prop"
			stripe
			show-overflow-tooltip
			v-bind="column"
			:prop="column.prop"
			:label="column.label"
		>
			<template #default="scope">
				<!-- <template v-if="$slots[column.prop]">
					<slot
						:name="column.prop"
						:row="scope.row"
					/>
				</template>
				<template v-else> -->
				<component
					v-if="typeof column?.render === 'function'"
					:is="column.render(scope.row)"
					:row="scope.row"
				/>
				<div v-else>{{ rendrValue(scope.row, column) }}</div>
				<!-- </template> -->
			</template>
		</el-table-column>
	</el-table>
</template>
<script setup lang="jsx">
// formatter
defineProps({
	columns: {
		type: Array,
		default() {
			return [];
		},
	},
	tableData: {
		type: Array,
		default() {
			return [];
		},
	},
});

const rendrValue = (row, column) => {
	if (typeof column?.formatter === 'function') {
		return column.formatter(row, column);
	}
	return row[column.prop];
};

const rowStyle = ({row}) => {
	if (row.hasError && row.errorReason) {
		return {
			color: 'rgba(245, 108, 108, 1)',
		};
	}
	return {};
};
</script>
<style lang="scss" scoped></style>
