<template>
  <div class="layout-padding no-padding">
    <div class="layout-padding-auto layout-padding-view">
      <el-tabs v-model="activeName" class="px-6">
        <el-tab-pane v-for="item in TabsOptions" :key="item.value" :label="item.label" :name="item.value">
        </el-tab-pane>
      </el-tabs>

      <yun-pro-table ref="proTableRef" v-model:pagination="pagination" :search-fields="searchFields" :table-columns="tableColumns" :remote-method="remoteMethod" :default-fetch="true" :layout="'whole'" auto-height :table-props="{
          'row-style': rowStyle,
          'row-class-name': rowClassName,
          ...tableProps,
          'show-table-setting': true,
          'show-summary': false,
        }">
        <template #t_processInstanceId="{ row }">
          <div class="process-instance-id-container">
            <el-icon class="copy-icon" @click.prevent.stop="handleCopyProcessId(row.processInstanceId)">
              <DocumentCopy />
            </el-icon>
            <span class="process-instance-text" @click.prevent.stop="handleDetail(row)">
              {{ row.processInstanceId }}
            </span>
          </div>
        </template>
        <template #t_processDefinitionVersion="{ row }">
          <el-tag type="primary">v{{row.processDefinitionVersion}}</el-tag>
        </template>

        <template #t_instanceStatus="{ row }">
          <div class="status-display">
            <span 
              class="status-dot" 
              :class="{
                'status-active': row.instanceStatus === InstanceStatusEnum.ACTIVE,
                'status-suspended': row.instanceStatus === InstanceStatusEnum.SUSPENDED,
                'status-completed': row.instanceStatus === InstanceStatusEnum.COMPLETED,
                'status-cancelled': row.instanceStatus === InstanceStatusEnum.CANCELLED
              }"
            ></span>
            <span class="status-text">{{ getInstanceStatusText(row.instanceStatus) }}</span>
          </div>
        </template>

        <template #t_action="{ row }">
          <yun-rest :limit="3">
            <el-button type="text" size="small" @click.prevent.stop="handleDetail(row)">
              详情
            </el-button>
            <el-button type="text" size="small" @click.prevent.stop="handleDiagram(row)">
              查看流程图
            </el-button>

            <!-- 进行中独有的操作按钮 -->
            <template v-if="activeName === TabsEnum.RUNNING">
              <el-button type="text" size="small" @click.prevent.stop="handleSuspend(row)"
                v-if="[InstanceStatusEnum.ACTIVE].includes(row.instanceStatus)">
                挂起
              </el-button>
              <el-button type="text" size="small" @click.prevent.stop="handleActivate(row)"
                v-if="[InstanceStatusEnum.SUSPENDED].includes(row.instanceStatus)">
                激活
              </el-button>
              <el-button type="text" size="small" @click.prevent.stop="handleTerminate(row)">
                终止
              </el-button>
            </template>
          </yun-rest>
          
        </template>
      </yun-pro-table>

      <ProcessView ref="processViewRef" :type="ExportPreviewType.PROCESS_DEFINITION" />

      <DetailDrawer ref="detailDrawerRef" />

      <!-- 进行中独有的终止弹窗 -->
      <TerminateModal v-if="activeName === TabsEnum.RUNNING" ref="terminateModalRef" @refresh="refresh" />

    </div>
  </div>
</template>

<script setup lang="tsx">
import { activateProcessInstance, activeInstance, historyInstance, suspendProcessInstance } from '@/api/workflow/process-management';
import { useConfirm } from '@/views/workflow/hooks/useConfirm';
import useHandler from '@/views/workflow/hooks/useHandler';
import ProcessView from '@/views/workflow/process-management/definition/components/process-view/index.vue';
import { ExportPreviewType } from '@/views/workflow/process-management/definition/const';
import { DocumentCopy } from '@element-plus/icons-vue';
import { useProTable } from '@ylz-use/core';
import { ElMessage } from 'yun-design';
import { computed, h, onMounted, ref, watch } from 'vue';
import DetailDrawer from './components/detail/index.vue';
import TerminateModal from './components/terminateModal.vue';
import { InstanceStatusEnum, InstanceStatusTextEnum, TabsEnum, TabsOptions } from './const';


const activeName = ref(TabsEnum.RUNNING);

// 根据类型确定默认状态和API
const getDefaultStatus = () => {
  switch (activeName.value) {
    case TabsEnum.RUNNING:
      return null; // 进行中页面没有默认状态
    case TabsEnum.HISTORY:
      return InstanceStatusEnum.COMPLETED;
    case TabsEnum.CANCELLED:
      return InstanceStatusEnum.CANCELLED;
    default:
      return null;
  }
};

// 根据类型确定API函数
const getApiFunction = (params) => {
  switch (activeName.value) {
    case TabsEnum.RUNNING:
      return activeInstance(params);
    case TabsEnum.HISTORY:
    case TabsEnum.CANCELLED:
      return historyInstance(params);
    default:
      return activeInstance(params);
  }
};

const terminateModalRef = ref();
const detailDrawerRef = ref();
const processViewRef = ref();
const { allCategoryList, getAllCategoryList } = useHandler();

// 搜索字段配置
const searchFields = computed(() => [
  {
    label: '实例ID',
    prop: 'processInstanceId',
    componentAttrs: {
      placeholder: '请输入实例ID',
      labelWidth: 80,
    },
    colAttrs: {
      span: 8,
    },
  },
  {
    label: '业务编码',
    prop: 'businessKey',
    componentAttrs: {
      placeholder: '请输入业务编码',
      labelWidth: 80,
    },
    colAttrs: {
      // lg: 4,
      // md: 4,
      // sm: 8,
      // xl: 4,
      // xs: 8,
      span: 8,
    },
  },
  {
    label: '流程编码',
    prop: 'processCode',
    componentAttrs: {
      placeholder: '请输入流程编码',
      labelWidth: 80,
    },
    colAttrs: {
      // lg: 4,
      // md: 4,
      // sm: 8,
      // xl: 4,
      // xs: 8,
      span: 8,
    },
  },
  {
    label: '流程名称',
    prop: 'processName',
    componentAttrs: {
      placeholder: '请输入流程名称',
      labelWidth: 80,
    },
    colAttrs: {
      span: 8,
    },
  },
  {
    label: '分类名称',
    prop: 'categoryCode',
    component: 'el-select',
    componentAttrs: {
      placeholder: '请选择分类',
      labelWidth: 80,
      filterable: true,
    },
    subComponent: 'el-option',
    options: allCategoryList.value,
    colAttrs: {
      span: 8,
    },
  },
]);

// 表格列配置
const tableColumns = computed(() => {
  const baseColumns = [
    {
      label: '实例ID',
      prop: 'processInstanceId',
      minWidth: 160,
    },
    {
      label: '流程编码',
      prop: 'processDefinitionKey',
      minWidth: 160,
    },
    {
      label: '流程名称',
      prop: 'processDefinitionName',
      minWidth: 140,
    },
    {
      label: '业务编码',
      prop: 'businessKey',
      minWidth: 160,
    },
    {
      label: '版本',
      prop: 'processDefinitionVersion',
      minWidth: 120,
      render: ({ row }) => {
        return h('el-tag', { type: 'primary' }, `v${row.processDefinitionVersion}`);
      },
    },
    {
      label: '分类名称',
      prop: 'categoryName',
      minWidth: 120,
    },
    {
      label: '开始时间',
      prop: 'startTime',
      minWidth: 160,
    }
  ];

  // 根据类型添加不同的列
  if (activeName.value === TabsEnum.RUNNING) {
    // 进行中页面的列配置
    baseColumns.push(
      // {
      //   label: '当前节点',
      //   prop: 'currentActivityName',
      //   minWidth: 140,
      // },
              {
          label: '状态',
          prop: 'instanceStatus',
          minWidth: 120,
        },
      {
        label: '更新时间',
        prop: 'updateTime',
        minWidth: 120,
        render: ({ row }) => {
          if (!row.updateTime) return '-';
          const date = new Date(row.updateTime);
          return date.toLocaleDateString('zh-CN', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit'
          });
        },
      }
    );
  } else {
    // 已结束/已取消页面的列配置
    baseColumns.push(
      {
        label: '结束时间',
        prop: 'endTime',
        minWidth: 180,
      },
      {
        label: '持续时间(毫秒)',
        prop: 'durationInMillis',
        minWidth: 160,
      },
              {
          label: '状态',
          prop: 'instanceStatus',
          minWidth: 160,
        },
      {
        label: '删除原因',
        prop: 'deleteReason',
        minWidth: 160,
      },
    );
  }

  // 添加操作列
  baseColumns.push({
    label: '操作',
    prop: 'action',
    width: 200,
    fixed: 'right',
  });

  return baseColumns;
});

const { pagination, remoteMethod, tableProps, proTableRef, reLoad } = useProTable({
  apiFn: getApiFunction,
  paramsHandler(params) {
    return {
      current: pagination.value.page,
      size: pagination.value.size || pagination.value.pageSize,
      ...params,
      ...(getDefaultStatus() && { instanceStatus: getDefaultStatus() }),
    };
  },
  customTotalHandler(result) {
    return result?.data?.total || 0;
  },
  querysHandler(query) {
    return {
      ...query,
      current: pagination.value.page,
      size: pagination.value.size || pagination.value.pageSize,
    };
  },
  responseHandler(result) {
    return result?.data?.records || result?.data || [];
  },
});

function refresh() {
  setTimeout(() => {
    reLoad();
  }, 200);
}

// 获取实例状态文本
function getInstanceStatusText(status) {
  return InstanceStatusTextEnum[status] || status;
}

// 样式相关
const rowClassName = ({ row }) => {
  if (row.hasError) {
    return 'process-instance-error-row';
  }
  return '';
};

const rowStyle = () => {
  return {};
};

// 事件处理
function handleDiagram(row) {
  processViewRef.value.show(row);
}

function handleDetail(row) {
  detailDrawerRef.value.show({ row });
}

async function handleCopyProcessId(processInstanceId) {
  try {
    await navigator.clipboard.writeText(processInstanceId);
    ElMessage.success('复制成功');
  } catch (error) {
    ElMessage.error('复制失败');
  }
}

// 进行中页面独有的操作
async function handleSuspend(row) {
  if (activeName.value !== TabsEnum.RUNNING) return;

  await useConfirm(
    {
      title: '提示',
      content: `是否确认要执行该操作？`,
    },
    () => {
      suspendProcessInstance({
        processInstanceId: row.processInstanceId,
      });
    }
  );
  refresh();
}

async function handleActivate(row) {
  if (activeName.value !== TabsEnum.RUNNING) return;

  await useConfirm(
    {
      title: '提示',
      content: `是否确认要执行该操作？`,
    },
    () => {
      activateProcessInstance({
        processInstanceId: row.processInstanceId,
      });
    }
  );
  refresh();
}

async function handleTerminate(row) {
  if (activeName.value !== TabsEnum.RUNNING) return;
  terminateModalRef.value.show(row);
}

// 监听tab切换，重新请求数据
watch(activeName, () => {
  refresh();
});

onMounted(async () => {
  await getAllCategoryList();
});
</script>

<style scoped lang="scss">
.no-padding {
	padding: 0 !important;
  .layout-padding-auto {
    padding: 0 !important;
  }
  :deep(.dm-filter-root) {
    margin-top: 0 !important;
  }
}
.status-display {
  display: flex;
  align-items: center;
  gap: 8px;
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  display: inline-block;
  
  &.status-active {
    background-color: #67c23a; // 绿色圆点 - 运行中
  }
  
  &.status-suspended {
    background-color: #e6a23c; // 橙色圆点 - 已挂起
  }
  
  &.status-completed {
    background-color: #909399; // 灰色圆点 - 已结束
  }
  
  &.status-cancelled {
    background-color: #f56c6c; // 红色圆点 - 已取消
  }
}

.status-text {
  font-size: 14px;
  color: var(--el-text-color-primary);
}

.process-instance-id-container {
	display: flex;
	align-items: center;
	gap: 8px;
	max-width: 100%;
	
	.copy-icon {
		opacity: 0;
		transition: opacity 0.2s ease-in-out;
		cursor: pointer;
		color: var(--el-color-primary);
		flex-shrink: 0;
		
		&:hover {
			color: var(--el-color-primary-light-3);
		}
	}
	
	.process-instance-text {
		overflow: hidden;
		white-space: nowrap;
		text-overflow: ellipsis;
		flex: 1;
		min-width: 0;
		cursor: pointer;
	}
	
	&:hover .copy-icon {
		opacity: 1;
	}
}
</style>
