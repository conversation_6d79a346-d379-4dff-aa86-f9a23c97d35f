
export const StatusEnum = {
	DRAFT: 'DRAFT',
	PENDING: 'PENDING',
	ACTIVE: 'ACTIVE',
};

export const StatusOptions = [
	{
		label: '草稿',
		value: 'DRAFT',
	},
	{
		label: '待发布',
		value: 'PENDING',
	},
	{
		label: '已发布',
		value: 'ACTIVE',
	},
];

// 操作类型
export const ActionType = {
	ADD: 'ADD',
	ADD_CHILD: 'ADD_CHILD',
	EDIT: 'EDIT',
	VIEW: 'VIEW',
	DELETE: 'DELETE',
};

export const StepEnum = {
	BASIC_INFO: 0,
	PROCESS_DESIGN: 1,
};

// 进行中  历史
export const TabsEnum = {
	RUNNING: 'RUNNING',
	HISTORY: 'HISTORY',
	CANCELLED: 'CANCELLED',
};

export const TabsOptions = [
	{
		label: '进行中',
		value: TabsEnum.RUNNING,
	},
	{
		label: '已结束',
		value: TabsEnum.HISTORY,
	},
	{
		label: '已取消',
		value: TabsEnum.CANCELLED,
	},
];

export const InstanceStatusEnum = {
	ACTIVE: 'ACTIVE',
	SUSPENDED: 'SUSPENDED',
	COMPLETED: 'COMPLETED',
	CANCELLED: 'CANCELLED',
};

export const InstanceStatusTextEnum = {
	ACTIVE: '运行中',
	SUSPENDED: '已挂起',
	COMPLETED: '已结束',
	CANCELLED: '已取消',
};

// 实例状态
export const InstanceStatusOptions = [
	{
		label: '运行中',
		value: 'ACTIVE',
	},
	{
		label: '已挂起',
		value: 'SUSPENDED',
	},
];

// 实例状态
export const InstanceStatusFinishedOptions = [
	// {
	// 	label: '运行中',
	// 	value: 'ACTIVE',
	// },
	// {
	// 	label: '已挂起',
	// 	value: 'SUSPENDED',
	// },
	{
		label: '已结束',
		value: 'COMPLETED',
	},
	{
		label: '已取消',
		value: 'CANCELLED',
	},
];
