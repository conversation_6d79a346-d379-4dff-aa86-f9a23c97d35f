import { computed, onMounted, ref } from 'vue';
import { getNodeTypeText } from '@/views/workflow/process-management/definition/const';
import { InstanceStatusFinishedOptions, InstanceStatusEnum, InstanceStatusOptions, InstanceStatusTextEnum } from '../../const';
import RenderTable from '@/views/workflow/process-management/components/table.vue';
import JsonEditor from '@axolo/json-editor-vue';

export function useColumns() {
	const activeCollection = ref({});
	const setActiveCollection = (key, value) => {
		activeCollection.value[key] = value;
	};
	const getActiveCollection = (key) => {
		return activeCollection.value[key];
	};

	onMounted(async () => {});
	const config = computed(() => {
		return {
			// title: '流程实例详情',
			labelWidth: 100,
			theme: 'simpleGreen',
			descriptions: {
				column: 2,
			},
		};
	});

	// 优化的 popover 组件
	const LazyPopover = ({ uniqueKey, data, renderContent, buttonText = '查看' }) => {
		return data && (data?.length > 0 || (typeof data === 'object' && Object.keys(data).length > 0)) ? (
			<el-popover
				placement="top-start"
				width="max-content"
				trigger="click"
				popper-class="json-popover"
				v-if={getActiveCollection(uniqueKey)}
				onHide={() => {
					setActiveCollection(uniqueKey, false);
				}}
				v-slots={{
					default: () => renderContent(data),
					reference: () => (
						<el-button
							onClick={async () => setActiveCollection(uniqueKey, true)}
							type="text"
						>
							{buttonText}
						</el-button>
					),
				}}
			/>
		) : (
			<span>--</span>
		);
	};

	const columns = computed(() => {
		return [
			{
				label: '实例ID',
				prop: 'processInstanceId',
				group: '基本信息',
				// tips: '客户的外部订单号',
			},
			{
				label: '实例名称',
				prop: 'processInstanceName',
				group: '基本信息',
			},
			{
				label: '业务Key',
				prop: 'businessKey',
				group: '基本信息',
			},
			{
				label: '流程ID',
				prop: 'processDefinitionId',
				group: '基本信息',
			},
			{
				label: '流程编码',
				prop: 'processDefinitionKey',
				group: '基本信息',
			},
			{
				label: '流程名称',
				prop: 'processDefinitionName',
				group: '基本信息',
			},
			{
				label: '版本',
				prop: 'processDefinitionVersion',
				group: '基本信息',
				render: (row) => {
					return <el-tag type="primary">v{row.processDefinitionVersion}</el-tag>;
				},
			},
			{
				label: '分类名称',
				prop: 'categoryName',
				group: '基本信息',
			},
			{
				label: '开始时间',
				prop: 'startTime',
				group: '基本信息',
			},
			{
				label: '结束时间',
				prop: 'endTime',
				group: '基本信息',
				show(row) {
					return [InstanceStatusEnum.CANCELLED, InstanceStatusEnum.COMPLETED].includes(row.instanceStatus);
				},
			},
			{
				label: '持续时间（毫秒）',
				prop: 'durationInMillis',
				group: '基本信息',
				show(row) {
					return [InstanceStatusEnum.CANCELLED, InstanceStatusEnum.COMPLETED].includes(row.instanceStatus);
				},
			},
			{
				label: '状态',
				prop: 'instanceStatus',
				group: '基本信息',
				type: 'enums',
				attrs: {
					options: [...InstanceStatusFinishedOptions, ...InstanceStatusOptions],
				},
			},
			{
				label: '是否已结束',
				prop: 'ended',
				group: '基本信息',
				render: (row) => {
					return <el-tag type="primary">{row.ended ? '是' : '否'}</el-tag>;
				},
				show(row) {
					return [InstanceStatusEnum.CANCELLED, InstanceStatusEnum.COMPLETED].includes(row.instanceStatus);
				},
			},
			{
				label: '是否挂起',
				prop: 'suspended',
				group: '基本信息',
				render: (row) => {
					return <el-tag type="primary">{row.suspended ? '是' : '否'}</el-tag>;
				},
				show(row) {
					return [InstanceStatusEnum.CANCELLED, InstanceStatusEnum.COMPLETED].includes(row.instanceStatus);
				},
			},
			{
				label: '删除原因',
				prop: 'deleteReason',
				group: '基本信息',
				show(row) {
					return [InstanceStatusEnum.CANCELLED, InstanceStatusEnum.COMPLETED].includes(row.instanceStatus);
				},
			},
			{
				label: '创建时间',
				prop: 'createTime',
				group: '基本信息',
			},
			{
				label: '更新时间',
				prop: 'updateTime',
				group: '基本信息',
			},
			{
				// label: '任务',
				prop: 'activities',
				group: '节点',
				span: 2,
				render: (row) => {
					return (
						<RenderTable
							columns={[
								{ label: '节点ID', prop: 'activityId', minWidth: 100 },
								{ label: '节点名称', prop: 'activityName', minWidth: 80 },
								{ label: '节点类型', prop: 'activityType', minWidth: 80, formatter: ({ activityType }) => getNodeTypeText(activityType) },
								{
									label: '状态',
									prop: 'completed',
									type: 'enums',
									formatter: ({ completed }) => (completed ? '已完成' : '进行中'),
									// attrs: {
									// 	options: [
									// 		{ label: '进行中', value: false },
									// 		{ label: '已完成', value: true },
									// 	],
									// },
									minWidth: 80,
								},
								{ label: '执行ID', prop: 'executionId', minWidth: 100 },
								{ label: '任务ID', prop: 'taskId', minWidth: 100 },
								{
									label: '变量',
									prop: 'variables',
									minWidth: 60,
									render: (row) => {
										return <LazyPopover
											uniqueKey={`variables_${row.jobId}`}
											data={row.variables}
											renderContent={(data) => (
												<JsonEditor
													readonly={true}
													modelValue={data}
													codec
												/>
											)}
										/>;
									},
								},
								{ label: '持续时间(毫秒)', prop: 'durationInMillis', minWidth: 100 },
								{ label: '开始时间', prop: 'startTime', minWidth: 160 },
								{ label: '结束时间', prop: 'endTime', minWidth: 160 },
								{
									label: '错误原因',
									prop: 'errorReason',
									minWidth: 160,
									formatter: ({ hasError, errorReason }) => {
										return hasError && errorReason ? errorReason : '--';
									},
								},
							]}
							tableData={row?.activities || []}
						/>
					);
				},
			},
			{
				// label: '任务',
				prop: 'tasks',
				group: '任务',
				render: (row) => {
					return (
						<RenderTable
							columns={[
								{ label: '任务ID', prop: 'taskId', minWidth: 100 },
								{ label: '任务名称', prop: 'taskName', minWidth: 80 },
								{
									label: '变量',
									prop: 'variables',
									minWidth: 60,
									render: (row) => {
										return <LazyPopover
											uniqueKey={`variables_${row.taskId}`}
											data={row.variables}
											renderContent={(data) => (
												<JsonEditor
													readonly={true}
													modelValue={data}
													codec
												/>
											)}
										/>;
									},
								},
								{
									label: '状态',
									prop: 'taskStatus',
									formatter: ({ taskStatus }) => InstanceStatusTextEnum[taskStatus],
									minWidth: 80,
								},
								{ label: '持续时间(毫秒)', prop: 'durationInMillis', minWidth: 100 },
								{ label: '开始时间', prop: 'startTime', minWidth: 160 },
								{ label: '结束时间', prop: 'endTime', minWidth: 160 },
							]}
							tableData={row?.tasks || []}
						/>
					);
				},
				span: 2,
			},
			{
				prop: 'variables',
				group: '变量',
				span: 2,
				items() {
					return [
						// { label: 'ID', prop: 'activityId', minWidth: 100 },
						{ label: '变量名称', prop: 'variableName', minWidth: 100 },
						{ label: '变量类型', prop: 'variableType', minWidth: 100 },
						// { label: '执行ID', prop: 'executionId', minWidth: 100 },
						// { label: '任务ID', prop: 'taskId', minWidth: 100 },
						{ label: '初始值', prop: 'initialValue', minWidth: 140 },
						{ label: '最终值', prop: 'finalValue', minWidth: 140 },
						{ label: '节点ID', prop: 'activityId', minWidth: 100 },
						{ label: '执行ID', prop: 'executionId', minWidth: 100 },
						{ label: '任务ID', prop: 'taskId', minWidth: 100 },
						{ label: '创建时间', prop: 'createTime', minWidth: 160 },
						{ label: '更新时间', prop: 'updateTime', minWidth: 160 },
						// { label: '作用域', prop: 'scopeType', minWidth: 100 },
					];
				},
			},
		];
	});
	return {
		columns,
		config,
	};
}
