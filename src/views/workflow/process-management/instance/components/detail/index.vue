<template>
	<yun-drawer
		v-model="visible"
		title="流程实例详情"
		size="large"
		@close="handleClose"
		:with-header="false"
		:show-confirm-button="false"
		:cancel-button-text="'关闭'"
	>
		<yun-pro-detail
			:detail="info"
			:config="config"
			:columns="columns"
			v-loading="loading"
			custom-class="instance-detail-drawer"
		>
			<!-- <template #processDefinitionVersion="{ row }">
				<el-tag type="primary">v{{row.processDefinitionVersion}}</el-tag>
			</template> -->
		</yun-pro-detail>
	</yun-drawer>
</template>

<script setup lang="jsx">
import { ref } from 'vue';
import { useColumns } from './useColumns';
import { detailProcessInstance } from '@/api/workflow/process-management';

const info = ref({});
const currentRow = ref({});
const visible = ref(false);
const loading = ref(false);
const { columns, config } = useColumns();

async function loadDetail() {
	try {
		const res = await detailProcessInstance({ processInstanceId: currentRow.value.processInstanceId });
		if (res?.data) {
			info.value = res?.data || {};
		}
		// eslint-disable-next-line no-empty
	} catch (error) {}
}

async function show({ row = null } = {}) {
	currentRow.value = row;
	visible.value = true;
	loading.value = true;
	await loadDetail();
	loading.value = false;
}

function handleClose() {
	visible.value = false;
}

defineExpose({
	show,
});
</script>

<style scoped lang="scss">
.instance-detail-drawer {
	:deep(.yun-descriptions) {
		padding: 0;
	}
}
</style>
