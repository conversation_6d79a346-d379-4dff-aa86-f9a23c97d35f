<template>
	<yun-dialog
		v-model="visible"
		title="终止流程实例"
		:size="'small'"
		:confirm-button-handler="confirmHandler"
		:confirm-button-text="'确定'"
		:cancel-button-text="'取消'"
		:before-close="handleClose"
	>
		<yun-pro-form
			ref="formRef"
			custom-class="batch-form"
			:form="form"
			:columns="schema"
			:config="config"
		/>
	</yun-dialog>
</template>
<script setup>
import { ref, computed } from 'vue';
import { ElMessage } from 'yun-design';
import { useForm } from '@/hooks/useForm';
import { terminateProcessInstance } from '@/api/workflow/process-management';

const emit = defineEmits(['refresh']);
const visible = ref(false);
const { formRef, form, config, validateBasicForm, resetForm, setForm } = useForm();
const schema = computed(() => [
	{
		prop: 'deleteReason',
		// label: '终止原因',
		type: 'input',
		labelWidth: '0',
		attrs: {
			type: 'textarea',
			placeholder: '请输入终止原因',
			maxlength: 200,
			rows: 4,
			showWordLimit: true,
		},
	},
]);

const show = (row) => {
	setForm({ processInstanceId: row.processInstanceId, deleteReason: null });
	visible.value = true;
};
function handleClose() {
	resetForm();
	visible.value = false;
	emit('onClose');
}

const confirmHandler = async () => {
	await validateBasicForm();
	try {
		await terminateProcessInstance({ ...form.value });
		ElMessage.success('操作成功');
		handleClose();
		emit('refresh');
	} catch (e) {
		// eslint-disable-next-line no-console
		console.log(e);
	}
};

defineExpose({
	show,
});
</script>
<style lang="scss" scoped>
.close-title {
	margin: 0 0 8px 0;
}
</style>
