import { ref } from 'vue';
import { listAllCategory } from '@/api/workflow/process-management';

export default () => {
	const allCategoryList = ref([]);
	const getAllCategoryList = async () => {
		const res = await listAllCategory({});
		allCategoryList.value = (res?.data || [])
			?.map((item) => ({
				label: item?.name,
				value: item?.code,
			}))
			?.filter((item) => item?.value);
	};

	const init = async () => {
		await getAllCategoryList();
	};

	return {
		allCategoryList,
		getAllCategoryList,
		init,
	};
};
