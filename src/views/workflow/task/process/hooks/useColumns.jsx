import { ref, computed, onMounted } from 'vue';
// import useHandler from '../../../hooks/useHandler';
// import { formatAmount } from '@/utils/util';

export default () => {
	// const { warehoseList, getWarehoseList } = useHandler();

	const searchFields = computed(() => [
		{
			label: '名称',
			prop: 'name',
			componentAttrs: {
				placeholder: '请输入名称',
				labelWidth: 80,
			},
			colAttrs: {
				// lg: 4,
				// md: 4,
				// sm: 8,
				// xl: 4,
				// xs: 8,
				span: 8,
			},
		},
		{
			prop: 'deployTime',
			label: '开始时间',
			component: 'el-date-picker',
			componentAttrs: {
				placeholder: '请选择开始时间',
				type: 'date',
				'value-format': 'YYYY-MM-DD',
				labelWidth: 80,
			},
			colAttrs: {
				// lg: 4,
				// md: 6,
				// sm: 8,
				// xl: 4,
				// xs: 8,
				span: 8,
			},
		},
		// {
		// 	prop: 'warehouseId',
		// 	label: '仓库',
		// 	component: 'el-select',
		// 	componentAttrs: {
		// 		placeholder: '请选择仓库',
		// 		labelWidth: 80,
		// 	},
		// 	subComponent: 'el-option',
		// 	options: warehoseList.value,
		// 	colAttrs: {
		// 		// lg: 4,
		// 		// md: 4,
		// 		// sm: 8,
		// 		// xl: 4,
		// 		// xs: 8,
		// 		span: 8,
		// 	},
		// },
	]);

	const tableColumns = ref([
		{
			type: 'selection',
			width: 52,
			fixed: 'left',
		},
		{
			label: '流程编号',
			prop: 'procInsId',
			minWidth: 120,
		},
		{
			label: '流程名称',
			prop: 'procDefName',
			minWidth: 140,
		},
		{
			label: '流程类别',
			prop: 'category',
			minWidth: 120,
		},
		{
			label: '流程版本',
			prop: 'procDefVersion',
			minWidth: 120,
		},
		{
			label: '提交时间',
			prop: 'createTime',
			minWidth: 160,
			// formatter: (row) => formatAmount(row.orderAmount),
		},
		{
			label: '流程状态',
			prop: 'assigneeId',
			minWidth: 120,
			render: (row) => {
				return row?.assigneeId ? <el-tag type="success">已完成</el-tag> : <el-tag>进行中</el-tag>;
			},
		},
		{
			label: '耗时',
			prop: 'duration',
			minWidth: 120,
		},
		{
			label: '当前节点',
			prop: 'taskName',
			minWidth: 120,
		},
		{
			label: '办理人',
			prop: 'assigneeName',
			minWidth: 140,
			render: ({row}) => {
				const { assigneeName, assigneeDeptName } = row;
				return !assigneeName && !assigneeDeptName ? <span>--</span> : <div>
						{assigneeName} <el-tag type="info">{assigneeDeptName}</el-tag>
					</div>
			},
		},
		{
			label: '操作',
			prop: 'action',
			width: 200,
			fixed: 'right',
		},
	]);

	onMounted(async () => {
		// await getWarehoseList();
	});

	return {
		searchFields,
		tableColumns,
	};
};
