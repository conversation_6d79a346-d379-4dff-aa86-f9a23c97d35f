import { ref, computed, onMounted } from 'vue';

export default () =>  {

	const searchFields = computed(() => [
		{
			label: '名称',
			prop: 'name',
			componentAttrs: {
				placeholder: '请输入名称',
				labelWidth: 80,
			},
			colAttrs: {
				span: 12,
			},
		},
	]);

	const tableColumns = ref([
		{
			label: '流程名称',
			prop: 'procDefName',
			minWidth: 140,
		},
		{
			label: '流程版本',
			prop: 'procDefVersion',
			minWidth: 120,
		},
		{
			label: '流程分类',
			prop: 'category',
			minWidth: 120,
		},
		{
			label: '操作',
			prop: 'action',
			width: 200,
			fixed: 'right',
		},
	]);

	onMounted(async () => {
	});

	return {
		searchFields,
		tableColumns,
	};
};
