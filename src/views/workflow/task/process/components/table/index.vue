<template>
	<yun-drawer
		:show-footer="false"
		v-model="visible"
		title="发起流程"
		size="large"
		@close="handleClose"
		custom-class="workflow-task-process-table-drawer"
	>
		<div class="workflow-task-process-table">
			<yun-pro-table
				ref="proTableRef"
				v-model:pagination="pagination"
				v-model:filter-data="filterTableData"
				:search-fields="searchFields"
				:table-columns="tableColumns"
				:remote-method="remoteMethod"
				:default-fetch="true"
				:layout="'whole'"
				auto-height
				:table-props="{
					...tableProps,
					'show-table-setting': true,
					'show-summary': false,
				}"
			>
				<template #t_action="{ row }">
					<el-button
						type="text"
						size="small"
						@click.prevent.stop="handleStart(row)"
					>
						发起流程
					</el-button>
				</template>
			</yun-pro-table>
		</div>
	</yun-drawer>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { useProTable } from '@ylz-use/core';
import useColumns from './useColumns';

const visible = ref(false);
const { tableColumns, searchFields } = useColumns();
const emits = defineEmits(['start']);
const {
	pagination,
	remoteMethod,
	tableProps,
	proTableRef,
	filterTableData,
	// reLoad,
} = useProTable({
	apiFn: () => {
		return Promise.resolve({
			data: [
				{
					taskId: '25688',
					executionId: null,
					taskName: '审批',
					taskDefKey: null,
					assigneeId: null,
					deptName: null,
					startDeptName: null,
					assigneeName: null,
					assigneeDeptName: null,
					startUserId: null,
					startUserName: null,
					category: '01001',
					variables: null,
					taskLocalVars: null,
					deployId: '25644',
					procDefId: null,
					procDefKey: null,
					procDefName: '需求审批',
					procDefVersion: 2,
					procInsId: '25671',
					hisProcInsId: null,
					duration: '28分钟',
					comment: null,
					candidate: null,
					createTime: '2025-05-27 17:03:48',
					finishTime: null,
				},
			],
		});
	},
	paramsHandler(params: Record<string, any>) {
		return {
			...params,
		};
	},
	querysHandler(query: Record<string, any>) {
		return {
			...query,
			current: pagination.value.page,
			size: pagination.value.size,
		};
	},
	responseHandler(result: Record<string, any>) {
		return result?.data || [];
	},
	plugins: {
		// 挂载插件
		config: {
			columns: tableColumns.value, // 开启搜索增强需要传入列配置
			searchFields: searchFields.value, // 开启搜索增强需要传入搜索配置
		},
		list: ['SEARCH_PLUS'], // 开启插件列表
	},
});

function handleStart(row: Record<string, any>) {
	handleClose();
	emits('start', row);
}

function handleClose() {
	visible.value = false;
}

function open() {
	visible.value = true;
}

defineExpose({
	open,
});
</script>

<style>
.workflow-task-process-table-drawer .el-drawer__body {
	padding: 0 !important;
}
.workflow-task-process-table-drawer .workflow-task-process-table {
	width: 100%;
	height: calc(100vh - 120px);
}
</style>
