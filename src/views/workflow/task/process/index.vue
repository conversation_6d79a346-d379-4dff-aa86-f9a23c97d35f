<template>
	<div class="workflow-task-process">
    <Record />

    <yun-pro-table
			ref="proTableRef"
			v-model:pagination="pagination"
			v-model:filter-data="filterTableData"
			:search-fields="searchFields"
			:table-columns="tableColumns"
			:remote-method="remoteMethod"
			:batch-fields="batchFields"
			:global-selection="true"
			v-model:selected="selected"
			:default-fetch="true"
			:layout="'whole'"
			auto-height
			:table-props="{
				...tableProps,
				'show-table-setting': true,
				'show-summary': false,
			}"
		>
			<template #tableHeaderLeft>
				<el-button
					:icon="Plus"
					type="primary"
					@click.prevent.stop="handleCreate"
				>
					新增
				</el-button>
				<el-button
					:icon="Delete"
					@click.prevent.stop="handleBatchDelete"
					:disabled="selected.length === 0"
				>
					删除
				</el-button>
			</template>
			<template #t_action="{ row }">
				<el-button
					type="text"
					size="small"
					@click.prevent.stop="handleDetail(row)"
				>
					详情
				</el-button>
				<el-button
					type="text"
					size="small"
					@click.prevent.stop="handleCancel(row)"
				>
					取消申请
				</el-button>
				<el-button
					type="text"
					size="small"
					@click.prevent.stop="handleDelete(row)"
				>
					删除
				</el-button>
			</template>
		</yun-pro-table>

	</div>
  <TableDrawer ref="tableDrawerRef" @start="handleStart" />
  <FormDrawer ref="formDrawerRef" @refresh="reLoad" />

</template>

<script setup lang="ts">
import { ref } from 'vue';
import { useProTable } from '@ylz-use/core';
import { Plus, Delete } from '@yun-design/icons-vue';
import useColumns from './hooks/useColumns';
import TableDrawer from './components/table/index.vue';
import FormDrawer from './components/form/index.vue';
import Record from '../components/record/index.vue';
import { useConfirm } from '../hooks/useConfirm';



const { tableColumns, searchFields } = useColumns();
const batchFields = ref([]);
const selected = ref([]);
const tableDrawerRef = ref();
const formDrawerRef = ref();
const {
	pagination,
	remoteMethod,
	tableProps,
	proTableRef,
	filterTableData,
	reLoad,
} = useProTable({
	apiFn: () => {
		return Promise.resolve({
			data: [
				{
					taskId: '25688',
					executionId: null,
					taskName: '审批',
					taskDefKey: null,
					assigneeId: null,
					deptName: null,
					startDeptName: null,
					assigneeName: null,
					assigneeDeptName: null,
					startUserId: null,
					startUserName: null,
					category: '01001',
					variables: null,
					taskLocalVars: null,
					deployId: '25644',
					procDefId: null,
					procDefKey: null,
					procDefName: '需求审批',
					procDefVersion: 2,
					procInsId: '25671',
					hisProcInsId: null,
					duration: '28分钟',
					comment: null,
					candidate: null,
					createTime: '2025-05-27 17:03:48',
					finishTime: null,
				},
			],
		});
	},
	paramsHandler(params: Record<string, any>) {
		return {
			...params,
		};
	},
	querysHandler(query: Record<string, any>) {
		return {
			...query,
			current: pagination.value.page,
			size: pagination.value.size,
		};
	},
	responseHandler(result: Record<string, any>) {
		return result?.data || []
	},
	plugins: {
		// 挂载插件
		config: {
			columns: tableColumns.value, // 开启搜索增强需要传入列配置
			searchFields: searchFields.value, // 开启搜索增强需要传入搜索配置
		},
		list: ['SEARCH_PLUS'], // 开启插件列表
	},
});

function handleCreate() {
	tableDrawerRef.value.open();
}

function handleStart(row: Record<string, any>) {
	formDrawerRef.value.open(row);
}

async function handleBatchDelete() {
	// eslint-disable-next-line no-console
	console.log('handleBatchDelete');
  const taskIds = selected.value.map((item: Record<string, any>) => item.taskId);
	await useConfirm(
		{
			title: '警告',
			content: `是否确认删除流程定义编号为【${taskIds.join(',')}】的数据项?`,
		},
		() => {}
		// delOrder({
		//     ids: data.id
		// })
	);
}

function handleDetail(row: Record<string, any>) {
	// eslint-disable-next-line no-console
	console.log('handleDetail', row);
}

function handleCancel(row: Record<string, any>) {
	// eslint-disable-next-line no-console
	console.log('handleCancel', row);
}

function handleDelete(row: Record<string, any>) {
	// eslint-disable-next-line no-console
	console.log('handleDelete', row);
}
</script>

<style scoped lang="scss">
.workflow-task-process {
	width: 100%;
	height: calc(100vh - 88px);
}
</style>
