<template>
	<div class="workflow-task-process">
		<yun-pro-table
			ref="proTableRef"
			v-model:pagination="pagination"
			v-model:filter-data="filterTableData"
			:search-fields="searchFields"
			:table-columns="tableColumns"
			:remote-method="remoteMethod"
			:batch-fields="batchFields"
			:global-selection="true"
			v-model:selected="selected"
			:default-fetch="true"
			:layout="'whole'"
			auto-height
			:table-props="{
				...tableProps,
				'show-table-setting': true,
				'show-summary': false,
			}"
		>
			<template #tableHeaderLeft>
				<el-button
					:icon="Delete"
					@click.prevent.stop="handleBatchDelete"
					:disabled="selected.length === 0"
				>
					删除
				</el-button>
			</template>
			<template #t_action="{ row }">
				<el-button
					type="text"
					size="small"
					@click.prevent.stop="handleTodo(row)"
				>
					处理
				</el-button>
			</template>
		</yun-pro-table>
	</div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { useProTable } from '@ylz-use/core';
import { Delete } from '@yun-design/icons-vue';
import useColumns from './hooks/useColumns';
import { useConfirm } from '../hooks/useConfirm';

const { tableColumns, searchFields } = useColumns();
const batchFields = ref([]);
const selected = ref([]);

const {
	pagination,
	remoteMethod,
	tableProps,
	proTableRef,
	filterTableData,
	// reLoad,
} = useProTable({
	apiFn: () => {
		return Promise.resolve({
			data: [
				{
					taskId: '25665',
					executionId: '25655',
					taskName: '审批',
					taskDefKey: 'Activity_036rma9',
					assigneeId: null,
					deptName: null,
					startDeptName: '若依科技',
					assigneeName: null,
					assigneeDeptName: null,
					startUserId: '3',
					startUserName: 'Tony',
					category: null,
					variables: null,
					taskLocalVars: null,
					deployId: '25644',
					procDefId: 'flow_f2x4mc6n:2:25647',
					procDefKey: null,
					procDefName: '需求审批',
					procDefVersion: 2,
					procInsId: '25648',
					hisProcInsId: null,
					duration: null,
					comment: null,
					candidate: null,
					createTime: '2025-05-27 16:41:13',
					finishTime: null,
				},
			],
		});
	},
	paramsHandler(params: Record<string, any>) {
		return {
			...params,
		};
	},
	querysHandler(query: Record<string, any>) {
		return {
			...query,
			current: pagination.value.page,
			size: pagination.value.size,
		};
	},
	responseHandler(result: Record<string, any>) {
		return result?.data || [];
	},
	plugins: {
		// 挂载插件
		config: {
			columns: tableColumns.value, // 开启搜索增强需要传入列配置
			searchFields: searchFields.value, // 开启搜索增强需要传入搜索配置
		},
		list: ['SEARCH_PLUS'], // 开启插件列表
	},
});

async function handleBatchDelete() {
	// eslint-disable-next-line no-console
	console.log('handleBatchDelete');
	const taskIds = selected.value.map((item: Record<string, any>) => item.taskId);
	await useConfirm(
		{
			title: '警告',
			content: `是否确认删除流程定义编号为【${taskIds.join(',')}】的数据项?`,
		},
		() => {}
		// delOrder({
		//     ids: data.id
		// })
	);
}

function handleTodo(row: Record<string, any>) {
	// eslint-disable-next-line no-console
	console.log('handleTodo', row);
}
</script>

<style scoped lang="scss">
.workflow-task-process {
	width: 100%;
	height: calc(100vh - 88px);
}
</style>
