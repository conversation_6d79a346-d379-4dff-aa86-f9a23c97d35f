import { ElMessage, ElMessageBox } from 'yun-design';

type ConfirmConfig = {
	title?: string;
	content?: string;
	confirmButtonText?: string;
	cancelButtonText?: string;
	successMessage?: string;
	errorMessage?: string;
};

export function useConfirm(
	confirmConfig: ConfirmConfig = {
		title: '提示',
		content: '是否进行该操作?',
		confirmButtonText: '确定',
		cancelButtonText: '取消',
		successMessage: '',
		errorMessage: '',
	},
	handler = (): any => {}
) {
	return new Promise((resolve, reject) => {
		ElMessageBox.confirm(confirmConfig.content, confirmConfig.title, {
			confirmButtonText: confirmConfig.confirmButtonText,
			cancelButtonText: confirmConfig.cancelButtonText,
		})
			.then(async () => {
				try {
					resolve(await handler());
					ElMessage.success(confirmConfig.successMessage || '操作成功');
				} catch (error: Error | any) {
					ElMessage.error(confirmConfig.errorMessage || error.msg || error?.message || error || '操作失败');
					reject(error)
				}
			})
			.catch(() => {});
			// .catch((error) => {
            //     reject(error)
            // });
	});
}
