<template>
	<div class="workflow-task-process">
		<yun-pro-table
			ref="proTableRef"
			v-model:pagination="pagination"
			v-model:filter-data="filterTableData"
			:search-fields="searchFields"
			:table-columns="tableColumns"
			:remote-method="remoteMethod"
			:batch-fields="batchFields"
			:global-selection="true"
			v-model:selected="selected"
			:default-fetch="true"
			:layout="'whole'"
			auto-height
			:table-props="{
				...tableProps,
				'show-table-setting': true,
				'show-summary': false,
			}"
		>
			<template #tableHeaderLeft>
				<el-button
					:icon="Delete"
					@click.prevent.stop="handleBatchDelete"
					:disabled="selected.length === 0"
				>
					删除
				</el-button>
			</template>
			<template #t_action="{ row }">
				<el-button
					type="text"
					size="small"
					@click.prevent.stop="handleRecords(row)"
				>
					流转记录
				</el-button>
				<el-button
					type="text"
					size="small"
					@click.prevent.stop="handleReject(row)"
				>
					撤回
				</el-button>
			</template>
		</yun-pro-table>
	</div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { useProTable } from '@ylz-use/core';
import { Delete } from '@yun-design/icons-vue';
import useColumns from './hooks/useColumns';
import { useConfirm } from '../hooks/useConfirm';


const { tableColumns, searchFields } = useColumns();
const batchFields = ref([]);
const selected = ref([]);

const {
	pagination,
	remoteMethod,
	tableProps,
	proTableRef,
	filterTableData,
	// reLoad,
} = useProTable({
	apiFn: () => {
		return Promise.resolve({
			data: [
				{
					taskId: '26106',
					executionId: null,
					taskName: '数据审核',
					taskDefKey: 'Activity_1x82k79',
					assigneeId: null,
					deptName: null,
					startDeptName: '若依科技',
					assigneeName: null,
					assigneeDeptName: null,
					startUserId: 'Tony',
					startUserName: 'Tony',
					category: null,
					variables: null,
					taskLocalVars: null,
					deployId: '25915',
					procDefId: 'flow_3we71qgk:7:25918',
					procDefKey: null,
					procDefName: 'flow_7ld3gopi',
					procDefVersion: 7,
					procInsId: '25966',
					hisProcInsId: '25966',
					duration: '12分钟',
					comment: null,
					candidate: null,
					createTime: '2025-05-27 18:12:11',
					finishTime: '2025-05-27 18:24:57',
				},
			],
		});
	},
	paramsHandler(params: Record<string, any>) {
		return {
			...params,
		};
	},
	querysHandler(query: Record<string, any>) {
		return {
			...query,
			current: pagination.value.page,
			size: pagination.value.size,
		};
	},
	responseHandler(result: Record<string, any>) {
		return result?.data || [];
	},
	plugins: {
		// 挂载插件
		config: {
			columns: tableColumns.value, // 开启搜索增强需要传入列配置
			searchFields: searchFields.value, // 开启搜索增强需要传入搜索配置
		},
		list: ['SEARCH_PLUS'], // 开启插件列表
	},
});

async function handleBatchDelete() {
	// eslint-disable-next-line no-console
	console.log('handleBatchDelete');
  const taskIds = selected.value.map((item: Record<string, any>) => item.taskId);
	await useConfirm(
		{
			title: '警告',
			content: `是否确认删除流程定义编号为【${taskIds.join(',')}】的数据项?`,
		},
		() => {}
		// delOrder({
		//     ids: data.id
		// })
	);
}

function handleRecords(row: Record<string, any>) {
	// eslint-disable-next-line no-console
	console.log('handleDetail', row);
}

async function handleReject(row: Record<string, any>) {
	// eslint-disable-next-line no-console
	console.log('handleReject', row);
  const taskIds = selected.value.map((item: Record<string, any>) => item.taskId);
	await useConfirm(
		{
			title: '提示',
			content: `是否确认要撤回流程定义编号为【${taskIds.join(',')}】的数据项?`,
		},
		() => {}
		// delOrder({
		//     ids: data.id
		// })
	);
}

</script>

<style scoped lang="scss">
.workflow-task-process {
	width: 100%;
	height: calc(100vh - 88px);
}
</style>
