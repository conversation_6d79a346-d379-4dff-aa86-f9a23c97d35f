import { ref, computed, onMounted } from 'vue';
// import useHandler from '../../../hooks/useHandler';
// import { formatAmount } from '@/utils/util';

export default () => {
	// const { warehoseList, getWarehoseList } = useHandler();

	const searchFields = computed(() => [
		{
			label: '名称',
			prop: 'name',
			componentAttrs: {
				placeholder: '请输入名称',
				labelWidth: 80,
			},
			colAttrs: {
				// lg: 4,
				// md: 4,
				// sm: 8,
				// xl: 4,
				// xs: 8,
				span: 8,
			},
		},
		{
			prop: 'deployTime',
			label: '开始时间',
			component: 'el-date-picker',
			componentAttrs: {
				placeholder: '请选择开始时间',
				type: 'date',
				'value-format': 'YYYY-MM-DD',
				labelWidth: 80,
			},
			colAttrs: {
				// lg: 4,
				// md: 6,
				// sm: 8,
				// xl: 4,
				// xs: 8,
				span: 8,
			},
		},
		// {
		// 	prop: 'warehouseId',
		// 	label: '仓库',
		// 	component: 'el-select',
		// 	componentAttrs: {
		// 		placeholder: '请选择仓库',
		// 		labelWidth: 80,
		// 	},
		// 	subComponent: 'el-option',
		// 	options: warehoseList.value,
		// 	colAttrs: {
		// 		// lg: 4,
		// 		// md: 4,
		// 		// sm: 8,
		// 		// xl: 4,
		// 		// xs: 8,
		// 		span: 8,
		// 	},
		// },
	]);

	const tableColumns = ref([
		{
			type: 'selection',
			width: 52,
			fixed: 'left',
		},
		{
			label: '任务编号',
			prop: 'taskId',
			minWidth: 120,
		},
		{
			label: '流程名称',
			prop: 'procDefName',
			minWidth: 140,
		},
		{
			label: '任务节点',
			prop: 'taskName',
			minWidth: 120,
		},
		{
			label: '接收时间',
			prop: 'createTime',
			minWidth: 180,
		},
		{
			label: '审批时间',
			prop: 'finishTime',
			minWidth: 180,
		},
		{
			label: '耗时',
			prop: 'duration',
			minWidth: 120,
		},
		{
			label: '当前节点',
			prop: 'taskName',
			minWidth: 120,
		},
		{
			label: '操作',
			prop: 'action',
			width: 160,
			fixed: 'right',
		},
	]);

	onMounted(async () => {
		// await getWarehoseList();
	});

	return {
		searchFields,
		tableColumns,
	};
};
