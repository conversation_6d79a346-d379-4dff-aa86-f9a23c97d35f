import { useDict } from '/@/hooks/dict';

export const STATUS_ENUM = {
	ENABLED: '启用',
	DISABLED: '禁用',
};
export const LINE_OF_BUSINESS = [
  { label: '物流实验室', value: 'LOGISTICS_LAB' },
  { label: '商流实验室', value: 'BUSINESS_LAB' },
  { label: '经管实验室', value: 'MANAGEMENT_LAB' },
  { label: 'IOT实验室', value: 'IOT_LAB' },
  { label: '基础架构', value: 'BASE' },
];

export const STATUSES = [
  { label: '启用', value: 'ENABLED' },
  { label: '禁用', value: 'DISABLED' },
  { label: '删除', value: 'DELETED' },
  // { label: 'DB中无该状态', value: 'FORCE_DELETED' },
];

export const { belongingSpace } = useDict('belongingSpace');
