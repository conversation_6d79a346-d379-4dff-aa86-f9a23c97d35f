<template>
	<div class="pro-table-bg">
		<yun-pro-table
			ref="proTableRef"
			v-model:pagination="page"
			:search-fields="searchFields"
			:table-columns="tableColumns"
			:remote-method="remoteMethod"
			:layout="'whole'"
			auto-height
			:table-props="{
				yunPagination: false,
				showTableSetting: true,
			}"
			@reset="initTable"
		>
			<template #tableHeaderLeft>
				<el-button type="primary" @click="handleUpsert">
					<i class="yun-iconfont icon-add" />
					新增
				</el-button>
			</template>
			<template #t_appId="{ row }">
				<p>
					{{ row.appId }}
					<el-button v-if="row.appId" type="action" @click="handleCopyText(row.appId)"> 复制 </el-button>
				</p>
			</template>
			<template #t_secretKey="{ row }">
				<div class="hidden">
					<span>{{ row.secretKey }}</span>
					<div>
						<el-button v-if="row.secretKey" type="action" @click="handleCopyText(row.secretKey)"> 复制 </el-button>
					</div>
				</div>
			</template>
			<template #t_action="{ row }">
				<yun-rest limit="3">
					<el-button type="action" @click="handleEdit(row)"> 编辑 </el-button>
					<el-button type="action" @click="handleInvoke(row)"> 调用 </el-button>
					<el-button type="action" @click="handleStatus(row)"> {{`${row.status === 'ENABLED'? '禁用': '启用'}`}} </el-button>
				</yun-rest>
			</template>
		</yun-pro-table>
		<Upsert ref="upsertRef" @upsert="initTable" />
		<Invoke ref="invokeRef" @upsert="initTable"></Invoke>
	</div>
</template>

<script setup lang="jsx">
import { ref } from 'vue';
import { ElMessage } from 'yun-design';
import useTable from './useTable.jsx';
import useConfig from './config.jsx';
import Upsert from './components/upsert.vue';
import Invoke from './components/invoke.vue';
const { proTableRef, page, initTable, remoteMethod, handleStatus } = useTable();
const { searchFields, tableColumns } = useConfig();

const upsertRef = ref(null);
function handleUpsert() {
	upsertRef.value?.show();
}
function handleEdit(row) {
	upsertRef.value?.show(row);
}

const invokeRef = ref(null);
function handleInvoke(row) {
	invokeRef.value?.show(row);
}
async function handleCopyText(text) {
	try {
		await navigator.clipboard.writeText(text);
		ElMessage.success('文本已复制到剪贴板');
	} catch (err) {
		ElMessage.success('复制到剪贴板失败');
	}
}
</script>

<style lang="scss" scoped>
.dark {
	.pro-table-bg {
		background: var(--el-color-dark-bg);
		padding: 16px;
	}
}
.pro-table-bg {
	box-sizing: border-box;
	height: calc(100vh - 116px);
}
.hidden {
	display: grid;
	grid-template-columns: auto 1fr;
	span {
		overflow: hidden;
		white-space: nowrap;
		text-overflow: ellipsis;
	}
}
</style>

<style lang="scss">
.tenant-drawer {
	.yun-pro-form-item__group-item-body {
		background-color: transparent;
	}
	.form-item-address {
		width: calc(100% + 16px) !important;
		transform: translateX(-16px);
	}
	.el-input-group__append {
		padding: 0 100px;
	}
	.yun-pro-form-item__group-item-label {
		font-size: 16px;
		color: var(--el-text-color-primary);
	}
}
</style>
