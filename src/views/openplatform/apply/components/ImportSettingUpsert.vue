<script setup>
import useUpsert from './ImportSettingUpsert.jsx';
import detail from '../../../service/interface/components/detail.vue';

const emits = defineEmits('upsert');
const detailRef = ref();
const { visible, isEdit, loading, formProps, form, columns, onConfirm, show } = useUpsert(detailRef);
const formRef = ref(null);
async function handleConfirt(done, loading) {
	await formRef.value?.elForm?.validate();
	try {
		loading.value = true;
		await onConfirm(form.value);
		done();
		emits('upsert');
	} finally {
		loading.value = false;
	}
}
defineExpose({
	show,
});
</script>
<template>
	<yun-drawer
		v-model="visible"
		size="medium"
		:title="isEdit ? '编辑' : '新增'"
		destroy-on-close
		custom-class="tenant-drawer"
		@confirm="handleConfirt"
		:confirm-button-text="'确定'"
		:cancel-button-text="'取消'"
	>
		<yun-pro-form v-loading="loading" ref="formRef" :form-props="formProps" :config="{ colProps: { span: 24 } }" :form="form" :columns="columns" />
		<detail ref="detailRef" @getData="getData" />
	</yun-drawer>
</template>
<style lang="scss" scoped></style>
