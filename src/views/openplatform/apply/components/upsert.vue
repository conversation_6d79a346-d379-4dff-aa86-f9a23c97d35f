<script setup>
import useUpsert from './upsert.jsx';

const emits = defineEmits('upsert');
const { visible, isEdit, loading, formProps, form, columns, onConfirm, show, formRef, errorConfig, errorExt } = useUpsert();
async function handleConfirt(done, loading) {
	if (errorConfig.value || errorExt.value) {
		return;
	}
	await formRef.value?.elForm?.validate();
	try {
		loading.value = true;
		await onConfirm(form.value);
		done();
		emits('upsert');
	} finally {
		loading.value = false;
	}
}
defineExpose({
	show,
});
</script>
<template>
	<yun-drawer
		v-model="visible"
		size="medium"
		:title="isEdit ? '编辑' : '新增'"
		destroy-on-close
		custom-class="apply-drawer"
		@confirm="handleConfirt"
		:confirm-button-text="'确定'"
		:cancel-button-text="'取消'"
	>
		<div v-loading="loading">
			<yun-pro-form v-if="!loading" ref="formRef" :form-props="formProps" :config="{ colProps: { span: 24 } }" :form="form" :columns="columns" />
		</div>
	</yun-drawer>
</template>
<style lang="scss" scoped></style>
