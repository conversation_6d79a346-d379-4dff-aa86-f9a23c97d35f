import { ref, computed, nextTick } from 'vue';
import { useUserInfo } from '/@/stores/userInfo';
import { fetchPage } from '@/api/openplatform/companies';
import { fetchItemList } from '@/api/admin/dict';
import CodeEditor from '/@/components/MonacoEditor/index.vue';
import { getSign, signSave, signUpdate } from '@/api/openplatform/apply';
import { fetchPage as tenantPage } from '@/api/admin/tenant';
import { LINE_OF_BUSINESS } from '../constant';

export default () => {
	const visible = ref(false);
	const isEdit = ref(false);
	const loading = ref(false);
	const formRef = ref(null);
	const formProps = {
		labelPosition: 'top',
	};
	const enterprise = ref([]);
	const currentuserName = computed(() => {
		return useUserInfo().userInfos.user.name;
	});
	async function getEnterprice(val) {
		const { data } = await fetchPage({ corpName: val }, { current: 1, size: 20, page: 1 });
		enterprise.value = data.records.map((item) => ({
			value: item.id,
			label: item.corpName,
		}));
	}
	const businessOptions = ref([]);
	(async () => {
		const resp = await fetchItemList({ dictType: 'tenant_identity_type' });
		businessOptions.value = resp?.data?.records || [];
		businessOptions.value.forEach((item) => {
			businessOptions.value[item.value] = item.label;
		});
	})();

	const tenantOptions = ref([]);
	async function getTenantPage(val) {
		const { data } = await tenantPage({ status: 0, current: 1, size: 999999999, name: val || undefined });
		tenantOptions.value = data.records.map((item) => ({
			value: item.id,
			label: item.name,
		}));
	}
	const body = ref(null);
	function addEvent() {
		body.value = document.querySelector('.apply-drawer .el-drawer__body');
	}

	const errorConfig = ref('');
	const errorExt = ref('');
	const columns = computed(() => [
		{
			label: '企业',
			prop: 'enterpriseCode',
			type: 'select',
			attrs: {
				placeholder: '请选择',
				filterable: true,
				clearable: true,
				remote: true,
				'remote-method': getEnterprice,
			},
			rules: [{ required: true, message: '请选择', trigger: 'change' }],
			enums: enterprise.value,
		},
		{
			label: '业务线',
			prop: 'businessId',
			type: 'select',
			rules: [{ required: true, message: '请选择', trigger: 'change' }],
			enums: LINE_OF_BUSINESS,
		},
		{
			label: '租户',
			prop: 'tenantId',
			type: 'select',
			attrs: {
				placeholder: '请选择',
				filterable: true,
				clearable: true,
			},
			rules: [{ required: true, message: '请选择', trigger: 'change' }],
			enums: tenantOptions.value,
		},
		{
			label: '应用名称',
			prop: 'appName',
			type: 'input',
			rules: [
				{ required: true, message: '请输入应用名称', trigger: 'blur' },
				{ max: 50, message: '不能超过50个字符' },
			],
		},
		{
			label: '白名单配置',
			prop: 'config',
			type: 'input',
			attrs: {
				type: 'textarea',
				autosize: {
					minRows: 2,
					maxRows: 4,
				},
			},
			error: errorConfig.value,
			tipProps: { effect: 'dark', width: 'unset' },
			renderTip: () => (
				<div>
					<p>白名单配置用作白名单使用(域名)</p>
					<p>只需要新增或修改里面的域名。这里不填写内容代表都可以访问</p>
					<p>示例:"white_ip_list":["127.0.0.1", "************"]。</p>
				</div>
			),
			render: (form) => {
				if (!form.config) {
					form.config = {
						white_ip_list: [],
					};
				}
				const config = JSON.stringify(form.config, null, 2);
				function handleChange(val) {
					try {
						form.config = val ? JSON.parse(val) : '';
						errorConfig.value = '';
					} catch {
						errorConfig.value = 'JSON格式不正确';
					}
				}
				return <CodeEditor scrollDom={body.value} modelValue={config} onUpdate:modelValue={handleChange}></CodeEditor>;
			},
		},
		{
			label: '扩展字段',
			prop: 'ext',
			type: 'input',
			error: errorExt.value,
			render: (form) => {
				if (!form.ext && !isEdit.value) {
					form.ext = { client_info: 'pig:pig' };
				}
				const ext = JSON.stringify(form.ext, null, 2);
				function handleChange(val) {
					try {
						form.ext = val ? JSON.parse(val) : '';
						errorExt.value = '';
					} catch {
						errorExt.value = 'JSON格式不正确';
					}
				}
				return <CodeEditor scrollDom={body.value} modelValue={ext} onUpdate:modelValue={handleChange}></CodeEditor>;
			},
		},
		{
			label: '备注',
			prop: 'remark',
			type: 'input',
			attrs: {
				type: 'textarea',
				maxlength: 200,
				'show-word-limit': true,
				autosize: {
					minRows: 2,
					maxRows: 4,
				},
			},
		},
	]);
	const form = ref({});
	async function getInfo(id) {
		try {
			loading.value = true;
			const resp = await getSign(id);
			form.value = resp?.data || {};
		} finally {
			loading.value = false;
		}
	}
	function show(row) {
		visible.value = true;
		isEdit.value = false;
		form.value = {};
		getEnterprice('');
		getTenantPage();
		if (row?.id) {
			isEdit.value = true;
			getInfo(row?.id);
		}
		nextTick(() => {
			addEvent();
		});
	}
	function onConfirm() {
		if (isEdit.value) {
			return signUpdate({ ...form.value, updateBy: currentuserName.value });
		} else {
			return signSave({ ...form.value, updateBy: currentuserName.value });
		}
	}
	return {
		visible,
		isEdit,
		loading,
		formProps,
		form,
		columns,
		onConfirm,
		show,
		formRef,
		errorConfig,
		errorExt,
	};
};
