import { ref, computed } from 'vue';
import { useUserInfo } from '/@/stores/userInfo';
import { modulePage, interfaceInfoPage, opImportSettingPageSave, opImportSettingPageUpdate, getOpImportSetting } from '@/api/openplatform/apply';

export default (detailRef) => {
	const currentuserName = computed(() => {
		return useUserInfo().userInfos.user.name;
	});
	const visible = ref(false);
	const isEdit = ref(false);
	const loading = ref(false);
	const formProps = {
		labelPosition: 'top',
	};
	const form = ref({});

	const modelOptions = ref([]);
	async function getModelOptions(val) {
		const { data } =
			(await modulePage(
				{ page: 1, size: 20 },
				{
					moduleName: val,
					status: 'ENABLED',
				}
			)) || {};
		modelOptions.value = (data?.records || []).map(({ id, moduleName }) => ({ label: moduleName, value: moduleName, id }));
	}
	const interfaceInfoOptions = ref([]);
	async function getInterfaceInfoOptions(val) {
		interfaceInfoOptions.value = [];
		const { data } =
			(await interfaceInfoPage(
				{ page: 1, size: 20 },
				{
					interfaceUrl: val,
					moduleName: form.value.serviceName,
					status: 'ENABLED',
				}
			)) || {};
		interfaceInfoOptions.value = (data?.records || []).map((item) => ({ label: item.interfaceName, value: item.interfaceUrl, data: item }));
	}

	const columns = computed(() => [
		{
			label: '关联服务名称',
			prop: 'serviceName',
			type: 'select',
			attrs: {
				placeholder: '请选择',
				filterable: true,
				clearable: true,
				remote: true,
				disabled: true,
				'remote-method': getModelOptions,
				onChange: () => {
					form.value.url = '';
					form.value.invokeMethod = '';
					form.value.command = '';
					getInterfaceInfoOptions('');
				},
			},
			enums: modelOptions.value,
			rules: [{ required: true, message: '请选择服务名称', trigger: 'blur' }],
		},
		{
			label: '接口列表',
			prop: 'url',
			type: 'select',
			rules: [{ required: true, message: '请选择服务名称', trigger: 'blur' }],
			render: (form) => {
				return (
					<div style="display: grid;grid-template-columns: 1fr 60px;">
						<el-select
							v-model={form.url}
							placeholder="请选择"
							filterable
							clearable
							remote
							disabled={true}
							onChange={() => {
								const item = interfaceInfoOptions.value.find((e) => e.value === form.url);
								form.invokeMethod = item.data?.interfaceJson?.method || '';
								form.command = item?.data?.command || '';
							}}
							remote-method={getInterfaceInfoOptions}
						>
							{interfaceInfoOptions.value.map((item) => {
								return <el-option label={item.label} value={item.value}></el-option>;
							})}
						</el-select>
						<el-button
							onClick={() => {
								const item = interfaceInfoOptions.value.find((e) => e.value === form.url);
								if (item) {
									detailRef.value.show(item.data);
								}
							}}
							disabled={!form.url}
							type="action"
						>
							查看
						</el-button>
					</div>
				);
			},
		},
		{
			label: '请求路径',
			prop: 'url',
			colProps: { span: 12 },
		},
		{
			label: '请求方式',
			prop: 'invokeMethod',
			colProps: { span: 6 },
		},
		{
			label: '指令',
			prop: 'command',
			colProps: { span: 6 },
		},
		{
			label: '是否直接转发',
			prop: 'isDirect',
			type: 'radio',
			enums: [
				{ label: '是', value: 1 },
				{ label: '否', value: 0 },
			],
			rules: [{ required: true, message: '请选择', trigger: 'change' }],
		},
		{
			label: '签名方法',
			prop: 'signatureMethod',
			type: 'input',
			attrs: {
				disabled: true,
			},
			rules: [{ required: true, message: '请选择', trigger: 'change' }],
		},
		{
			label: '输入脚本',
			prop: 'inboundScript',
			type: 'input',
			attrs: {
				type: 'textarea',
				autosize: {
					minRows: 2,
					maxRows: 4,
				},
			},
		},
		{
			label: '输出脚本',
			prop: 'outboundScript',
			type: 'input',
			attrs: {
				type: 'textarea',
				autosize: {
					minRows: 2,
					maxRows: 4,
				},
			},
		},
		{
			label: '备注',
			prop: 'remark',
			type: 'input',
			attrs: {
				type: 'textarea',
				autosize: {
					minRows: 2,
					maxRows: 4,
				},
			},
		},
	]);
	async function getInfo(id) {
		try {
			loading.value = true;
			const { data } = (await getOpImportSetting(id)) || {};
			form.value = data
				? {
						...data,
						isDirect: typeof data.isDirect === 'boolean' ? (data.isDirect ? 1 : 0) : '',
				  }
				: {};
			getModelOptions(form.value.serviceName);
			getInterfaceInfoOptions(form.value.url);
		} finally {
			loading.value = false;
		}
	}
	function show(data, row) {
		visible.value = true;
		isEdit.value = false;
		form.value = {
			...data,
			signatureMethod: 'default',
		};
		if (row?.id) {
			isEdit.value = true;
			getInfo(row?.id);
		} else {
			getModelOptions('');
		}
	}
	function onConfirm() {
		if (isEdit.value) {
			return opImportSettingPageUpdate({ ...form.value, updateBy: currentuserName.value });
		} else {
			return opImportSettingPageSave({ ...form.value, updateBy: currentuserName.value });
		}
	}
	return {
		visible,
		isEdit,
		loading,
		formProps,
		form,
		columns,
		onConfirm,
		show,
	};
};
