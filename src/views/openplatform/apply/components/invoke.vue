<script setup>
import { ref } from 'vue';
import ImportSetting from './ImportSetting.vue';
import ExportSetting from './ExportSetting.vue';

const visible = ref(false);
const activeName = ref('import');
const appid = ref('');
const enterpriseCode = ref('');
function show(row) {
	activeName.value = 'import';
	visible.value = true;
	appid.value = row.appId;
	enterpriseCode.value = row.enterpriseCode;
}
defineExpose({
	show,
});
</script>
<template>
	<yun-drawer
		v-model="visible"
		size="X-large"
		title="调用"
		destroy-on-close
		custom-class="tenant-drawer"
		:show-confirm-button="false"
		:close-on-click-modal="false"
		:cancel-button-text="'返回'"
	>
		<div class="body">
			<el-tabs v-model="activeName" class="demo-tabs">
				<el-tab-pane label="内部调用" name="import"></el-tab-pane>
				<el-tab-pane label="外部调用" name="export"></el-tab-pane>
			</el-tabs>
			<div v-if="visible" class="tab-body">
				<div v-if="activeName === 'import'" style="height: 100%"><ImportSetting :appid="appid" :enterpriseCode="enterpriseCode" /></div>
				<div v-if="activeName === 'export'" style="height: 100%"><ExportSetting :appid="appid" :enterpriseCode="enterpriseCode" /></div>
			</div>
		</div>
	</yun-drawer>
</template>
<style lang="scss" scoped>
.body {
	height: 100%;
	display: flex;
	flex-direction: column;
}
.tab-body {
	flex: 1;
	overflow: hidden;
}
</style>
