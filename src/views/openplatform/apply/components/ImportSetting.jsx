import { ref, computed } from 'vue';
import { opImportSettingPage, opImportSettingRemove, opImportSettingStatus } from '@/api/openplatform/apply';
import useInitTable from '@/hooks/useInitTable';
import { ElMessageBox } from 'yun-design';
import { STATUS_ENUM } from '../constant';

export default (props) => {
	const searchFields = computed(() => [
		{
			label: '关联服务名称',
			prop: 'serviceName',
			component: 'el-input',
		},
		{
			label: '指令',
			prop: 'command',
			component: 'el-input',
		},
	]);
	const tableColumns = [
		{
			label: '关联服务名称',
			prop: 'serviceName',
			minWidth: 140,
		},
		{
			label: '指令',
			prop: 'command',
		},
		{
			label: '接口地址',
			prop: 'url',
		},
		{
			label: 'APP ID',
			prop: 'appId',
		},
		// {
		// 	label: '企业',
		// 	prop: 'enterpriseName',
		// },
		{
			label: '创建时间',
			prop: 'createTime',
		},
		{
			label: '状态',
			prop: 'status',
			formatter: ({ status }) => STATUS_ENUM[status],
		},
		{
			label: '操作',
			prop: 'action',
			width: 200,
			fixed: 'right',
		},
	];

	const proTableRef = ref(null);
	const page = ref({
		page: 1,
		total: 0,
		size: 10,
	});
	const { initTable } = useInitTable({
		page,
		tableRef: proTableRef,
	});
	async function remoteMethod({ searchData, pagination }) {
		const { ...restParasm } = searchData;
		const data = {
			...restParasm,
			appId: props.appid,
		};
		const { size, page } = pagination;
		const res = await opImportSettingPage({ ...data }, { current: page, size, page });
		return res?.data;
	}

	async function handleRemove(id) {
		await ElMessageBox.confirm(`确认删除`, '提示', {
			confirmButtonText: '确定',
			cancelButtonText: '取消',
			type: 'warning',
			beforeClose: async (action, instance, done) => {
				if (action === 'confirm') {
					instance.confirmButtonLoading = true;
					try {
						await opImportSettingRemove({
							ids: [id],
						});
						done();
						initTable();
					} finally {
						instance.confirmButtonLoading = false;
					}
				} else {
					done();
				}
			},
		});
	}
	async function handleStatus(row) {
		const msg = row.status === 'ENABLED' ? '禁用' : '启用';
		await ElMessageBox.confirm(`确认${msg}`, '提示', {
			confirmButtonText: '确定',
			cancelButtonText: '取消',
			type: 'warning',
			beforeClose: async (action, instance, done) => {
				if (action === 'confirm') {
					instance.confirmButtonLoading = true;
					try {
						await opImportSettingStatus({
							id: row.id,
							status: row.status === 'ENABLED' ? 'DISABLED' : 'ENABLED',
						});
						done();
						initTable();
					} finally {
						instance.confirmButtonLoading = false;
					}
				} else {
					done();
				}
			},
		});
	}
	return {
		searchFields,
		tableColumns,
		proTableRef,
		page,
		initTable,
		remoteMethod,
		handleRemove,
		handleStatus,
	};
};
