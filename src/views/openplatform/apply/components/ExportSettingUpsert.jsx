import { ref, computed, nextTick } from 'vue';
import { useUserInfo } from '/@/stores/userInfo';
import CodeEditor from '/@/components/MonacoEditor/index.vue';
import { opExportSettingSave, opExportSettingUpdate, getOpExportSetting } from '@/api/openplatform/apply';

function generateRandomString() {
	const part1 = getRandomAlphanumeric(2);
	const part2 = getRandomAlphanumeric(4);
	const part3 = getRandomAlphanumeric(4);

	return `${part1}.${part2}.${part3}`;
}

// 生成随机字母和数字组合的字符串
function getRandomAlphanumeric(length) {
	let result = '';
	const characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789'; // 字母和数字
	for (let i = 0; i < length; i++) {
		result += characters.charAt(Math.floor(Math.random() * characters.length));
	}
	return result;
}
export default () => {
	const currentuserName = computed(() => {
		return useUserInfo().userInfos.user.name;
	});
	const visible = ref(false);
	const isEdit = ref(false);
	const loading = ref(false);
	const formProps = {
		labelPosition: 'top',
	};
	const form = ref({});

	const body = ref(null);
	function addEvent() {
		body.value = document.querySelector('.exportSettingUpsert-drawer .el-drawer__body');
	}
	const errorConfig = ref('');
	const columns = computed(() => [
		{
			label: '名称',
			prop: 'remark',
			type: 'input',
			attrs: {
			},
			rules: [
				{ required: true, message: '请输入', trigger: 'change' },
				{ max: 50, message: '不能超过50个字符' },
			],
		},
		{
			label: '指令',
			prop: 'command',
			type: 'input',
			attrs: {
				disabled: isEdit.value,
			},
			rules: [
				{ required: true, message: '请输入', trigger: 'change' },
				{ max: 50, message: '不能超过50个字符' },
			],
		},
		// {
		// 	label: '消息匹配key',
		// 	prop: 'eventKey',
		// 	type: 'input',
		// 	rules: [
		// 		{ required: true, message: '请输入', trigger: 'change' },
		// 		{ max: 50, message: '不能超过50个字符' },
		// 	],
		// },
		{
			label: '处理器KEY',
			prop: 'invokeIdentity',
			type: 'input',
			rules: [
				{ required: true, message: '请输入', trigger: 'change' },
				{ max: 50, message: '不能超过50个字符' },
			],
		},
		{
			label: '指令类型',
			prop: 'commandType',
			type: 'radio',
			enums: [
				{ label: '同步', value: 'SYNC' },
				{ label: '异步', value: 'ASYNC' },
			],
			rules: [{ required: true, message: '请选择', trigger: 'change' }],
		},
		{
			label: '扩展字段',
			prop: 'config',
			type: 'input',
			error: errorConfig.value,
			render: (form) => {
				let config = '';
				if (!form.config) {
					form.config = {
						url: '',
						appld: '',
						secretKey: '',
						httpMethod: 'POST',
					};
				}
				config = JSON.stringify(form.config, null, 2);
				function handleChange(val) {
					try {
						form.config = val ? JSON.parse(val) : '';
						errorConfig.value = '';
					} catch {
						errorConfig.value = 'JSON格式不正确';
					}
				}
				return <CodeEditor scrollDom={body.value} modelValue={config} onUpdate:modelValue={handleChange}></CodeEditor>;
			},
		},
		{
			label: '输入脚本',
			prop: 'inboundScript',
			type: 'input',
			attrs: {
				type: 'textarea',
				autosize: {
					minRows: 2,
					maxRows: 4,
				},
			},
		},
		{
			label: '输出脚本',
			prop: 'outboundScript',
			type: 'input',
			attrs: {
				type: 'textarea',
				autosize: {
					minRows: 2,
					maxRows: 4,
				},
			},
		},
	]);
	async function getInfo(id) {
		try {
			loading.value = true;
			const { data } = await getOpExportSetting(id);
			form.value = data
				? {
						...data,
						isDirect: typeof data.isDirect === 'boolean' ? (data.isDirect ? 1 : 0) : '',
				  }
				: {};
		} finally {
			loading.value = false;
		}
	}
	function show(data, row) {
		visible.value = true;
		isEdit.value = false;
		form.value = {
			...data,
		};
		if (row?.id) {
			isEdit.value = true;
			getInfo(row?.id);
		} else {
			form.value.command = generateRandomString();
		}
		nextTick(() => {
			addEvent();
		});
	}
	function onConfirm() {
		if (isEdit.value) {
			return opExportSettingUpdate({ ...form.value, updateBy: currentuserName.value });
		} else {
			return opExportSettingSave({ ...form.value, updateBy: currentuserName.value });
		}
	}
	return {
		visible,
		isEdit,
		loading,
		formProps,
		form,
		columns,
		onConfirm,
		show,
		errorConfig,
	};
};
