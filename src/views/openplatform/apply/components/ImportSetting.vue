<script setup lang="jsx">
import useImportSetting from './ImportSetting.jsx';
import ImportSettingUpsert from './ImportSettingUpsert.vue';

const props = defineProps({
	appid: {
		type: String,
		requird: true,
	},
	enterpriseCode: {
		type: String,
		requird: true,
	},
});

const { searchFields, tableColumns, proTableRef, page, initTable, remoteMethod, handleRemove, handleStatus } = useImportSetting(props);

const importSettingUpsertRef = ref(null);
// function handelAdd() {
// 	importSettingUpsertRef.value.show({
// 		appId: props.appid,
// 		enterpriseCode: props.enterpriseCode,
// 	});
// }
function handleEdit(row) {
	importSettingUpsertRef.value.show(
		{
			appId: props.appid,
			enterpriseCode: props.enterpriseCode,
		},
		row
	);
}
</script>
<template>
	<yun-pro-table
		ref="proTableRef"
		v-model:pagination="page"
		:search-fields="searchFields"
		:table-columns="tableColumns"
		:remote-method="remoteMethod"
		:layout="'whole'"
		auto-height
		:table-props="{
			yunPagination: false,
			showTableSetting: true,
		}"
		@reset="initTable"
	>
		<template #tableHeaderLeft>
			<!-- <el-button type="primary" @click="handelAdd">
				<i class="yun-iconfont icon-add" />
				新增
			</el-button> -->
		</template>
		<template #t_action="{ row }">
			<yun-rest limit="3">
				<el-button type="action" @click="handleEdit(row)"> 编辑 </el-button>
				<el-button type="action" @click="handleRemove(row.id)"> 删除 </el-button>
				<el-button type="action" @click="handleStatus(row)"> {{`${row.status === 'ENABLED'? '禁用': '启用'}`}} </el-button>
			</yun-rest>
		</template>
	</yun-pro-table>
	<ImportSettingUpsert ref="importSettingUpsertRef" @upsert="initTable" />
</template>
<style lang="less" scoped></style>
