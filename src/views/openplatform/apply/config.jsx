import { STATUS_ENUM } from './constant';
export default () => {
	const searchFields = [
		{
			label: '应用名称',
			prop: 'appName',
			component: 'el-input',
		},
		{
			label: 'APP ID',
			prop: 'appId',
			component: 'el-input',
		},
	];
	const tableColumns = [
		{
			label: '应用名称',
			prop: 'appName',
		},
		{
			label: 'APP ID',
			prop: 'appId',
			minWidth: 330,
		},
		{
			label: 'secretKey',
			prop: 'secretKey',
			minWidth: 180,
		},
		{
			label: '企业',
			prop: 'enterpriseName',
		},
		{
			label: '创建时间',
			prop: 'createTime',
			width: 180,
		},
		{
			label: '状态',
			prop: 'status',
			formatter: ({ status }) => STATUS_ENUM[status],
		},
		{
			label: '操作',
			prop: 'action',
			width: 200,
			fixed: 'right',
		},
	];
	return {
		searchFields,
		tableColumns,
	};
};
