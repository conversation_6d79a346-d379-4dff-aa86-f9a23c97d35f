import { ref } from 'vue';
import { fetchPage, signRemove, signStatus } from '@/api/openplatform/apply';
import useInitTable from '@/hooks/useInitTable';
import { ElMessageBox } from 'yun-design';

export default () => {
	const proTableRef = ref(null);
	const page = ref({
		page: 1,
		total: 0,
		size: 10,
	});
	const { initTable } = useInitTable({
		page,
		tableRef: proTableRef,
	});
	const remoteMethod = async ({ searchData, pagination }) => {
		const { ...restParasm } = searchData;
		const data = {
			...restParasm,
			descs: 'create_time',
		};
		const { size, page } = pagination;
		const res = await fetchPage({ ...data }, { current: page, size, page });
		return res?.data;
	};

	async function handleRemove(id) {
		await ElMessageBox.confirm(`确认删除`, '提示', {
			confirmButtonText: '确定',
			cancelButtonText: '取消',
			type: 'warning',
			beforeClose: async (action, instance, done) => {
				if (action === 'confirm') {
					instance.confirmButtonLoading = true;
					try {
						await signRemove({
							ids: [id],
						});
						done();
						initTable();
					} finally {
						instance.confirmButtonLoading = false;
					}
				} else {
					done();
				}
			},
		});
	}
	async function handleStatus(row) {
		const msg = row.status === 'ENABLED' ? '禁用' : '启用';
		await ElMessageBox.confirm(`确认${msg}`, '提示', {
			confirmButtonText: '确定',
			cancelButtonText: '取消',
			type: 'warning',
			beforeClose: async (action, instance, done) => {
				if (action === 'confirm') {
					instance.confirmButtonLoading = true;
					try {
						await signStatus({
							id: row.id,
							status: row.status === 'ENABLED' ? 'DISABLED' : 'ENABLED',
						});
						done();
						initTable();
					} finally {
						instance.confirmButtonLoading = false;
					}
				} else {
					done();
				}
			},
		});
	}
	return {
		proTableRef,
		page,
		initTable,
		remoteMethod,
		handleRemove,
		handleStatus,
	};
};
