import { ref, computed } from 'vue';
import { opImportSettingPage } from '@/api/openplatform/apply';
import useInitTable from '@/hooks/useInitTable';
import { STATUS_ENUM } from './constant';

export default () => {
	const searchFields = computed(() => [
		{
			label: '指令',
			prop: 'command',
			component: 'el-input',
		},
		{
			label: '关联服务名称',
			prop: 'serviceName',
			component: 'el-input',
		},
	]);
	const tableColumns = [
		{
			label: '指令',
			prop: 'command',
			minWidth: 140,
		},
		{
			label: 'APP ID',
			prop: 'appId',
			minWidth: 140,
		},
		{
			label: '关联服务名称',
			prop: 'serviceName',
			minWidth: 140,
		},
		{
			label: '接口地址',
			prop: 'url',
		},
		// {
		// 	label: '企业',
		// 	prop: 'enterpriseName',
		// },
		{
			label: '创建时间',
			prop: 'createTime',
		},
		{
			label: '状态',
			prop: 'status',
			formatter: ({ status }) => STATUS_ENUM[status],
		},
		{
			label: '操作',
			prop: 'action',
			width: 100,
			fixed: 'right',
		},
	];

	const proTableRef = ref(null);
	const page = ref({
		page: 1,
		total: 0,
		size: 10,
	});
	const { initTable } = useInitTable({
		page,
		tableRef: proTableRef,
	});
	async function remoteMethod({ searchData, pagination }) {
		const { ...restParasm } = searchData;
		const data = {
			...restParasm,
		};
		const { size, page } = pagination;
		const res = await opImportSettingPage({ ...data }, { current: page, size, page });
		return res?.data;
	}

	return {
		searchFields,
		tableColumns,
		proTableRef,
		page,
		initTable,
		remoteMethod,
	};
};
