<template>
	<div class="pro-table-bg">
		<yun-pro-table
			ref="proTableRef"
			v-model:pagination="page"
			:search-fields="searchFields"
			:table-columns="tableColumns"
			:remote-method="remoteMethod"
			:layout="'whole'"
			auto-height
			:table-props="{
				yunPagination: false,
				showTableSetting: true,
			}"
			@reset="initTable"
		>
			<template #tableHeaderLeft>
				<el-button type="primary" @click="handleUpsert">
					<i class="yun-iconfont icon-add" />
					入驻
				</el-button>
			</template>
			<template #t_businessLicenseUrl="{ row }">
				<template v-if="row.businessLicenseUrl">
					<el-button type="action" v-if="row.businessLicenseUrl.endsWith('.pdf')" @click="handleDownload(row.businessLicenseUrl)">pdf 下载</el-button>
					<el-image
						v-else
						style="width: 50px; height: 50px"
						:src="row.businessLicenseUrl"
						fit="contain"
						append-to-body
						:preview-src-list="[row.businessLicenseUrl]"
					></el-image>
				</template>
			</template>
			<template #t_action="{ row }">
				<yun-rest limit="3">
					<el-button type="action" @click="handleEdit(row)"> 编辑 </el-button>
					<!-- <el-button type="action" @click="handleRemove(row.id)"> 删除 </el-button> -->
				</yun-rest>
			</template>
		</yun-pro-table>
		<Upsert ref="upsertRef" @upsert="initTable" />
	</div>
</template>

<script setup lang="jsx">
import { ref } from 'vue';
import useTable from './useTable.jsx';
import useConfig from './config.jsx';
import Upsert from './components/upsert.vue';
const {
	proTableRef,
	page,
	initTable,
	remoteMethod,
	//  handleRemove
} = useTable();
const { searchFields, tableColumns } = useConfig();

const upsertRef = ref(null);
function handleUpsert() {
	upsertRef.value?.show();
}
function handleEdit(row) {
	upsertRef.value?.show(row);
}
function handleDownload(url) {
	window.open(url);
}
</script>

<style lang="scss" scoped>
.dark {
	.pro-table-bg {
		background: var(--el-color-dark-bg);
		padding: 16px;
	}
}
.pro-table-bg {
	box-sizing: border-box;
	height: calc(100vh - 116px);
}
</style>

<style lang="scss">
.tenant-drawer {
	.yun-pro-form-item__group-item-body {
		background-color: transparent;
	}
	.form-item-address {
		width: calc(100% + 16px) !important;
		transform: translateX(-16px);
	}
	.el-input-group__append {
		padding: 0 100px;
	}
	.yun-pro-form-item__group-item-label {
		font-size: 16px;
		color: var(--el-text-color-primary);
	}
}
</style>
