import { ref } from 'vue';
import { fetchPage, enterpriseInfoRemove } from '@/api/openplatform/companies';
import useInitTable from '@/hooks/useInitTable';
import { ElMessageBox } from 'yun-design';

export default () => {
	const proTableRef = ref(null);
	const page = ref({
		page: 1,
		total: 0,
		size: 10,
	});
	const { initTable } = useInitTable({
		page,
		tableRef: proTableRef,
	});
	const remoteMethod = async ({ searchData, pagination }) => {
		const { ...restParasm } = searchData;
		const data = {
			...restParasm,
			descs: 'create_time',
		};
		const { size, page } = pagination;
		const res = await fetchPage({ ...data }, { current: page, size, page });
		return res?.data;
	};

	async function handleRemove(id) {
		await ElMessageBox.confirm(`确认删除`, '提示', {
			confirmButtonText: '确定',
			cancelButtonText: '取消',
			type: 'warning',
			beforeClose: async (action, instance, done) => {
				if (action === 'confirm') {
					instance.confirmButtonLoading = true;
					try {
						await enterpriseInfoRemove({
							ids: [id],
						});
						done();
						initTable();
					} finally {
						instance.confirmButtonLoading = false;
					}
				} else {
					done();
				}
			},
		});
	}
	return {
		proTableRef,
		page,
		initTable,
		remoteMethod,
		handleRemove,
	};
};
