import { ref, computed } from 'vue';
import UploadImg from '/@/components/Upload/Image.vue';
import { enterpriseInfoSave, enterpriseInfoGet, enterpriseInfoUpdate, apiOpen } from '@/api/openplatform/companies';

async function fetchCompanyList(str, cb) {
	let list = [];
	if (!str) {
		cb(list);
		return;
	}
	if (str.length < 2) return;
	try {
		const { data } = (await apiOpen({ keyWord: str })) || {};
		list =
			data?.data?.Result?.map((item) => {
				return {
					...item,
					value: item.Name,
				};
			}) || [];
	} finally {
		cb(list);
	}
}
export default () => {
	const visible = ref(false);
	const isEdit = ref(false);
	const loading = ref(false);
	const formProps = {
		labelPosition: 'top',
	};
	const columns = computed(() => [
		{
			label: '公司名称',
			prop: 'corpName',
			type: 'input',
			rules: [
				{ required: true, message: '请输入公司名称', trigger: 'blur' },
				{ max: 30, message: '不能超过30个字符' },
			],
			render: (form) => {
				function handleSelectCompany(val) {
					form.creditCode = val.CreditCode;
					form.businessLegal = val.OperName;
					form.address = val.Address;
				}
				return (
					<el-autocomplete
						style="width: 100%"
						v-model={form.corpName}
						fetchSuggestions={fetchCompanyList}
						placeholder="请输入客户名称"
						onSelect={handleSelectCompany}
					/>
				);
			},
		},
		{
			label: '统一社会信用代码',
			prop: 'creditCode',
			type: 'input',
			rules: [
				{
					required: true,
					message: '统一社会信用代码不能为空',
				},
				{ max: 18, message: '统一社会信用代码不能超过18个字符' },
			],
		},
		{
			label: '营业执照',
			prop: 'businessLicenseUrl',
			rules: [
				{
					required: true,
					message: '请上传',
				},
			],
			render: (formData) => {
				formData.businessLicenseUrl = formData.businessLicenseUrl || '';
				return (
					<div>
						{formData.businessLicenseUrl.endsWith('.pdf') ? (
							<div
								class="pdf-box"
								style="position: relative;border-radius: 8px; width: 150px;height: 150px;display: flex;align-items: center;justify-content: center;border: 1px dashed var(--el-border-color-darker); margin-bottom: 4px;"
							>
								<p style="font-size: 24px;">pdf</p>
								<div
									class="icon-box"
									style="position: absolute;top: 50%;left: 50%;width: 100%;transform: translate(-50%, -50%);justify-content: center;gap: 12px;"
								>
									<el-icon
										size={18}
										style="color: #fff;background:transparent; cursor: pointer;"
										onClick={() => {
											window.open(formData.businessLicenseUrl, 'target');
										}}
									>
										<Download />
									</el-icon>
									<el-icon
										size={18}
										style="color: #fff;background:transparent; cursor: pointer;"
										onClick={() => {
											formData.businessLicenseUrl = '';
										}}
									>
										<Delete />
									</el-icon>
								</div>
							</div>
						) : (
							<UploadImg
								v-model:imageUrl={formData.businessLicenseUrl}
								isFullUrl
								fileSize={10}
								fileType={['image/jpeg', 'image/jpeg', 'image/png', 'image/gif', 'image/bmp', 'application/pdf']}
							/>
						)}
						<div class="text-xs">支持jpg/jpeg/png/gif/bmp/pdf格式，10MB以内</div>
					</div>
				);
			},
		},
		{
			label: '企业法人',
			prop: 'businessLegal',
			type: 'input',
			rules: [
				{ required: true, message: '请输入', trigger: 'blur' },
				{ max: 20, message: '不能超过20个字符' },
			],
		},
		{
			label: '公司地址',
			prop: 'address',
			type: 'input',
			rules: [
				{ required: true, message: '请输入', trigger: 'blur' },
				{ max: 50, message: '不能超过50个字符' },
			],
		},
		{
			label: '企业联系人',
			prop: 'contacts',
			type: 'input',
			rules: [
				{ required: true, message: '请输入企业联系人', trigger: 'blur' },
				{ max: 20, message: '不能超过20个字符' },
			],
		},
		{
			label: '联系电话',
			prop: 'telPhone',
			type: 'input',
			colProps: { span: 24 },
			rules: [
				{ required: true, message: '请输入联系电话', trigger: 'blur' },
				{ pattern: /^\d{10,12}$/, message: '联系电话必须是10至12位数字', trigger: 'change' },
			],
		},
		{
			label: '联系邮箱',
			prop: 'email',
			type: 'input',
			rules: [
				{ required: true, message: '邮箱不能为空', trigger: 'blur' },
				{ max: 30, message: '不能超过30个字符' },
			],
		},
	]);
	const form = ref({});
	async function getInfo(id) {
		try {
			loading.value = true;
			const resp = await enterpriseInfoGet(id);
			form.value = resp?.data || {};
		} finally {
			loading.value = false;
		}
	}
	function show(row) {
		visible.value = true;
		isEdit.value = false;
		form.value = {};
		if (row?.id) {
			isEdit.value = true;
			getInfo(row?.id);
		}
	}
	function onConfirm() {
		if (isEdit.value) {
			return enterpriseInfoUpdate(form.value);
		} else {
			return enterpriseInfoSave(form.value);
		}
	}
	return {
		visible,
		isEdit,
		loading,
		formProps,
		form,
		columns,
		onConfirm,
		show,
	};
};
