<script setup>
import useUpsert from './upsert.jsx';

const emits = defineEmits('upsert');
const { visible, isEdit, loading, formProps, form, columns, onConfirm, show } = useUpsert();
const formRef = ref(null);
async function handleConfirt(done, loading) {
	await formRef.value?.elForm?.validate();
	try {
		loading.value = true;
		await onConfirm(form.value);
		done();
		emits('upsert');
	} finally {
		loading.value = false;
	}
}
defineExpose({
	show,
});
</script>
<template>
	<yun-drawer
		v-model="visible"
		size="medium"
		:title="isEdit ? '编辑' : '入驻'"
		destroy-on-close
		z-index="1000"
		custom-class="tenant-drawer"
		@confirm="handleConfirt"
		:confirm-button-text="'确定'"
		:cancel-button-text="'取消'"
	>
		<yun-pro-form v-loading="loading" ref="formRef" :form-props="formProps" :config="{ colProps: { span: 24 } }" :form="form" :columns="columns" />
	</yun-drawer>
</template>
<style lang="scss" scoped></style>
<style lang="scss">
.pdf-box .icon-box {
	display: none;
	overflow: hidden;
}
.pdf-box:hover {
	background-color: rgba(#000000, 0.6);
	.icon-box {
		display: flex;
	}
}
</style>
