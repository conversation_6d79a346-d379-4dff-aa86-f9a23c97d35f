<script setup lang="jsx" name="opExportSettingPage">
import useConfig from './config.jsx';
import Log from './components/log.vue';

const { searchFields, tableColumns, proTableRef, page, initTable, remoteMethod } = useConfig();
const logRef = ref(null);
function handleLog(row) {
	logRef.value.show(row);
}
</script>
<template>
	<div class="pro-table-bg">
		<yun-pro-table
			ref="proTableRef"
			v-model:pagination="page"
			:search-fields="searchFields"
			:table-columns="tableColumns"
			:remote-method="remoteMethod"
			:layout="'whole'"
			auto-height
			:table-props="{
				yunPagination: false,
				showTableSetting: true,
			}"
			@reset="initTable"
		>
			<template #t_action="{ row }">
				<yun-rest limit="3">
					<el-button type="action" @click="handleLog(row)"> 日志 </el-button>
				</yun-rest>
			</template>
		</yun-pro-table>
		<Log ref="logRef" />
	</div>
</template>
<style lang="scss" scoped>
.dark {
	.pro-table-bg {
		background: var(--el-color-dark-bg);
		padding: 16px;
	}
}
.pro-table-bg {
	box-sizing: border-box;
	height: calc(100vh - 116px);
}
</style>
