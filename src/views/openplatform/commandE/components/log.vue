<script setup lang="jsx">
import { ref } from 'vue';
import { opExportSettingLog } from '@/api/openplatform/apply';
import { STATE_ENUM } from '../constant';

const searchFields = computed(() => [
	{
		label: '创建时间',
		prop: 'createTime',
		component: 'el-date-picker',
		filterItemAttrs: {
			'label-width': 'auto',
		},
		componentAttrs: {
			startPlaceholder: '开始日期',
			endPlaceholder: '结束日期',
			type: 'daterange',
			format: 'YYYY-MM-DD',
			valueFormat: 'YYYY-MM-DD HH:mm:ss',
			defaultTime: [new Date(2000, 1, 1, 0, 0, 0), new Date(2000, 2, 1, 23, 59, 59)],
		},
	},
]);

const tableColumns = [
	{
		label: '请求发送方Id',
		prop: 'requestId',
	},
	{
		label: '跟踪Id',
		prop: 'traceId',
	},
	{
		label: '调用方式',
		prop: 'invokeIdentity',
	},
	{
		label: '状态',
		prop: 'state',
		width: 80,
		formatter: ({ state }) => STATE_ENUM[state],
	},
	{
		label: '返回状态码',
		prop: 'httpStatus',
		width: 110,
	},
	{
		label: '创建时间',
		prop: 'createTime',
	},
	{
		label: '备注',
		prop: 'remark',
	},
];

const visible = ref(false);
const form = ref({});
const pagination = ref({
	page: 1,
	total: 0,
	size: 10,
});

const tableData = ref([]);

async function remoteMethod() {
	const { size, page } = pagination.value;
	const { createTime, ...base } = form.value;
	const [createTimeStart, createTimeEnd] = createTime || [];
	const { data } = await opExportSettingLog({ current: page, size, page }, { ...base, createTimeStart, createTimeEnd });
	const { records, total } = data;
	pagination.value.total = total;
	tableData.value = records;
}
function handleSearch() {
	pagination.value.page = 1;
	remoteMethod();
}
function handleReset() {
	form.value.createTime = [];
	handleSearch();
}
function show(row) {
	visible.value = true;
	form.value = {
		appId: row.appId,
		command: row.command,
	};
	remoteMethod();
}
defineExpose({
	show,
});
</script>
<template>
	<yun-drawer
		v-model="visible"
		size="large"
		title="日志"
		destroy-on-close
		custom-class="command-log_drawer"
		:show-confirm-button="false"
		:cancel-button-text="'返回'"
	>
		<div class="info">
			<div>
				<p>指令：</p>
				{{ form.command }}
			</div>
			<div>
				<p>APP ID：</p>
				{{ form.appId }}
			</div>
		</div>
		<yun-filter v-model="form" :fields="searchFields" :customContainerWidth="1200" @submit="handleSearch" @reset="handleReset" />
		<el-table :data="tableData" :border="false" style="width: 100%">
			<el-table-column type="expand">
				<template #default="props">
					<pre>{{ JSON.stringify(props.row, null, 4) }}</pre>
				</template>
			</el-table-column>
			<el-table-column v-for="(item, index) in tableColumns" :key="index" :label="item.label" show-overflow-tooltip :prop="item.prop">
				<template #default="scope">
					<div v-if="item.formatter" class="cell-formatter">
						{{ item.formatter(scope.row) }}
					</div>
					<div v-else>{{ scope.row[item.prop] }}</div>
				</template>
			</el-table-column>
		</el-table>
		<yun-pagination
			class="pagination"
			v-model="pagination.size"
			v-model:total="pagination.total"
			v-model:currentPage="pagination.page"
			@size-change="
				(val) => {
					pagination.size = val;
					remoteMethod();
				}
			"
			@current-change="remoteMethod"
		/>
	</yun-drawer>
</template>
<style lang="scss" scoped>
.info {
	margin-left: 24px;
	display: flex;
	gap: 24px;
	div {
		color: var(--el-text-color-primary);
	}
	p {
		color: var(--el-text-color-secondary);
	}
}
.pagination {
	margin-top: 24px;
}
</style>
<style lang="scss"></style>
