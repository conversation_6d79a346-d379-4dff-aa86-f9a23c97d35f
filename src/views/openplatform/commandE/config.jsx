import { ref } from 'vue';
import { opExportSettingPage } from '@/api/openplatform/apply';
import useInitTable from '@/hooks/useInitTable';
import { STATUS_ENUM } from './constant';

export default () => {
	const searchFields = [
		{
			label: '指令',
			prop: 'command',
			component: 'el-input',
		},
		// {
		// 	label: '消息匹配key',
		// 	prop: 'eventKey',
		// 	component: 'el-input',
		// },
		{
			label: '调用方式',
			prop: 'invokeIdentity',
			component: 'el-input',
		},
	];
	const tableColumns = [
		{
			label: '指令',
			prop: 'command',
			minWidth: 140,
		},
		{
			label: 'APP ID',
			prop: 'appId',
			minWidth: 140,
		},
		// {
		// 	label: '消息匹配key',
		// 	prop: 'eventKey',
		// },
		{
			label: '调用方式',
			prop: 'invokeIdentity',
		},
		{
			label: '企业',
			prop: 'enterpriseName',
		},
		{
			label: '创建时间',
			prop: 'createTime',
		},
		{
			label: '状态',
			prop: 'status',
			width: 90,
			formatter: ({ status }) => STATUS_ENUM[status],
		},
		{
			label: '操作',
			prop: 'action',
			width: 100,
			fixed: 'right',
		},
	];

	const proTableRef = ref(null);
	const page = ref({
		page: 1,
		total: 0,
		size: 10,
	});
	const { initTable } = useInitTable({
		page,
		tableRef: proTableRef,
	});
	async function remoteMethod({ searchData, pagination }) {
		const { ...restParasm } = searchData;
		const data = {
			...restParasm,
		};
		const { size, page } = pagination;
		const res = await opExportSettingPage({ ...data }, { current: page, size, page });
		return res?.data;
	}

	return {
		searchFields,
		tableColumns,
		proTableRef,
		page,
		initTable,
		remoteMethod,
	};
};
