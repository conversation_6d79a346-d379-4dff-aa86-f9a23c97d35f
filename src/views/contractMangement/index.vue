<template>
  <div class="contract-container">
    <yun-pro-table
      ref="proTableRef"
      v-model:pagination="pagination"
      v-model:searchData="searchData"
      :search-fields="searchFields"
      :layout="'whole'"
      :auto-height="true"
      :table-columns="tableColumns"
      :remote-method="remoteMethod"
      :table-props="tableProps"
      :default-fetch="true"
    >
      <template #tableHeaderLeft>
        <el-button
          v-if="isPurchaser"
          type="primary"
          @click="handleCreate"
        >
          <el-icon><Plus /></el-icon>
          创建合同
        </el-button>
      </template>
      <template #t_contractStatus="{ row }">
        <el-tag
          :type="getContractStatusType(row.contractStatus)"
          size="small"
        >
          {{ getContractStatusText(row.contractStatus) }}
        </el-tag>
      </template>
      <template #t_approvalStatus="{ row }">
        <el-tag
          :type="getApprovalStatusType(row.approvalStatus)"
          size="small"
        >
          {{ getApprovalStatusText(row.approvalStatus) }}
        </el-tag>
      </template>
      <template #t_action="{ row }">
        <yun-rest limit="3">
          <el-button
            type="text"
            @click="handleView(row)"
          >
            查看
          </el-button>
          <el-button
            v-if="isPurchaser && canEditContract(row)"
            type="text"
            @click="handleEdit(row)"
          >
            编辑
          </el-button>

          <el-button
            v-if="isPurchaser && canCancelContract(row)"
            type="text"
            @click="handleCancelContract(row, reLoad)"
          >
            作废
          </el-button>

          <el-button
            v-if="isPurchaser && canChangeContract(row)"
            type="text"
            @click="handleChange(row)"
          >
            变更
          </el-button>

          <el-button
            v-if="isPurchaser && canRevokeContract(row)"
            type="text"
            @click="handleRevoke(row, reLoad)"
          >
            撤回
          </el-button>
        </yun-rest>
      </template>
    </yun-pro-table>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue';
import { Plus } from '@element-plus/icons-vue';
import { useUserRole } from '@/views/procurementSourcing/biddingProcess/utils';
import { useProTable } from '@ylz-use/core';
import { useContractOptions, useContractHandler } from './hooks';
import { getContractList } from '@/api/contract';
import { getOwnSupplier } from '@/api/purchasing/bid';

// 用户角色判断
const { isPurchaser, isSupplier } = useUserRole();

// 供应商信息
const ownSupplier = ref<any>(null);

// 使用自定义hooks
const { searchFields, tableColumns, tablePropsObj } = useContractOptions(isPurchaser.value);
const {
  getContractStatusType,
  getContractStatusText,
  getApprovalStatusType,
  getApprovalStatusText,
  handleCreate,
  handleView,
  handleEdit,
  handleRevoke,
  canEditContract,
  canRevokeContract,
  canCancelContract,
  handleCancelContract,
  canChangeContract,
  handleChange
} = useContractHandler(isPurchaser.value);

// 搜索数据
const searchData = ref({});

// 自定义远程方法，支持供应商ID过滤
const customRemoteMethod = async (params: any) => {
  try {
    // 如果是供应商，添加supplierIds参数
    const requestParams = { ...params, isPurchase: isPurchaser.value };

    if (isSupplier.value && ownSupplier.value?.id) {
      requestParams.supplierIds = [ownSupplier.value.id];
    } else if (isSupplier.value && !ownSupplier.value?.id) {
      return { data: { records: [], total: 0 } };
    }

    const response = await getContractList(requestParams);
    return response;
  } catch (error) {
    return { data: { records: [], total: 0 } };
  }
};

const { pagination, remoteMethod, tableProps, proTableRef, filterTableData, reLoad } = useProTable({
  apiFn: customRemoteMethod,
  defaultParams: {
    current: 1,
    size: 20,
  },
  tableProps: tablePropsObj,
  responseHandler(result: any) {
    // 返回data字段，这是yun-pro-table期望的格式
    return result.data;
  },
  plugins: {
    config: {
      columns: tableColumns.value,
      searchFields: searchFields.value,
    },
    list: ['SEARCH_PLUS'],
  },
});

// 获取供应商信息
const getOwnSupplierData = async () => {
  if (!isSupplier.value) return;

  try {
    const response = await getOwnSupplier();
    if (response.code === 0) {
      ownSupplier.value = response.data;
      reLoad();
    } else {
      ownSupplier.value = null;
    }
  } catch (error) {
    ownSupplier.value = null;
  }
};

// 组件挂载时初始化
onMounted(async () => {
  // 如果是供应商，先获取供应商信息
  if (isSupplier.value) {
    await getOwnSupplierData();
  }
});

// 组件卸载时清理
onUnmounted(() => {
  ownSupplier.value = null;
});
</script>

<style lang="scss" scoped>
.contract-container {
  width: 100%;
  height: calc(100vh - 88px);
  display: flex;
  flex-direction: column;

  :deep(.el-form .el-form-item:last-of-type) {
    margin-bottom: 24px !important;
  }

  :deep(.yun-pro-table) {
    flex: 1;
    width: 100%;
  }

  :deep(.el-table) {
    width: 100% !important;
  }
}
</style>
