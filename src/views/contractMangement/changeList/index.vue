<template>
  <div class="change-list-container">
    <yun-pro-table
      ref="proTableRef"
      v-model:pagination="pagination"
      v-model:searchData="searchData"
      :search-fields="searchFields"
      :layout="'whole'"
      :auto-height="true"
      :table-columns="tableColumns"
      :remote-method="remoteMethod"
      :table-props="tableProps"
    >
      <template #tableHeaderLeft>
        <el-button
          type="primary"
          @click="handleAddChange"
        >
          <el-icon><Plus /></el-icon>
          新建变更
        </el-button>
      </template>
      <template #t_approvalStatus="{ row }">
        <el-tag
          :type="getApprovalStatusType(row.approvalStatus)"
          size="small"
        >
          {{ getApprovalStatusText(row.approvalStatus) }}
        </el-tag>
      </template>
      <template #t_action="{ row }">
        <yun-rest limit="3">
          <el-button
            type="text"
            size="small"
            @click="handleView(row)"
          >
            查看变更合同详情
          </el-button>
          <el-button
            type="text"
            size="small"
            @click="handleViewBeforeContract(row)"
          >
            查看变更前合同详情
          </el-button>
          <el-button
            v-if="canEditChange(row)"
            type="text"
            size="small"
            @click="handleEdit(row)"
          >
            编辑
          </el-button>
          <el-button
            v-if="canRevokeChange(row)"
            type="text"
            size="small"
            @click="handleRevoke(row)"
          >
            撤回
          </el-button>
        </yun-rest>
      </template>
    </yun-pro-table>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { Plus } from '@element-plus/icons-vue';
import { useContractStore } from '@/stores/contract';
import { useProTable } from '@ylz-use/core';
import { ElMessage, ElMessageBox } from 'element-plus';
import { getContractChangeList } from '@/api/contract';
import { ApprovalStatus } from '@/types/contract';

// 路由相关
const route = useRoute();
const router = useRouter();
const contractStore = useContractStore();

// 获取合同ID和项目名称
const contractId = computed(() => route.query.id as string);
const projectName = computed(() => route.query.projectName as string);

// 搜索字段配置
const searchFields = computed(() => [
  {
    label: '变更人',
    prop: 'changeBy',
    component: 'el-input',
    componentAttrs: {
      placeholder: '请选择',
      clearable: true,
    },
  },
  {
    label: '变更时间',
    prop: 'changeTime',
    component: 'el-date-picker',
    componentAttrs: {
      type: 'date',
      placeholder: '请选择',
      clearable: true,
      format: 'YYYY-MM-DD',
      valueFormat: 'YYYY-MM-DD',
      style: 'width: 100%',
    },
  },
  {
    label: '审核状态',
    prop: 'approvalStatus',
    component: 'el-select',
    componentAttrs: {
      placeholder: '请选择',
      clearable: true,
      style: 'width: 100%',
    },
    options: [
      { label: '待审批', value: ApprovalStatus.TO_APPROVE },
      { label: '审批中', value: ApprovalStatus.APPROVING },
      { label: '审批通过', value: ApprovalStatus.APPROVE },
      { label: '审批驳回', value: ApprovalStatus.APPROVE_REJECT },
      { label: '审批撤回', value: ApprovalStatus.APPROVE_REVOKE },
    ],
  },
]);

// 表格列配置
const tableColumns = computed(() => [
  {
    label: '序号',
    type: 'index',
  },
  {
    label: '项目名称',
    prop: 'projectName',
  },
  {
    label: '变更人',
    prop: 'changeBy',
  },
  {
    label: '变更完成时间',
    prop: 'changeCompleteTime',
  },
  {
    label: '审批状态',
    prop: 'approvalStatus',
  },
  {
    label: '操作',
    prop: 'action',
    fixed: 'right',
  },
]);

// 表格属性配置
const tableProps = computed(() => ({
  stripe: true,
  border: true,
  'show-summary': false,
}));

// 搜索数据
const searchData = ref({});

// 模拟数据
const mockChangeList = [
  {
    id: '1',
    contractId: 'CONTRACT001',
    projectName: '2024年度办公设备采购项目',
    changeBy: '张三',
    changeCompleteTime: '2024-01-15 14:30:00',
    changeApprovalStatus: '审批通过',
    approvalStatus: ApprovalStatus.APPROVE,
    changeType: 'AMOUNT',
    changeReason: '供应商报价调整',
    changeContent: '合同金额从50万调整为52万',
    changeDescription: '由于原材料价格上涨，供应商申请调整合同金额',
    effectiveDate: '2024-01-20',
    priority: 'HIGH',
    createTime: '2024-01-10 09:00:00',
  },
  {
    id: '2',
    contractId: 'CONTRACT002',
    projectName: '数据中心建设服务合同',
    changeBy: '李四',
    changeCompleteTime: '2024-01-12 16:45:00',
    changeApprovalStatus: '审批驳回',
    approvalStatus: ApprovalStatus.APPROVE_REJECT,
    changeType: 'DELIVERY_DATE',
    changeReason: '交付日期延期',
    changeContent: '交付日期从2024-03-01延期到2024-04-01',
    changeDescription: '由于设备到货延迟，申请延期交付',
    effectiveDate: '2024-02-01',
    priority: 'URGENT',
    createTime: '2024-01-08 11:20:00',
  },
  {
    id: '3',
    contractId: 'CONTRACT003',
    projectName: '软件开发服务合同',
    changeBy: '王五',
    changeCompleteTime: null,
    changeApprovalStatus: '审批中',
    approvalStatus: ApprovalStatus.APPROVING,
    changeType: 'SCOPE',
    changeReason: '功能范围调整',
    changeContent: '增加用户管理模块开发',
    changeDescription: '客户需求变更，需要增加用户管理功能',
    effectiveDate: '2024-01-25',
    priority: 'MEDIUM',
    createTime: '2024-01-14 15:30:00',
  },
  {
    id: '4',
    contractId: 'CONTRACT004',
    projectName: '物流运输服务合同',
    changeBy: '赵六',
    changeCompleteTime: null,
    changeApprovalStatus: '待审批',
    approvalStatus: ApprovalStatus.TO_APPROVE,
    changeType: 'PAYMENT_TERMS',
    changeReason: '付款条件调整',
    changeContent: '付款方式从月结30天改为月结60天',
    changeDescription: '由于资金周转需要，申请延长付款周期',
    effectiveDate: '2024-02-01',
    priority: 'LOW',
    createTime: '2024-01-16 10:15:00',
  },
  {
    id: '5',
    contractId: 'CONTRACT005',
    projectName: '设备维护服务合同',
    changeBy: '孙七',
    changeCompleteTime: '2024-01-18 13:20:00',
    changeApprovalStatus: '审批撤回',
    approvalStatus: ApprovalStatus.APPROVE_REVOKE,
    changeType: 'SPECIFICATION',
    changeReason: '设备规格变更',
    changeContent: '维护设备型号从A型改为B型',
    changeDescription: '原设备型号停产，更换为同等级别的新型号',
    effectiveDate: '2024-01-25',
    priority: 'HIGH',
    createTime: '2024-01-12 14:45:00',
  },
];

// 远程方法
const customRemoteMethod = async (params: any) => {
  try {
    // 模拟API延迟
    await new Promise((resolve) => setTimeout(resolve, 500));

    // 模拟搜索过滤
    let filteredData = [...mockChangeList];

    if (params.projectName) {
      filteredData = filteredData.filter((item) => item.projectName.includes(params.projectName));
    }

    if (params.changeBy) {
      filteredData = filteredData.filter((item) => item.changeBy.includes(params.changeBy));
    }

    if (params.changeTime) {
      filteredData = filteredData.filter((item) => item.createTime.startsWith(params.changeTime));
    }

    if (params.approvalStatus) {
      filteredData = filteredData.filter((item) => item.approvalStatus === params.approvalStatus);
    }

    // 模拟分页
    const { current = 1, size = 10 } = params;
    const start = (current - 1) * size;
    const end = start + size;
    const records = filteredData.slice(start, end);

    return {
      code: 0,
      data: {
        records,
        total: filteredData.length,
        current,
        size,
      },
      msg: 'success',
    };
  } catch (error) {
    console.error('获取变更列表失败:', error);
    return { data: { records: [], total: 0 } };
  }
};

// 使用yun-pro-table
const { pagination, remoteMethod, proTableRef, reLoad } = useProTable({
  apiFn: customRemoteMethod,
  defaultParams: {
    current: 1,
    size: 20,
  },
  responseHandler(result: any) {
    return result.data;
  },
  plugins: {
    config: {
      columns: tableColumns.value,
      searchFields: searchFields.value,
    },
    list: ['SEARCH_PLUS'],
  },
});

// 审批状态相关方法
const getApprovalStatusType = (status: ApprovalStatus) => {
  const statusMap = {
    [ApprovalStatus.TO_APPROVE]: 'info',
    [ApprovalStatus.APPROVING]: 'warning',
    [ApprovalStatus.APPROVE]: 'success',
    [ApprovalStatus.APPROVE_REJECT]: 'danger',
    [ApprovalStatus.APPROVE_REVOKE]: 'info',
  };
  return statusMap[status] || 'info';
};

const getApprovalStatusText = (status: ApprovalStatus) => {
  const statusMap = {
    [ApprovalStatus.TO_APPROVE]: '待审批',
    [ApprovalStatus.APPROVING]: '审批中',
    [ApprovalStatus.APPROVE]: '审批通过',
    [ApprovalStatus.APPROVE_REJECT]: '审批驳回',
    [ApprovalStatus.APPROVE_REVOKE]: '审批撤回',
  };
  return statusMap[status] || '-';
};

// 权限判断方法
const canEditChange = (row: any): boolean => {
  // 审批驳回、审批撤回可以编辑
  return [ApprovalStatus.APPROVE_REJECT, ApprovalStatus.APPROVE_REVOKE].includes(row.approvalStatus);
};

const canRevokeChange = (row: any): boolean => {
  // 审批中可以有撤回
  return row.approvalStatus === ApprovalStatus.APPROVING;
};

// 事件处理方法
const handleAddChange = () => {
  contractStore.setShouldResetForm(true);
  router.push({
    path: '/contractMangement/changeForm',
    query: {
      mode: 'create',
      id: contractId.value,
      projectName: projectName.value,
      type: 'change',
    },
  });
};

const handleView = (row: any) => {
  // 查看变更合同详情
  router.push({
    path: '/contractMangement/changeView',
    query: {
      mode: 'view',
      id: contractId.value,
      projectName: projectName.value,
      type: 'change',
    },
  });
};

const handleViewBeforeContract = (row: any) => {
  router.push({
    path: '/contractMangement/view',
    query: {
      mode: 'view',
      id: contractId.value,
      projectName: projectName.value,
    },
  });
};

const handleEdit = (row: any) => {
  // 编辑变更
  router.push({
    path: '/contractMangement/changeEdit',
    query: {
      mode: 'edit',
      id: contractId.value,
      projectName: projectName.value,
      type: 'change',
    },
  });
};

const handleRevoke = async (row: any) => {
  try {
    await ElMessageBox.confirm('确定要撤回这条变更记录吗？', '确认撤回', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    });

    // 这里需要调用撤回API
    // await revokeContractChange(row.id);
    ElMessage.success('撤回成功');
    reLoad();
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('撤回失败');
    }
  }
};



// 监听合同ID变化，重新加载数据
watch(contractId, (newContractId, oldContractId) => {
  if (newContractId && newContractId !== oldContractId) {
    reLoad();
  }
});

// 组件挂载时初始化
onMounted(() => {
  // 设置页面标题
  document.title = `变更列表 - ${projectName.value || '合同管理'}`;
});
</script>

<style scoped lang="scss">
.change-list-container {
  width: 100%;
  height: calc(100vh - 88px);
  display: flex;
  flex-direction: column;
  .text-red-500 {
    color: #ef4444;
  }

  .flex {
    display: flex;
  }

  .gap-2 {
    gap: 0.5rem;
  }
}
</style>
