<template>
  <yun-drawer
    v-model="isVisible"
    title="选择中标物料"
    size="56%"
    @close="handleClose"
  >
    <template #default>
      <div class="material-selector">
        <yun-pro-table
          ref="proTableRef"
          v-model:tableData="tableData"
          v-model:selected="selectedMaterials"
          v-model:searchData="searchData"
          v-model:pagination="pagination"
          :search-fields="searchFields"
          :table-columns="mergedColumns"
          :loading="loading"
          :auto-height="true"
          :layout="'whole'"
          :remote-method="handleRemoteMethod"
          :default-fetch="true"
        />
      </div>
    </template>

    <template #footer>
      <div class="drawer-footer">
        <el-button @click="handleClose">取 消</el-button>
        <el-button
          type="primary"
          @click="handleConfirm"
          :disabled="selectedMaterials.length === 0"
        >
          确 认
        </el-button>
      </div>
    </template>
  </yun-drawer>
</template>

<script setup lang="ts">
import { ref, watch, computed } from 'vue';
import type { ContractItem } from '@/types/contract';

interface Props {
  visible?: boolean;
  availableMaterials: ContractItem[];
  dynamicColumns?: any[];
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  dynamicColumns: () => [],
});

const emit = defineEmits<{
  (e: 'update:visible', value: boolean): void;
  (e: 'confirm', materials: ContractItem[]): void;
}>();

// 表格数据
const tableData = ref<ContractItem[]>([]);
const selectedMaterials = ref<ContractItem[]>([]);
const loading = ref(false);
const searchData = ref({});
const proTableRef = ref();

// 分页配置
const pagination = ref({
  current: 1,
  size: 10,
  total: 0,
});

// 搜索字段配置
const searchFields = ref([
  {
    label: '物料名称',
    prop: 'materialName',
    component: 'el-input',
    componentProps: {
      placeholder: '请输入物料名称',
      clearable: true,
    },
  },
  {
    label: '需求行号',
    prop: 'demandLineNo',
    component: 'el-input',
    componentProps: {
      placeholder: '请输入需求行号',
      clearable: true,
    },
  },
]);

// 合并表格列配置
const mergedColumns = computed(() => {
  const baseColumns = [
    {
      type: 'selection',
      width: 55,
      fixed: 'left',
    },
  ];

  // 如果有动态列，使用动态列
  if (props.dynamicColumns && props.dynamicColumns.length > 0) {
    return [
      ...baseColumns,
      ...props.dynamicColumns.filter((item: any) => !item.children),
      {
        label: '中标金额',
        prop: 'unitPrice',
        minWidth: 100,
        formatter: (row: ContractItem) => {
          return (row.paymentList?.find((item: any) => item.value === row.procurementProjectPaymentId)?.price ?? 0) * row.awardedQuantity;
        },
      },
    ];
  }
});

// 可见性控制
const isVisible = ref(props.visible);

// 处理远程搜索
const handleRemoteMethod = (params: any) => {
  loading.value = true;

  return new Promise((resolve) => {
    const { materialName, demandLineNo } = params;
    const { pagination } = params;
    const { current, size } = pagination;

    const filteredData = props.availableMaterials.filter((item) => {
      const matchMaterialName = !materialName || (item.materialName && item.materialName.toLowerCase().includes(materialName.toLowerCase()));
      const matchLineNo = !demandLineNo || (item.demandLineNo && item.demandLineNo.includes(demandLineNo));

      return matchMaterialName && matchLineNo;
    });

    // 计算分页数据
    const total = filteredData.length;
    const pages = Math.ceil(total / size);
    const start = (current - 1) * size;
    const end = start + size;
    const records = filteredData.slice(start, end);

    setTimeout(() => {
      loading.value = false;
      resolve({
        records,
        total,
        size,
        pages,
        current,
      });
    }, 100);
  });
};

// 监听外部visible变化
watch(
  () => props.visible,
  (newVal) => {
    isVisible.value = newVal;
    if (newVal && proTableRef.value) {
      // 当抽屉打开时，重新加载数据
      proTableRef.value?.getData();
    }
  }
);

// 监听内部isVisible变化
watch(
  () => isVisible.value,
  (newVal) => {
    emit('update:visible', newVal);
    if (!newVal) {
      selectedMaterials.value = [];
      searchData.value = {};
      // 重置分页
      pagination.value = {
        current: 1,
        size: 10,
        total: 0,
      };
    }
  }
);

// 监听可用物料变化
watch(
  () => props.availableMaterials,
  (newVal) => {
    if (newVal && newVal.length > 0 && proTableRef.value) {
      proTableRef.value.getData();
    }
  },
  { immediate: true }
);

// 处理确认
const handleConfirm = () => {
  emit('confirm', selectedMaterials.value);
  isVisible.value = false;
};

// 处理关闭
const handleClose = () => {
  isVisible.value = false;
};
</script>

<style lang="scss" scoped>
.material-selector {
  height: calc(100vh - 180px);
  overflow-y: auto;
}

.drawer-footer {
  text-align: right;

  .el-button + .el-button {
    margin-left: 12px;
  }
}
</style>
