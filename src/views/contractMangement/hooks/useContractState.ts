/**
 * 合同状态管理Hook
 */
import { reactive } from 'vue';
import type { ContractQueryParams } from '@/types/contract';

export function useContractState() {
  // 状态管理
  const state = reactive({
    page: {
      total: 0,
      page: 1,
      size: 20,
    },
    searchData: {
      contractName: '',
      supplierName: '',
      purchaserDeptName: '',
      signDate: '',
      projectName: '',
    } as ContractQueryParams,
    loading: false,
  });

  // 重置搜索条件
  const resetSearchData = () => {
    state.searchData = {
      contractName: '',
      supplierName: '',
      purchaserDeptName: '',
      signDate: '',
      projectName: '',
    };
  };

  // 重置分页
  const resetPagination = () => {
    state.page.page = 1;
  };

  // 设置加载状态
  const setLoading = (loading: boolean) => {
    state.loading = loading;
  };

  return {
    state,
    resetSearchData,
    resetPagination,
    setLoading,
  };
} 