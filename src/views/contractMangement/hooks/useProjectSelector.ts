import { ref, computed, type Ref } from 'vue';
import { ElMessage } from 'yun-design';
import { inquiryListApi, queryProjectUnSingList } from '@/api/purchasing/proposal';

// 项目信息接口
export interface ProjectInfo {
    id: string;
    projectCode: string;
    projectName: string;
    projectType: string;
    status: string;
    purchaseDeptId?: string; // 采购部门ID
    projectMemberList?: any[]; // 项目成员列表
    createTime?: string;
    updateTime?: string;
}

// 项目查询参数
export interface ProjectQueryParams {
    projectName?: string;
    projectCode?: string;
    status?: string;
    current?: number;
    size?: number;
}

// 项目选择hook返回值
export interface UseProjectSelectorReturn {
    // 项目列表数据
    projectOptions: Ref<ProjectInfo[]>;
    // 选中的项目
    selectedProject: Ref<ProjectInfo | null>;
    // 加载状态
    loading: Ref<boolean>;
    // 获取项目列表
    getProjectList: (params?: ProjectQueryParams) => Promise<void>;
    // 搜索项目
    searchProjects: (query: string) => Promise<void>;
    // 设置选中项目
    setSelectedProject: (project: ProjectInfo | null) => void;
    // 清空选中项目
    clearSelectedProject: () => void;
    // 处理项目选择变化
    handleProjectChange: (projectId: string) => void;
    // 处理项目搜索
    handleProjectSearch: (query: string) => Promise<void>;
}

export function useProjectSelector(mode: string): UseProjectSelectorReturn {
    // 项目列表数据
    const projectOptions = ref<ProjectInfo[]>([]);

    // 选中的项目
    const selectedProject = ref<ProjectInfo | null>(null);

    // 加载状态
    const loading = ref(false);

    // 获取项目列表
    const getProjectList = async (params?: ProjectQueryParams) => {
        loading.value = true;
        try {
            const server = mode === 'create' ? queryProjectUnSingList : inquiryListApi;
            const response = await server({
                current: 1,
                size: 20,
                needNodeStatus: true,
                progressStatus: ['COMPLETED'],
                status: ['EFFECT'],
                searchContent: params?.projectName,
            });

            if (response.code === 0) {
                projectOptions.value = response.data.records || [];
            }
        } catch (error) {
            console.error('获取项目列表失败:', error);
        } finally {
            loading.value = false;
        }
    };

    // 搜索项目
    const searchProjects = async (query: string) => {
        if (!query) {
            await getProjectList();
            return;
        }

        await getProjectList({
            projectName: query,
        });
    };

    // 设置选中项目
    const setSelectedProject = (project: ProjectInfo | null) => {
        selectedProject.value = project;
    };

    // 清空选中项目
    const clearSelectedProject = () => {
        selectedProject.value = null;
    };

    // 处理项目选择变化
    const handleProjectChange = (projectId: string) => {
        const project = projectOptions.value.find(item => item.id === projectId);
        setSelectedProject(project || null);
    };

    // 处理项目搜索
    const handleProjectSearch = async (query: string) => {
        await searchProjects(query);
    };

    return {
        projectOptions,
        selectedProject,
        loading,
        getProjectList,
        searchProjects,
        setSelectedProject,
        clearSelectedProject,
        handleProjectChange,
        handleProjectSearch,
    };
} 