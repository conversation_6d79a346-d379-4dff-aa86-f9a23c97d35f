import { useRouter } from 'vue-router';
import { ElMessage, ElMessageBox } from 'yun-design';
import { cancelApproveProposal, finishInquiryApi } from '@/api/purchasing/proposal.ts';

export default () => {
  const router = useRouter();
  // 跳转到采购计划详情
  const toPlanDetail = (planCode) => {
    router.push(`/purchasing/plan/detail/index?planCode=${planCode}&pageType=view`);
  };

  const sourcingTypeMap = {
    'XJCG': '询价采购',
    'JZTP': '竞谈采购',
    'ZB': '招标采购',
    'ZJWT': '采购项目',
  };
  // 跳转到发标
  const toBidding = (row, step) => {
    const params = {
      path: '/procurementSourcing/biddingProcess/index',
      query: {
        projectCode: row?.projectCode,
        // isInviteMode: row?.inviteMethod === 'INVITE' ? '1' : null,
        step,
        pageType: 'biddingView',
        projectId: row?.id,
        tagsViewName: sourcingTypeMap[row.sourcingType],
      }
    }
    if(row?.inviteMethod === 'INVITE'){
      params.query.isInviteMode = '1'
    }
    router.push(params)
  }

  // 撤销审批
  const handleCancelApproval = (row, refreshTable) => {
    let bizType = '';
    if(['NOTICE_AUDITING', 'INVITE_AUDITING'].includes(row.nodeStatus)){
      bizType = 'SRM_TENDER_NOTICE_AUDIT';
    } else if(row.nodeStatus === 'CHANGE_AUDITING') {
      bizType = 'SRM_CHANGE_AUDIT';
    } else if(row.nodeStatus === 'FAILED_AUDITING') {
      bizType = 'SRM_TENDER_FAILED_AUDIT';
    } else if(row.nodeStatus === 'PUBLICITY_AUDITING') {
      bizType = 'SRM_PUBLICITY_AUDIT';
    } else if(row.nodeStatus === 'PUBLIC_NOTICE_AUDITING') {
      bizType = 'SRM_BID_NOTICE_AUDIT';
    } else if(row.nodeStatus === 'TENDER_DOC_AUDITING') {
      bizType = 'SRM_TENDER_DOC_AUDIT';
    } else {
      bizType = '';
    }
    ElMessageBox.confirm('确定要撤销审批吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })
      .then(() => {
        cancelApproveProposal({bizType, bizKey: row.bizKey}).then(() => {
          refreshTable?.();
          ElMessage({
            type: 'success',
            message: '撤销审批成功！',
          });
        });
      })
      .catch(() => {});
  };

  // 标记询价完成
  const handleFinish = (row, refreshTable) => {
    ElMessageBox.confirm('确定要标记询价完成吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })
      .then(() => {
        // 调用接口标记询价完成
        finishInquiryApi(row.projectCode).then(() => {
          refreshTable?.();
          ElMessage({
            type: 'success',
            message: '标记询价完成成功！',
          });
        })
      })
      .catch(() => {});
  };

  const toChangeList = (row) => {

    const params = {
      path: '/procurementSourcing/changeList/index',
      query: {
        projectCode: row?.projectCode,
        projectId: row?.id,
      }
    }
    if(row?.inviteMethod === 'INVITE'){
      params.query.isInviteMode = '1'
    }
    router.push(params)

  }

  return {
    toPlanDetail,
    toBidding,
    handleCancelApproval,
    handleFinish,
    toChangeList
  }
}
