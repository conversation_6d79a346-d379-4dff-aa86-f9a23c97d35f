import { computed } from 'vue';
import { useDict } from '@/hooks/dict';
import { useUserRole } from '@/views/procurementSourcing/biddingProcess/utils';
const { isSupplier } = useUserRole();

const getDict = computed(() => (str) => {
  if (!str) return [];
  const value = useDict(str)?.[str]?.value;
  return value;
});

const replaceSourcingType = (str, sourcingType) => {
  const mapping = { JZTP: '竞谈', ZB: '招标' };
  return str.replace('询价', mapping[sourcingType] || '询价');
}

export default function useOptions(route) {
  const type = computed(() => route.params.type || route.path.split('/').pop());

  const searchFields = computed(() => {
    const fields = [
      {
        label: '项目编号/名称',
        prop: 'searchContent',
        component: 'el-input',
      },
    ];
    if (!isSupplier.value) {
      fields.push({
        label: '审批状态',
        prop: 'nodeStatus',
        type: 'select',
        attrs: {
          multiple: true,
          filterable: true,
          collapseTags: true,
        },
        options: (getDict.value('inquiry_approve_status') || []).map(i => {
          if(i.label.indexOf('询价') !== -1) {
            return { ...i, label: replaceSourcingType(i.label, type.value) };
          }
          return { ...i };
        }),
      });
    }
    fields.push(
      {
        label: '采购进度',
        prop: 'progressStatus',
        component: 'el-select',
        attrs: {
          multiple: true,
          filterable: true,
          collapseTags: true,
        },
        enums: ((getDict.value('project_process') || []).map(i => {
          if(i.label.indexOf('询价') !== -1) {
            return { ...i, label: replaceSourcingType(i.label, type.value) };
          }
          return { ...i };
        })).slice(0, -1),
      },
      ...(type.value === 'ZJWT'
        ? [
            {
              label: '报价起止时间',
              prop: 'quoteTime',
              component: 'el-date-picker',
              fieldMapToTime: ['quoteStartTime', 'quoteEndTime'],
              colAttrs: {
                span: 12,
              },
              componentAttrs: {
                startPlaceholder: '开始时间',
                endPlaceholder: '截止时间',
                type: 'datetimerange',
                format: 'YYYY-MM-DD HH:mm:ss',
                valueFormat: 'YYYY-MM-DD HH:mm:ss',
                clearable: true,
                defaultTime: [new Date(2000, 1, 1, 0, 0, 0), new Date(2000, 2, 1, 23, 59, 59)],
              },
            },
          ]
        : []),
      {
        label: '采购部门',
        prop: 'buyerDeptName',
        component: 'el-input',
      },
      {
        label: '项目负责人',
        prop: 'projectLeaderName',
        component: 'el-input',
      },
      {
        label: '开标起止时间',
        prop: 'bidOpenTime',
        component: 'el-date-picker',
        colAttrs: {
          span: 12,
        },
        componentAttrs: {
          startPlaceholder: '开始时间',
          endPlaceholder: '结束时间',
          type: 'datetimerange',
          format: 'YYYY-MM-DD HH:mm:ss',
          valueFormat: 'YYYY-MM-DD HH:mm:ss',
          clearable: true,
          defaultTime: [new Date(2000, 1, 1, 0, 0, 0), new Date(2000, 2, 1, 23, 59, 59)],
        },
      },
    )
    return fields;
  })
  const tableColumns = [
    {
      label: '序号',
      prop: 'index',
      type: 'index',
    },
    {
      label: '项目编号',
      prop: 'projectCode',
      minWidth: 175,
    },
    {
      label: '项目名称',
      prop: 'projectName',
    },
    {
      label: '采购执行编号',
      prop: 'code',
      minWidth: 175,
    },
    {
      label: '采购业务类型',
      prop: 'serviceTypeName',
      minWidth: 150,
    },
    {
      label: '寻源方式',
      prop: 'sourcingType',
      formatter(row){
        const value = row['sourcingType'];
        const options = getDict.value('plan_recruit_method') || [];
        if (Array.isArray(value)) {
          return value.map((item) => options?.find((opt) => opt.value == item)?.label).join('、');
        }
        return options?.find((item) => item.value == value)?.label || '-';
      }
    },
    {
      label: '采购范围',
      prop: 'inviteMethod',
      formatter(row){
        return row.inviteMethod === 'PUBLICITY' ? '公开' : '邀请'
      }
    },
    {
      label: '需求部门',
      prop: 'purchaseDeptName',
    },
    {
      label: '采购部门',
      prop: 'buyerDeptName',
    },
    {
      label: '项目预算',
      prop: 'budgetAmount',
    },
    {
      label: '支付方式',
      prop: 'paymentName',
    },
    {
      label: '账期时间',
      prop: 'paymentPeriodName',
    },
    {
      label: '项目负责人',
      prop: 'projectLeaderName',
      minWidth: 150,
    },
    {
      label: '报名截止时间',
      prop: 'registerEndTime',
      width: 170,
    },
    {
      label: '预审截止时间',
      prop: 'preReviewEndTime',
      width: 170,
    },
    {
      label: '报价截止时间',
      prop: 'quoteEndTime',
      width: 170,
    },
    {
      label: '开标时间',
      prop: 'bidOpenTime',
      width: 170,
    },
    {
      label: '采购进度',
      prop: 'progressStatus',
      formatter(row){
        const value = row['progressStatus'];
        const options = getDict.value('project_process') || [];
        const obj = options?.find((item) => item.value == value);
        const label = replaceSourcingType(obj?.label || '-', type.value)
        return label || '-';
      }
    },
  ];
  if (!isSupplier.value) {
    tableColumns.push({
      label: '审批状态',
      prop: 'nodeStatus',
      width: 160,
      formatter(row) {
        const value = row['nodeStatus'];
        const options = getDict.value('inquiry_approve_status') || [];
        const obj = options?.find((item) => item.value == value);
        const label = replaceSourcingType(obj?.label || '-', type.value)
        return label || '-';
      }
    });
  }

  tableColumns.push(
    {
      label: '关联采购计划',
      prop: 'relationPlanCodeList',
      minWidth: 180,
    },
    {
      label: '操作',
      prop: 'action',
      width: 300,
      fixed: 'right',
    }
  );

  const tablePropsObj = computed(() => {
    return {
      stripe: false,
      border: true,
      showHeader: true,
      defaultExpandAll: false,
      highlightCurrentRow: true,
      showTableSetting: true,
    };
  })

  return {
    searchFields,
    tableColumns,
    tablePropsObj,
  };
}
