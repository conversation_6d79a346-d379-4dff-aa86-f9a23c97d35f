<template>
  <div class="plan-container">
    <yun-pro-table
      ref="tableRef"
      v-model:pagination="state.page"
      v-model:searchData="state.searchData"
      :search-fields="searchFields"
      :layout="'whole'"
      :auto-height="true"
      :table-columns="tableColumns"
      :remote-method="remoteMethod"
      :table-props="tablePropsObj"
    >
      <template #t_bidOpenTime="{ row }">
        <TimeCountdown :time="row.bidOpenTime || ''" />
      </template>
      <template #t_relationPlanCodeList="{ row }">
        <div v-if="row.relationPlanCodeList && row.relationPlanCodeList.length">
          <template
            v-for="planCode in row.relationPlanCodeList"
            :key="planCode"
          >
            <a
              :href="`/purchasing/plan/detail/index?planCode=${planCode}&pageType=view`"
              style="color: #0069ff; text-decoration: none; margin-right: 8px; display: block"
              @click.prevent="toPlanDetail(planCode)"
            >
              {{ planCode }}
            </a>
          </template>
        </div>
        <span v-else>-</span>
      </template>
      <template #t_action="{ row }">
        <yun-rest limit="3">
          <el-button
            type="text"
            @click="toBidding(row)"
            v-if="!isSupplier"
          >
            查看
          </el-button>
          <template v-if="isPurchaser">
            <template v-if="hasAuth(row.projectMemberList)">
              <el-button
                @click="handleCancelApproval(row, refreshTable)"
                type="text"
                v-if="
                  ['NOTICE_AUDITING', 'INVITE_AUDITING', 'CHANGE_AUDITING', 'FAILED_AUDITING', 'PUBLICITY_AUDITING', 'PUBLIC_NOTICE_AUDITING', 'TENDER_DOC_AUDITING'].includes(
                    row.nodeStatus
                  )
                "
              >
                撤销审批
              </el-button>
              <template
                v-if="
                  (row.progressStatus === 'TO_NOTICE' && row.nodeStatus !== 'NOTICE_AUDITING') ||
                  (row.progressStatus === 'TO_INVITE' && row.nodeStatus !== 'INVITE_AUDITING')
                "
              >
                <el-button
                  type="text"
                  @click="toBidding(row)"
                >
                  发{{ row.inviteMethod === 'PUBLICITY' ? '公告' : '邀请' }}
                </el-button>
              </template>
              <el-button
                  type="text"
                  @click="toBidding(row)"
                  v-if="['NOTICE', 'INVITE'].includes(row.progressStatus) && ['TENDER_DOC_REVOKE', 'TENDER_DOC_REJECTED', 'NOTICE_APPROVED', 'INVITE_APPROVED'].includes(row.nodeStatus) && ['JZTP', 'ZB'].includes(row.sourcingType)"
                >
                  发文件
                </el-button>
              <el-button
                type="text"
                @click="toChangeList(row)"
                v-if="
                  !['CHANGE_AUDITING', 'FAILED_AUDITING', 'AWARD_AUDITING', 'PUBLICITY_AUDITING', 'PUBLIC_NOTICE_AUDITING', 'NOTICE_AUDITING', 'INVITE_AUDITING', 'NULL', 'NOTICE_REVOKE', 'NOTICE_REJECTED', 'INVITE_REVOKE', 'INVITE_REJECTED', 'TENDER_DOC_AUDITING'].includes(row.nodeStatus) &&
                  !['FAILED', 'COMPLETED'].includes(row.progressStatus)
                "
              >
                变更
              </el-button>
              <el-button
                type="text"
                v-if="
                  !['CHANGE_AUDITING', 'FAILED_AUDITING', 'AWARD_AUDITING', 'NOTICE_AUDITING', 'INVITE_AUDITING', 'TENDER_DOC_AUDITING'].includes(row.nodeStatus) &&
                  !['TO_NOTICE', 'TO_INVITE', 'AWARDED', 'BID_WON_PUBLICITY', 'BID_WON_NOTICE', 'COMPLETED', 'FAILED'].includes(row.progressStatus)
                "
                @click="handleTenderFailed(row)"
              >
                流标
              </el-button>
              <el-button
                type="text"
                @click="toBidding(row)"
                v-if="['TO_BID_OPEN'].includes(row.progressStatus)"
              >
                开标
              </el-button>
              <el-button
                type="text"
                @click="toBidding(row)"
                v-if="['END_OPENED'].includes(row.progressStatus)"
              >
                定标
              </el-button>
              <template v-if="row.inviteMethod === 'PUBLICITY'">
                <el-button
                  type="text"
                  @click="toBidding(row)"
                  v-if="!['PUBLICITY_AUDITING'].includes(row.nodeStatus) && ['AWARDED'].includes(row.progressStatus)
                  "
                >
                  发中标公示
                </el-button>
                <el-button
                  type="text"
                  @click="toBidding(row)"
                  v-if="
                    (['PUBLICITY_APPROVED'].includes(row.nodeStatus) && ['BID_WON_PUBLICITY'].includes(row.progressStatus)) ||
                    (['BID_WON_NOTICE'].includes(row.progressStatus) && row.nodeStatus !== 'PUBLIC_NOTICE_AUDITING')
                  "
                >
                  发中标公告
                </el-button>
              </template>
              <el-button
                type="text"
                @click="toBidding(row)"
                v-if="['BID_WON_NOTICE'].includes(row.progressStatus) && row.nodeStatus === 'PUBLIC_NOTICE_APPROVED'"
              >
                发中标通知书
              </el-button>
              <el-button
                type="text"
                @click="handleFinish(row)"
                v-if="['BID_WON_NOTICE'].includes(row.progressStatus) && row.nodeStatus === 'PUBLIC_NOTICE_APPROVED'"
              >
                标记询价完成
              </el-button>
            </template>
            <el-button
              type="text"
              @click="toBidding(row)"
              v-if="['BID_OPENED', 'END_OPENED', 'EVALUATED', 'EVALUATING', 'AWARDED', 'BID_WON_PUBLICITY', 'BID_WON_NOTICE', 'COMPLETED'].includes(row.progressStatus)"
            >
              开标记录
            </el-button>
            <el-button
              type="text"
              @click="toBidding(row)"
              v-if="['BID_WON_PUBLICITY', 'BID_WON_NOTICE', 'COMPLETED'].includes(row.progressStatus) || ['PUBLICITY_AUDITING'].includes(row.nodeStatus)"
            >
              查看中标公示
            </el-button>
            <el-button
              type="text"
              @click="toBidding(row)"
              v-if="['COMPLETED'].includes(row.progressStatus)"
            >
              查看中标公告
            </el-button>
            <el-button
              type="text"
              @click="toBidding(row)"
              v-if="['COMPLETED'].includes(row.progressStatus)"
            >
              查看中标通知书
            </el-button>
          </template>
          <template v-if="isSupplier">
            <el-button
              type="text"
              @click="toBidding(row)"
              v-if="!['TO_NOTICE', 'TO_INVITE'].includes(row.progressStatus)"
            >
              查看
            </el-button>
            <el-button
              type="text"
              @click="toBidding(row)"
              v-if="['INVITE'].includes(row.progressStatus) && row.inviteReceipt"
            >
              邀请回执
            </el-button>
            <el-button
              type="text"
              @click="toBidding(row)"
              v-if="['NOTICE', 'INVITE', 'REGISTER', 'QUOTING'].includes(row.progressStatus) && row.preQualification && isInRegisterPeriod(row) && row.sourcingType === 'XJCG'"
            >
              报名
            </el-button>
            <el-button
              type="text"
              @click="toBidding(row)"
              v-if="['TENDER_DOC', 'REGISTER', 'QUOTING'].includes(row.progressStatus) && row.preQualification && isInRegisterPeriod(row) && ['JZTP', 'ZB'].includes(row.sourcingType)"
            >
              报名
            </el-button>
            <el-button
              type="text"
              @click="toBidding(row)"
              v-if="['TENDER_DOC', 'REGISTER', 'QUOTING'].includes(row.progressStatus) && row.needTenderFee && isInFeePeriod(row)"
            >
              标书费缴纳
            </el-button>
            <el-button
              type="text"
              @click="toBidding(row)"
              v-if="['REGISTER', 'QUOTING'].includes(row.progressStatus) && row.needDeposit && ['JZTP', 'ZB'].includes(row.sourcingType)"
            >
              保证金缴纳
            </el-button>
            <el-button
              type="text"
              @click="toBidding(row)"
              v-if="['NOTICE', 'INVITE', 'REGISTER', 'QUOTING'].includes(row.progressStatus) && isInQuotePeriod(row) && row.sourcingType === 'XJCG'"
            >
              报价
            </el-button>
            <el-button
              type="text"
              @click="toBidding(row)"
              v-if="['TENDER_DOC', 'REGISTER', 'QUOTING'].includes(row.progressStatus) && isInQuotePeriod(row) && ['JZTP', 'ZB'].includes(row.sourcingType)"
            >
              报价
            </el-button>
            <el-button
              type="text"
              @click="toBidding(row)"
              v-if="['BID_OPENED', 'END_OPENED', 'EVALUATED', 'EVALUATING', 'AWARDED', 'BID_WON_PUBLICITY', 'BID_WON_NOTICE', 'COMPLETED'].includes(row.progressStatus)"
            >
              开标记录
            </el-button>
            <el-button
              type="text"
              @click="toBidding(row)"
              v-if="['BID_WON_PUBLICITY', 'BID_WON_NOTICE', 'COMPLETED'].includes(row.progressStatus) && row.nodeStatus !== 'PUBLIC_NOTICE_REVOKE'"
            >
              查看中标公示
            </el-button>
            <el-button
              type="text"
              @click="toBidding(row)"
              v-if="
                ['BID_WON_NOTICE', 'COMPLETED'].includes(row.progressStatus) &&
                !['PUBLIC_NOTICE_AUDITING', 'PUBLIC_NOTICE_REVOKE', 'PUBLIC_NOTICE_REJECTED'].includes(row.nodeStatus)
              "
            >
              查看中标公告
            </el-button>
            <el-button
              type="text"
              @click="toBidding(row)"
              v-if="['COMPLETED'].includes(row.progressStatus)"
            >
              查看中标通知书
            </el-button>
          </template>
          <template v-if="isBidExpert">
            <el-button
              type="text"
              @click="toBidding(row)"
              v-if="['END_OPENED', 'EVALUATING'].includes(row.progressStatus) && isEvaluationMember(row.projectMemberList) === 'EVALUATION_MEMBER'"
            >
              在线评标
            </el-button>
            <el-button
              type="text"
              @click="toBidding(row)"
              v-if="['EVALUATING'].includes(row.progressStatus) && isEvaluationMember(row.projectMemberList) === 'EVALUATION_LEADER'"
            >
              评标汇总
            </el-button>
          </template>
        </yun-rest>
      </template>
    </yun-pro-table>
    <!-- 流标抽屉 -->
    <TenderFailedDrawer
      ref="tenderFailedDrawerRef"
      :project-data="currentProject"
      @success="handleTenderFailedSuccess"
    />
  </div>
</template>

<script setup name="inquiry" lang="tsx">
import { ref, reactive } from 'vue';
import { inquiryListApi } from '@/api/purchasing/proposal.ts';
import useOptions from './hooks/useOptions.jsx';
import useHandler from './hooks/useHandler.js';
import TimeCountdown from '@/views/purchasing/projectProposal/components/timeCountdown.vue';
import { useUserRole } from '@/views/procurementSourcing/biddingProcess/utils';
import TenderFailedDrawer from '@/views/procurementSourcing/biddingProcess/components/TenderFailed/TenderFailedDrawer.vue'
import { isProjectMember, isEvaluationMember } from '@/views/procurementSourcing/biddingProcess/utils/bid';
const route = useRoute();
const { isPurchaser, isSupplier, isBidExpert } = useUserRole();
const { toPlanDetail, toBidding, toChangeList, handleCancelApproval, handleFinish } = useHandler();
const { searchFields, tableColumns, tablePropsObj } = useOptions(route);
const hasAuth = (projectMemberList: any) => isProjectMember(projectMemberList) === 'PROJECT_LEADER';

const state = reactive({
  page: {
    total: 0, // 总页数
    page: 1, // 当前页数
    size: 20, // 每页显示多少条
  },
  searchData: {
    status: ['EFFECT'],
  },
  loading: false,
});
const tableRef = ref();
const tenderFailedDrawerRef = ref();
const currentProject = ref({});

const remoteMethod = async ({ searchData, pagination }) => {
  const { bidOpenTime, quoteTime, ...rest } = searchData;
	const path = route.path.split('/');
  const { data } = await inquiryListApi(
    {
      ...rest,
      bidOpenStartTime: bidOpenTime && bidOpenTime.length ? bidOpenTime[0] : '',
      bidOpenEndTime: bidOpenTime && bidOpenTime.length > 1 ? bidOpenTime[1] : '',
      quoteStartTime: quoteTime && quoteTime.length ? quoteTime[0] : '',
      quoteEndTime: quoteTime && quoteTime.length > 1 ? quoteTime[1] : '',
      needNodeStatus: true,
			sourcingType: path.slice(-1),
    },
    { current: pagination.page, size: pagination.size },
    isSupplier.value,
  );
  return data;
};

const refreshTable = () => {
  state.page.page = 1;
  tableRef.value?.getData?.();
};

// 处理流标点击
const handleTenderFailed = (row) => {
  // tenderFailedDrawerVisible.value = true;
  currentProject.value = row;
  tenderFailedDrawerRef.value.show();
};

const handleTenderFailedSuccess = () => {
  refreshTable();
};

const isInRegisterPeriod = (row) => {
  if (!row.registerStartTime || !row.registerEndTime) return false;
  const now = new Date();
  const start = new Date(row.registerStartTime);
  const end = new Date(row.registerEndTime);
  return now >= start && now <= end;
};

const isInQuotePeriod = (row) => {
  if (!row.quoteStartTime || !row.quoteEndTime) return false;
  const now = new Date();
  const start = new Date(row.quoteStartTime);
  const end = new Date(row.quoteEndTime);
  return now >= start && now <= end;
};

const isInFeePeriod = (row) => {
  if (!row.tenderFeePayStartTime || !row.tenderFeePayEndTime) return false;
  const now = new Date();
  const start = new Date(row.tenderFeePayStartTime);
  const end = new Date(row.tenderFeePayEndTime);
  return now >= start && now <= end;
};
</script>

<style lang="scss" scoped>
.plan-container {
  width: 100%;
  height: calc(100vh - 88px);
  :deep(.el-form .el-form-item:last-of-type) {
    margin-bottom: 24px !important;
  }
}
</style>
