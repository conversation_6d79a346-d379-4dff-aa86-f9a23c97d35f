/**
 * 评标相关API
 */
import request from '/@/utils/request'

// 获取评标信息
export function getEvaluationInfo(projectId: string) {
  return request({
    url: `/evaluation/${projectId}`,
    method: 'get'
  })
}

// 选择评标专家
export function selectExperts(data: any) {
  return request({
    url: '/evaluation/experts',
    method: 'post',
    data
  })
}

// 开始评标
export function startEvaluation(projectId: string) {
  return request({
    url: `/evaluation/${projectId}/start`,
    method: 'post'
  })
}

// 提交评分
export function submitScore(data: any) {
  return request({
    url: '/evaluation/score',
    method: 'post',
    data
  })
}

// 生成评标报告
export function generateReport(projectId: string) {
  return request({
    url: `/evaluation/${projectId}/report`,
    method: 'post'
  })
} 