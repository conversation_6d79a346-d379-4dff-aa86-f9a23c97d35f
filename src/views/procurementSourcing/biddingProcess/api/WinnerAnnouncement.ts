import request from '/@/utils/request';

/**
 * 获取中标供应商列表
 * @param noticeId 公告ID
 * @param projectId 项目ID
 * @param params 请求参数
 */
export function getAwardedSuppliers(
  noticeId: number,
  projectId: number,
  params?: any
) {
  return request({
    url: `/admin/tenderBid/awardedSuppliers`,
    method: 'post',
    data: {
      noticeId,
      projectId,
      ...params,
    },
  });
}

/**
 * 保存中标公告信息
 * @param data 公告数据
 */
export function saveWinnerAnnouncement(data: any) {
  return request({
    url: `/admin/tenderBid/bidPublicNotice`,
    method: 'post',
    data,
  });
} 