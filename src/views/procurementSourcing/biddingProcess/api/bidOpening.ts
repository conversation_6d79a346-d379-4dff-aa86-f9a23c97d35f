/**
 * 开标相关API
 */
import request from '/@/utils/request'

/**
 * 获取开标操作日志
 * @param data 查询参数
 * @returns Promise<any>
 */
export function getBidOpeningOperationLog(data: any) {
  return request({
    url: '/admin/tenderOpen/operationLog',
    method: 'post',
    data
  })
}

/**
 * 添加开标操作日志
 * @param data
 */
export function addBidOpeningOperationLog(data: any) {
  return request({
    url: '/admin/tenderOpen/operationLogAdd',
    method: 'post',
    data
  })
}

/**
 * 开始开标
 * @param data 开标参数
 * @returns Promise<any>
 */
export function startBidOpening(data: any) {
  return request({
    url: '/admin/tenderOpen/openBid',
    method: 'post',
    data
  })
}

/**
 * 发起再次报价
 * @param data 再次报价参数
 * @returns Promise<any>
 */
export function reQuote(data: any) {
  return request({
    url: '/admin/srmTenderBidderQuote/quoteAgain',
    method: 'post',
    data
  })
}

/**
 * 获取供应商列表
 * @param data
 */
export function getQuoteAgainSupplierList(data: any) {
  return request({
    url: 'admin/srmTenderBidderQuote/getQuoteAgainSupplierList',
    method: 'post',
    data
  })
}

/**
 * 结束开标
 * @param data
 */
export function endBidOpening(data: any) {
  return request({
    url: '/admin/tenderOpen/endOpenBid',
    method: 'post',
    data
  })
}

/**
 * 获取开标记录 - 按物料查看
 * @param data 查询参数 { projectId, noticeId, sectionId, roundNo, materialName }
 * @returns Promise<any>
 */
export function getBidOpeningRecordByMaterial(data: any) {
  return request({
    url: '/admin/tenderOpen/openTenderInfo/material',
    method: 'post',
    data
  })
}

/**
 * 获取开标记录 - 按供应商查看
 * @param data 查询参数 { projectId, noticeId, sectionId, roundNo, supplierName }
 * @returns Promise<any>
 */
export function getBidOpeningRecordBySupplier(data: any) {
  return request({
    url: '/admin/tenderOpen/openTenderInfo/supplier',
    method: 'post',
    data
  })
}

export function getIpWarnData(data: any) {
  return request({
    url: '/admin/tenderOpen/ip/warn',
    method: 'post',
    data
  })
}

export function getQuoteCount(data: any) {
  return request({
    url: '/admin/srmTenderBidderQuote/quoteCount',
    method: 'post',
    data
  })
}

/**
 * 获取再次报价的开标信息
 * @param data 查询参数
 * @returns Promise<any>
 */
export function getQuoteAgainBidOpenInfo(data: any) {
  return request({
    url: '/admin/tenderOpen/getQuoteAgainBidOpenInfo',
    method: 'post',
    data
  })
}
