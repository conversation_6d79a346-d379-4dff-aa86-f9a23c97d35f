/**
 * 线下评标相关API
 */
import request from '/@/utils/request'

// 线下评标记录接口
export interface OfflineEvaluationRecord {
  id: number
  projectName: string
  sectionName: string
  summaryStatus: 'PENDING' | 'IN_PROGRESS' | 'COMPLETED'
  summaryPerson: string
  summaryTime: string
  evaluationReport: string
  signProgress: string
  signedReport: string
  hasUploadedData: boolean
  remark?: string
}

// 查询参数接口
export interface OfflineEvaluationQuery {
  sectionId?: string
  projectId?: string
  current?: number
  size?: number
}

// 上传评标数据参数接口
export interface UploadEvaluationData {
  sectionId: string
  projectId: string
  evaluationData: string
  remark?: string
}

// 获取线下评标列表
export function getOfflineEvaluationList(params: OfflineEvaluationQuery) {
  return request({
    url: '/admin/offlineEvaluation/list',
    method: 'get',
    params
  })
}

// 上传评标数据
export function uploadEvaluationData(data: UploadEvaluationData) {
  return request({
    url: '/admin/offlineEvaluation/upload',
    method: 'post',
    data
  })
}

// 下载评标报告
export function downloadEvaluationReport(reportId: string) {
  return request({
    url: `/admin/offlineEvaluation/download/${reportId}`,
    method: 'get',
    responseType: 'blob'
  })
}

// 获取评标详情
export function getOfflineEvaluationDetail(id: string) {
  return request({
    url: `/admin/offlineEvaluation/detail/${id}`,
    method: 'get'
  })
}

// 更新汇总状态
export function updateSummaryStatus(id: string, status: string) {
  return request({
    url: `/admin/offlineEvaluation/updateStatus/${id}`,
    method: 'put',
    data: { status }
  })
}

// 模拟数据生成函数（开发阶段使用）
export function getMockOfflineEvaluationList(params: OfflineEvaluationQuery): Promise<any> {
  return new Promise((resolve) => {
    setTimeout(() => {
      const mockData: OfflineEvaluationRecord[] = [
        {
          id: 1,
          projectName: '办公用品采购项目',
          sectionName: '标段01: 文具用品',
          summaryStatus: 'COMPLETED',
          summaryPerson: '张三',
          summaryTime: '2024-01-15 14:30:00',
          evaluationReport: 'report_001.pdf',
          signProgress: '3/3 (100%)',
          signedReport: 'signed_report_001.pdf',
          hasUploadedData: true,
          remark: '评标完成，所有专家已签名'
        },
        {
          id: 2,
          projectName: '办公用品采购项目',
          sectionName: '标段02: 电子设备',
          summaryStatus: 'IN_PROGRESS',
          summaryPerson: '李四',
          summaryTime: '2024-01-15 16:20:00',
          evaluationReport: 'report_002.pdf',
          signProgress: '2/3 (67%)',
          signedReport: '',
          hasUploadedData: true,
          remark: '评标进行中，等待最后一位专家签名'
        },
        {
          id: 3,
          projectName: '办公用品采购项目',
          sectionName: '标段03: 办公家具',
          summaryStatus: 'PENDING',
          summaryPerson: '',
          summaryTime: '',
          evaluationReport: '',
          signProgress: '0/3 (0%)',
          signedReport: '',
          hasUploadedData: false,
          remark: ''
        },
        {
          id: 4,
          projectName: '设备采购项目',
          sectionName: '标段01: 计算机设备',
          summaryStatus: 'COMPLETED',
          summaryPerson: '王五',
          summaryTime: '2024-01-14 10:15:00',
          evaluationReport: 'report_004.pdf',
          signProgress: '5/5 (100%)',
          signedReport: 'signed_report_004.pdf',
          hasUploadedData: true,
          remark: '评标顺利完成'
        },
        {
          id: 5,
          projectName: '设备采购项目',
          sectionName: '标段02: 网络设备',
          summaryStatus: 'IN_PROGRESS',
          summaryPerson: '赵六',
          summaryTime: '2024-01-16 09:30:00',
          evaluationReport: 'report_005.pdf',
          signProgress: '3/5 (60%)',
          signedReport: '',
          hasUploadedData: true,
          remark: '评标进行中'
        }
      ]

      // 根据标段ID过滤数据
      let filteredData = mockData
      if (params.sectionId) {
        // 这里可以根据实际需要过滤数据
        filteredData = mockData.filter(item =>
          item.sectionName.includes('标段01') ||
          item.sectionName.includes('标段02') ||
          item.sectionName.includes('标段03')
        )
      }

      // 分页处理
      const current = params.current || 1
      const size = params.size || 10
      const start = (current - 1) * size
      const end = start + size
      const records = filteredData.slice(start, end)

      resolve({
        code: 0,
        msg: 'success',
        data: {
          records,
          total: filteredData.length,
          current,
          size
        }
      })
    }, 500) // 模拟网络延迟
  })
}
