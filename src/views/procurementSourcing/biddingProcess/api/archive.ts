/**
 * 归档相关API
 */
import request from '/@/utils/request'

// 获取归档列表
export function getArchiveList(params: any) {
  return request({
    url: '/archive/list',
    method: 'get',
    params
  })
}

// 归档项目
export function archiveProject(data: any) {
  return request({
    url: '/archive/project',
    method: 'post',
    data
  })
}

// 获取项目总结
export function getProjectSummary(projectId: string) {
  return request({
    url: `/archive/summary/${projectId}`,
    method: 'get'
  })
} 

/**
 * 获取项目归档信息
 * @param projectCode 项目编码
 * @returns Promise
 */
export const getProjectArchive = (projectCode: string) => {
  return request({
    url: `/admin/srmProcurementProject/getProjectArchive/${projectCode}`,
    method: 'get',
  });
}; 