/**
 * 合同相关API
 */
import request from '/@/utils/request'

// 获取合同列表
export function getContractList(params: any) {
  return request({
    url: '/contract/list',
    method: 'get',
    params
  })
}

// 起草合同
export function draftContract(data: any) {
  return request({
    url: '/contract/draft',
    method: 'post',
    data
  })
}

// 审核合同
export function reviewContract(id: string, data: any) {
  return request({
    url: `/contract/${id}/review`,
    method: 'post',
    data
  })
}

// 签署合同
export function signContract(id: string, data: any) {
  return request({
    url: `/contract/${id}/sign`,
    method: 'post',
    data
  })
} 