import request from '/@/utils/request'
import type { 
  QueryMaterialParams, 
  SubmitAwardReviewData, 
  TenderBidDetailResponse 
} from '../types/award'

/**
 * 定标相关API
 */

// 查询物料列表
export const queryMaterialList = (params: QueryMaterialParams) => {
  return request({
    url: '/admin/srmTenderBidderQuote/queryQuoteListByMaterial',
    method: 'post',
    data: params
  })
}

// 获取模板列表
export const getTemplateList = (query: any) => {
  return request({
    url: '/admin/baseAnnouncementTemplate/query/page',
    method: 'get',
    params: query
  })
}

// 定标提交审核
export const submitAwardReview = (data: SubmitAwardReviewData) => {
  return request({
    url: '/admin/tenderBid/review',
    method: 'post',
    data
  })
}

// 根据ID获取中标公示详情
export const getTenderBidById = (noticeId: number, projectId: number): Promise<TenderBidDetailResponse> => {
  return request({
    url: `/admin/tenderBid/getById/${noticeId}/${projectId}`,
    method: 'get'
  })
}

// 获取中标结果列表
export const getTenderBidList = (params: { projectId: string | number; noticeId: string | number }) => {
  return request({
    url: '/admin/tenderBid/list',
    method: 'get',
    params
  })
}