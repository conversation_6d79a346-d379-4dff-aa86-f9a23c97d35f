import request from '/@/utils/request';
import type {
  AwardedSupplierInfo,
  AwardedSuppliersResponse,
  GetAwardedSuppliersParams,
  SaveBidPublicityParams
} from '../types/winnerPublicity';

/**
 * 获取中标供应商列表
 * @param noticeId 公告ID
 * @param projectId 项目ID
 * @param params 请求参数
 */
export function getAwardedSuppliers(
  noticeId: number,
  projectId: number,
  params?: GetAwardedSuppliersParams
) {
  return request({
    url: `/admin/tenderBid/awardedSuppliers`,
    method: 'post',
    data: {
      noticeId,
      projectId,
      ...params,
    },
  });
}

/**
 * 保存中标公示信息
 * @param data 公示数据
 */
export function saveBidPublicity(data: SaveBidPublicityParams) {
  return request({
    url: `/admin/tenderBid/bidPublicity`,
    method: 'post',
    data,
  });
}

/**
 * 查询中标明细列表
 * @param data 公示数据
 */
export function getAwardedItems(data: any) {
  return request({
    url: `/admin/tenderBid/awardedItems`,
    method: 'post',
    data,
  });
}
