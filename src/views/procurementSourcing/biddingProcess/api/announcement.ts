/**
 * 发标公告相关API
 */
import request from '/@/utils/request'

// 获取公告列表
export function getAnnouncementList(params: any) {
  return request({
    url: '/announcement/list',
    method: 'get',
    params
  })
}

// 获取公告详情
export function getAnnouncementDetail(id: string) {
  return request({
    url: `/announcement/${id}`,
    method: 'get'
  })
}

// 创建公告
export function createAnnouncement(data: any) {
  return request({
    url: '/announcement',
    method: 'post',
    data
  })
}

// 发布公告
export function publishAnnouncement(id: string) {
  return request({
    url: `/announcement/${id}/publish`,
    method: 'post'
  })
}

export const getTemplateList = (query: any) => {
  return request({
    url: '/admin/baseAnnouncementTemplate/query/page',
    method: 'get',
    params: query
  })
}

export const getTemplateDetail = (id: string) => {
  return request({
    url: `/admin/baseAnnouncementTemplate/getById/${id}`,
    method: 'get'
  })
}

// 获取采购项目详情
export const getSrmProcurementProject = (projectCode: string) => {
  return request({
    url: `/admin/srmProcurementProject/detail`,
    method: 'get',
    params: {
      projectCode
    }
  })
}

// 保存采购公告
export const saveAnnouncementData = (data: any) => {
  return request({
    url: '/admin/srmTenderNotice/add',
    method: 'post',
    data
  })
}

// 更新采购公告
export const updateAnnouncementData = (data: any) => {
  return request({
    url: '/admin/procurementAnnouncement/update',
    method: 'post',
    data
  })
}
export const reviewApi = (data: any) => {
  return request({
    url: `/admin/srmTenderNotice/toReview/${data.noticeId}`,
    method: 'post',
  })
}

// 获取采购公告详情
export const getAnnouncementData = (id: string) => {
  return request({
    url: `/admin/srmTenderNotice/detail/${id}`,
    method: 'post'
  })
}
