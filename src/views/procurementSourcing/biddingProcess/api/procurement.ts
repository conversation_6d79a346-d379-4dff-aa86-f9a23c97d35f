/**
 * 采购项目相关API
 */
import request from '/@/utils/request'

// 获取采购项目列表
export function getProcurementList(params: any) {
  return request({
    url: '/procurement/list',
    method: 'get',
    params
  })
}

// 获取采购项目详情
export function getProcurementDetail(id: string) {
  return request({
    url: `/procurement/${id}`,
    method: 'get'
  })
}

// 创建采购项目
export function createProcurement(data: any) {
  return request({
    url: '/procurement',
    method: 'post',
    data
  })
}

// 更新采购项目
export function updateProcurement(id: string, data: any) {
  return request({
    url: `/procurement/${id}`,
    method: 'put',
    data
  })
}

// 删除采购项目
export function deleteProcurement(id: string) {
  return request({
    url: `/procurement/${id}`,
    method: 'delete'
  })
} 