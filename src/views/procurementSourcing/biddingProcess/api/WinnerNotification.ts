import request from '/@/utils/request';
import type { SendNotificationParams, EditNotificationContentParams } from '../types/winnerNotification';

/**
 * 获取中标供应商列表
 * @param noticeId 公告ID
 * @param projectId 项目ID
 * @param params 请求参数
 */
export function getAwardedSuppliers(
  noticeId: number,
  projectId: number,
  params?: any
) {
  return request({
    url: `/admin/tenderBid/awardedSuppliers`,
    method: 'post',
    data: {
      noticeId,
      projectId,
      ...params,
    },
  });
}

/**
 * 发送中标通知
 * @param data 通知数据
 */
export function sendWinnerNotification(data: SendNotificationParams) {
  return request({
    url: `/admin/tenderBid/awardNoticeSent`,
    method: 'post',
    data,
  });
}

/**
 * 一键发送所有中标通知
 * @param data 批量通知数据
 */
export function batchSendNotifications(data: any) {
  return request({
    url: `/admin/tenderBid/batchSendNotifications`,
    method: 'post',
    data,
  });
}

/**
 * 编辑中标通知书内容
 * @param data 通知书数据
 */
export function editNotificationContent(data: EditNotificationContentParams) {
  return request({
    url: `/admin/tenderBid/editAwardNotice`,
    method: 'post',
    data,
  });
}

/**
 * 电子签章
 * @param data 签章数据
 */
export function electronicSignature(data: any) {
  return request({
    url: `/admin/tenderBid/electronicSignature`,
    method: 'post',
    data,
  });
}

/**
 * 批量保存中标通知书数据接口
 */
export interface BatchAwardNoticeItem {
  noticeId: number;
  projectId: number;
  sectionId: number;
  templateId: number;
  tenantSupplierId: number;
  noticeContent: string;
}

/**
 * 批量保存中标通知书
 * @param data 通知书数据数组
 */
export function batchAwardNotice(data: BatchAwardNoticeItem[]) {
  return request({
    url: `/admin/tenderBid/batchAwardNotice`,
    method: 'post',
    data,
  });
}

/**
 * 获取中标通知书详情
 * @param noticeId 公告ID
 */
export function getAwardNoticeDetail(noticeId: number, sectionId: number, supplierId: number) {
  return request({
    url: `/admin/tenderBid/awardNotice/detail`,
    method: 'post',
    data: {
      noticeId,
      sectionId,
      tenantSupplierId:supplierId,
    },
  });
} 