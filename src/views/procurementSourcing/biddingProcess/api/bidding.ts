/**
 * 投标相关API
 */
import request from '/@/utils/request'

// 获取投标列表
export function getBiddingList(params: any) {
  return request({
    url: '/bidding/list',
    method: 'get',
    params
  })
}

// 投标报名
export function registerBidding(data: any) {
  return request({
    url: '/bidding/register',
    method: 'post',
    data
  })
}

// 提交投标文档
export function submitBiddingDocument(data: any) {
  return request({
    url: '/bidding/document',
    method: 'post',
    data
  })
}

// 审核投标文档
export function reviewBiddingDocument(id: string, data: any) {
  return request({
    url: `/bidding/document/${id}/review`,
    method: 'post',
    data
  })
}

// 缴纳保证金
export function payBond(data: any) {
  return request({
    url: '/bidding/bond/pay',
    method: 'post',
    data
  })
}

// 提交报价
export function submitQuotation(data: any) {
  return request({
    url: '/bidding/quotation',
    method: 'post',
    data
  })
}

// 获取项目标段列表
export function getProjectSections(projectId: string) {
  return request({
    url: `/admin/srmProcurementProjectSelection/getListByProjectId/${projectId}`,
    method: 'get'
  })
} 