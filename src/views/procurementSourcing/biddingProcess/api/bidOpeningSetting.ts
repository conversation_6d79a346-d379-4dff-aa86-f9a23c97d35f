/**
 * 开标设置相关API
 */
import request from '/@/utils/request'
import type {
  BidOpenConfigData
} from '../types/bidOpeningSetting'

// 重新导出类型供其他模块使用
export type { BidOpenConfigData }

/**
 * 保存开标配置
 * @param data 开标配置数据
 */
export function saveBidOpenConfig(data: BidOpenConfigData) {
  return request({
    url: '/admin/tenderOpen/bidOpenConfig',
    method: 'post',
    data
  })
}

/**
 * 根据公告ID获取开标配置
 * @param noticeId 公告ID
 */
export function getBidOpeningSettingByNotice(noticeId: string) {
  return request({
    url: `/admin/tenderOpen/getBidOpenConfig/${noticeId}`,
    method: 'get'
  })
}


export function getUserByIds (userIds: []) {
  return request({
    url: `/admin/user/list`,
    method: 'get',
    params: {
      userIds
    }
  })
}
