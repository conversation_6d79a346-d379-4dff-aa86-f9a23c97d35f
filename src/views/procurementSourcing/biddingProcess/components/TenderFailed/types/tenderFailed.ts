/**
 * 流标相关类型定义
 */

// 异常处理方式枚举
export enum ExceptionHandleType {
  RE_PURCHASE = 'RE_PURCHASE', // 重新采购
  TERMINATE_PROJECT = 'TERMINATE_PROJECT' // 项目终止
}

// 异常处理方式选项
export const EXCEPTION_HANDLE_TYPE_OPTIONS = [
  { label: '重新采购', value: ExceptionHandleType.RE_PURCHASE },
  { label: '项目终止', value: ExceptionHandleType.TERMINATE_PROJECT }
]

// 异常分类枚举
export enum ExceptionType {
  TEMPORARY_NOT_PURCHASE = 'TEMPORARY_NOT_PURCHASE', // 临时不采购
  SUPPLIER_QUOTE_NOT_APPROPRIATE = 'SUPPLIER_QUOTE_NOT_APPROPRIATE', // 供应商报价不恰当
  OTHER = 'OTHER' // 其他
}

// 异常分类选项
export const EXCEPTION_TYPE_OPTIONS = [
  { label: '临时不采购', value: ExceptionType.TEMPORARY_NOT_PURCHASE },
  { label: '供应商报价不恰当', value: ExceptionType.SUPPLIER_QUOTE_NOT_APPROPRIATE },
  { label: '其他', value: ExceptionType.OTHER }
]

// 附件信息接口
export interface AttachmentInfo {
  fileName: string
  filePath: string
  fileSize?: number
  fileType?: string
}

// 流标表单数据接口
export interface TenderFailedForm {
  projectName: string // 项目名称（带入显示）
  projectId: string // 项目id
  noticeId: string //
  exceptionHandleType: ExceptionHandleType // 异常处理方式
  exceptionType: ExceptionType // 所属异常分类
  isPublicity: boolean // 是否发布流标公示
  remark: string // 异常情况描述
  tenderFailedTitle?: string // 公告标题
  tenderFailedContent?: string // 流标公示内容
  attachmentInfos?: AttachmentInfo[] // 附件
  sectionIdList?: string[] // 标段ID列表
}

// 流标提交参数接口
export interface TenderFailedSubmitParams extends TenderFailedForm {
  projectId: string // 项目ID
}
