<template>
  <div>
    <!-- 抽屉模式 -->
    <el-drawer
      v-if="!contentOnly"
      v-model="visible"
      title="流标处理"
      size="80%"
      :before-close="handleClose"
      destroy-on-close
    >
      <div class="tender-failed-drawer">
        <el-form
          ref="formRef"
          :model="formData"
          :rules="rules"
          label-width="140px"
          class="tender-failed-form"
        >
          <!-- 项目名称 -->
          <el-form-item label="项目名称">
            <el-input
              v-model="formData.projectName"
              readonly
              class="readonly-input"
            />
          </el-form-item>

          <!-- 异常处理方式 -->
          <el-form-item label="异常处理方式" prop="exceptionHandleType" required>
            <el-radio-group v-model="formData.exceptionHandleType" :disabled="props.isEdit">
              <el-radio
                v-for="item in EXCEPTION_HANDLE_TYPE_OPTIONS"
                :key="item.value"
                :label="item.value"
              >
                {{ item.label }}
              </el-radio>
            </el-radio-group>
          </el-form-item>

          <!-- 所属异常分类 -->
          <el-form-item label="所属异常分类" prop="exceptionType" required>
            <el-select
              v-model="formData.exceptionType"
              placeholder="请选择异常分类"
              style="width: 100%"
              :disabled="props.isEdit"
            >
              <el-option
                v-for="item in EXCEPTION_TYPE_OPTIONS"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>

          <!-- 是否发布流标公示 -->
          <el-form-item label="是否发布流标公示" prop="isPublicity" required>
            <el-radio-group v-model="formData.isPublicity" :disabled="props.isEdit">
              <el-radio :label="true">是</el-radio>
              <el-radio :label="false">否</el-radio>
            </el-radio-group>
          </el-form-item>

          <!-- 异常情况描述 -->
          <el-form-item label="异常情况描述" prop="remark" required>
            <el-input
              v-model="formData.remark"
              type="textarea"
              placeholder="请详细描述异常情况"
              :rows="4"
              maxlength="200"
              show-word-limit
              :disabled="props.isEdit"
            />
          </el-form-item>

          <!-- 流标公告区域 -->
          <div v-if="formData.isPublicity">
            <div class="header-title">流标公告</div>

            <!-- 公告标题 -->
            <el-form-item label="公告标题" prop="tenderFailedTitle" required>
              <el-input
                v-model="formData.tenderFailedTitle"
                placeholder="请输入公告标题"
                maxlength="50"
                show-word-limit
                :disabled="props.isEdit"
              />
            </el-form-item>

            <!-- 流标公示内容 -->
            <el-form-item label="" prop="tenderFailedContent" label-width="0">
              <div class="rich-editor-container">
                <Toolbar
                  :editor="editorRef"
                  :defaultConfig="toolbarConfig"
                  mode="default"
                />
                <Editor
                  style="flex: 1; overflow: hidden; min-height: 300px;"
                  v-model="formData.tenderFailedContent"
                  :defaultConfig="editorConfig"
                  mode="default"
                  @onCreated="handleCreated"
                  :disabled="props.isEdit"
                />
              </div>
            </el-form-item>

            <!-- 附件 -->
            <el-form-item label="附件">
              <YunUpload
                v-model="attachmentInfos"
                @change="handleUploadFile"
              />
            </el-form-item>
          </div>
        </el-form>

        <!-- 操作按钮 -->
        <div v-if="!props.isEdit" class="drawer-footer">
          <el-button @click="handleClose">取消</el-button>
          <el-button type="primary" @click="handleSubmit" :loading="loading">
            提交
          </el-button>
        </div>
        <div v-else class="drawer-footer">
          <el-button @click="handleClose">关闭</el-button>
        </div>
      </div>
    </el-drawer>

    <!-- 仅内容模式 -->
    <div v-else class="tender-failed-content">
      <el-form
        ref="formRef"
        :model="formData"
        :rules="rules"
        label-width="140px"
        class="tender-failed-form"
      >
        <!-- 项目名称 -->
        <el-form-item label="项目名称">
          <el-input
            v-model="formData.projectName"
            readonly
            class="readonly-input"
          />
        </el-form-item>

        <!-- 异常处理方式 -->
        <el-form-item label="异常处理方式" prop="exceptionHandleType" required>
          <el-radio-group v-model="formData.exceptionHandleType" :disabled="props.isEdit">
            <el-radio
              v-for="item in EXCEPTION_HANDLE_TYPE_OPTIONS"
              :key="item.value"
              :label="item.value"
            >
              {{ item.label }}
            </el-radio>
          </el-radio-group>
        </el-form-item>

        <!-- 所属异常分类 -->
        <el-form-item label="所属异常分类" prop="exceptionType" required>
          <el-select
            v-model="formData.exceptionType"
            placeholder="请选择异常分类"
            style="width: 100%"
            :disabled="props.isEdit"
          >
            <el-option
              v-for="item in EXCEPTION_TYPE_OPTIONS"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>

        <!-- 是否发布流标公示 -->
        <el-form-item label="是否发布流标公示" prop="isPublicity" required>
          <el-radio-group v-model="formData.isPublicity" :disabled="props.isEdit">
            <el-radio :label="true">是</el-radio>
            <el-radio :label="false">否</el-radio>
          </el-radio-group>
          <!-- 调试信息 -->
          <div style="font-size: 12px; color: #999; margin-top: 4px;">
            当前值: {{ formData.isPublicity }}
          </div>
        </el-form-item>

        <!-- 异常情况描述 -->
        <el-form-item label="异常情况描述" prop="remark" required>
          <el-input
            v-model="formData.remark"
            type="textarea"
            placeholder="请详细描述异常情况"
            :rows="4"
            maxlength="1000"
            show-word-limit
            :disabled="props.isEdit"
          />
        </el-form-item>

        <!-- 流标公告区域 -->
        <div v-if="formData.isPublicity">
          <div class="header-title">流标公告</div>

          <!-- 公告标题 -->
          <el-form-item label="公告标题" prop="tenderFailedTitle" required>
            <el-input
              v-model="formData.tenderFailedTitle"
              placeholder="请输入公告标题"
              maxlength="100"
              show-word-limit
              :disabled="props.isEdit"
            />
          </el-form-item>

          <!-- 流标公示内容 -->
          <el-form-item label="" prop="tenderFailedContent" label-width="0">
            <div class="rich-editor-container">
              <Toolbar
                :editor="editorRef"
                :defaultConfig="toolbarConfig"
                mode="default"
              />
              <Editor
                style="flex: 1; overflow: hidden; min-height: 300px;"
                v-model="formData.tenderFailedContent"
                :defaultConfig="editorConfig"
                mode="default"
                @onCreated="handleCreated"
                :disabled="props.isEdit"
              />
            </div>
          </el-form-item>

          <!-- 附件 -->
          <el-form-item label="附件">
            <YunUpload
              v-model="attachmentInfos"
              @change="handleUploadFile"
            />
          </el-form-item>
        </div>
      </el-form>

      <!-- 操作按钮 -->
      <div v-if="!hideFooter && !props.isEdit" class="content-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="loading">
          提交
        </el-button>
      </div>
      <div v-else-if="props.isEdit" class="content-footer">
        <el-button @click="handleClose">关闭</el-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, shallowRef, onBeforeUnmount, watch, onMounted } from 'vue'
import { useRoute } from 'vue-router'
import { ElMessage } from 'yun-design'
import type { FormInstance, FormRules } from 'element-plus'
import '@wangeditor/editor/dist/css/style.css'
import { Editor, Toolbar } from '@wangeditor/editor-for-vue'
import type { IDomEditor, IEditorConfig, IToolbarConfig } from '@wangeditor/editor'
import YunUpload from '@/components/YunUpload/index.vue'
import { submitTenderFailed, getTenderFailedDetail } from './api/tenderFailed'
import type { TenderFailedForm } from './types/tenderFailed'
import {
  EXCEPTION_HANDLE_TYPE_OPTIONS,
  EXCEPTION_TYPE_OPTIONS,
  ExceptionHandleType,
  ExceptionType
} from './types/tenderFailed'

interface Props {
  modelValue: boolean
  projectData?: object
  contentOnly?: boolean  // 是否只显示内容，不显示抽屉
  hideFooter?: boolean   // 是否隐藏底部操作按钮（仅在 contentOnly 模式下生效）
  id?: string           // 流标ID，用于详情查看
  isEdit?: boolean      // 是否为编辑模式，true时禁用表单并隐藏提交按钮
}

interface Emits {
  (e: 'update:modelValue', value: boolean): void
  (e: 'success'): void
}

const props = withDefaults(defineProps<Props>(), {
  projectData: () => ({}),
  contentOnly: false,
  hideFooter: false,
  id: '',
  isEdit: false,
})
const emit = defineEmits<Emits>()
const route = useRoute()

const visible = ref(false)

// 获取流标ID（优先使用props，其次使用route）
const tenderId = computed(() => {
  return props.projectData?.failedNoticeId || (route.query.failedNoticeId as string) || ''
})

// 是否为详情模式
const isDetailMode = computed(() => {
  return !!tenderId.value
})

const formRef = ref<FormInstance>()
const loading = ref(false)
const detailLoading = ref(false)
const editorRef = shallowRef<IDomEditor>()
const attachmentInfos = ref<any[]>([])

// 富文本编辑器配置
const toolbarConfig: Partial<IToolbarConfig> = {
  excludeKeys: ['fullScreen'],
}

const editorConfig: Partial<IEditorConfig> = {
  placeholder: '请输入流标公示内容...',
}

// 表单验证规则
const rules: FormRules = {
  exceptionHandleType: [
    { required: true, message: '请选择异常处理方式', trigger: 'change' }
  ],
  exceptionType: [
    { required: true, message: '请选择异常分类', trigger: 'change' }
  ],
  isPublicity: [
    { required: true, message: '请选择是否发布流标公示', trigger: 'change' }
  ],
  remark: [
    { required: true, message: '请输入异常情况描述', trigger: 'blur' },
    { min: 1, max: 1000, message: '异常情况描述长度在1到200个字符', trigger: 'blur' }
  ],
  tenderFailedTitle: [
    {
      required: true,
      message: '请输入公告标题',
      trigger: 'blur',
      validator: (rule, value, callback) => {
        if (formData.isPublicity && !value) {
          callback(new Error('请输入公告标题'))
        } else {
          callback()
        }
      }
    },
    { min: 1, max: 50, message: '公告标题长度在1到50个字符', trigger: 'blur' }
  ],
  tenderFailedContent: [
    {
      required: true,
      message: '请输入流标公示内容',
      trigger: 'blur',
      validator: (rule, value, callback) => {
        if (formData.isPublicity && !value) {
          callback(new Error('请输入流标公示内容'))
        } else {
          callback()
        }
      }
    }
  ]
}

// 表单数据
const formData = reactive<TenderFailedForm>({
  projectName: '',
  noticeId: '',
  projectId: '',
  exceptionHandleType: ExceptionHandleType.RE_PURCHASE,
  exceptionType: ExceptionType.TEMPORARY_NOT_PURCHASE,
  isPublicity: false,
  remark: '',
  tenderFailedTitle: '',
  tenderFailedContent: '',
  attachmentInfos: [],
})

// 获取流标详情
const fetchTenderFailedDetail = async () => {
  if (!tenderId.value) return

  try {
    detailLoading.value = true
    const response = await getTenderFailedDetail(tenderId.value)

    // 更新表单数据
    Object.assign(formData, {
      ...response.data,
      isPublicity: response.data.isPublicity === 1
    })

    // 更新附件信息
    if (response.data.attachmentInfos) {
      attachmentInfos.value = response.data.attachmentInfos.map(item => ({
        name: item.fileName,
        url: item.filePath,
        size: item.fileSize,
        type: item.fileType
      }))
    }

    console.log('流标详情加载成功:', response.data)
  } catch (error) {
    console.error('获取流标详情失败:', error)
    ElMessage.error('获取流标详情失败')
  } finally {
    detailLoading.value = false
  }
}

// 富文本编辑器创建回调
function handleCreated(editor: IDomEditor) {
  editorRef.value = editor
}

// 附件上传处理
const handleUploadFile = (files: any[]) => {
  formData.attachmentInfos = attachmentInfos.value.map(item => ({
    fileName: item.name,
    filePath: item.url,
    fileSize: item.size,
    fileType: item.type
  }))
}

// 监听项目名称变化
watch(() => props.projectData, (newVal) => {
  if (newVal) {
    formData.projectName = newVal.projectName
    formData.projectId = newVal.id
    formData.noticeId = newVal.effectNoticeId
    // if(newVal.failedNoticeId) {
      // tenderId.value = newVal.failedNoticeId
    // }
  }
}, { immediate: true })

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return

  try {
    const valid = await formRef.value.validate()
    if (!valid) return

    loading.value = true

    const submitData = {
      ...formData,
    }

    await submitTenderFailed(submitData)

    ElMessage.success('流标申请提交成功')
    emit('success')
    handleClose()
  } catch (error) {
    console.error('提交流标申请失败:', error)
  } finally {
    loading.value = false
  }
}

// 关闭抽屉
const handleClose = () => {
  formRef.value?.resetFields()
  formData.tenderFailedContent = ''
  attachmentInfos.value = []
  if (!props.contentOnly) {
    visible.value = false
  } else {
    // 在 contentOnly 模式下，触发自定义关闭事件
    emit('update:modelValue', false)
  }
}

// // 组件挂载时获取详情
// onMounted(() => {
//   if (isDetailMode.value) {
//     fetchTenderFailedDetail()
//   }
// })
//
// // 组件销毁时清理编辑器
// onBeforeUnmount(() => {
//   if (editorRef.value) {
//     editorRef.value.destroy()
//   }
// })

watch(() => tenderId.value, (newVal) => {
  if (newVal) {
    fetchTenderFailedDetail()
  }
}, {
  immediate: true
})

defineExpose({
  show: () => {
    visible.value = true
    if (isDetailMode.value) {
      fetchTenderFailedDetail()
    }
  }
})
</script>

<style scoped lang="scss">
.tender-failed-drawer {
  padding: 0 24px 24px;
  height: 100%;
  display: flex;
  flex-direction: column;

  .tender-failed-form {
    flex: 1;
    overflow-y: auto;
    padding-right: 16px;

    .readonly-input {
      :deep(.el-input__inner) {
        background-color: #f5f7fa;
        color: #606266;
      }
    }

    .publicity-section {
      margin-top: 24px;
      padding: 20px;
      background: #f8f9fa;
      border-radius: 6px;
      border: 1px solid #e4e7ed;

      .section-title {
        font-size: 16px;
        font-weight: 600;
        color: #303133;
        margin-bottom: 20px;
        padding-bottom: 10px;
        border-bottom: 2px solid #409eff;
      }


    }
  }

  .drawer-footer {
    display: flex;
    justify-content: end;
    gap: 16px;
  }
}

// 仅内容模式样式
.tender-failed-content {
  padding: 24px;

  .tender-failed-form {
    .readonly-input {
      :deep(.el-input__inner) {
        background-color: #f5f7fa;
        color: #606266;
      }
    }
  }

  .content-footer {
    display: flex;
    justify-content: end;
    gap: 16px;
    margin-top: 24px;
    padding-top: 24px;
    border-top: 1px solid #e4e7ed;
  }
}

.header-title {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  margin: 24px 0 20px 0;
  padding-bottom: 10px;
  border-bottom: 2px solid #409eff;
}
.rich-editor-container {
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  background: #ffffff;
  display: flex;
  flex-direction: column;
  min-height: 350px;

  :deep(.w-e-toolbar) {
    border-bottom: 1px solid #dcdfe6;
  }

  :deep(.w-e-text-container) {
    flex: 1;
  }
}

// 表单项样式调整
:deep(.el-form-item) {
  margin-bottom: 24px;

  .el-form-item__label {
    font-weight: 500;
    color: #303133;
  }

  .el-radio-group {
    .el-radio {
      margin-right: 24px;
    }
  }
}
</style>
