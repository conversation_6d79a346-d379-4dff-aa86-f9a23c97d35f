import request from "@/utils/request"
import type { TenderFailedSubmitParams } from '../types/tenderFailed'

/**
 * 提交流标申请
 * @param data 流标数据
 * @returns
 */
export function submitTenderFailed(data: TenderFailedSubmitParams) {
  return request({
    url: `/admin/tenderOpen/failedTender`,
    method: 'post',
    data
  })
}

/**
 * 获取流标详情
 * @param id 流标ID
 * @returns
 */
export function getTenderFailedDetail(id: string) {
  return request({
    url: `/admin/tenderOpen/failedTender/${id}`,
    method: 'get'
  })
}
