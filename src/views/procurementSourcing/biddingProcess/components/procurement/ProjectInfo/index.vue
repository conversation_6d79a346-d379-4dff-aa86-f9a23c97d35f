<template>
	<div class="procurement-project-container">
		<!-- 左侧锚点导航 -->
		<div class="anchor-navigation">
			<ul class="nav-list">
				<!-- 高亮滑块 -->
				<div
					v-show="isSliderReady"
					class="nav-slider"
					:style="{
						height: sliderStyle.height,
						transform: sliderStyle.transform,
						top: '4px',
					}"
				></div>

				<li
					v-for="anchor in anchorList"
					:key="anchor.id"
					class="nav-item"
					:class="{ active: activeAnchor === anchor.id }"
					@click="scrollToSection(anchor.id)"
				>
					{{ anchor.label }}
				</li>
			</ul>
		</div>

		<!-- 右侧内容区域 -->
		<div class="content-area">
			<div
				:id="'section-' + 'basic'"
				class="content-section"
			>
				<el-collapse
					v-model="activeCollapse.basic"
					class="section-collapse"
					expand-icon-position="left"
				>
					<el-collapse-item
						title="基础信息"
						name="basic"
					>
						<template #title>
							<span class="collapse-title">
								<el-icon class="collapse-icon"><CaretRight /></el-icon>
								基础信息
							</span>
						</template>
						<div class="form-content">
							<el-descriptions
								:column="2"
								class="section-form"
								:label-style="{ width: '120px', textAlign: 'right' }"
								label-class-name="label-right-align"
							>
								<el-descriptions-item label="采购项目">
									{{ projectForm.basicInfo.projectName || '-' }}
								</el-descriptions-item>
								<el-descriptions-item label="项目名称">
									{{ projectForm.basicInfo.name || '-' }}
								</el-descriptions-item>
								<el-descriptions-item label="采购方式">
									{{
										projectForm.basicInfo.procurementMethod === 'public'
											? '公开'
											: projectForm.basicInfo.procurementMethod === 'invite'
											? '邀请'
											: projectForm.basicInfo.procurementMethod || '-'
									}}
								</el-descriptions-item>
								<el-descriptions-item label="建议">
									{{ projectForm.basicInfo.suggestion || '-' }}
								</el-descriptions-item>
								<el-descriptions-item label="采购预算">
									{{ projectForm.basicInfo.budget || '-' }}
								</el-descriptions-item>
								<el-descriptions-item label="预算单位">
									{{
										projectForm.basicInfo.budgetUnit === 'wan'
											? '万元'
											: projectForm.basicInfo.budgetUnit === 'yuan'
											? '元'
											: projectForm.basicInfo.budgetUnit || '-'
									}}
								</el-descriptions-item>
								<el-descriptions-item
									label="采购需求说明"
									:span="2"
								>
									{{ projectForm.basicInfo.description || '-' }}
								</el-descriptions-item>
							</el-descriptions>
						</div>
					</el-collapse-item>
				</el-collapse>
			</div>

			<div
				:id="'section-' + 'timeLimit'"
				class="content-section"
			>
				<el-collapse
					v-model="activeCollapse.timeLimit"
					class="section-collapse"
					expand-icon-position="left"
				>
					<el-collapse-item
						title="项目时间限制"
						name="timeLimit"
					>
						<template #title>
							<span class="collapse-title">
								<el-icon class="collapse-icon"><CaretRight /></el-icon>
								项目时间限制
							</span>
						</template>
						<div class="form-content">
							<el-descriptions
								:column="3"
								class="section-form"
								:label-style="{ width: '120px', textAlign: 'right' }"
								label-class-name="label-right-align"
							>
								<el-descriptions-item label="报名开始时间">
									{{ projectForm.timeLimit.registrationStart ? new Date(projectForm.timeLimit.registrationStart).toLocaleString() : '-' }}
								</el-descriptions-item>
								<el-descriptions-item label="开标时间">
									{{ projectForm.timeLimit.openingTime ? new Date(projectForm.timeLimit.openingTime).toLocaleString() : '-' }}
								</el-descriptions-item>
								<el-descriptions-item label="截止时间">
									{{ projectForm.timeLimit.deadline ? new Date(projectForm.timeLimit.deadline).toLocaleString() : '-' }}
								</el-descriptions-item>
							</el-descriptions>
						</div>
					</el-collapse-item>
				</el-collapse>
			</div>

			<div
				:id="'section-' + 'payment'"
				class="content-section"
			>
				<el-collapse
					v-model="activeCollapse.payment"
					class="section-collapse team-section"
					expand-icon-position="left"
				>
					<el-collapse-item name="payment">
						<template #title>
							<span class="collapse-title">
								<el-icon class="collapse-icon"><CaretRight /></el-icon>
								支付信息
							</span>
						</template>
						<div class="form-content">
							<div class="payment-section">
								<el-table
									:data="projectForm.paymentInfo"
									style="width: 100%"
									class="editable-table"
								>
									<el-table-column
										label="支付方式"
										prop="method"
									/>
									<el-table-column
										label="账期时间"
										prop="accountPeriod"
									/>
									<el-table-column
										label="账期备注"
										prop="accountNotes"
									/>
								</el-table>
							</div>
						</div>
					</el-collapse-item>
				</el-collapse>
			</div>

			<div
				:id="'section-' + 'team'"
				class="content-section"
			>
				<el-collapse
					v-model="activeCollapse.team"
					class="section-collapse team-section"
					expand-icon-position="left"
				>
					<el-collapse-item
						title="项目小组成员"
						name="team"
					>
						<template #title>
							<span class="collapse-title">
								<el-icon class="collapse-icon"><CaretRight /></el-icon>
								项目小组成员
							</span>
						</template>
						<div class="form-content">
							<div class="team-section">
								<el-table
									:data="projectForm.teamMembers"
									style="width: 100%"
									class="editable-table"
								>
									<el-table-column
										label="角色"
										prop="role"
									/>
									<el-table-column
										label="姓名"
										prop="name"
									/>
									<el-table-column
										label="联系电话"
										prop="phone"
									/>
									<el-table-column
										label="所属组织"
										prop="position"
									/>
								</el-table>
							</div>
						</div>
					</el-collapse-item>
				</el-collapse>
			</div>

			<div
				:id="'section-' + 'target'"
				class="content-section"
			>
				<el-collapse
					v-model="activeCollapse.target"
					class="section-collapse team-section"
					expand-icon-position="left"
				>
					<el-collapse-item
						title="标段信息"
						name="target"
					>
						<template #title>
							<span class="collapse-title">
								<el-icon class="collapse-icon"><CaretRight /></el-icon>
								标段信息
							</span>
						</template>
						<div class="form-content">
							<el-descriptions
								:column="1"
								class="section-form"
								:label-style="{ width: '120px', textAlign: 'right' }"
								label-class-name="label-right-align"
							>
								<el-descriptions-item label="标段类型">
									{{
										projectForm.bidSectionInfo.sectionType === 'single'
											? '单标段采购'
											: projectForm.bidSectionInfo.sectionType === 'multiple'
											? '多标段采购'
											: '-'
									}}
								</el-descriptions-item>
							</el-descriptions>

							<!-- 多标段列表表格 -->
							<div
								v-if="projectForm.bidSectionInfo.sectionType === 'multiple'"
								class="bid-sections-container"
							>
								<el-table
									:data="projectForm.bidSectionInfo.sections"
									style="width: 100%"
									class="editable-table"
								>
									<el-table-column
										label="标段序号"
										width="120"
									>
										<template #default="{ $index }">
											{{ String($index + 1).padStart(2, '0') }}
										</template>
									</el-table-column>
									<el-table-column
										label="标段名称"
										prop="sectionName"
									>
										<template #default="{ row }">
											{{ row.sectionName || '-' }}
										</template>
									</el-table-column>
									<el-table-column
										label="标段编号"
										prop="sectionNumber"
									>
										<template #default="{ row }">
											{{ row.sectionNumber || '-' }}
										</template>
									</el-table-column>
								</el-table>
							</div>
						</div>
					</el-collapse-item>
				</el-collapse>
			</div>

			<div
				:id="'section-' + 'list'"
				class="content-section"
			>
				<el-collapse
					v-model="activeCollapse.list"
					class="section-collapse team-section"
					expand-icon-position="left"
				>
					<el-collapse-item
						title="项目清单"
						name="list"
					>
						<template #title>
							<span class="collapse-title">
								<el-icon class="collapse-icon"><CaretRight /></el-icon>
								项目清单
							</span>
						</template>
						<div class="form-content">
							<div class="project-list-section">
								<div class="list-tabs">
									<el-tabs v-model="projectForm.projectList.activeTab">
										<el-tab-pane
											label="材料1"
											name="material1"
										>
											<el-table
												:data="projectForm.projectList.materials.material1"
												style="width: 100%;margin-top: 12px;"

												class="editable-table"
											>
												<el-table-column
													prop="index"
													label="序号"
													width="80"
												/>
												<el-table-column
													prop="category"
													label="类型"
													width="100"
												/>
												<el-table-column
													prop="name"
													label="品牌名称"
												/>
												<el-table-column
													prop="model"
													label="规格型号"
												/>
												<el-table-column
													prop="unit"
													label="单位"
													width="80"
												/>
												<el-table-column
													prop="quantity"
													label="数量参数"
													width="100"
												/>
												<el-table-column
													prop="unitPrice"
													label="单价(元)"
													width="120"
												/>
												<el-table-column
													prop="amount"
													label="合计(元)"
													width="120"
												/>
												<el-table-column
													prop="deliveryDate"
													label="交货日期"
													width="120"
												/>
												<el-table-column
													prop="notes"
													label="备注"
												/>
											</el-table>
										</el-tab-pane>
										<el-tab-pane
											label="材料2"
											name="material2"
										>
										<el-table
												:data="projectForm.projectList.materials.material1"
												style="width: 100%;margin-top: 12px;"

												class="editable-table"
											>
												<el-table-column
													prop="index"
													label="序号"
													width="80"
												/>
												<el-table-column
													prop="category"
													label="类型"
													width="100"
												/>
												<el-table-column
													prop="name"
													label="品牌名称"
												/>
												<el-table-column
													prop="model"
													label="规格型号"
												/>
												<el-table-column
													prop="unit"
													label="单位"
													width="80"
												/>
												<el-table-column
													prop="quantity"
													label="数量参数"
													width="100"
												/>
												<el-table-column
													prop="unitPrice"
													label="单价(元)"
													width="120"
												/>
												<el-table-column
													prop="amount"
													label="合计(元)"
													width="120"
												/>
												<el-table-column
													prop="deliveryDate"
													label="交货日期"
													width="120"
												/>
												<el-table-column
													prop="notes"
													label="备注"
												/>
											</el-table>
										</el-tab-pane>
										<el-tab-pane
											label="材料3"
											name="material3"
										>
										<el-table
												:data="projectForm.projectList.materials.material1"
												style="width: 100%;margin-top: 12px;"

												class="editable-table"
											>
												<el-table-column
													prop="index"
													label="序号"
													width="80"
												/>
												<el-table-column
													prop="category"
													label="类型"
													width="100"
												/>
												<el-table-column
													prop="name"
													label="品牌名称"
												/>
												<el-table-column
													prop="model"
													label="规格型号"
												/>
												<el-table-column
													prop="unit"
													label="单位"
													width="80"
												/>
												<el-table-column
													prop="quantity"
													label="数量参数"
													width="100"
												/>
												<el-table-column
													prop="unitPrice"
													label="单价(元)"
													width="120"
												/>
												<el-table-column
													prop="amount"
													label="合计(元)"
													width="120"
												/>
												<el-table-column
													prop="deliveryDate"
													label="交货日期"
													width="120"
												/>
												<el-table-column
													prop="notes"
													label="备注"
												/>
											</el-table>
										</el-tab-pane>
									</el-tabs>
								</div>
							</div>
						</div>
					</el-collapse-item>
				</el-collapse>
			</div>

			<div
				:id="'section-' + 'attachments'"
				class="content-section"
			>
				<el-collapse
					v-model="activeCollapse.attachments"
					class="section-collapse"
					expand-icon-position="left"
				>
					<el-collapse-item
						title="项目附件"
						name="attachments"
					>
						<template #title>
							<span class="collapse-title">
								<el-icon class="collapse-icon"><CaretRight /></el-icon>
								项目附件
							</span>
						</template>
						<div class="form-content">
							<div class="attachment-section">
								<el-descriptions
									:column="1"
									:label-style="{ width: '120px', textAlign: 'right' }"
									label-class-name="label-right-align"
								>
									<el-descriptions-item label="附件列表">
										<div v-if="projectForm.attachments && projectForm.attachments.length > 0">
											<div
												v-for="(file, index) in projectForm.attachments"
												:key="index"
												style="margin-bottom: 8px"
											>
												<el-link
													type="primary"
													:href="file.url"
													target="_blank"
												>
													{{ file.name }}
												</el-link>
											</div>
										</div>
										<span
											v-else
											style="color: #909399"
											>暂无附件</span
										>
									</el-descriptions-item>
								</el-descriptions>
							</div>
						</div>
					</el-collapse-item>
				</el-collapse>
			</div>
		</div>
	</div>
</template>

<script setup lang="ts">
import { ref, onMounted, nextTick, onUnmounted } from 'vue';
import { CaretRight } from '@element-plus/icons-vue';
import type { ProjectFormData } from '../../../types/project-form';

// 锚点导航数据
const anchorList = ref([
	{ id: 'basic', label: '基础信息' },
	{ id: 'timeLimit', label: '项目时间限制' },
	{ id: 'payment', label: '支付信息' },
	{ id: 'team', label: '项目小组成员' },
	{ id: 'target', label: '标段信息' },
	{ id: 'list', label: '项目清单' },
	{ id: 'attachments', label: '项目附件' },
]);

// 当前活跃的锚点
const activeAnchor = ref('basic');

// 滑块位置
const sliderStyle = ref({
	transform: 'translateY(0px)',
	height: '16px',
	opacity: 0, // 初始隐藏，等待计算完成
});

// 初始化状态
const isSliderReady = ref(false);

// 折叠面板状态
const activeCollapse = ref({
	basic: ['basic'],
	payment: ['payment'],
	timeLimit: ['timeLimit'],
	team: ['team'],
	target: ['target'],
	list: ['list'],
	attachments: ['attachments'],
});

// 统一的项目表单数据
const projectForm = ref<ProjectFormData>({
	// 基础信息
	basicInfo: {
		projectName: '大豆蛋白肽',
		name: '',
		procurementMethod: 'public',
		suggestion: '建议',
		budget: '',
		budgetUnit: 'wan',
		description: '',
	},
	// 支付信息
	paymentInfo: [
		{
			id: 1,
			method: '银行转账',
			accountPeriod: '30天',
			accountNotes: '收到发票后30天内付款',
		},
		{
			id: 2,
			method: '支票支付',
			accountPeriod: '15天',
			accountNotes: '验收合格后15天内付款',
		},
	],
	// 时间限制
	timeLimit: {
		registrationStart: null,
		openingTime: null,
		deadline: null,
	},
	// 团队成员列表
	teamMembers: [
		{
			id: 1,
			role: '项目负责人',
			name: '张三',
			phone: '***********',
			position: '采购部',
		},
	],
	// 标段信息
	bidSectionInfo: {
		sectionType: 'multiple',
		sections: [
			{
				id: 1,
				sectionName: '设备采购标段',
				sectionNumber: '标段01',
			},
			{
				id: 2,
				sectionName: '服务采购标段',
				sectionNumber: '标段02',
			},
			{
				id: 3,
				sectionName: '材料采购标段',
				sectionNumber: '标段03',
			},
		],
	},
	// 项目清单
	projectList: {
		activeTab: 'material1',
		materials: {
			material1: [
				{
					index: 1,
					category: '服务类',
					name: '某种材料',
					model: '规格1',
					unit: '个',
					quantity: '100',
					unitPrice: '27,610.61',
					amount: '2,761,061',
					deliveryDate: '2024/12/2024/12',
					notes: '备注',
				},
			],
			material2: [],
			material3: [],
		},
	},
	// 附件信息
	attachments: [],
});

// 滚动到指定区域
function scrollToSection(sectionId: string) {
	const targetElement = document.getElementById(`section-${sectionId}`);
	const biddingContainer = document.querySelector('.bidding-process-container');

	if (targetElement && biddingContainer) {
		// 立即更新 activeAnchor 和滑块位置
		activeAnchor.value = sectionId;
		updateSliderPosition();

		// 计算目标元素相对于滚动容器的位置
		const containerRect = biddingContainer.getBoundingClientRect();
		const targetRect = targetElement.getBoundingClientRect();
		const scrollTop = biddingContainer.scrollTop;

		// 计算需要滚动到的位置
		const offsetTop = targetRect.top - containerRect.top + scrollTop - 20; // 减去20px间距

		// 滚动容器而不是页面
		biddingContainer.scrollTo({
			top: offsetTop,
			behavior: 'smooth',
		});
	}
}

// 处理滚动事件，更新活跃锚点
function handleScroll() {
	const biddingContainer = document.querySelector('.bidding-process-container') as HTMLElement;
	if (!biddingContainer) return;

	const scrollTop = biddingContainer.scrollTop;
	const containerRect = biddingContainer.getBoundingClientRect();

	const sections = anchorList.value
		.map((anchor) => {
			const element = document.getElementById(`section-${anchor.id}`);
			if (!element) return null;

			const elementRect = element.getBoundingClientRect();
			// 计算相对于滚动容器的位置
			const relativeTop = elementRect.top - containerRect.top + scrollTop;

			return {
				id: anchor.id,
				element,
				offsetTop: relativeTop,
			};
		})
		.filter((item) => item !== null);

	// 找到当前滚动位置对应的区域
	for (let i = sections.length - 1; i >= 0; i--) {
		const section = sections[i];
		if (section && section.offsetTop <= scrollTop + 100) {
			if (activeAnchor.value !== section.id) {
				activeAnchor.value = section.id;
				updateSliderPosition();
			}
			break;
		}
	}
}

// 计算滑块位置
function updateSliderPosition() {
	nextTick(() => {
		const activeItem = document.querySelector('.nav-item.active') as HTMLElement;

		if (activeItem) {
			const relativeTop = activeItem.offsetTop;
			const itemHeight = activeItem.offsetHeight;

			sliderStyle.value = {
				transform: `translateY(${relativeTop}px)`,
				height: `16px`,
				opacity: 1,
			};

			// 首次计算完成后显示滑块
			if (!isSliderReady.value) {
				isSliderReady.value = true;
			}
		}
	});
}

onMounted(() => {
	console.log('采购项目组件已加载');
	// 监听容器滚动事件
	nextTick(() => {
		const biddingContainer = document.querySelector('.bidding-process-container');
		if (biddingContainer) {
			biddingContainer.addEventListener('scroll', handleScroll);
		}
		// 延迟初始化滑块位置，确保DOM完全渲染
		setTimeout(() => {
			updateSliderPosition();
		}, 100);
	});
});

// 组件卸载时移除滚动监听器
onUnmounted(() => {
	const biddingContainer = document.querySelector('.bidding-process-container');
	if (biddingContainer) {
		biddingContainer.removeEventListener('scroll', handleScroll);
	}
});
</script>

<style lang="scss" scoped>
@import '../../../styles/collapse-panel.scss';

// 右对齐 label 样式
:deep(.el-descriptions__label) {
	text-align: right !important;
	justify-content: flex-end !important;
}

// 自定义 label 类名样式
:deep(.label-right-align) {
	text-align: right !important;
}

// 描述列表整体样式优化
:deep(.el-descriptions) {
	.el-descriptions__label {
		text-align: right !important;
		justify-content: flex-end !important;
		padding-right: 12px !important;
	}

	.el-descriptions__content {
		text-align: left !important;
	}
}

// 去除 el-tabs 底部边框
:deep(.el-tabs__header) {
	border-bottom: none !important;
	margin-bottom: 0 !important;
}

:deep(.el-tabs__nav-wrap::after) {
	display: none !important;
}

.procurement-project-container {
	display: flex;
	gap: 20px;
	padding: 20px 20px 20px 20px;
	border-radius: 6px;
	background: var(--Color-Fill-fill-color-blank, #fff);

	// 响应式布局：小屏幕时改为垂直布局
	@media (max-width: 768px) {
		flex-direction: column;
		gap: 16px;
		padding: 16px;
	}

	// 左侧锚点导航
	.anchor-navigation {
		width: 120px; // 恢复固定宽度
		flex-shrink: 0; // 防止在空间不足时被压缩
		position: sticky;
		border-left: 1px solid #e6eaf0;
		top: 20px;
		height: fit-content;

		// 小屏幕时的样式调整
		@media (max-width: 768px) {
			width: 100%;
			position: relative;
			border-left: none;
			border-bottom: 1px solid #e6eaf0;
			padding-bottom: 12px;
			margin-bottom: 12px;

			.nav-list {
				display: flex;
				flex-wrap: wrap;
				gap: 8px;

				.nav-slider {
					display: none; // 小屏幕时隐藏滑块
				}

				.nav-item {
					padding: 4px 8px;
					border: 1px solid #e6eaf0;
					border-radius: 4px;
					font-size: 11px;

					&.active {
						border-color: var(--Color-Primary-color-primary, #0069ff);
						background-color: rgba(0, 105, 255, 0.1);
					}
				}
			}
		}

		.nav-list {
			list-style: none;
			padding: 0;
			margin: 0;
			position: relative; // 为滑块定位提供上下文

			// 高亮滑块
			.nav-slider {
				position: absolute;
				left: -1px;
				width: 1px;
				height: 16px;
				background-color: #0069ff;
				transition: transform 0.2s ease-out;
				will-change: transform;
				z-index: 1;

				// 淡入效果
				&[v-show] {
					transition: transform 0.2s ease-out, opacity 0.3s ease;
				}
			}

			.nav-item {
				padding: 2px 12px;
				cursor: pointer;
				border-radius: 4px;
				transition: all 0.3s;
				color: var(--Light-Light-el-text-color-primary, #1c2026);
				font-family: 'PingFang SC';
				font-size: 12px;
				font-style: normal;
				font-weight: 400;
				line-height: 20px;
				position: relative; // 确保层级关系

				&.active {
					color: var(--Color-Primary-color-primary, #0069ff);
					font-family: 'PingFang SC';
					font-size: 12px;
					font-style: normal;
					font-weight: 600;
					line-height: 20px;
				}
			}
		}
	}

	// 右侧内容区域
	.content-area {
		flex: 1;
		min-width: 0; // 关键！防止内容溢出，确保能够收缩
		padding-right: 20px;

		@media (max-width: 768px) {
			padding-right: 0;
		}

		// 确保内部表单元素也能正确自适应
		.section-form {
			// 响应式表单布局
			.el-row {
				@media (max-width: 768px) {
					.el-col {
						&:not(:last-child) {
							margin-bottom: 16px;
						}
					}
				}

				@media (max-width: 576px) {
					.el-col {
						&[span='12'],
						&[span='8'] {
							span: 24 !important; // 强制全宽
						}
					}
				}
			}

			// 表格容器响应式
			.el-table {
				@media (max-width: 768px) {
					font-size: 12px;
				}
			}

			// 输入框和选择器响应式
			.el-input,
			.el-select,
			.el-date-picker {
				@media (max-width: 576px) {
					width: 100% !important;
				}
			}
		}

		// 全局响应式处理
		@media (max-width: 768px) {
			// 强制表单列变为单列布局
			:deep(.el-col) {
				&.el-col-12,
				&.el-col-8 {
					max-width: 100% !important;
					flex: 0 0 100% !important;
				}
			}

			// 表格在小屏幕上的优化
			:deep(.el-table) {
				.el-table__body-wrapper {
					overflow-x: auto;
				}
			}

			// 折叠面板优化
			:deep(.el-collapse) {
				.el-collapse-item__header {
					padding: 12px 16px;
					font-size: 14px;
				}

				.el-collapse-item__content {
					padding: 16px;
				}
			}
		}

		// 超小屏幕优化
		@media (max-width: 576px) {
			:deep(.el-form-item) {
				margin-bottom: 16px;

				.el-form-item__label {
					font-size: 12px;
					padding-bottom: 4px;
				}

				.el-form-item__content {
					.el-input__inner,
					.el-textarea__inner {
						font-size: 14px;
					}
				}
			}

			// 表格操作按钮响应式
			:deep(.table-actions) {
				display: flex;
				flex-direction: column;
				gap: 4px;

				.el-button {
					font-size: 12px;
					padding: 4px 8px;
				}
			}
		}
	}
}

// 标段信息样式
.bid-sections-container {
	.bid-section-row {
		display: flex;
		align-items: center;
		gap: 12px;
		margin-bottom: 20px;

		.section-item {
			flex: 1;

			.section-label {
				display: block;
				margin-bottom: 6px;
				font-size: 14px;
				color: #606266;

				&.required::before {
					content: '*';
					color: #f56c6c;
					margin-right: 2px;
				}
			}

			.section-name-input,
			.section-number-input {
				width: 100%;
			}
		}

		.section-actions {
			flex-shrink: 0;

			.delete-btn {
				width: 32px;
				height: 32px;
				padding: 0;
				display: flex;
				align-items: center;
				justify-content: center;
			}
		}
	}

	.add-section-btn-container {
		margin-top: 16px;
		text-align: left;

		.add-section-btn {
			border: 1px dashed #409eff;
			background-color: #fff;
			color: #409eff;

			&:hover {
				background-color: #ecf5ff;
			}
		}
	}
}


</style>
