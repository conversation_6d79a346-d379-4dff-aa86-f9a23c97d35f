<template>
	<div class="project-notice-container">
		<div class="project-notice-header">
			<div class="project-notice-header-top">
				<div class="project-notice-header-top-title">采购公告</div>
				<div class="project-notice-header-top-export" @click="handleExportWord">
					<img
						src="https://oss-public.yunlizhi.cn/frontend/wordIcon.svg"
						alt="导出word"
					/>
					<span>导出word</span>
				</div>
			</div>
			<div class="project-notice-header-bottom">
				<div class="summary">公告信息、资格预审要求、报价须响应条件、议程安排情况、资格评审方法</div>
				<div class="static">公告信息浏览量: {{ formData.viewCount || '0' }}</div>
			</div>
		</div>

		<div class="project-notice-content">
      <div v-html="formData.noticeContent"></div>
    </div>
	</div>
  <div class="mt-5">
    <div class="bg-[#fff] p-5">
      <div class="sub-title">相关附件</div>
      <div class="flex gap-4 mt-3">
        <div class="file-label">采购公告附件：</div>
        <div class="flex flex-col gap-2">
          <div class="flex gap-4 items-center" v-for="(item, index) in formData.attachmentInfos" :key="index">
            <div class="file-item-label" @click="handleDownload(item)">{{item.fileName}}</div>
            <!--                <div class="cursor-pointer" @click="handleDownload(item)">-->
            <!--                  <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 12 12" fill="none">-->
            <!--                    <path d="M6.5 1.5L6.5 6.29289L8.25 4.54289L8.95711 5.25L6 8.20711L3.04289 5.25L3.75 4.54289L5.5 6.29289L5.5 1.5L6.5 1.5ZM2.25 7V9.5H9.75V7H10.75V10.5H1.25V7H2.25Z" fill="#0069FF"/>-->
            <!--                  </svg>-->
            <!--                </div>-->
          </div>
        </div>
      </div>
    </div>
  </div>
<!--	<div class="form-actions-wrapper">-->
<!--		<div class="form-actions">-->
<!--			<el-button type="primary"> 我要报名 </el-button>-->
<!--		</div>-->
<!--	</div>-->
</template>

<script setup lang="ts">
import { computed, reactive, ref } from 'vue';
import { ElMessage } from 'yun-design';
import { exportProcurementNoticeToWord } from '@/utils/exportWord';
import { getAnnouncementData } from '@/views/procurementSourcing/biddingProcess/api';
import downloadUrlFile from '@/utils/downloadUrl';
import { useBiddingStore } from '@/views/procurementSourcing/biddingProcess/stores';

const route = useRoute();
const formData = ref({
  noticeContent: ''
})

const biddingStore = useBiddingStore()
const noticeId = computed(() => biddingStore?.noticeId);

// 导出Word文档
const handleExportWord = async () => {
  try {
    // 从内容中提取项目名称（如果有的话）
    const projectName = extractProjectName(formData.value.noticeContent);

    // 调用导出函数
    exportProcurementNoticeToWord(formData.value.noticeContent, projectName);

    // 显示成功提示
    ElMessage.success('Word文档导出成功');
  } catch (error) {
    console.error('导出Word失败:', error);
    ElMessage.error('导出Word文档失败，请稍后重试');
  }
};

// 从HTML内容中提取项目名称
const extractProjectName = (htmlContent: string): string => {
  try {
    // 创建临时DOM元素来解析HTML
    const tempDiv = document.createElement('div');
    tempDiv.innerHTML = htmlContent;

    // 尝试从第一个h2标签中提取项目名称
    const h2Element = tempDiv.querySelector('h2');
    if (h2Element) {
      const fullTitle = h2Element.textContent || '';
      // 移除"公告"、"定标公告"等后缀，提取项目名称
      const projectName = fullTitle
        .replace(/定标公告$/, '')
        .replace(/采购公告$/, '')
        .replace(/公告$/, '')
        .trim();
      return projectName || '采购项目';
    }

    return '采购项目';
  } catch (error) {
    console.error('提取项目名称失败:', error);
    return '采购项目';
  }
};


const init = async () => {
  try {
		if(!noticeId.value) { return; }
    const {data} = await getAnnouncementData(noticeId.value)
    formData.value = data
  } catch (e) {
    console.log(e);
  }
}


const handleDownload = (item : any) => {
  downloadUrlFile(item.filePath, item.fileName);
}

onMounted(() => {
  // biddingStore.initProjectDetail().then(() => {
    if(noticeId.value) {
      init();
    }
  // })
})
</script>

<style scoped lang="scss">
.project-notice-container {
  color: #4E5969;
	flex: 1;
	display: flex;
	flex-direction: column;
	.project-notice-header {
		display: flex;
		flex-direction: column;
		background: #fff;
		padding: 20px;
		border-radius: 4px;
		gap: 12px;

		&-top {
			display: flex;
			align-content: center;
			justify-content: space-between;

			&-title {
				color: var(--Color-Text-text-color-primary, #1d2129);
				font-family: 'PingFang SC';
				font-size: 16px;
				font-style: normal;
				font-weight: 600;
				line-height: 24px;
			}

			&-export {
				display: flex;
				align-items: center;
				gap: 6px;
				color: var(--Color-Text-text-color-regular, #4e5969);
				text-align: center;
				font-family: 'PingFang SC';
				font-size: 12px;
				font-style: normal;
				font-weight: 500;
				line-height: 20px;
				cursor: pointer;
				padding: 4px 8px;
				border-radius: 4px;
				transition: all 0.3s ease;

				&:hover {
					background-color: #f5f7fa;
					color: var(--Color-Primary-color-primary, #0069ff);
				}

				&:active {
					transform: translateY(1px);
				}
			}
		}

		&-bottom {
			display: flex;
			gap: 24px;

			.summary {
				color: var(--Color-Text-text-color-regular, #4e5969);
				font-family: 'PingFang SC';
				font-size: 13px;
				font-style: normal;
				font-weight: 400;
				line-height: 22px;
			}

			.static {
				color: var(--Color-Warning-color-warning, #ff9500);
				font-family: 'PingFang SC';
				font-size: 13px;
				font-style: normal;
				font-weight: 400;
				line-height: 22px;
			}
		}
	}

	.project-notice-content {
		flex: 1;
		background: #fff;
		margin-top: 20px;
		border-radius: 4px;
    display: flex;
    padding: 32px 40px;
    flex-direction: column;
    gap: 16px;
    align-self: stretch;
	}
}
.form-actions-wrapper {
	width: 100%;
	box-sizing: border-box;
	background-color: #fff;
	z-index: 1000;
	position: sticky;
	bottom: -20px;
	left: 0;
	right: 0;
	padding: 16px 24px;
	display: flex;
	gap: 12px;
	border-top: 1px solid var(--Color-Border-border-color-light, #e4e7ed);

	.form-actions {
		display: flex;
		justify-content: center;
		gap: 12px;
		width: 100%;
	}
}


.sub-title {
  color: var(--Color-Text-text-color-primary, #1D2129);
  font-size: 16px;
  font-weight: 600;
  line-height: 24px; /* 150% */
}

.file-label {
  color: var(--Color-Text-text-color-regular, #4E5969);
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 22px; /* 157.143% */
}

.file-item-label {
  color: var(--Color-Primary-color-primary, #0069FF);
  font-size: 14px;
  font-weight: 400;
  cursor: pointer;
}
</style>
