<template>
  <div class="bid-evaluation-committee-container need-hide-table-card">
    <yun-pro-table
      ref="proTableRef"
      v-model:pagination="pagination"
      :table-columns="columns"
      :auto-height="true"
      :remote-method="remoteMethod"
      :table-props="tablePropsObj"
      layout="whole"
    >
      <template #t_action="{ row }">
        <el-button
          type="text"
          size="small"
          v-if="row.canExtract && hasEvaluationAuth"
          @click.prevent="handler(row, OPERATION_TYPE_ENUM.ADD)"
        >
          抽取
        </el-button>
        <el-button
          type="text"
          size="small"
          v-if="row.canViewDetail"
          @click.prevent="handler(row, OPERATION_TYPE_ENUM.VIEW)"
        >
          查看详情
        </el-button>
        <el-button
          type="text"
          size="small"
          v-if="row.canReExtract && hasEvaluationAuth"
          @click.prevent="handler(row, OPERATION_TYPE_ENUM.EDIT)"
        >
          重新抽取
        </el-button>
      </template>
    </yun-pro-table>

    <Form
      ref="formRef"
      @refresh="handleRefresh"
    />
  </div>
</template>

<script lang="ts" setup>
import { ref, computed } from 'vue';
import { useProTable } from '@ylz-use/core';
import { pageSections } from '@/api/purchasing/evaluation';
import { useColumns } from './hooks/useColumns';
import { OPERATION_TYPE_ENUM } from './const';
import Form from './components/Form/index.vue';
import { useBiddingStore } from '@/views/procurementSourcing/biddingProcess/stores';
import { isProjectMember } from '@/views/procurementSourcing/biddingProcess/utils/bid';

const biddingStore = useBiddingStore();
const projectId = computed(() => biddingStore?.projectId);
const formRef = ref();
// const pagination = ref({});

// 项目负责人/开标人
const hasEvaluationAuth = computed(() => {
  const isProjectLeader = isProjectMember(biddingStore.projectDetail?.projectMemberList) === 'PROJECT_LEADER';
  return isProjectLeader || biddingStore?.isBidOpener;
});

const { columns, searchFields } = useColumns();
const { remoteMethod, tableProps, proTableRef, pagination } = useProTable({
  apiFn: pageSections,
  responseHandler(result: any) {
    return result.data?.records || [];
  },
  customTotalHandler(data: any) {
    return data?.data?.total;
  },
  paramsHandler(params: any) {
    return {
      current: pagination.value.page,
      size: pagination.value.size || pagination.value.pageSize,
      projectId: projectId.value,
      ...params,
    };
  },
  // querysHandler() {
  //   const querysData = {
  //     current: pagination.value.page,
  //     size: pagination.value.size || pagination.value.pageSize,
  //   };
  //   return querysData;
  // },
  plugins: {
    config: {
      columns: columns.value,
      searchFields: searchFields.value,
    },
    list: ['SEARCH_PLUS'],
  },
});
const tablePropsObj = computed(() => ({
  ...tableProps.value,
  stripe: true,
  border: true,
  headerCellStyle: {
    backgroundColor: '#f5f7fa',
    color: '#303133',
    fontWeight: 'bold',
    height: '40px',
  },
  cellStyle: {
    padding: '0',
    height: '40px',
    'vertical-align': 'middle',
  },
  rowStyle: {
    height: '40px',
  },
  rowHeight: 40,
}));
function handler(row: any, type: any) {
  formRef.value?.show({ row, type });
}

function handleRefresh() {
  proTableRef.value?.getData();
}
</script>

<style lang="scss" scoped>
// @import '@/views/procurementSourcing/biddingProcess/styles/collapse-panel.scss';
.bid-evaluation-committee-container {
  width: 100%;
  height: 100%;
  background-color: #fff;
  flex: 1;
  padding: 20px;
  box-sizing: border-box;

}
</style>
