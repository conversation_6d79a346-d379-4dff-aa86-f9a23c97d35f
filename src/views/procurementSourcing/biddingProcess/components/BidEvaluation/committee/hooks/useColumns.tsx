import { ref, computed } from 'vue';
import { EXTRACT_STATUS_OPTIONS, EXTRACT_STATUS_TAGS } from '@/views/procurementSourcing/biddingProcess/components/BidEvaluation/committee/const';
import CommonTag from '@/views/procurementSourcing/biddingProcess/components/BidEvaluation/components/CommonTag.vue';

export function useColumns() {
  const searchFields = ref([]);
  const searchData = ref({});
  const columns = computed(() => {
    return [
      {
        label: '序号',
        type: 'index',
        width: '60px',
      },
      {
        label: '项目名称',
        prop: 'projectName',
      },
      {
        label: '标段名称',
        prop: 'sectionName',
      },
      {
        label: '专家抽取状态',
        prop: 'expertExtractionStatus',
        enums: EXTRACT_STATUS_OPTIONS,
        render: ({ row }: any) => {
          const expertExtractionStatus = row?.expertExtractionStatus;
          return expertExtractionStatus ? <CommonTag {...(EXTRACT_STATUS_TAGS[expertExtractionStatus] || {})} /> : '-';
        },
      },
      {
        label: '操作人',
        prop: 'operatorName',
      },
      {
        label: '操作时间',
        prop: 'operationTime',
        width: '180px',
      },
      {
        label: '操作',
        prop: 'action',
        isSlot: true,
        width: '160px',
        fixed: 'right',
      },
    ];
  });

  return {
    columns,
    searchFields,
    searchData,
  };
}
