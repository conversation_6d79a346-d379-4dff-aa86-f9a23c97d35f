import { ref } from 'vue';
import { getListBySectionId } from '@/api/purchasing/evaluation';
export function useHandler() {
  const loading = ref(false);
  const basic = ref<any>({});
  async function getListBySectionIdData(sectionId: string) {
    loading.value = true;
    basic.value = {};
    try {
      const res: any = await getListBySectionId(sectionId);
      basic.value = res?.data || {};
      // eslint-disable-next-line no-empty
    } catch (error) {}
  }

  return {
    loading,
    basic,
    getListBySectionIdData,
  };
}
