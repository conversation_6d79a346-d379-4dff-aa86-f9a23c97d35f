// 对象转数组
function objToArr(obj: any) {
  return Object.entries(obj).map(([value, label]) => ({
    label,
    value,
  }));
}

export const STATUS_ENUM = {
  ENABLED: "ENABLED",
  DISABLED: "DISABLED",
}

export const STATUS_OBJ = {
  [STATUS_ENUM.ENABLED]: '启用',
  [STATUS_ENUM.DISABLED]: '禁用',
}

export const STATUS_OPTIONS = objToArr(STATUS_OBJ);



// 操作类型
export const OPERATION_TYPE_ENUM = {
  EDIT: "EDIT",
  ADD: "ADD",
  VIEW: "VIEW",
}


// 评标成员角色信息
export const EVALUATION_MEMBER_ROLE_ENUM = {
  EVALUATION_LEADER: "EVALUATION_LEADER",
  EVALUATION_MEMBER: "EVALUATION_MEMBER",
  PROJECT_LEADER: "PROJECT_LEADER",
  PROJECT_MEMBER: "PROJECT_MEMBER",
}
export const EVALUATION_MEMBER_ROLE_OBJ = {
  [EVALUATION_MEMBER_ROLE_ENUM.EVALUATION_LEADER]: '评标小组负责人',
  [EVALUATION_MEMBER_ROLE_ENUM.EVALUATION_MEMBER]: '评标小组成员',
  [EVALUATION_MEMBER_ROLE_ENUM.PROJECT_LEADER]: '项目负责人',
  [EVALUATION_MEMBER_ROLE_ENUM.PROJECT_MEMBER]: '项目成员',
}
export const EVALUATION_MEMBER_ROLE_OPTIONS = objToArr(EVALUATION_MEMBER_ROLE_OBJ)?.filter((item: any) => item.value !== EVALUATION_MEMBER_ROLE_ENUM.PROJECT_MEMBER && item.value !== EVALUATION_MEMBER_ROLE_ENUM.PROJECT_LEADER);

// 专家抽取状态
export const EXTRACT_STATUS_ENUM = {
  PENDING: "PENDING",
  EXTRACTED: "EXTRACTED",
}
export const EXTRACT_STATUS_OBJ = {
  [EXTRACT_STATUS_ENUM.PENDING]: '待抽取',
  [EXTRACT_STATUS_ENUM.EXTRACTED]: '已抽取',
}
export const EXTRACT_STATUS_OPTIONS = objToArr(EXTRACT_STATUS_OBJ);
export const EXTRACT_STATUS_TAGS: Record<string, any> = {
  [EXTRACT_STATUS_ENUM.PENDING]: {
    text: '待抽取',
    backgroundColor: '#FFF9E8',
    color: '#D27400',
  },
  [EXTRACT_STATUS_ENUM.EXTRACTED]: {
    text: '已抽取',
    backgroundColor: '#E8FFEA',
    color: '#009A29',
  },
}

// 汇总状态
export const SUMMARY_STATUS_ENUM = {
  PENDING: "PENDING",
  SUMMARIZED: "SUMMARIZED",
  SIGNED: "SIGNED",
}
export const SUMMARY_STATUS_OBJ = {
  [SUMMARY_STATUS_ENUM.PENDING]: '待汇总',
  [SUMMARY_STATUS_ENUM.SUMMARIZED]: '已汇总',
  [SUMMARY_STATUS_ENUM.SIGNED]: '已签名',
}
export const SUMMARY_STATUS_TAGS = {
  [SUMMARY_STATUS_ENUM.PENDING]: {
    text: '待汇总',
    backgroundColor: '#FFF9E8',
    color: '#D27400',
  },
  [SUMMARY_STATUS_ENUM.SUMMARIZED]: {
    text: '已汇总',
    backgroundColor: '#E8FFEA',
    color: '#009A29',
  },
  [SUMMARY_STATUS_ENUM.SIGNED]: {
    text: '已签名',
    backgroundColor: '#E8FFEA',
    color: '#009A29',
  },
}

// 专家分类
export const ENTERPRISE_NATURE_ENUM = {
  INTERNAL: "INTERNAL",
  EXTERNAL: "EXTERNAL",
}
export const ENTERPRISE_NATURE_OBJ = {
  [ENTERPRISE_NATURE_ENUM.INTERNAL]: '内部专家',
  [ENTERPRISE_NATURE_ENUM.EXTERNAL]: '外部专家',
}
export const ENTERPRISE_NATURE_OPTIONS = objToArr(ENTERPRISE_NATURE_OBJ);
export const ENTERPRISE_NATURE_TAGS: Record<string, any> = {
  [ENTERPRISE_NATURE_ENUM.INTERNAL]: {
    text: '内部专家',
    backgroundColor: '#E8F3F5',
    color: '#126A7A',
  },
  [ENTERPRISE_NATURE_ENUM.EXTERNAL]: {
    text: '外部专家',
    backgroundColor: '#F0EDF8',
    color: '#563F96',
  },
}

// 专家类型
// BUSINESS_EXPERT("商务专家"),
// TECHNICAL_EXPERT("技术专家"),
// AUDIT_EXPERT("审计专家"),
// OTHER("其它");
export const EXPERT_TYPE_ENUM = {
  BUSINESS_EXPERT: "BUSINESS_EXPERT",
  TECHNICAL_EXPERT: "TECHNICAL_EXPERT",
  AUDIT_EXPERT: "AUDIT_EXPERT",
  OTHER: "OTHER",
}
export const EXPERT_TYPE_OBJ = {
  [EXPERT_TYPE_ENUM.BUSINESS_EXPERT]: '商务专家',
  [EXPERT_TYPE_ENUM.TECHNICAL_EXPERT]: '技术专家',
  [EXPERT_TYPE_ENUM.AUDIT_EXPERT]: '审计专家',
  [EXPERT_TYPE_ENUM.OTHER]: '其它',
}
export const EXPERT_TYPE_OPTIONS = objToArr(EXPERT_TYPE_OBJ);