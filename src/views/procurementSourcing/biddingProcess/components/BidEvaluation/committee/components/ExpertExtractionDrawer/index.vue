<template>
  <yun-drawer
    v-model="showVisible"
    size="X-large"
    title="专家抽取"
    append-to-body
    destroy-on-close
    @confirm="handleConfirm"
    custom-class="bid-custom-drawer"
    confirm-button-text="提交"
  >
    <div class="expert-extraction-drawer-container">
      <yun-pro-table
        ref="proTableRef"
        v-model:pagination="pagination"
        v-model:filter-data="filterTableData"
        v-model:searchData="searchData"
        :table-columns="columns"
        :search-fields="searchFields"
        :auto-height="true"
        :global-selection="true"
        :remote-method="remoteMethod"
        v-model:selected="selectedRow"
        :selection-key="'phone'"
        :table-props="tablePropsObj"
        layout="whole"
      >
      </yun-pro-table>
    </div>
  </yun-drawer>
</template>

<script lang="ts" setup>
import { ref, computed } from 'vue';
import { useColumns } from './hooks/useColumns';
import { useProTable } from '@ylz-use/core';
import { ElMessage } from 'yun-design';
import { baseExpertPage } from '@/api/purchasing/evaluation';

const showVisible = ref(false);
const selectedRow = ref<any[]>([]);
const emits = defineEmits(['confirm']);

function show({ selectedRow: rowList }: any = {}) {
  selectedRow.value = JSON.parse(JSON.stringify(rowList || []));
  showVisible.value = true;
}
const tablePropsObj = computed(() => ({
  ...tableProps.value,
  stripe: true,
  border: true,
  headerCellStyle: {
    backgroundColor: '#f5f7fa',
    color: '#303133',
    fontWeight: 'bold',
    height: '40px',
  },
  cellStyle: {
    padding: '0',
    height: '40px',
    'vertical-align': 'middle',
  },
  rowStyle: {
    height: '40px',
  },
  rowHeight: 40,
  rowKey: 'phone',
}));

function handleConfirm(done: any, loading: any) {
  if (selectedRow.value.length === 0) {
    ElMessage.warning('请选择数据');
    return;
  }
  loading.value = true;
  done();
  emits('confirm', JSON.parse(JSON.stringify(selectedRow.value)));
  loading.value = false;
}

const { columns, searchFields, searchData } = useColumns();
const { pagination, remoteMethod, tableProps, proTableRef, filterTableData } = useProTable({
  apiFn: baseExpertPage,
  responseHandler(result: any) {
    return result.data?.records || [];
  },
  customTotalHandler(data: any) {
    return data?.data?.total;
  },
  paramsHandler(params: any) {
    return {
      ...params,
    };
  },
  querysHandler() {
    const querysData = {
      current: pagination.value.page,
      size: pagination.value.size || pagination.value.pageSize,
    };
    return querysData;
  },
  plugins: {
    config: {
      columns: columns.value,
      searchFields: searchFields.value,
    },
    list: ['SEARCH_PLUS'],
  },
});

defineExpose({
  show,
});
</script>

<style lang="scss" scoped>
.expert-extraction-drawer-container {
  height: 98%;
  :deep(.el-card__body) {
    padding: 0 !important;
  }
}
</style>
