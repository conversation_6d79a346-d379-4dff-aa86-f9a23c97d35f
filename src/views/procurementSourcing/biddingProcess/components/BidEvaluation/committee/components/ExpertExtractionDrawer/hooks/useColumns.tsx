import { ref, computed } from 'vue';
import { filterUtils } from '@ylz-use/core';
import {
  ENTERPRISE_NATURE_OPTIONS,
  EXPERT_TYPE_OPTIONS,
  EXPERT_TYPE_OBJ,
  ENTERPRISE_NATURE_TAGS,
} from '@/views/procurementSourcing/biddingProcess/components/BidEvaluation/committee/const';
import CommonTag from '@/views/procurementSourcing/biddingProcess/components/BidEvaluation/components/CommonTag.vue';

export function useColumns() {
  const { inputHeader, selectHeader } = filterUtils || {};
  const searchData = ref({});
  const columns = computed(() => {
    return [
      {
        type: 'selection',
        width: 52,
        fixed: 'left',
      },
      {
        label: '专家分类',
        prop: 'expertCategory',
        enums: ENTERPRISE_NATURE_OPTIONS,
        header: selectHeader({
          options: ENTERPRISE_NATURE_OPTIONS,
          attrs: {
            multiple: true,
          },
        }),
        // minWidth:120,
        filterAlias: 'expertCategoryList',
        render: ({ row }: any) => {
          const expertCategory = row?.expertCategory;
          return expertCategory ? <CommonTag {...(ENTERPRISE_NATURE_TAGS[expertCategory] || {})} /> : '-';
        },
      },
      {
        label: '姓名',
        prop: 'name',
        header: inputHeader(),
      },
      {
        label: '手机号',
        prop: 'phone',
        header: inputHeader(),
      },
      {
        label: '身份证号码',
        prop: 'idNumber',
        minWidth: '180px',
        header: inputHeader(),
      },
      {
        label: '专家类型',
        prop: 'expertType',
        enums: EXPERT_TYPE_OPTIONS,
        header: selectHeader({
          options: EXPERT_TYPE_OPTIONS,
          attrs: {
            // multiple: true,
          },
        }),
        formatter: (row: any) => {
          const temp = row.expertType?.split(',') || [];
          return temp.map((item: any) => EXPERT_TYPE_OBJ[item]).join(',') || '-';
        },
      },
      {
        label: '邮箱',
        prop: 'email',
        header: inputHeader(),
      },
    ];
  });

  const searchFields = ref([
    {
      label: '姓名',
      prop: 'name',
      type: 'text',
    },
    {
      label: '专家分类',
      prop: 'expertCategoryList',
      type: 'select',
      componentAttrs: {
        filterable: true,
        multiple: true,
        clearable: true,
      },
      options: ENTERPRISE_NATURE_OPTIONS,
    },
    {
      label: '手机号',
      prop: 'phone',
      type: 'text',
    },
  ]);

  return {
    columns,
    searchFields,
    searchData,
  };
}
