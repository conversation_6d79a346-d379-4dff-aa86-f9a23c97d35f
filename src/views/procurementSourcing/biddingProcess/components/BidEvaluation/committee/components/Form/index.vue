<template>
  <yun-drawer
    v-model="showVisible"
    size="X-large"
    :title="title"
    append-to-body
    destroy-on-close
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :show-cancel-button="operationType !== OPERATION_TYPE_ENUM.VIEW"
    :confirm-button-text="operationType === OPERATION_TYPE_ENUM.VIEW ? '关闭' : '确定'"
    @confirm="handleConfirm"
    custom-class="bid-custom-drawer"
  >
    <div class="bid-evaluation-committee-form-container" v-loading="pageLoading">
      <div class="bid-evaluation-committee-header">
        <div class="item">
          <div class="item-title">操作人</div>
          <div class="item-value">{{ currentRow.operatorName || '-' }}</div>
        </div>
        <div class="item">
          <div class="item-title">设置时间</div>
          <div class="item-value">{{ currentRow.operationTime ? moment(currentRow.operationTime).format('YYYY-MM-DD HH:mm') : '-' }}</div>
        </div>
        <div class="item">
          <div class="item-title">专家抽取状态</div>
          <div class="item-value">
            <CommonTag
              v-if="currentRow.expertExtractionStatus"
              v-bind="EXTRACT_STATUS_TAGS[currentRow.expertExtractionStatus]"
            />
            <span v-else>-</span>
          </div>
        </div>
      </div>
      <div class="bid-evaluation-committee-form">
        <div class="form-section">
          <div class="label-header">评标委员会</div>
        </div>
        <yun-pro-form
          ref="formRef"
          :form="form"
          :columns="formColumns"
          :config="config"
          :form-props="{ labelPosition: 'top', labelWidth: '240px' }"
          v-if="operationType !== OPERATION_TYPE_ENUM.VIEW"
        />
        <yun-pro-detail
          :detail="form"
          :config="detailConfig"
          :columns="detailColumns"
          v-else
        />
      </div>
      <div class="bid-evaluation-committee-table">
        <el-button
          v-if="operationType !== OPERATION_TYPE_ENUM.VIEW"
          class="expert-extraction-btn"
          type="primary"
          @click="onExpertExtraction"
          border
          :icon="Plus"
          >专家抽取</el-button
        >
        <div class="form-section">
          <div class="label-header">评标专家组</div>
        </div>
        <div class="table-wrapper">
          <yun-pro-table
            ref="proTableRef"
            :table-columns="columns"
            :auto-height="true"
            :table-props="tablePropsObj"
            layout="whole"
            v-model:tableData="form.memberList"
          >
            <template #t_role="{ row }">
              <el-select
                v-model="row.role"
                placeholder="请选择"
                v-if="operationType !== OPERATION_TYPE_ENUM.VIEW"
                :disabled="operationType === OPERATION_TYPE_ENUM.VIEW"
              >
                <el-option
                  v-for="item in EVALUATION_MEMBER_ROLE_OPTIONS"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
              <span v-else>{{ row.roleName || '-' }}</span>
            </template>
            <template #t_action="{ row, $index }">
              <el-link
                type="danger"
                @click="handleDelete(row, $index)"
                >删除</el-link
              >
            </template>
          </yun-pro-table>
        </div>
      </div>
    </div>
    <ExpertExtractionDrawer
      @confirm="handleExpertExtractionConfirm"
      ref="expertExtractionDrawerRef"
    />
  </yun-drawer>
</template>

<script lang="ts" setup>
import { ref, computed } from 'vue';
import { useColumns } from './hooks/useColumns';
import { useProTable } from '@ylz-use/core';
import { ElMessage } from 'yun-design';
import {
  OPERATION_TYPE_ENUM,
  EXTRACT_STATUS_TAGS,
  EVALUATION_MEMBER_ROLE_OPTIONS,
  EVALUATION_MEMBER_ROLE_ENUM,
  EVALUATION_MEMBER_ROLE_OBJ,
} from '@/views/procurementSourcing/biddingProcess/components/BidEvaluation/committee/const';
import CommonTag from '@/views/procurementSourcing/biddingProcess/components/BidEvaluation/components/CommonTag.vue';
import { Plus } from '@yun-design/icons-vue';
import ExpertExtractionDrawer from '@/views/procurementSourcing/biddingProcess/components/BidEvaluation/committee/components/ExpertExtractionDrawer/index.vue';
// @ts-ignore
import { useForm } from '@/hooks/useForm';
import { saveEvaluation, updateEvaluation } from '@/api/purchasing/evaluation';
import { useBiddingStore } from '@/views/procurementSourcing/biddingProcess/stores';
import { useHandler } from '@/views/procurementSourcing/biddingProcess/components/BidEvaluation/committee/hooks/useHandler';
import moment from 'moment';
import { useUserInfo } from '/@/stores/userInfo';

const stores = useUserInfo();
const { getListBySectionIdData, basic } = useHandler();
const biddingStore = useBiddingStore();
const projectDetail = computed(() => biddingStore?.projectDetail);
const emits = defineEmits(['refresh']);
const { formRef, form, config, validateBasicForm, resetForm, setForm } = useForm();
const { tableColumns, formColumns, detailColumns, detailConfig } = useColumns();
const columns = computed(() => {
  if (operationType.value === OPERATION_TYPE_ENUM.VIEW) {
    return tableColumns.value?.filter((item: any) => item.prop !== 'action');
  }
  return tableColumns.value;
});
const expertExtractionDrawerRef = ref();
const showVisible = ref(false);
const pageLoading = ref(false);
const currentRow = ref<any>({});
const operationType = ref('');
const title = computed(() => {
  if (operationType.value === OPERATION_TYPE_ENUM.ADD) {
    return '新建评标委员会';
  }
  if (operationType.value === OPERATION_TYPE_ENUM.EDIT) {
    return '编辑评标委员会';
  }
  return '评标委员会详情';
});

const { tableProps, proTableRef } = useProTable({});

const tablePropsObj = computed(() => ({
  ...tableProps.value,
  stripe: true,
  border: true,
  headerCellStyle: {
    backgroundColor: '#f5f7fa',
    color: '#303133',
    fontWeight: 'bold',
    height: '40px',
  },
  cellStyle: {
    padding: '0',
    height: '40px',
    'vertical-align': 'middle',
  },
  rowStyle: {
    height: '40px',
  },
  rowHeight: 40,
}));

// 初始化表单
function initForm() {
  const {
    evaluationId: id,
    projectId,
    sectionId,
    noticeId,

    name,
    address,
    contacts,
    contactsPhone,
    memberList = [],
  } = currentRow.value || {};
  setForm({
    // 默认值
    id,
    projectId,
    sectionId,
    noticeId,

    // 用于表单回显
    name,
    address,
    contacts: contacts || projectDetail.value?.effectNotice?.contactPerson,
    contactsPhone: contactsPhone || projectDetail.value?.effectNotice?.contactPhone,

    memberList,
  });
}

function handleExpertExtractionConfirm(rowList: any[]) {
  const memberList = rowList.map((item: any) => ({
    ...item,
    role: item?.role || EVALUATION_MEMBER_ROLE_ENUM.EVALUATION_MEMBER,
  }));
  setForm({
    memberList: JSON.parse(JSON.stringify(memberList)),
  });
}

// 格式化成员信息
function formatMemberList(memberList: any[]) {
  if (!memberList || memberList.length === 0 || !Array.isArray(memberList)) {
    return [];
  }
  return memberList.map((item: any) => {
    const { contactPhone: phone, expertName: name, idNumber, role, roleName } = item || {};
    return {
      ...item,
      name,
      phone,
      idNumber,
      roleName: EVALUATION_MEMBER_ROLE_OBJ[role] || roleName,
    };
  });
}

async function show({ row, type }: { row: any; type: string }) {
  pageLoading.value = true;
  currentRow.value = row || {};
  operationType.value = type || '';
  showVisible.value = true;
  resetForm();
  initForm();

  // 新增时候特殊赋值
  if (type === OPERATION_TYPE_ENUM.ADD) {
    currentRow.value = {
      ...currentRow.value,
      operatorName: stores?.userInfos?.user?.name || currentRow.value?.operatorName || '',
      operationTime: moment().format('YYYY-MM-DD HH:mm'),
    };
  }

  // 回填一些信息
  if ([OPERATION_TYPE_ENUM.VIEW, OPERATION_TYPE_ENUM.EDIT].includes(type)) {
    try {
      await getListBySectionIdData(row.sectionId);
      const { name, address, contacts, contactsPhone, memberList } = basic.value || {};
      setForm({ name, address, contacts, contactsPhone, memberList: formatMemberList(memberList) });

      // 防止未查到数据时候  回先系统数据默认值
      if (!contacts) {
        setForm({ contacts: projectDetail.value?.effectNotice?.contactPerson });
      }
      if (!contactsPhone) {
        setForm({ contactsPhone: projectDetail.value?.effectNotice?.contactPhone });
      }
    // eslint-disable-next-line no-empty
    } catch (error) {}
  }
  pageLoading.value = false;
}

function handleDelete(row: any, index: number) {
  form.value.memberList.splice(index, 1);
}
function onExpertExtraction() {
  expertExtractionDrawerRef.value?.show({
    selectedRow: form.value.memberList || [],
  });
}

async function handleConfirm(done: any, loading: any) {
  if (operationType.value === OPERATION_TYPE_ENUM.VIEW) {
    showVisible.value = false;
    return;
  }
  await validateBasicForm();
  loading.value = true;
  const params = {
    ...form.value,
    memberList: form.value.memberList.map((item: any) => {
      return {
        id: item.id || null,
        userId: item.userId || null,
        role: item.role || null,
        contactPhone: item.phone || null,
      };
    }),
  };

  const members = params?.memberList?.filter((item: any) => item.role === EVALUATION_MEMBER_ROLE_ENUM.EVALUATION_MEMBER);
  const leaders = params?.memberList?.filter((item: any) => item.role === EVALUATION_MEMBER_ROLE_ENUM.EVALUATION_LEADER);
  if (leaders.length > 1) {
    ElMessage.error('【评标小组负责人】只能有一个');
    loading.value = false;
    return;
  }
  if (members.length === 0) {
    ElMessage.error('至少选择一个【评标小组成员】');
    loading.value = false;
    return;
  }
  if (leaders.length === 0) {
    ElMessage.error('请选择一个【评标小组负责人】');
    loading.value = false;
    return;
  }

  const apiFn = operationType.value === OPERATION_TYPE_ENUM.ADD ? saveEvaluation : updateEvaluation;
  try {
    await apiFn(params);
    ElMessage.success('操作成功');
    done();
    emits('refresh');
    loading.value = false;
    showVisible.value = false;
  } catch (error) {
    loading.value = false;
  }
}

defineExpose({
  show,
});
</script>

<style lang="scss" scoped>
.bid-evaluation-committee-form-container {
  height: 100%;
  display: flex;
  flex-direction: column;
  .form-section {
    border-bottom: none;
  }
  :deep(.el-card__body) {
    padding: 0;
  }
  :deep(.yun-descriptions) {
    padding: 0;
  }
  :deep(.yun-description-item_content) {
    margin-bottom: 0;
  }
  .bid-evaluation-committee-form {
    margin-bottom: 20px;
  }
  .bid-evaluation-committee-header {
    height: 36px;
    padding-bottom: 6px;
    border-bottom: 1px solid var(--Color-Border-border-color-light, #e4e7ed);
    display: flex;
    align-items: center;
    gap: 20px;
    box-sizing: content-box;
    .item {
      display: flex;
      align-items: center;
      justify-content: space-between;
      font-size: 14px;
      font-family: 'PingFang SC';
      font-style: normal;
      font-weight: 400;
      line-height: 22px; /* 157.143% */
      .item-title {
        color: var(--Color-Text-text-color-secondary, #86909c);
        margin-right: 8px;
      }
      .item-value {
        font-weight: 500;
        color: var(--Color-Text-text-color-primary, #1d2129);
      }
    }
  }
  .bid-evaluation-committee-table {
    flex: 1;
    position: relative;
    .expert-extraction-btn {
      position: absolute;
      left: 120px;
      top: 12px;
      z-index: 1;
    }
    .table-wrapper {
      height: calc(100% - 60px);
    }
  }
}
</style>
