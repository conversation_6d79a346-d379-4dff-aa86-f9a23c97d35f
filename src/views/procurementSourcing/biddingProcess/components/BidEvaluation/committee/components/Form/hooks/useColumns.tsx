import { ref, computed } from 'vue';
import {
  ENTERPRISE_NATURE_OPTIONS,
  ENTERPRISE_NATURE_TAGS,
} from '@/views/procurementSourcing/biddingProcess/components/BidEvaluation/committee/const';
import CommonTag from '@/views/procurementSourcing/biddingProcess/components/BidEvaluation/components/CommonTag.vue';

export function useColumns() {
  const searchData = ref({});
  const searchFields = ref([]);
  const tableColumns = computed(() => {
    return [
      {
        label: '序号',
        type: 'index',
        width: '60px',
      },
      {
        label: '姓名',
        prop: 'name',
      },
      {
        label: '联系电话',
        prop: 'phone',
      },
      {
        label: '证件号码',
        prop: 'idNumber',
      },
      {
        label: '专家分类',
        prop: 'expertCategory',
        enums: ENTERPRISE_NATURE_OPTIONS,
        render: ({ row }: any) => {
          const expertCategory = row?.expertCategory;
          return expertCategory ? <CommonTag {...(ENTERPRISE_NATURE_TAGS[expertCategory] || {})} /> : '-';
        },
      },
      {
        label: '专家角色',
        prop: 'role',
      },
      {
        label: '操作',
        prop: 'action',
        isSlot: true,
        width: '100px',
        fixed: 'right',
      },
    ];
  });

  const formColumns = computed(() => [
    {
      label: '评标委员会名称',
      prop: 'name',
      type: 'input',
      rules: [
        { required: true, message: '请输入评标委员会名称', trigger: 'blur' },
        { max: 30, message: '长度在30个字符内' },
      ],
      colProps: { span: 12 },
    },
    {
      label: '评标地址',
      prop: 'address',
      type: 'input',
      attrs: {
        maxLength: 50,
      },
      rules: [
        { required: true, message: '请输入评标地址', trigger: 'blur' },
        { max: 50, message: '长度在50个字符内' },
      ],
      colProps: { span: 12 },
    },
    {
      label: '联系人',
      prop: 'contacts',
      hideMarginBottom: true,
      type: 'input',
      colProps: { span: 12 },
      rules: [
        { required: true, message: '请输入评标地址', trigger: 'blur' },
        { max: 50, message: '长度在50个字符内' },
      ],
    },
    {
      label: '联系电话',
      prop: 'contactsPhone',
      hideMarginBottom: true,
      type: 'input',
      colProps: { span: 12 },
      attrs: {
        readonly: true,
        disabled: true,
      },
      rules: [
        { required: true, message: '请输入评标地址', trigger: 'blur' },
        { max: 50, message: '长度在50个字符内' },
        { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码' },
      ],
    },
  ]);

  const detailColumns = computed(() => [
    {
      label: '评标委员会名称',
      prop: 'name',
    },
    {
      label: '评标地址',
      prop: 'address',
    },
    {
      label: '联系人',
      prop: 'contacts',
    },
    {
      label: '联系电话',
      prop: 'contactsPhone',
    },
  ]);

  const detailConfig = computed(() => ({
    title: '',
    descriptions: {
      labelPosition: 'top',
      column: 4,
    },
  }));

  return {
    searchFields,
    searchData,
    tableColumns,
    formColumns,
    detailColumns,
    detailConfig,
  };
}
