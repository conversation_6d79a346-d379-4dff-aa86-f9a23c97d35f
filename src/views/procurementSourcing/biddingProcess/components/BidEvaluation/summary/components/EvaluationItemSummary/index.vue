<template>
  <yun-drawer
    v-model="showVisible"
    title="评审项汇总"
    size="X-large"
    :with-header="true"
    :append-to-body="false"
    destroy-on-close
    :show-footer="true"
    :show-confirm-button="true"
    :show-cancel-button="false"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    cancel-button-text="关闭"
    custom-class="bid-custom-drawer"
    @confirm="handleConfirm"
  >
    <div
      class="need-hide-table-card"
      style="height: 98%"
      v-loading="loading"
      ref="drawerContainerRef"
    >
      <el-table
        :data="tableData"
        style="width: 100%"
        fit
        v-bind="tablePropsObj"
      >
        <el-table-column
          fixed="left"
          type="index"
          label="序号"
          width="60"
        ></el-table-column>
        <el-table-column
          prop="expertName"
          label="专家名称"
          width="150"
          fixed="left"
          show-overflow-tooltip
        />
        <el-table-column
          v-for="column in dynamicColumns"
          :key="column.prop"
          :prop="column.prop"
          :label="column.label"
          :minWidth="column.minWidth"
        >
          <template #default="{ row }">
            <div
              class="flex items-center gap-2"
              element-loading-text="提交中..."
              v-loading="row[`loading${speator}${column?.standardId}`]"
            >
              <!-- @change="(val: any) => handleChange(val, row, column)" -->
              <el-radio-group
                :disabled="!row.isOverallSummary || row[`isConformOld${speator}${column?.standardId}`] === 1 || !isLeader"
                v-model="row[`isConform${speator}${column?.standardId}`]"
              >
                <el-radio
                  v-for="item in CONFORMITY_RESULT_OPTIONS"
                  :key="item.value"
                  :label="String(item.value)"
                >
                  {{ item.label }}
                </el-radio>
              </el-radio-group>
              <template v-if="!row.isOverallSummary">
                <el-popover
                  placement="bottom"
                  title=""
                  width="200px"
                  trigger="click"
                  v-model:visible="popoverVisible[`${row.expertCode}${speator}${column.standardId}`]"
                  :teleported="true"
                  popper-class="table-popover"
                >
                  <div class="flex flex-col items-center gap-2">
                    <el-input
                      class="w-full transparent-input"
                      placeholder="请输入备注"
                      size="small"
                      style="border: none; outline: none; background-color: transparent"
                      :value="remark || '暂未填写备注内容'"
                      :maxlength="200"
                      readonly
                    ></el-input>
                  </div>
                  <template #reference>
                    <el-button
                      type="text"
                      class="ml-6"
                      @click="handleEdit(row, column)"
                    >
                      <span>备注</span>
                      <el-icon class="ml-2">
                        <EditPen />
                      </el-icon>
                    </el-button>
                  </template>
                </el-popover>
              </template>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </div>
  </yun-drawer>
</template>

<script lang="ts" setup>
import { ref, computed, watch, onMounted } from 'vue';
import { CONFORMITY_RESULT_OPTIONS } from '@/views/procurementSourcing/biddingProcess/components/BidEvaluation/online/const';
import { useBiddingStore } from '@/views/procurementSourcing/biddingProcess/stores';
import { querySupplierReviewDetail, leaderSaveSummaryReview, getMyMemberList } from '@/api/purchasing/evaluation';
import { ElMessage } from 'yun-design';
import { useUserInfo } from '/@/stores/userInfo';
import { isEmptyValue } from '@/views/procurementSourcing/biddingProcess/utils/bid';

const stores = useUserInfo();
const userId = computed(() => stores?.userInfos?.user?.userId);
const memberList = ref<any[]>([]);

// 判断是否是评标小组长
const isLeader = computed(() => {
  return memberList.value?.some(
    (item: any) => item?.userId === userId.value && item?.memberType === 'EVALUATION_MEMBER' && item?.role === 'EVALUATION_LEADER'
  );
});

const drawerContainerRef = ref<HTMLElement | null>(null);
const showVisible = ref(false);

const biddingStore = useBiddingStore();
const projectId = computed(() => biddingStore?.projectId);
const noticeId = computed(() => biddingStore?.noticeId);

const loading = ref(false);
// const submitLoading = ref(false);

const currentRow = ref<any>(null);
// 分隔符
const speator = ref('__');
const remark = ref();
// 控制每个 popover 的显示状态
const popoverVisible = ref<Record<string, boolean>>({});

const tableData = ref<any[]>([]);
const tableColumns = computed<any>(() => {
  const list = tableData.value || [];
  const n: any = list?.find((item: any) => item?.nodeReviewResults?.length > 0) || {};
  const itemList = n?.nodeReviewResults || [];
  const temp = itemList?.map((item: any) => {
    return {
      ...item,
      label: `${item?.itemName}`,
      prop: `standardId${speator.value}${item?.standardId}`,
      isDynamic: true,
      minWidth: '200px',
      showOverflowTooltip: false,
    };
  });

  return temp;
});
interface DynamicColumn {
  standardId: string;
  label: string;
  prop: string;
  isDynamic: boolean;
  minWidth: string;
}

const dynamicColumns = computed<DynamicColumn[]>(() => {
  return tableColumns.value?.filter((item: any) => item?.isDynamic) || [];
});

const tablePropsObj = computed(() => ({
  stripe: false,
  border: true,
  headerCellStyle: {
    backgroundColor: '#FAFBFC',
    color: '#505762',
    fontWeight: 'bold',
    height: '40px',
    borderColor: '#DCDFE6',
  },
  cellStyle: {
    padding: '0',
    height: '40px',
    'vertical-align': 'middle',
    borderColor: '#DCDFE6',
  },
  rowStyle: {
    height: '40px',
    borderColor: '#DCDFE6',
  },
  rowHeight: 40,
}));
// 点击外部关闭所有 popover
function handleClickOutside(event: Event) {
  const target = event.target as HTMLElement;

  // 检查是否点击在 popover 内部或者 reference 按钮上
  const isPopoverContent =
    target.closest('.el-popover') || target.closest('.el-button') || target.closest('.el-input') || target.closest('.el-radio-group');

  if (!isPopoverContent) {
    // 关闭所有打开的 popover
    Object.keys(popoverVisible.value).forEach((key) => {
      popoverVisible.value[key] = false;
    });
    remark.value = '';
  }
}
// async function handleChange(val: any, row: any, column: any) {
//   if (submitLoading.value) {
//     return;
//   }
//   submitLoading.value = true;
//   row[`loading${speator.value}${column?.standardId}`] = true;
//   const scoringId = row?.nodeReviewResults?.find((item: any) => item.standardId === column.standardId)?.scoringId || null;
//   const params = {
//     sectionId: currentRow.value?.sectionId,
//     tenantSupplierId: currentRow.value?.tenantSupplierId,
//     noticeId: noticeId.value,
//     projectId: projectId.value,
//     reviewItemSummaryList: [
//       {
//         standardId: column.standardId,
//         scoringId: scoringId,
//         reviewSummaryResult: val,
//         summaryConclusion: val === 1 ? '通过' : '不通过',
//       },
//     ],
//   };
//   try {
//     await leaderSaveSummaryReview(params);
//     loadData();
//     ElMessage.success('操作成功');
//     // eslint-disable-next-line no-empty
//   } catch (error) {
//     // eslint-disable-next-line no-console
//     console.log(error);
//   } finally {
//     submitLoading.value = false;
//     row[`loading${speator.value}${column?.standardId}`] = false;
//   }
// }

function handleEdit(row: any, column: any) {
  // 先关闭所有其他的 popover
  Object.keys(popoverVisible.value).forEach((key) => {
    popoverVisible.value[key] = false;
  });

  // 设置当前行的备注内容
  remark.value = row[`conclusion${speator.value}${column?.standardId}`];

  // 打开当前的 popover
  const popoverKey = `${row.expertCode}${speator.value}${column.standardId}`;
  nextTick(() => {
    popoverVisible.value[popoverKey] = true;
  });
}

// 数据转换函数
function transformSupplierDataToExpertTable(data: any[]) {
  if (!data || data.length === 0) {
    return [];
  }
  // 收集评审项
  const evaluationMap = new Map();
  // 遍历数据，收集评审项
  data.forEach((item: any) => {
    item.nodeReviewResults?.forEach((ev: any) => {
      if (!evaluationMap.has(ev.standardId)) {
        evaluationMap.set(ev.standardId, {
          standardId: ev.standardId,
          itemName: ev.itemName,
        });
      }
    });
  });

  const evaluations = Array.from(evaluationMap.values());

  const normalList = data?.filter((item: any) => item?.expertCode !== 'LEADER_SUMMARY');
  const leaderList = data?.filter((item: any) => item?.expertCode === 'LEADER_SUMMARY');

  // 构建表格数据 - 以专家为行
  const normalTableData = normalList.map((evItem: any) => {
    const row: any = {
      ...evItem,
    };
    // 为每个供应商添加对应的专家评审数据
    evaluations.forEach((ev) => {
      const details = evItem?.nodeReviewResults?.find((item: any) => item.standardId === ev.standardId);
      if (details) {
        row[`isConformDesc${speator.value}${ev.standardId}`] = details.isConformDesc;
        row[`conclusion${speator.value}${ev.standardId}`] = details.conclusion;
        row[`isConform${speator.value}${ev.standardId}`] = isEmptyValue(details.isConform) ? null : String(details.isConform);
        row[`isConformOld${speator.value}${ev.standardId}`] = isEmptyValue(details.isConform) ? null : String(details.isConform);
        row[`loading${speator.value}${ev.standardId}`] = false;
      } else {
        row[`isConformDesc${speator.value}${ev.standardId}`] = null;
        row[`conclusion${speator.value}${ev.standardId}`] = null;
        row[`isConform${speator.value}${ev.standardId}`] = null;
        row[`isConformOld${speator.value}${ev.standardId}`] = null;
        row[`loading${speator.value}${ev.standardId}`] = false;
      }
    });
    return row;
  });

  const leaderTableData = leaderList.map((evItem: any) => {
    const row: any = {
      ...evItem,
      isOverallSummary: true,
      expertName: '结论',
    };
    // 为每个供应商添加对应的专家评审数据
    evaluations.forEach((ev) => {
      const details = evItem?.nodeReviewResults?.find((item: any) => item.standardId === ev.standardId);
      const isAllPass = normalTableData?.every((item: any) => item[`isConform${speator.value}${ev.standardId}`] === '1');
      row[`isConformDesc${speator.value}${ev.standardId}`] = isEmptyValue(details.isConformDesc) ? isAllPass ? '通过' : '不通过' : details?.isConformDesc;
      row[`conclusion${speator.value}${ev.standardId}`] = isEmptyValue(details.conclusion) ? isAllPass ? '通过' : '不通过' : details?.conclusion;
      row[`isConform${speator.value}${ev.standardId}`] = isEmptyValue(details.isConform) ? isAllPass ? '1' : '0' : details?.isConform;
      row[`isConformOld${speator.value}${ev.standardId}`] = isEmptyValue(details.isConform) ? isAllPass ? '1' : '0' : details?.isConform;
      row[`loading${speator.value}${ev.standardId}`] = false;
    });

    return row;
  });


  return [...normalTableData, ...leaderTableData];
}

async function loadData() {
  loading.value = true;
  try {
    const res = await querySupplierReviewDetail({
      projectId: projectId.value,
      noticeId: noticeId.value,
      sectionId: currentRow.value?.sectionId,
      tenantSupplierId: currentRow.value?.tenantSupplierId, // 28 35
      evaluationId: currentRow.value?.evaluationId,
    });
    const result = res?.data?.expertReviewList || [];
    tableData.value = transformSupplierDataToExpertTable(result);
  } catch (error) {
    // eslint-disable-next-line no-console
    console.error('加载数据失败:', error);
  }
  loading.value = false;
}

function show(row: any = {}) {
  currentRow.value = row;
  showVisible.value = true;
  loadData();
}

watch(
  () => showVisible.value,
  (newVal) => {
    if (newVal) {
      setTimeout(() => {
        drawerContainerRef?.value?.removeEventListener('click', handleClickOutside);
        drawerContainerRef?.value?.addEventListener('click', handleClickOutside);
      }, 500);
    } else {
      drawerContainerRef?.value?.removeEventListener('click', handleClickOutside);
    }
  }
);

async function loadMyMemberList() {
  try {
    const res = await getMyMemberList(projectId.value);
    memberList.value = res?.data || [];
  } catch (error) {
    memberList.value = [];
  }
}

async function handleConfirm(done: any, loadingRef: any) {
  const row = tableData.value?.find((item: any) => item?.isOverallSummary) || {};
  const result = row?.nodeReviewResults || [];
  const params = {
    sectionId: currentRow.value?.sectionId,
    tenantSupplierId: currentRow.value?.tenantSupplierId,
    noticeId: noticeId.value,
    projectId: projectId.value,
    evaluationId: currentRow.value?.evaluationId,
    reviewItemSummaryList: result?.map((item: any) => {
      const standardId = item.standardId;
      const isConform = row[`isConform${speator.value}${standardId}`];
      return {
        standardId,
        scoringId: item.scoringId,
        reviewSummaryResult: isConform,
        summaryConclusion: Number(isConform) === 1 ? '通过' : '不通过',
      };
    }),
  };

  const valids = params?.reviewItemSummaryList?.filter((item: any) => isEmptyValue(item?.reviewSummaryResult));
  if (params?.reviewItemSummaryList?.length === 0) {
    ElMessage.error('暂无评审项');
    return;
  }
  if (valids?.length) {
    ElMessage.error('请将评审项填写完整');
    return;
  }


  try {
    loadingRef.value = true;
    await leaderSaveSummaryReview(params);
    done();
    ElMessage.success('操作成功');
  } catch (error) {
    // eslint-disable-next-line no-console
    console.error('操作失败:', error);
  } finally {
    loadingRef.value = false;
  }
}

onMounted(async () => {
  loadMyMemberList();
});

defineExpose({
  show,
});
</script>

<style lang="scss" scoped>
@import '@/views/procurementSourcing/biddingProcess/styles/collapse-panel.scss';

.transparent-input {
  :deep(.el-input__inner) {
    border: none;
    outline: none;
    background-color: transparent;
    appearance: none;
    padding: 0;
    box-shadow: none;
    &:focus {
      border: none;
      outline: none;
      background-color: transparent;
      appearance: none;
      box-shadow: none;
    }
  }
}
</style>

<style lang="scss">
// 全局样式，确保 popover 能够正确显示在表格之上
.table-popover {
  z-index: 9999 !important;
}
</style>
