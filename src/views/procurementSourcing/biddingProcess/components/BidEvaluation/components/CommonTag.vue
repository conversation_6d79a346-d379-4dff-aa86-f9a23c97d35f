<template>
  <div class="cp-custom-tag" v-if="text" :style="{ color, backgroundColor }">
    {{ text }}
  </div>
</template>

<script setup lang="ts">
defineProps({
  text: {
    type: String,
    default: '',
  },
  color: {
    type: String,
    default: '#009A29',
  },
  backgroundColor: {
    type: String,
    default: '#E8FFEA',
  },
});
</script>

<style lang="scss" scoped>
.cp-custom-tag {
  box-sizing: border-box;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0 8px;
  border-radius: 4px;
  background-color: #f0f2f5;
  color: #303133;
  height: 24px;
  font-size: 12px;
  border-radius: var(--Radius-border-radius-small, 2px);
  background: var(--Color-Success-color-success-light-9, #E8FFEA);

  color: var(--Color-Success-color-success-dark-2, #009A29);
  text-align: center;
  /* regular/extra-small */
  font-family: "PingFang SC";
  font-size: 12px;
  font-style: normal;
  font-weight: 600;
  line-height: 20px; /* 166.667% */
}
</style>