<template>
  <div class="bid-evaluation-management-container">
    <!-- 步骤指示器 -->
    <StepsIndicator
      :steps="stepsData"
      @step-click="handleStepClick"
    />
    <!-- 动态组件渲染区域 -->
    <div
      class="component-content"
      v-if="biddingStore.noticeId"
    >
      <component :is="currentComponent" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue';
import StepsIndicator from '../StepsIndicator/index.vue';
// import Summary from './summary/index.vue';
import Online from './online/index.vue';
import Offline from './offline/index.vue';
import Committee from './committee/index.vue';
import { useBiddingStore } from '@/views/procurementSourcing/biddingProcess/stores';
import { useUserRole } from '@/views/procurementSourcing/biddingProcess/utils';

const { isBidExpert } = useUserRole();

const biddingStore = useBiddingStore();

const noticeInfo = computed(() => {
  return biddingStore?.noticeInfo
});

const isOfflineEvaluation = computed(() => {
  return noticeInfo.value?.tenderWay === 'OFFLINE'
});

const isOfflineCount = computed(
  () => !['TO_NOTICE', 'NOTICE', 'REGISTER', 'QUOTING'].includes(biddingStore.projectDetail?.progressStatus)
);

// 当前激活的步骤索引
const activeStepIndex = ref(0);

// const isPublic = computed(() => biddingStore?.isPublic);
// 资格预审标识 1-需要 其他-不需要
// const isNeedPreQualification = computed(() => [1, true]?.includes(biddingStore?.projectDetail?.preQualification));
// 邀请回执标识 1-需要 其他-不需要
// const isNeedInviteReceipt = computed(() => [1, true]?.includes(biddingStore?.projectDetail?.inviteReceipt));
// 保证金管理标识 1-需要 其他-不需要
// const isNeedDeposit = computed(() => [1, true]?.includes(biddingStore?.noticeInfo?.needDeposit));

// 组件挂载时启动倒计时
onMounted(() => {
  biddingStore?.checkEvaluationAllCompleted();
});

// 步骤数据
// inviteReceipt

// 在线评标点击权限
const isOnlineEvaluation = computed(() => {
  if (isBidExpert.value) {
    return biddingStore?.isMSEMember
  }
  return biddingStore?.isBidOpened && biddingStore?.isMSEMember
});
// // 评标汇总点击权限
// const isOnlineEvaluationSummary = computed(() => {
//   if (isBidExpert.value) {
//     return biddingStore?.isEvaluationAllCompleted
//   }
//   return biddingStore?.isBidOpened && biddingStore?.isEvaluationAllCompleted
// });

const steps = computed(() => {
  if(!isOfflineEvaluation.value) {
    return [
      {
        id: 0,
        number: 0,
        label: '评标委员会',
        completed: false,
        current: false,
      },
      {
        id: 1,
        number: 1,
        label: '在线评标',
        completed: false,
        current: false,
        accessible: isOnlineEvaluation.value,
      },
      {
        id: 2,
        number: 2,
        label: '评标汇总',
        completed: false,
        current: false,
        // accessible: isOnlineEvaluationSummary.value,
        // errorMsg: '专家尚未评标完成，暂不能汇总',
      },
    ];
  }
  return [
    {
      id: 0,
      number: 0,
      label: '评标委员会',
      completed: false,
      current: false,
    },
    {
      id: 1,
      number: 1,
      label: '线下评标结果',
      completed: false,
      current: false,
      accessible: isOfflineCount.value,
    },
  ];

});
watch(
  () => steps.value,
  () => {
    activeStepIndex.value = (steps?.value?.[0]?.id || 0) as number;
  },
  { immediate: true }
);

const stepsData = computed(() => {
  return steps?.value?.map((item, index) => ({
    ...item,
    current: item.id === activeStepIndex.value,
    number: index + 1, // 节点数字
  }));
});

// 组件映射
const componentMap = computed(() => {
  if(!isOfflineEvaluation.value) {
    return {
      0: Committee,
      1: Online,
      2: Offline,
    }
  }
  return {
    0: Committee,
    1: Offline,
  }
});

// 当前显示的组件
const currentComponent = computed(() => {
  // @ts-ignore
  return componentMap.value[activeStepIndex.value as keyof typeof componentMap];
});

// 处理步骤点击事件 - 只切换显示的组件，不修改步骤状态
function handleStepClick(step: any) {
  activeStepIndex.value = step.id as number;
}

biddingStore?.initData();
</script>

<style lang="scss" scoped>
.bid-evaluation-management-container {
  min-height: 100%;
  display: flex;
  flex-direction: column;

  .component-content {
    flex: 1;
    margin-top: 12px;
    border-radius: 6px;
    background: var(--Color-Fill-fill-color-blank, #fff);
  }
}
</style>

<style>

</style>
