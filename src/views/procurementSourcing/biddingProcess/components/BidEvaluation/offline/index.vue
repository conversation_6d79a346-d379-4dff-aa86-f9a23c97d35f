<template>
  <div class="offline-evaluation-container">
    <div class="flex justify-between items-center mb-4">
      <el-select
        v-model="currentRound"
        placeholder="选择评标轮次"
        @change="loadData"
      >
        <el-option
          v-for="item in quotationRounds"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        />
      </el-select>
    </div>
    <!-- 评标结果表格 -->
    <div class="table-content">
      <el-table
        ref="tableRef"
        v-loading="loading"
        :data="tableData"
        class="editable-table"
        style="width: 100%"
      >
        <!-- 序号列 -->
        <el-table-column
          type="index"
          label="序号"
          width="80"
          align="center"
        />

        <!-- 项目名称列 -->
        <el-table-column
          prop="projectName"
          label="项目名称"
          min-width="200"
          show-overflow-tooltip
        />

        <!-- 标段名称列 -->
        <el-table-column
          prop="sectionName"
          label="标段名称"
          min-width="150"
          show-overflow-tooltip
        />

        <!-- 汇总状态列 -->
        <el-table-column
          prop="summaryStatus"
          label="汇总状态"
          width="120"
          align="center"
        >
          <template #default="{ row }">
            <el-tag
              :type="getSummaryStatusType(row.summaryStatus)"
              effect="light"
            >
              {{ getSummaryStatusLabel(row.summaryStatus) }}
            </el-tag>
          </template>
        </el-table-column>

        <!-- 汇总人列 -->
        <el-table-column
          prop="summaryByName"
          label="汇总人"
          width="120"
          align="center"
        />

        <!-- 汇总时间列 -->
        <el-table-column
          prop="summaryTime"
          label="汇总时间"
          width="180"
          align="center"
        />

        <!-- 评标报告列 -->
        <el-table-column
          label="评标报告"
          v-if="isOfflineEvaluation"
          width="120"
          align="center"
        >
          <template #default="{ row }">
            <el-button
              v-if="row.reportContent"
              type="text"
              size="small"
              @click="handleDownloadReport(row.reportContent)"
            >
              下载报告
            </el-button>
            <span v-else class="text-gray-400">暂无</span>
          </template>
        </el-table-column>
        <el-table-column
          label="评标报告"
          v-else
          width="120"
          align="center"
        >
          <template #default="{ row }">
            <el-button
              v-if="row.reportContent"
              type="text"
              size="small"
              @click="handleViewContent(row)"
            >
              查看报告
            </el-button>
          </template>
        </el-table-column>
        <!-- 签名进度列 -->
        <el-table-column
          prop="signatureProgress"
          label="签名进度"
          width="120"
          align="center"
        >
          <template #default="{ row }">
            <el-tag
              :type="getSignProgressType(row.signatureProgress)"
              effect="light"
            >
              {{ row.signatureProgress }}
            </el-tag>
          </template>
        </el-table-column>

        <!-- 签章后评标报告列 -->
        <el-table-column
          label="签章后评标报告"
          width="150"
          align="center"
        >
          <template #default="{ row }">
            <el-button
              v-if="row.signedReport"
              type="text"
              size="small"
              @click="handleDownloadReport(row.signedReport)"
            >
              下载报告
            </el-button>
            <span v-else class="text-gray-400">暂无</span>
          </template>
        </el-table-column>

        <!-- 操作列 -->
        <el-table-column
          label="操作"
          width="240"
          align="center"
          fixed="right"
        >
          <template #default="{ row }">
            <!-- 线下评标操作 -->
            <template v-if="isOfflineEvaluation">
              <el-button
                v-if="row.summaryStatus === EvaluationSummaryStatusEnum.PENDING && isAllowOfflineEvaluation"
                type="text"
                size="small"
                @click="handleUploadData(row)"
              >
                上传评标数据
              </el-button>
              <el-button
                v-if="showViewBtn(row)"
                type="text"
                size="small"
                @click="handleViewDetails(row)"
              >
                查看
              </el-button>
            </template>
            <!-- 线上评标操作 -->
            <template v-else>
              <el-button
                v-if="showSummaryBtn(row)"
                type="text"
                size="small"
                @click="handleOnlineEvaluationSummary(row)"
              >
                评标汇总
              </el-button>
              <el-button
                v-if="showViewBtn(row)"
                type="text"
                size="small"
                @click="handleViewOnlineDetails(row)"
              >
                查看
              </el-button>
            </template>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页组件 -->
      <div class="pagination-container mt-4" v-if="pagination.total > 0">
        <Pagination
          :current="pagination.current"
          :size="pagination.size"
          :total="pagination.total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>

    <!-- 线下评标汇总组件 -->
    <OfflineEvaluationSummary
      v-if="isOfflineEvaluation"
      ref="evaluationSummaryRef"
      :project-info="currentProjectInfo"
      @submit="handleEvaluationSubmit"
    />

    <!-- 线上评标汇总组件 -->
    <OnlineEvaluationSummary
      v-if="!isOfflineEvaluation"
      ref="onlineEvaluationSummaryRef"
      :project-info="currentProjectInfo"
      @submit="handleEvaluationSubmit"
    />

    <!-- 公告内容编辑抽屉 -->
    <AnnouncementEditor
      ref="announcementEditorRef"
      :readonly="true"
      @save="handleSaveContent"
    />

  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'yun-design'
import Pagination from '@/components/Pagination/index.vue'
import OfflineEvaluationSummary from './components/OfflineEvaluationSummary.vue'
import OnlineEvaluationSummary from './components/OnlineEvaluationSummary.vue'
import {
  getEvaluationSectionPage,
  type EvaluationSectionRecord,
  type EvaluationSectionPageParams,
  EvaluationSummaryStatusEnum
} from '@/api/purchasing/evaluation'
import { useBiddingStore } from '../../../stores'
import downloadUrlFile from '@/utils/downloadUrl';
import AnnouncementEditor
  from '@/views/procurementSourcing/biddingProcess/components/award/AwardResult/components/AnnouncementEditor.vue';
import { numberToChinese } from '@/views/procurementSourcing/biddingProcess/utils';
import { maxBy } from 'lodash';

// 接口和类型定义已在 API 文件中定义
const announcementEditorRef = ref() // 公告编辑器引用
// Store 和计算属性
const biddingStore = useBiddingStore()
const projectId = computed(() => biddingStore?.projectId)
const noticeInfo = computed(() => biddingStore?.noticeInfo)

// 是否允许 线下评标---“新建评标数据”
// 只有项目负责人或评标委员会负责人才能看到
const isAllowOfflineEvaluation = computed(() => biddingStore?.isEvaluationMember || biddingStore?.isProjectMemberLeader)

// 判断是否为线下评标
const isOfflineEvaluation = computed(() => {
  return noticeInfo.value?.tenderWay === 'OFFLINE'
})

// 响应式数据
const tableRef = ref()
const evaluationSummaryRef = ref()
const onlineEvaluationSummaryRef = ref()
const loading = ref(false)
const currentRow = ref<EvaluationSectionRecord | null>(null)
const currentRound = ref(1);
const quotationRounds = ref<any>([]);

// 表格数据
const tableData = ref<EvaluationSectionRecord[]>([])

// 分页数据
const pagination = reactive({
  current: 1,
  size: 10,
  total: 0
})

// 当前项目信息
const currentProjectInfo = computed(() => {
  if (!currentRow.value) return undefined
  return {
    projectName: currentRow.value.projectName,
    sectionName: currentRow.value.sectionName,
    evaluationId: currentRow.value.evaluationId,
    sectionId: currentRow.value.sectionId
  }
})

// 展示汇总按钮  必须得是小组负责人 且 汇总状态为待汇总
const showSummaryBtn = (row: EvaluationSectionRecord & { evaluationLeaderId: string }) => {
  return row.summaryStatus === EvaluationSummaryStatusEnum.PENDING && biddingStore?.isEvaluationMember && biddingStore?.currentUserId === row?.evaluationLeaderId
}

// 展示查看按钮
const showViewBtn = (row: EvaluationSectionRecord & { evaluationLeaderId: string }) => {
  return row.summaryStatus !== EvaluationSummaryStatusEnum.PENDING && biddingStore?.isMSEMember
}

// 获取汇总状态标签类型
const getSummaryStatusType = (status: EvaluationSummaryStatusEnum) => {
  const statusMap = {
    [EvaluationSummaryStatusEnum.PENDING]: 'info',
    [EvaluationSummaryStatusEnum.SUMMARIZED]: 'warning',
    [EvaluationSummaryStatusEnum.SIGNED]: 'success'
  }
  return statusMap[status] || 'info'
}

// 获取汇总状态标签文本
const getSummaryStatusLabel = (status: EvaluationSummaryStatusEnum) => {
  const statusMap = {
    [EvaluationSummaryStatusEnum.PENDING]: '待汇总',
    [EvaluationSummaryStatusEnum.SUMMARIZED]: '已汇总',
    [EvaluationSummaryStatusEnum.SIGNED]: '已签名'
  }
  return statusMap[status] || status
}

// 获取签名进度标签类型
const getSignProgressType = (progress: string) => {
  if(!progress) {
    return ''
  }
  if (progress.includes('100%') || progress.includes('已完成')) {
    return 'success'
  } else if (progress.includes('0%') || progress.includes('未开始')) {
    return 'info'
  } else {
    return 'warning'
  }
}

// 标段变化处理函数
const handleSectionChange = async (sectionId: number) => {
  pagination.current = 1
  await fetchTableData()
}

// 获取表格数据
const fetchTableData = async () => {
  if (!projectId.value) {
    ElMessage.warning('项目ID不存在')
    return
  }

  loading.value = true
  try {
    const params: EvaluationSectionPageParams = {
      projectId: projectId.value,
      current: pagination.current,
      size: pagination.size,
      currentRound: currentRound.value
    }

    const response = await getEvaluationSectionPage(params)

    if (response.code === 0) {
      tableData.value = response.data.records
      pagination.total = response.data.total
    } else {
      ElMessage.error(response.msg || '获取数据失败')
    }
  } catch (error) {
    console.error('获取表格数据失败:', error)
  } finally {
    loading.value = false
  }
}



// 分页大小变化处理
const handleSizeChange = (size: number) => {
  pagination.size = size
  pagination.current = 1
  fetchTableData()
}

// 当前页变化处理
const handleCurrentChange = (current: number) => {
  pagination.current = current
  fetchTableData()
}

const getFileName = (url) => {
  if (!url) return ''

  try {
    // 创建URL对象来解析查询参数
    const urlObj = new URL(url, window.location.origin)
    const fileName = urlObj.searchParams.get('fileName')
    return fileName || ''
  } catch (error) {
    // 如果URL解析失败，尝试使用正则表达式提取
    const match = url.match(/fileName=([^&]+)/)
    return match ? decodeURIComponent(match[1]) : ''
  }
}

// 下载报告
const handleDownloadReport = (reportUrl: string) => {
  // 实际项目中应该调用下载API
  // ElMessage.success(`正在下载报告: ${reportUrl}`)
  const fileName = getFileName(reportUrl)
  downloadUrlFile(reportUrl, fileName)
}

// 上传评标数据
const handleUploadData = (row: EvaluationSectionRecord) => {
  currentRow.value = row
  evaluationSummaryRef.value?.open(row.sectionId, false)
}

// 处理评标提交
const handleEvaluationSubmit = async () => {
  try {
    // 重新获取表格数据
    await fetchTableData()

    currentRow.value = null
  } catch (error) {
    console.error('提交失败:', error)
    ElMessage.error('提交失败，请重试')
  }
}

// 查看详情（线下评标）
const handleViewDetails = (row: EvaluationSectionRecord) => {
  // 设置当前行数据
  currentRow.value = row

  // 直接调用 openReadonly，传入 sectionId，让组件内部使用相同的API获取数据
  evaluationSummaryRef.value?.open(row.sectionId, true)
}

// 线上评标汇总
const handleOnlineEvaluationSummary = (row: EvaluationSectionRecord & { isEvaluationCompleted: boolean }) => {
  if (!row.isEvaluationCompleted) {
    ElMessage.warning('专家尚未评标完成，暂不能汇总')
    return
  }
  currentRow.value = row
  nextTick(() => {
    onlineEvaluationSummaryRef.value?.open(row.sectionId, false)
  })
}

// 查看详情（线上评标）
const handleViewOnlineDetails = (row: EvaluationSectionRecord) => {
  currentRow.value = row
  nextTick(() => {
    onlineEvaluationSummaryRef.value?.open(row.sectionId, true)
  })
}

// 查看内容
const handleViewContent = (row) => {
  announcementEditorRef.value?.show(row.reportContent, '查看评标报告内容', true)
}

// 初始化轮次数据
function initRoundsData() {
  const quoteRoundVoList = biddingStore?.noticeInfo?.quoteRoundVoList || [];
  const quoteRoundCount = maxBy(quoteRoundVoList, 'currentQuoteRound')?.currentQuoteRound || 1;
  // const quoteRoundCount =
  //   (quoteRoundVoList?.find((item: any) => String(item?.sectionId) === String(currentSectionId.value)) || {})?.currentQuoteRound || 1;
  quotationRounds.value = Array.from({ length: quoteRoundCount }, (_, i) => i + 1).map((item) => {
    return {
      label: `第${numberToChinese(item)}轮报价`,
      value: item,
    };
  });
  currentRound.value = quotationRounds.value[quotationRounds.value.length - 1]?.value || 1;
}

// 组件挂载时的初始化
onMounted(() => {
  // 如果有 projectId，直接获取数据
  if (projectId.value) {
    initRoundsData()
    fetchTableData()
  }
})
</script>

<style scoped lang="scss">
@import '@/views/procurementSourcing/biddingProcess/styles/collapse-panel.scss';

.offline-evaluation-container {
  padding: 20px;
  background: #fff;
  border-radius: 8px;

  .table-content {

    .text-gray-400 {
      color: #9ca3af;
    }

    .pagination-container {
      display: flex;
      justify-content: flex-end;
      padding: 16px 0;
    }
  }

}
</style>
