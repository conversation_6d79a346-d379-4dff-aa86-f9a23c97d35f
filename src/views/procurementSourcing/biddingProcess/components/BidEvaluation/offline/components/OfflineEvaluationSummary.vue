<template>
  <el-drawer
    v-model="visible"
    :title="isReadonly ? '查看详情' : '线下评标汇总'"
    size="80%"
    @close="handleClose"
  >
    <div class="evaluation-drawer-content" v-if="visible">
      <!-- 汇总信息 -->
      <div class="summary-info-section mb-6">
        <div class="section-title mb-3">
          <div class="header-title">汇总信息</div>
        </div>
        <div class="summary-info-content">
          <div class="info-row">
            <div class="info-item">
              <label class="info-label">汇总人员：</label>
              <span class="info-value">{{ summaryInfo.summaryByName || '-' }}</span>
            </div>
            <div class="info-item">
              <label class="info-label">汇总时间：</label>
              <span class="info-value">{{ summaryInfo.summaryTime || '-' }}</span>
            </div>
            <div class="info-item">
              <label class="info-label">汇总状态：</label>
              <el-tag
                :type="getSummaryStatusType(summaryInfo.summaryStatus)"
                effect="light"
              >
                {{ getSummaryStatusLabel(summaryInfo.summaryStatus) }}
              </el-tag>
            </div>
          </div>
        </div>
      </div>

      <!-- 评标清单表格 -->
      <div class="evaluation-table-section mb-6">
        <div class="section-title mb-3">
          <div class="header-title">评标清单</div>
          <el-button
            v-if="!isReadonly"
            type="primary"
            size="small"
            @click="handleAddSupplier"
          >
            添加供应商
          </el-button>
        </div>

        <el-table
          :data="evaluationTableData"
          :class="isReadonly ? 'readonly-table' : 'editable-table'"
          style="width: 100%"
        >
          <!-- 序号列 -->
          <el-table-column
            type="index"
            label="序号"
            width="80"
            align="center"
          />

          <!-- 供应商列 -->
          <el-table-column
            prop="supplierName"
            label="供应商"
            min-width="200"
            align="center"
          />

          <!-- 评标总分列 -->
          <el-table-column
            label="评标总分"
            width="220"
            align="center"
          >
            <template #default="{ row }">
              <el-input-number
                v-if="!isReadonly"
                v-model="row.totalScore"
                :min="0"
                :max="100"
                :precision="2"
                style="width: 100%"
              />
              <span v-else class="readonly-value">{{ row.totalScore }}</span>
            </template>
          </el-table-column>

          <!-- 投标价格列 -->
          <el-table-column
            label="投标价格"
            width="280"
            align="center"
          >
            <template #default="{ row }">
              <el-input-number
                v-if="!isReadonly"
                v-model="row.bidPrice"
                :min="0"
                :precision="2"
                style="width: 100%"
              />
              <span v-else class="readonly-value">{{ formatPrice(row.bidPrice) }}</span>
            </template>
          </el-table-column>

          <!-- 排名列 -->
          <el-table-column
            prop="ranking"
            width="140"
            align="center"
            fixed="right"
          >
            <template #header>
              <div class="ranking-header">
                <span>排名</span>
                <el-button
                  v-if="!isReadonly"
                  type="primary"
                  size="small"
                  :icon="RefreshRight"
                  @click="handleRefreshRanking"
                  style="margin-left: 8px"
                >
                  刷新
                </el-button>
              </div>
            </template>
            <template #default="{ row }">
              <el-tag
                v-if="isReadonly"
                :type="getRankingType(row.ranking)"
                effect="light"
              >
                第{{ row.ranking }}名
              </el-tag>
              <span v-else>{{ row.ranking }}</span>
            </template>
          </el-table-column>

          <!-- 是否推荐中标列 -->
          <el-table-column
            label="是否推荐中标"
            width="120"
            align="center"
            fixed="right"
          >
            <template #default="{ row }">
              <el-checkbox
                v-if="!isReadonly"
                v-model="row.isRecommended"
                @change="handleRecommendChange(row)"
              />
              <el-tag
                v-else
                :type="row.isRecommended ? 'success' : 'info'"
                effect="light"
              >
                {{ row.isRecommended ? '是' : '否' }}
              </el-tag>
            </template>
          </el-table-column>

          <!-- 中标后顺序列 -->
          <el-table-column
            label="中标后顺序"
            width="250"
            align="center"
            fixed="right"
          >
            <template #default="{ row }">
              <div v-if="!isReadonly">
                <el-select
                  v-model="row.winOrder"
                  style="width: 200px"
                  :disabled="!row.isRecommended"
                  @change="handleWinOrderChange"
                >
                  <el-option
                    label="第一中标候选人"
                    value="1"
                  />
                  <el-option
                    label="第二中标候选人"
                    value="2"
                  />
                  <el-option
                    label="第三中标候选人"
                    value="3"
                  />
                </el-select>
              </div>
              <span v-else-if="row.isRecommended && row.winOrder" class="readonly-value">
                {{ getWinOrderLabel(row.winOrder) }}
              </span>
              <span v-else class="text-gray-400">-</span>
            </template>
          </el-table-column>

          <!-- 操作列 -->
          <el-table-column
            v-if="!isReadonly"
            label="操作"
            width="100"
            align="center"
            fixed="right"
          >
            <template #default="{ row, $index }">
              <el-button
                type="text"
                size="small"
                @click="handleDeleteSupplier($index)"
              >
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- 评标报告上传/展示 -->
      <div class="evaluation-report-section mb-6">
        <div class="section-title mb-3">
          <div class="header-title">评标报告</div>
        </div>
        <YunUpload
          v-if="!isReadonly"
          v-model="evaluationReport"
          @change="handelUploadFile"
          :limit="1"
          :file-type="['pdf', 'doc', 'docx']"
          :multiple="true"
        ></YunUpload>
<!--        <Upload-->
<!--          v-if="!isReadonly"-->
<!--          v-model="evaluationReport"-->
<!--          :limit="1"-->
<!--          :file-type="['pdf', 'doc', 'docx']"-->
<!--          type="simple"-->
<!--        />-->
        <div v-else class="report-display">
          <div v-if="evaluationReport.length" class="report-item">
            <el-icon class="file-icon"><Document /></el-icon>
            <span class="file-name">{{ evaluationReport[0].fileName }}</span>
            <el-button
              type="text"
              size="small"
              @click="handleDownloadReport(evaluationReport[0].filePath)"
            >
              下载
            </el-button>
          </div>
          <div v-else class="no-report">
            <span class="text-gray-400">暂无评标报告</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 抽屉底部操作按钮 -->
    <template #footer>
      <div class="drawer-footer">
        <el-button @click="handleClose">{{ isReadonly ? '关闭' : '返回' }}</el-button>
        <el-button
          v-if="!isReadonly"
          type="primary"
          :loading="loading"
          @click="handleSubmit"
        >
          提交
        </el-button>
      </div>
    </template>
  </el-drawer>

  <!-- 供应商选择抽屉 -->
  <el-drawer
    v-model="supplierDrawerVisible"
    title="选择供应商"
    size="600px"
    @close="handleCancelSupplierSelection"
  >

    <el-table
        ref="supplierTableRef"
        :data="availableSuppliers"
        style="width: 100%"
        @selection-change="handleSupplierSelectionChange"
        v-loading="supplierLoading"
        max-height="400"
      >
        <el-table-column
          type="selection"
          width="55"
          :selectable="() => true"
        />
        <el-table-column
          type="index"
          label="序号"
          width="80"
          align="center"
        />
        <el-table-column
          prop="value"
          label="供应商名称"
          min-width="200"
        />
    </el-table>

    <template #footer>
      <el-button @click="handleCancelSupplierSelection">取消</el-button>
      <el-button
        type="primary"
        @click="handleConfirmSupplierSelection"
        :loading="supplierLoading"
      >
        确定
      </el-button>
    </template>
  </el-drawer>
</template>

<script setup lang="ts">
import { ref, reactive, watch, computed, defineEmits, defineExpose, nextTick } from 'vue'
import { ElMessage } from 'yun-design'
import { Document, RefreshRight } from '@element-plus/icons-vue'
import Upload from '@/components/Upload/index.vue'
import {
  queryOfflineEvaluationSummary,
  saveOfflineEvaluationSummary,
  type SupplierInfo,
  type OfflineEvaluationSummaryParams,
  type SaveOfflineEvaluationSummaryParams,
  type SupplierEvaluationInfo,
  WinnerCandidateOrderEnum,
  EvaluationSummaryStatusEnum
} from '@/api/purchasing/evaluation'
import { useBiddingStore } from '../../../../stores'
import YunUpload from '@/components/YunUpload/index.vue';
import downloadUrlFile from '@/utils/downloadUrl';
import { formatDate } from '@/utils/formatTime';
import { useUserInfo } from '@/stores/userInfo'
import { getQuoteAgainSupplierList, getQuoteCount } from '@/views/procurementSourcing/biddingProcess/api/bidOpening'

// 获取用户信息 store
const userInfoStore = useUserInfo()
// 评标清单数据接口
interface EvaluationTableRow {
  tenantSupplierId: string | number
  supplierName: string
  totalScore: number
  bidPrice: number
  ranking: number
  isRecommended: boolean
  winOrder: string
  winnerCandidateOrder?: WinnerCandidateOrderEnum
  winnerCandidateOrderDesc?: string
}

// 项目信息接口
interface ProjectInfo {
  projectName: string
  sectionName: string
  sectionId?: string | number
  evaluationId: string | number
}

// 汇总信息接口
interface SummaryInfo {
  summaryByName?: string // 汇总人员
  summaryTime?: string // 汇总时间
  summaryStatus?: EvaluationSummaryStatusEnum // 汇总状态
}

// Props
const props = defineProps<{
  projectInfo?: ProjectInfo
}>()

// Emits
const emit = defineEmits([
  'submit'
])

// Store 和计算属性
const biddingStore = useBiddingStore()
const projectId = computed(() => biddingStore?.projectId)
const noticeId = computed(() => biddingStore?.noticeId)

// 响应式数据
const visible = ref(false)
const loading = ref(false)
const isReadonly = ref(false)
const evaluationReport = ref<any[]>([]);
const evaluationTableData = ref<EvaluationTableRow[]>([])
const currentSectionId = ref<string | number | null>(null)

// 汇总信息
const summaryInfo = ref<SummaryInfo>({})

// 供应商选择相关
const supplierDrawerVisible = ref(false)
const supplierLoading = ref(false)
const availableSuppliers = ref<any[]>([])
const selectedSupplierIds = ref<string[]>([])
const currentQuoteRound = ref(0)
const supplierTableRef = ref()

// 获取线下评标汇总数据
const fetchOfflineEvaluationData = async () => {
  if (!currentSectionId.value || !noticeId.value || !projectId.value) {
    console.warn('缺少必要参数:', { sectionId: currentSectionId.value, noticeId: noticeId.value, projectId: projectId.value })
    initDefaultEvaluationTableData()
    return
  }

  loading.value = true
  try {
    const params: OfflineEvaluationSummaryParams = {
      sectionId: currentSectionId.value,
      noticeId: noticeId.value,
      projectId: projectId.value,
      evaluationId: props.projectInfo?.evaluationId
    }

    const response = await queryOfflineEvaluationSummary(params)

    if (response.code === 0 && response.data) {
      // 设置汇总信息
      summaryInfo.value = {
        summaryByName: response.data.summaryByName || userInfoStore.userInfos.user?.username || '',
        summaryTime: response.data.summaryTime || formatDate(new Date(), 'YYYY-mm-dd HH:MM:SS'),
        summaryStatus: response.data.summaryStatus
      }

      // 转换供应商数据格式
      evaluationTableData.value = response.data.supplierList.map((supplier: SupplierInfo) => ({
        tenantSupplierId: supplier.tenantSupplierId,
        supplierName: supplier.supplierName,
        totalScore: supplier.totalScore,
        bidPrice: supplier.bidPrice,
        ranking: supplier.ranking,
        isRecommended: supplier.isRecommendedWinner === 1,
        winOrder: getWinOrderFromEnum(supplier.winnerCandidateOrder),
        winnerCandidateOrder: supplier.winnerCandidateOrder,
        winnerCandidateOrderDesc: supplier.winnerCandidateOrderDesc
      }))

      if(response.data.reportContent) {
        evaluationReport.value = [
          {
            fileName: '评标报告',
            filePath: response.data.reportContent,
          }
        ]
      }


      // 如果有评标报告内容，设置到报告字段
      // if (response.data.supplierList.length > 0 && response.data.supplierList[0].reportContent) {
        // evaluationReport.value = response.data.supplierList[0].reportContent
      // }
      handleRefreshRanking()
    } else {
      ElMessage.error(response.msg || '获取评标数据失败')
      initDefaultEvaluationTableData()
    }
  } catch (error) {
    console.error('获取线下评标汇总数据失败:', error)
    initDefaultEvaluationTableData()
  } finally {
    loading.value = false
  }
}

// 将枚举转换为winOrder字符串
const getWinOrderFromEnum = (candidateOrder: WinnerCandidateOrderEnum): string => {
  const orderMap = {
    [WinnerCandidateOrderEnum.FIRST]: '1',
    [WinnerCandidateOrderEnum.SECOND]: '2',
    [WinnerCandidateOrderEnum.THIRD]: '3'
  }
  return orderMap[candidateOrder] || ''
}

// 将winOrder字符串转换为枚举
const getEnumFromWinOrder = (winOrder: string): WinnerCandidateOrderEnum | undefined => {
  const orderMap = {
    '1': WinnerCandidateOrderEnum.FIRST,
    '2': WinnerCandidateOrderEnum.SECOND,
    '3': WinnerCandidateOrderEnum.THIRD
  }
  return orderMap[winOrder]
}

// 初始化默认评标清单数据（当接口调用失败时使用）
const initDefaultEvaluationTableData = () => {
  evaluationTableData.value = []
  handleRefreshRanking()
}

// 添加供应商 - 打开供应商选择抽屉
const handleAddSupplier = async () => {
  try {
    supplierLoading.value = true

    // 获取轮次信息
    const quoteCountResponse = await getQuoteCount({ noticeId: noticeId.value })
    currentQuoteRound.value = quoteCountResponse.data ? quoteCountResponse.data : 0

    // 获取供应商列表
    const supplierResponse = await getQuoteAgainSupplierList({
      sectionId: currentSectionId.value,
      noticeId: noticeId.value,
      currentQuoteRound: currentQuoteRound.value
    })

    availableSuppliers.value = supplierResponse.data || []

    // 设置已选择的供应商（与现有评标清单中的供应商一致的默认勾选）
    selectedSupplierIds.value = evaluationTableData.value
      .map(item => String(item.tenantSupplierId))
      .filter(id => availableSuppliers.value.some(supplier => String(supplier.key) === id))

    supplierDrawerVisible.value = true

    // 等待抽屉打开后设置默认选中
    await nextTick()
    setDefaultSelectedSuppliers()
  } catch (error) {
    console.error('获取供应商列表失败:', error)
  } finally {
    supplierLoading.value = false
  }
}

// 设置默认选中的供应商
const setDefaultSelectedSuppliers = () => {
  if (!supplierTableRef.value) return

  // 清除所有选中状态
  supplierTableRef.value.clearSelection()

  // 设置默认选中
  availableSuppliers.value.forEach(supplier => {
    if (selectedSupplierIds.value.includes(String(supplier.key))) {
      supplierTableRef.value.toggleRowSelection(supplier, true)
    }
  })
}

// 供应商选择变化处理
const handleSupplierSelectionChange = (selection: any[]) => {
  selectedSupplierIds.value = selection.map(item => String(item.key))
}

// 确认选择供应商
const handleConfirmSupplierSelection = () => {
  const selectedSuppliers = availableSuppliers.value.filter(supplier =>
    selectedSupplierIds.value.includes(String(supplier.key))
  )

  // 移除现有的供应商，添加新选择的供应商
  const existingSupplierIds = evaluationTableData.value.map(item => String(item.tenantSupplierId))

  // 添加新选择的供应商
  selectedSuppliers.forEach(supplier => {
    if (!existingSupplierIds.includes(String(supplier.key))) {
      const newSupplier: EvaluationTableRow = {
        tenantSupplierId: supplier.key,
        supplierName: supplier.value,
        totalScore: 0,
        bidPrice: 0,
        ranking: evaluationTableData.value.length + 1,
        isRecommended: false,
        winOrder: ''
      }
      evaluationTableData.value.push(newSupplier)
    }
  })

  // 移除未选择的供应商
  evaluationTableData.value = evaluationTableData.value.filter(item =>
    selectedSupplierIds.value.includes(String(item.tenantSupplierId))
  )

  handleRefreshRanking()
  supplierDrawerVisible.value = false
}

// 取消供应商选择
const handleCancelSupplierSelection = () => {
  supplierDrawerVisible.value = false
  selectedSupplierIds.value = []
}

// 删除供应商
const handleDeleteSupplier = (index: number) => {
  evaluationTableData.value.splice(index, 1)
  handleRefreshRanking()
  handleWinOrderChange()
}

// 推荐中标变化处理
const handleRecommendChange = (row: EvaluationTableRow) => {
  if (!row.isRecommended) {
    row.winOrder = ''
  }
  handleWinOrderChange()
}

// 中标顺序变化处理 - 允许多个相同顺序
const handleWinOrderChange = () => {
  // 不做任何限制，允许多个供应商有相同的中标顺序
  // 这样可以处理分数相同的情况，同时为第一或第二等
}

// 手动刷新排名
const handleRefreshRanking = () => {
  // 按分数从高到低排序，并重新排列表格数据顺序
  const sortedData = [...evaluationTableData.value].sort((a, b) => b.totalScore - a.totalScore)

  // 更新排名并重新排列表格数据 - 支持并列排名
  let currentRank = 1
  let previousScore = null

  sortedData.forEach((item, index) => {
    // 如果分数与前一个不同，更新排名；如果分数相同，保持相同排名（并列）
    if (previousScore !== null && item.totalScore !== previousScore) {
      currentRank = index + 1
    }
    item.ranking = currentRank
    previousScore = item.totalScore
  })

  // 重新设置表格数据为排序后的数据
  evaluationTableData.value = sortedData

  // 先清空所有推荐状态和中标顺序
  evaluationTableData.value.forEach(item => {
    item.isRecommended = false
    item.winOrder = ''
  })
  let currentWinOrder = 1
  let previousScoreForWin = null

  // 设置前三个为推荐中标，并设置默认顺序 - 支持并列中标候选人
  evaluationTableData.value.slice(0, 3).forEach((item, index) => {
    item.isRecommended = true
    // 如果分数与前一个不同，更新中标顺序；如果分数相同，保持相同中标顺序（并列）
    if (previousScoreForWin !== null && item.totalScore !== previousScoreForWin) {
      currentWinOrder = index + 1
    }
    item.winOrder = String(currentWinOrder) // 第一、第二、第三
    previousScoreForWin = item.totalScore
  })

  // ElMessage.success('排名已刷新，表格已按分数从高到低排序，已默认推荐前三名中标')
}

// 格式化价格
const formatPrice = (price: number) => {
  if (price === 0) return '0'
  return new Intl.NumberFormat('zh-CN', {
    style: 'currency',
    currency: 'CNY'
  }).format(price)
}

// 获取排名标签类型
const getRankingType = (ranking: number) => {
  if (ranking === 1) return 'success'
  if (ranking === 2) return 'warning'
  if (ranking === 3) return 'info'
  return ''
}

// 获取汇总状态标签类型
const getSummaryStatusType = (status: EvaluationSummaryStatusEnum) => {
  const statusMap = {
    [EvaluationSummaryStatusEnum.PENDING]: 'info',
    [EvaluationSummaryStatusEnum.SUMMARIZED]: 'warning',
    [EvaluationSummaryStatusEnum.SIGNED]: 'success'
  }
  return statusMap[status] || 'info'
}

// 获取汇总状态标签文本
const getSummaryStatusLabel = (status: EvaluationSummaryStatusEnum) => {
  const statusMap = {
    [EvaluationSummaryStatusEnum.PENDING]: '待汇总',
    [EvaluationSummaryStatusEnum.SUMMARIZED]: '已汇总',
    [EvaluationSummaryStatusEnum.SIGNED]: '已签名'
  }
  return statusMap[status] || status
}

// 获取中标顺序标签
const getWinOrderLabel = (order: string) => {
  const orderMap = {
    '1': '第一中标候选人',
    '2': '第二中标候选人',
    '3': '第三中标候选人'
  }
  return orderMap[order] || order
}

const getFileName = (url) => {
  if (!url) return ''

  try {
    // 创建URL对象来解析查询参数
    const urlObj = new URL(url, window.location.origin)
    const fileName = urlObj.searchParams.get('fileName')
    return fileName || ''
  } catch (error) {
    // 如果URL解析失败，尝试使用正则表达式提取
    const match = url.match(/fileName=([^&]+)/)
    return match ? decodeURIComponent(match[1]) : ''
  }
}

// 下载报告
const handleDownloadReport = (reportUrl) => {
  const fileName = getFileName(reportUrl)
  downloadUrlFile(reportUrl, fileName)
}

// 移除自动更新排名的监听器，改为手动刷新排名
// watch(
//   () => evaluationTableData.value.map(item => item.totalScore),
//   () => {
//     handleRefreshRanking()
//   },
//   { deep: true }
// )

// 打开抽屉 - 编辑模式
const open = (sectionId: string | number, openReadonly: boolean) => {
  visible.value = true
  isReadonly.value = openReadonly
  currentSectionId.value = sectionId || null
  evaluationReport.value = []

  // 如果有 sectionId，调用接口获取数据，否则使用默认数据
  if (sectionId) {
    fetchOfflineEvaluationData()
  } else {
    // initDefaultEvaluationTableData()
  }
}

// 关闭抽屉
const handleClose = () => {
  visible.value = false
  isReadonly.value = false
  evaluationTableData.value = []
  evaluationReport.value = []
  currentSectionId.value = null
  summaryInfo.value = {}
}

const handelUploadFile = (file: any[]) => {
  console.log(file);
  // evaluationReport.value = file.map((item) => {
  //   return {
  //     fileName: item.name,
  //     filePath: item.url,
  //   };
  // });
};

// 提交数据
const handleSubmit = async () => {
  try {
    // 验证标段ID
    if (!currentSectionId.value) {
      ElMessage.error('缺少标段ID，无法提交')
      return
    }

    // 验证评标报告是否上传
    if (!evaluationReport.value.length) {
      ElMessage.error('请上传评标报告')
      return
    }

    if (!props.projectInfo.evaluationId) {
      ElMessage.error('请完成评标委员会')
      return
    }

    // 验证是否有推荐中标的供应商
    const recommendedSuppliers = evaluationTableData.value.filter(item => item.isRecommended)
    if (recommendedSuppliers.length === 0) {
      ElMessage.error('请至少推荐一个中标供应商')
      return
    }

    // 验证推荐中标的供应商是否都设置了中标顺序
    const hasEmptyWinOrder = recommendedSuppliers.some(item => !item.winOrder)
    if (hasEmptyWinOrder) {
      ElMessage.error('请为所有推荐中标的供应商设置中标顺序')
      return
    }

    loading.value = true

    // 构造供应商评标信息列表
    const supplierEvaluationInfoList: SupplierEvaluationInfo[] = evaluationTableData.value.map(supplier => {
      const supplierInfo: SupplierEvaluationInfo = {
        tenantSupplierId: supplier.tenantSupplierId,
        totalScore: supplier.totalScore,
        bidPrice: supplier.bidPrice,
        isRecommendedWinner: supplier.isRecommended ? 1 : 0
      }

      // 如果推荐中标且有中标顺序，则设置中标候选顺序
      if (supplier.isRecommended && supplier.winOrder) {
        const candidateOrder = getEnumFromWinOrder(supplier.winOrder)
        if (candidateOrder) {
          supplierInfo.winnerCandidateOrder = candidateOrder
        }
      }

      return supplierInfo
    })

    // 构造请求参数
    const saveParams: SaveOfflineEvaluationSummaryParams = {
      sectionId: currentSectionId.value,
      evaluationId: props.projectInfo.evaluationId,
      reportContent: evaluationReport.value[0].url,
      supplierEvaluationInfoList
    }

    // 调用保存接口
    const response = await saveOfflineEvaluationSummary(saveParams)

    ElMessage.success('评标数据提交成功')

    // 触发提交事件（保持向后兼容）
    emit('submit')

    handleClose()
  } catch (error) {
    console.error('提交失败:', error)
  } finally {
    loading.value = false
  }
}

// 暴露方法
defineExpose({
  open
})
</script>

<style scoped lang="scss">
@import '@/views/procurementSourcing/biddingProcess/styles/collapse-panel.scss';

.evaluation-drawer-content {
  .summary-info-section {
    .section-title {
      h3 {
        margin: 0;
        font-size: 16px;
        font-weight: 600;
        color: #333;
      }
    }

    .summary-info-content {
      .info-row {
        display: flex;
        gap: 40px;
        align-items: center;
        padding: 16px;
        background: #f8f9fa;
        border-radius: 6px;

        .info-item {
          display: flex;
          align-items: center;

          .info-label {
            font-weight: 500;
            color: #666;
            margin-right: 8px;
            min-width: 80px;
          }

          .info-value {
            color: #333;
            font-weight: 500;
          }
        }
      }
    }
  }

  .evaluation-table-section {
    .section-title {
      display: flex;
      justify-content: space-between;
      align-items: center;

      h3 {
        margin: 0;
        font-size: 16px;
        font-weight: 600;
        color: #333;
      }
    }

    .readonly-table {
      .readonly-value {
        color: #333;
        font-weight: 500;
      }
    }
  }

  .evaluation-report-section {
    .section-title {
      h3 {
        margin: 0;
        font-size: 16px;
        font-weight: 600;
        color: #333;
      }
    }

    .report-display {
      .report-item {
        display: flex;
        align-items: center;
        padding: 12px;
        background: #f8f9fa;
        border-radius: 6px;

        .file-icon {
          margin-right: 8px;
          color: #409eff;
        }

        .file-name {
          flex: 1;
          color: #333;
        }
      }

      .no-report {
        padding: 12px;
        text-align: center;
        background: #f8f9fa;
        border-radius: 6px;
      }
    }
  }

  .text-gray-400 {
    color: #9ca3af;
  }

  // 排名表头样式
  .ranking-header {
    display: flex;
    align-items: center;
    justify-content: center;
    flex-wrap: wrap;
    gap: 4px;
  }
}

.drawer-footer {
  display: flex;
  justify-content: flex-end;
}
</style>
