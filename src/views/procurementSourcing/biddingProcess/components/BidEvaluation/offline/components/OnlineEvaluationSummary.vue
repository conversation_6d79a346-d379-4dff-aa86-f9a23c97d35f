<template>
  <el-drawer
    v-model="visible"
    :title="isReadonly ? '查看详情' : '线上评标汇总'"
    size="80%"
    @close="handleClose"
  >
    <div class="evaluation-drawer-content" v-if="visible">
      <!-- 汇总信息 -->
      <div class="summary-info-section mb-6">
        <div class="section-title mb-3">
          <div class="header-title">汇总信息</div>
        </div>
        <div class="summary-info-content">
          <div class="info-row">
            <div class="info-item">
              <label class="info-label">汇总人员：</label>
              <span class="info-value">{{ summaryInfo.summaryByName || '-' }}</span>
            </div>
            <div class="info-item">
              <label class="info-label">汇总时间：</label>
              <span class="info-value">{{ summaryInfo.summaryTime || '-' }}</span>
            </div>
            <div class="info-item">
              <label class="info-label">汇总状态：</label>
              <el-tag
                :type="getSummaryStatusType(summaryInfo.summaryStatus)"
                effect="light"
              >
                {{ getSummaryStatusLabel(summaryInfo.summaryStatus) }}
              </el-tag>
            </div>
          </div>
        </div>
      </div>

      <!-- 评标清单表格 -->
      <div class="evaluation-table-section mb-6">
        <div class="section-title mb-3">
          <div class="header-title">评标清单</div>
        </div>

        <el-table
          :data="evaluationTableData"
          :class="isReadonly ? 'readonly-table' : 'editable-table'"
          style="width: 100%"
        >
          <!-- 序号列 -->
          <el-table-column
            type="index"
            label="序号"
            width="80"
            align="center"
          />

          <!-- 供应商列 -->
          <el-table-column
            prop="supplierName"
            label="供应商"
            min-width="200"
            align="center"
          />

          <!-- 评审项汇总列 -->
          <el-table-column
            label="评审项汇总"
            width="120"
            align="center"
          >
            <template #default="{ row }">
              <el-button
                type="text"
                :class="getReviewSummaryClass(row.reviewSummary)"
                @click="handleReviewSummaryClick(row)"
              >
                {{ row.reviewSummary }}
              </el-button>
            </template>
          </el-table-column>

          <!-- 动态评审节点评分列 -->
          <el-table-column
            v-for="(score, nodeName) in getNodeScoreColumns()"
            :key="nodeName"
            :label="nodeName"
            width="220"
            align="center"
          >
            <template #default="{ row }">
              <el-input-number
                v-if="!isReadonly"
                v-model="row.nodeScores[nodeName]"
                :min="0"
                :precision="2"
                size="small"
                style="width: 180px"
                @change="handleScoreChange(row)"
              />
              <span v-else class="readonly-value">{{ row.nodeScores[nodeName] || '0' }}</span>
            </template>
          </el-table-column>

          <!-- 评标总分列 -->
          <el-table-column
            label="评标总分"
            width="120"
            align="center"
            prop="totalScore"
          >
            <template #default="{ row }">
              <span class="total-score">{{ row.totalScore || '0' }}</span>
            </template>
          </el-table-column>

          <!-- 投标价格列 -->
          <el-table-column
            label="投标价格"
            width="280"
            align="center"
            prop="bidPrice"
          >
          </el-table-column>

          <!-- 排名列 -->
          <el-table-column
            prop="ranking"
            width="140"
            align="center"
            fixed="right"
          >
            <template #header>
              <div class="ranking-header">
                <span>排名</span>
                <el-button
                  v-if="!isReadonly"
                  type="primary"
                  size="small"
                  :icon="RefreshRight"
                  @click="handleRefreshRanking"
                  style="margin-left: 8px"
                >
                  刷新
                </el-button>
              </div>
            </template>
            <template #default="{ row }">
              <span>{{ row.ranking }}</span>
            </template>
          </el-table-column>

          <!-- 是否推荐中标列 -->
          <el-table-column
            label="是否推荐中标"
            width="120"
            fixed="right"
            align="center"
          >
            <template #default="{ row }">
              <el-checkbox
                v-if="!isReadonly"
                v-model="row.isRecommended"
                @change="handleRecommendChange(row)"
              />
              <el-tag
                v-else
                :type="row.isRecommended ? 'success' : 'info'"
                effect="light"
              >
                {{ row.isRecommended ? '是' : '否' }}
              </el-tag>
            </template>
          </el-table-column>

          <!-- 中标后顺序列 -->
          <el-table-column
            label="中标候选人顺序"
            width="250"
            fixed="right"
            align="center"
          >
            <template #default="{ row }">
              <div v-if="!isReadonly">
                <el-select
                  v-model="row.winOrder"
                  style="width: 200px"
                  :disabled="!row.isRecommended"
                  @change="handleWinOrderChange"
                >
                  <el-option
                    label="第一中标候选人"
                    value="1"
                  />
                  <el-option
                    label="第二中标候选人"
                    value="2"
                  />
                  <el-option
                    label="第三中标候选人"
                    value="3"
                  />
                </el-select>
              </div>
              <span v-else-if="row.isRecommended && row.winOrder" class="readonly-value">
                {{ getWinOrderLabel(row.winOrder) }}
              </span>
              <span v-else class="text-gray-400">-</span>
            </template>
          </el-table-column>


        </el-table>
      </div>

      <!-- 评标报告 -->
      <div class="evaluation-report-section mb-6">
        <div class="section-title mb-3">
          <div class="header-title">评标报告</div>
        </div>

        <div v-if="!isReadonly" class="report-edit-section">
          <el-form>
            <!-- 模板选择 -->
            <div class="template-selection mb-4">
              <el-form-item label="引用模板：" label-width="100px">
                <el-select
                  v-model="reportTemplateId"
                  placeholder="请选择评标报告模板"
                  clearable
                  style="width: 300px"
                  @change="handleTemplateChange"
                >
                  <el-option
                    v-for="template in reportTemplates"
                    :key="template.id"
                    :label="template.templateName"
                    :value="template.id"
                  >
                  </el-option>
                </el-select>
              </el-form-item>
            </div>

            <!-- 公告内容编辑 -->
            <div class="content-edit mb-4">
              <el-form-item label="公告内容：" label-width="100px">
                <el-button
                  type="primary"
                  @click="handleEditContent"
                  :disabled="!reportTemplateId"
                >
                  编辑内容
                </el-button>
                <span v-if="reportContent" class="content-status">
                <el-icon class="success-icon"><Check /></el-icon>
                内容已编辑
              </span>
              </el-form-item>
            </div>
          </el-form>
        </div>

        <div v-else class="report-display">
          <div v-if="reportContent" class="report-content">
            <div class="content-preview" v-html="getContentPreview()"></div>
            <el-button
              type="text"
              size="small"
              @click="handleViewContent"
            >
              查看完整内容
            </el-button>
          </div>
          <div v-else class="no-report">
            <span class="text-gray-400">暂无评标报告内容</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 抽屉底部操作按钮 -->
    <template #footer>
      <div class="drawer-footer">
        <el-button @click="handleClose">{{ isReadonly ? '关闭' : '返回' }}</el-button>
        <el-button
          v-if="!isReadonly"
          type="primary"
          :loading="loading"
          @click="handleSubmit"
        >
          提交
        </el-button>
      </div>
    </template>
  </el-drawer>

  <!-- 评审项汇总弹窗 -->
  <el-dialog
    v-model="reviewSummaryDialogVisible"
    title="评审项汇总详情"
    width="60%"
    @close="handleReviewSummaryDialogClose"
  >
    <template #footer>
      <el-button @click="handleReviewSummaryDialogClose">关闭</el-button>
    </template>
  </el-dialog>

  <!-- 公告内容编辑抽屉 -->
  <AnnouncementEditor
    ref="announcementEditorRef"
    :readonly="isReadonly"
    @save="handleSaveContent"
  />

  <EvaluationItemSummary ref="evaluationItemSummaryRef" />
</template>

<script setup lang="ts">
import { ref, watch, computed, defineEmits, defineExpose } from 'vue'
import { ElMessage } from 'yun-design'
import { Check, RefreshRight } from '@element-plus/icons-vue'
import { useBiddingStore } from '../../../../stores'
import { queryEvaluationSummary, summaryEvaluation, EvaluationSummaryStatusEnum } from '@/api/purchasing/evaluation'
import AnnouncementEditor from '../../../award/AwardResult/components/AnnouncementEditor.vue'
import { getTemplateList } from '@/views/procurementSourcing/biddingProcess/api/award';
import { getTemplateDetail } from '@/views/procurementSourcing/biddingProcess/api';
import EvaluationItemSummary from '@/views/procurementSourcing/biddingProcess/components/BidEvaluation/summary/components/EvaluationItemSummary/index.vue'
import { useUserInfo } from '@/stores/userInfo'
import { formatDate } from '@/utils/formatTime'
import { templateContentFormatter } from '@/views/procurementSourcing/biddingProcess/components/announcement/ProcurementDocument/templateFormatter'

const evaluationItemSummaryRef = ref<any>();

// 获取用户信息 store
const userInfoStore = useUserInfo()

// 评标清单数据接口
interface EvaluationTableRow {
  tenantSupplierId: string | number
  supplierName: string
  reviewSummary: 'QUALIFIED' | 'UNQUALIFIED' // 评审项汇总：符合/不符合
  nodeScores: Record<string, number> // 动态节点评分
  totalScore: number
  bidPrice: number
  ranking: number
  isRecommended: boolean
  winOrder: string
  winnerCandidateOrder?: string
  winnerCandidateOrderDesc?: string
  evaluationId: string,
}



// 汇总信息接口
interface SummaryInfo {
  summaryByName?: string // 汇总人员
  summaryTime?: string // 汇总时间
  summaryStatus?: EvaluationSummaryStatusEnum // 汇总状态
}

// 项目信息接口
interface ProjectInfo {
  projectName: string
  sectionName: string
  sectionId?: string | number
  evaluationId: string | number
}

// Props
const props = defineProps<{
  projectInfo?: ProjectInfo
}>()

// Emits
const emit = defineEmits([
  'submit'
])


// Store 和计算属性
const biddingStore = useBiddingStore()
const projectId = computed(() => biddingStore?.projectId)
const noticeId = computed(() => biddingStore?.noticeId)

// 响应式数据
const visible = ref(false)
const loading = ref(false)
const isReadonly = ref(false)
const evaluationReport = ref<any[]>([])
const evaluationTableData = ref<EvaluationTableRow[]>([])
const currentSectionId = ref<string | number | null>(null)

// 评审项汇总弹窗相关
const reviewSummaryDialogVisible = ref(false)
const currentReviewSupplier = ref<EvaluationTableRow | null>(null)

// 汇总信息
const summaryInfo = ref<SummaryInfo>({})

// 评标报告相关
const reportTemplateId = ref<string>('') // 选择的模板ID
const reportContent = ref<string>('') // 签名报告内容
const reportTemplates = ref<any[]>([]) // 可选模板列表
const announcementEditorRef = ref() // 公告编辑器引用

// 获取线上评标汇总数据
const fetchOnlineEvaluationData = async () => {
  if (!currentSectionId.value || !noticeId.value || !projectId.value) {
    // 缺少必要参数，使用默认数据
    // initDefaultEvaluationTableData()
    return
  }

  loading.value = true
  try {
    const params = {
      sectionId: currentSectionId.value,
      noticeId: noticeId.value,
      projectId: projectId.value,
      evaluationId: props.projectInfo?.evaluationId
    }

    // 调用线上评标汇总接口
    const response = await queryEvaluationSummary(params)

    if (response.code === 0 && response.data) {
      // 设置汇总信息
      summaryInfo.value = {
        summaryByName: response.data.summaryByName || userInfoStore.userInfos.user?.username || '',
        summaryTime: response.data.summaryTime || formatDate(new Date(), 'YYYY-mm-dd HH:MM:SS'),
        summaryStatus: response.data.summaryStatus
      }

      // 转换供应商数据格式，包含新增的字段
      evaluationTableData.value = response.data.supplierList.map((supplier: any) => {
        // 确保nodeScores有默认值
        const nodeScores = supplier.nodeScores || {}

        // 如果nodeScores为空，初始化一些默认的评分项
        // if (Object.keys(nodeScores).length === 0) {
        //   nodeScores['技术评分'] = 0
        //   nodeScores['商务评分'] = 0
        //   nodeScores['价格评分'] = 0
        // }

        return {
          tenantSupplierId: supplier.tenantSupplierId,
          supplierName: supplier.supplierName,
          reviewSummary: supplier.reviewSummary || 'QUALIFIED', // 评审项汇总
          nodeScores: nodeScores, // 动态节点评分
          totalScore: supplier.totalScore || 0,
          bidPrice: supplier.bidPrice,
          ranking: supplier.ranking,
          isRecommended: supplier.isRecommendedWinner === 1,
          winOrder: getWinOrderFromEnum(supplier.winnerCandidateOrder),
          evaluationId: response.data.evaluationId,
        }
      })

      // 处理报告相关数据
      if (response.data.reportTemplateId) {
        reportTemplateId.value = response.data.reportTemplateId
      }
      if (response.data.reportContent) {
        reportContent.value = response.data.reportContent
      }

      // updateRanking()
      handleRefreshRanking()
    } else {
      ElMessage.error(response.msg || '获取评标数据失败')
      // initDefaultEvaluationTableData()
    }
  } catch (error) {
    // 获取线上评标汇总数据失败
    ElMessage.error('获取评标数据失败')
    // initDefaultEvaluationTableData()
  } finally {
    loading.value = false
  }
}

// 将枚举转换为winOrder字符串
const getWinOrderFromEnum = (candidateOrder: any): string => {
  const orderMap = {
    'FIRST': '1',
    'SECOND': '2',
    'THIRD': '3'
  }
  return orderMap[candidateOrder] || ''
}

// 将winOrder字符串转换为枚举
const getEnumFromWinOrder = (winOrder: string): string | undefined => {
  const orderMap = {
    '1': 'FIRST',
    '2': 'SECOND',
    '3': 'THIRD'
  }
  return orderMap[winOrder]
}

// 初始化默认评标清单数据
const initDefaultEvaluationTableData = () => {
  // 如果没有数据，创建一些示例数据用于测试
  if (evaluationTableData.value.length === 0) {
    evaluationTableData.value = [
      {
        tenantSupplierId: 'supplier_1',
        supplierName: '供应商A',
        reviewSummary: 'QUALIFIED',
        nodeScores: {
          '技术评分': 85,
          '商务评分': 78,
          '价格评分': 92
        },
        totalScore: 255,
        bidPrice: 100000,
        ranking: 1,
        isRecommended: false,
        winOrder: '',
        evaluationId: 'eval_1'
      },
      {
        tenantSupplierId: 'supplier_2',
        supplierName: '供应商B',
        reviewSummary: 'QUALIFIED',
        nodeScores: {
          '技术评分': 90,
          '商务评分': 82,
          '价格评分': 88
        },
        totalScore: 260,
        bidPrice: 95000,
        ranking: 2,
        isRecommended: false,
        winOrder: '',
        evaluationId: 'eval_2'
      },
      {
        tenantSupplierId: 'supplier_3',
        supplierName: '供应商C',
        reviewSummary: 'QUALIFIED',
        nodeScores: {
          '技术评分': 75,
          '商务评分': 85,
          '价格评分': 95
        },
        totalScore: 255,
        bidPrice: 98000,
        ranking: 3,
        isRecommended: false,
        winOrder: '',
        evaluationId: 'eval_3'
      }
    ]
  }
  // updateRanking()
}

// 获取节点评分列 - 从第一个供应商的nodeScores中获取所有键名
const getNodeScoreColumns = () => {
  if (evaluationTableData.value.length === 0) return {}

  // 获取第一个供应商的nodeScores作为列的基准
  const firstSupplier = evaluationTableData.value[0]
  return firstSupplier.nodeScores || {}
}

// 获取评审项汇总的样式类
const getReviewSummaryClass = (reviewSummary: string) => {
  return reviewSummary === '符合' ? 'text-success' : 'text-danger'
}

// 获取评审项汇总的文本
const getReviewSummaryText = (reviewSummary: string) => {
  return reviewSummary === 'QUALIFIED' ? '符合' : '不符合'
}

// 点击评审项汇总按钮
const handleReviewSummaryClick = (row: EvaluationTableRow) => {
  // currentReviewSupplier.value = row
  // reviewSummaryDialogVisible.value = true
  evaluationItemSummaryRef.value?.show({
    sectionId: currentSectionId.value,
    tenantSupplierId: row.tenantSupplierId,
    evaluationId: row.evaluationId,
  });

}

// 关闭评审项汇总弹窗
const handleReviewSummaryDialogClose = () => {
  reviewSummaryDialogVisible.value = false
  currentReviewSupplier.value = null
}

// 获取汇总状态标签类型
const getSummaryStatusType = (status: EvaluationSummaryStatusEnum) => {
  const statusMap = {
    [EvaluationSummaryStatusEnum.PENDING]: 'info',
    [EvaluationSummaryStatusEnum.SUMMARIZED]: 'warning',
    [EvaluationSummaryStatusEnum.SIGNED]: 'success'
  }
  return statusMap[status] || 'info'
}

// 获取汇总状态标签文本
const getSummaryStatusLabel = (status: EvaluationSummaryStatusEnum) => {
  const statusMap = {
    [EvaluationSummaryStatusEnum.PENDING]: '待汇总',
    [EvaluationSummaryStatusEnum.SUMMARIZED]: '已汇总',
    [EvaluationSummaryStatusEnum.SIGNED]: '已签名'
  }
  return statusMap[status] || status
}

// 获取模板列表
const fetchReportTemplates = async () => {
  try {
    // 这里需要调用实际的模板列表接口
    const response = await getTemplateList({
      type: 'AWARD_NOTIFICATION',
    });
    reportTemplates.value = response.data.records || []
  } catch (error) {
    // 获取模板列表失败
  }
}

// 模板选择变化处理
const handleTemplateChange = (templateId: string) => {
  if (!templateId) {
    reportContent.value = ''
    return
  }

  // 可以在这里根据模板ID获取模板内容
  // 选择的模板ID: templateId
}

// 编辑内容
const handleEditContent = async () => {
  if (!reportTemplateId.value) {
    ElMessage.warning('请先选择模板')
    return
  }
  await loadNotificationTemplateContent(reportTemplateId.value)
  // 打开公告编辑器
  announcementEditorRef.value?.show(reportContent.value || '', '编辑评标报告内容')
}

// 查看内容
const handleViewContent = () => {
  announcementEditorRef.value?.show(reportContent.value, '查看评标报告内容', true)
}

async function loadNotificationTemplateContent(templateId: string) {
  try {
    const { data } = await getTemplateDetail(templateId);
    reportContent.value = data.content || '';
  } catch (error) {
    // 加载失败时保持空内容
  }
}

// 保存内容
const handleSaveContent = (content: string) => {
  reportContent.value = content
  ElMessage.success('内容保存成功')
}

// 获取内容预览
const getContentPreview = () => {
  if (!reportContent.value) return ''

  // 移除HTML标签，只显示纯文本预览
  const textContent = reportContent.value.replace(/<[^>]*>/g, '')
  return textContent.length > 100 ? textContent.substring(0, 100) + '...' : textContent
}



// 评分变化处理 - 实时计算总分
const handleScoreChange = (row: EvaluationTableRow) => {
  // 计算总分：所有节点评分的总和
  let totalScore = 0
  Object.values(row.nodeScores || {}).forEach(score => {
    totalScore += Number(score) || 0
  })
  row.totalScore = Number(totalScore.toFixed(2))

  // 自动更新排名
  // updateRanking()
}

// 手动刷新排名
const handleRefreshRanking = () => {
  // 按分数从高到低排序，并重新排列表格数据顺序
  const sortedData = [...evaluationTableData.value].sort((a, b) => b.totalScore - a.totalScore)

  // 更新排名并重新排列表格数据 - 支持并列排名
  let currentRank = 1
  let previousScore = null

  sortedData.forEach((item, index) => {
    // 如果分数与前一个不同，更新排名；如果分数相同，保持相同排名（并列）
    if (previousScore !== null && item.totalScore !== previousScore) {
      currentRank = index + 1
    }
    item.ranking = currentRank
    previousScore = item.totalScore
  })

  // 重新设置表格数据为排序后的数据
  evaluationTableData.value = sortedData

  // 先清空所有推荐状态和中标顺序
  evaluationTableData.value.forEach(item => {
    item.isRecommended = false
    item.winOrder = ''
  })
  let currentWinOrder = 1
  let previousScore1 = null

  // 设置前三个为推荐中标，并设置默认顺序 - 支持并列中标候选人
  evaluationTableData.value.slice(0, 3).forEach((item, index) => {
    item.isRecommended = true
    // 如果分数与前一个不同，更新中标顺序；如果分数相同，保持相同中标顺序（并列）
    if (previousScore1 !== null && item.totalScore !== previousScore1) {
      currentWinOrder = index + 1
    }
    item.winOrder = String(currentWinOrder) // 第一、第二、第三
    previousScore1 = item.totalScore
  })

  // ElMessage.success('排名已刷新，表格已按分数从高到低排序，已默认推荐前三名中标')
}

// 推荐中标变化处理
const handleRecommendChange = (row: EvaluationTableRow) => {
  if (!row.isRecommended) {
    row.winOrder = ''
  }
}

// 中标顺序变化处理 - 允许多个相同顺序
const handleWinOrderChange = () => {
  // 不做任何限制，允许多个供应商有相同的中标顺序
  // 这样可以处理分数相同的情况，同时为第一或第二等
}

// 获取中标顺序标签
const getWinOrderLabel = (order: string) => {
  const orderMap = {
    '1': '第一中标候选人',
    '2': '第二中标候选人',
    '3': '第三中标候选人'
  }
  return orderMap[order] || order
}

// 打开抽屉 - 编辑模式
const open = (sectionId: string | number, openReadonly: boolean) => {
  visible.value = true
  isReadonly.value = openReadonly
  currentSectionId.value = sectionId || null
  evaluationReport.value = []

  // 获取模板列表
  fetchReportTemplates()

  // 如果有 sectionId，调用接口获取数据，否则使用默认数据
  if (sectionId) {
    fetchOnlineEvaluationData()
  } else {
    // initDefaultEvaluationTableData()
  }
}

// 关闭抽屉
const handleClose = () => {
  visible.value = false
  isReadonly.value = false
  evaluationTableData.value = []
  evaluationReport.value = []
  currentSectionId.value = null
  summaryInfo.value = {}
  reportTemplateId.value = ''
  reportContent.value = ''
  reportTemplates.value = []
}

// 提交数据
const handleSubmit = async () => {
  try {
    // 验证标段ID
    if (!currentSectionId.value) {
      ElMessage.error('缺少标段ID，无法提交')
      return
    }

    // 验证评标报告内容
    if (!reportTemplateId.value) {
      ElMessage.error('请选择评标报告模板')
      return
    }

    if (!reportContent.value) {
      ElMessage.error('请编辑评标报告内容')
      return
    }

    if (!props.projectInfo?.evaluationId) {
      ElMessage.error('请完成评标委员会')
      return
    }

    // 验证是否有推荐中标的供应商
    const recommendedSuppliers = evaluationTableData.value.filter(item => item.isRecommended)
    if (recommendedSuppliers.length === 0) {
      ElMessage.error('请至少推荐一个中标供应商')
      return
    }

    // 验证推荐中标的供应商是否都设置了中标顺序
    const hasEmptyWinOrder = recommendedSuppliers.some(item => !item.winOrder)
    if (hasEmptyWinOrder) {
      ElMessage.error('请为所有推荐中标的供应商设置中标顺序')
      return
    }

    loading.value = true

    // 构造供应商评标信息列表
    const supplierWinnerInfoList = evaluationTableData.value.map(supplier => {
      const supplierInfo: any = {
        tenantSupplierId: supplier.tenantSupplierId,
        totalScore: supplier.totalScore,
        bidPrice: supplier.bidPrice,
        isRecommendedWinner: supplier.isRecommended ? 1 : 0,
        reviewSummary: supplier.reviewSummary,
        nodeScores: supplier.nodeScores || {}
      }

      // 如果推荐中标且有中标顺序，则设置中标候选顺序
      if (supplier.isRecommended && supplier.winOrder) {
        const candidateOrder = getEnumFromWinOrder(supplier.winOrder)
        if (candidateOrder) {
          supplierInfo.winnerCandidateOrder = candidateOrder
        }
      }

      return supplierInfo
    })

    // 使用模板格式化器处理公告内容
    const processedReportContent = templateContentFormatter(reportContent.value, {
      // 汇总信息
      summaryByName: summaryInfo.value.summaryByName || userInfoStore.userInfos.user?.username || '',
      summaryTime: summaryInfo.value.summaryTime || formatDate(new Date(), 'YYYY-mm-dd HH:MM:SS'),
      currentTime: formatDate(new Date(), 'YYYY-mm-dd HH:MM:SS'),
      currentDate: formatDate(new Date(), 'YYYY年mm月dd日'),

      // 评标数据
      evaluationData: evaluationTableData.value,
      supplierList: evaluationTableData.value,

      // 项目信息
      projectInfo: props.projectInfo,
      projectName: props.projectInfo?.projectName || '',
      sectionName: props.projectInfo?.sectionName || '',

      // 推荐中标供应商
      recommendedSuppliers: evaluationTableData.value.filter(item => item.isRecommended)
    }, {
      removeEmptyLoops: true,
      removeEmptyTags: true,
      debug: false
    })

    // 构造请求参数
    const saveParams = {
      sectionId: currentSectionId.value,
      evaluationId: props.projectInfo?.evaluationId,
      reportTemplateId: reportTemplateId.value,
      reportContent: processedReportContent,
      supplierWinnerInfoList
    }

    // 调用线上评标汇总保存接口
    await summaryEvaluation(saveParams)
    emit('submit')
    handleClose()
  } catch (error) {
    // 提交失败
  } finally {
    loading.value = false
  }
}

// 暴露方法
defineExpose({
  open
})
</script>

<style scoped lang="scss">
@import '@/views/procurementSourcing/biddingProcess/styles/collapse-panel.scss';

.evaluation-drawer-content {
  .summary-info-section {
    .section-title {
      h3 {
        margin: 0;
        font-size: 16px;
        font-weight: 600;
        color: #333;
      }
    }

    .summary-info-content {
      .info-row {
        display: flex;
        gap: 40px;
        align-items: center;
        padding: 16px;
        background: #f8f9fa;
        border-radius: 6px;

        .info-item {
          display: flex;
          align-items: center;

          .info-label {
            font-weight: 500;
            color: #666;
            margin-right: 8px;
            min-width: 80px;
          }

          .info-value {
            color: #333;
            font-weight: 500;
          }
        }
      }
    }
  }
  .evaluation-table-section {
    .section-title {
      display: flex;
      justify-content: space-between;
      align-items: center;

      h3 {
        margin: 0;
        font-size: 16px;
        font-weight: 600;
        color: #333;
      }
    }

    .readonly-table {
      .readonly-value {
        color: #333;
        font-weight: 500;
      }
    }
  }

  .evaluation-report-section {
    .section-title {
      h3 {
        margin: 0;
        font-size: 16px;
        font-weight: 600;
        color: #333;
      }
    }

    .report-edit-section {
      .template-selection {
        .el-form-item {
          margin-bottom: 16px;
        }
      }

      .content-edit {
        .content-status {
          margin-left: 12px;
          color: #67c23a;
          font-size: 14px;

          .success-icon {
            margin-right: 4px;
          }
        }
      }
    }

    .report-display {
      .report-content {
        padding: 16px;
        background: #f8f9fa;
        border-radius: 6px;

        .content-preview {
          max-height: 100px;
          overflow: hidden;
          margin-bottom: 12px;
          line-height: 1.6;
          color: #666;
        }
      }

      .no-report {
        padding: 12px;
        text-align: center;
        background: #f8f9fa;
        border-radius: 6px;
      }
    }
  }

  .text-gray-400 {
    color: #9ca3af;
  }

  // 排名表头样式
  .ranking-header {
    display: flex;
    align-items: center;
    justify-content: center;
    flex-wrap: wrap;
    gap: 4px;
  }

  // 总分样式
  .total-score {
    font-weight: 600;
    color: #409eff;
    font-size: 14px;
  }
}

.drawer-footer {
  display: flex;
  justify-content: flex-end;
}

.review-summary-content {
  padding: 20px;

  p {
    margin-bottom: 12px;
    line-height: 1.6;
  }
}

// 评审项汇总按钮样式
.text-success {
  color: #67c23a !important;
}

.text-danger {
  color: #f56c6c !important;
}
</style>
