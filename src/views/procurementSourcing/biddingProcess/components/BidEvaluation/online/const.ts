// 对象转数组
function objToArr(obj: any) {
  return Object.entries(obj).map(([value, label]) => ({
    label,
    value: isNaN(Number(value)) ? value : Number(value), // 如果是数字字符串，转换为数字类型
  }));
}

// 符合结果
export const CONFORMITY_RESULT_ENUM = {
  PASS: "1",
  FAIL: "0",
}
export const CONFORMITY_RESULT_OBJ = {
  [CONFORMITY_RESULT_ENUM.PASS]: '符合',
  [CONFORMITY_RESULT_ENUM.FAIL]: '不符合',
}
export const CONFORMITY_RESULT_OPTIONS = objToArr(CONFORMITY_RESULT_OBJ);

// 导出提交枚举类型
export const SUBMIT_STATUS_ENUM = {
  WAIT_SUBMIT: 'WAIT_SUBMIT',
  SUBMITTED: 'SUBMITTED',
  SUMMARIZED: 'SUMMARIZED',
}
export const SUBMIT_STATUS_OBJ = {
  [SUBMIT_STATUS_ENUM.WAIT_SUBMIT]: '待提交',
  [SUBMIT_STATUS_ENUM.SUBMITTED]: '已提交',
  [SUBMIT_STATUS_ENUM.SUMMARIZED]: '已汇总',
}

// 操作类型
export const OPERATION_TYPE_ENUM = {
  EDIT: "EDIT",
  ADD: "ADD",
  VIEW: "VIEW",
}
