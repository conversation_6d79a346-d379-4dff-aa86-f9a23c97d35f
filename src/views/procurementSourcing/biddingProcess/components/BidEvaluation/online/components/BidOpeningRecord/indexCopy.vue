<template>
  <yun-drawer
    v-model="showVisible"
    size="X-large"
    title="查看开标记录"
    append-to-body
    destroy-on-close
    :show-footer="false"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
  >
    <div class="quotation-management">
      <!-- 第一行：视图切换和报价信息 -->
      <div class="view-control">
        <div class="left-section">
          <el-radio-group
            v-model="viewMode"
            class="view-tabs"
            @change="handleViewModeChange"
          >
            <el-radio-button
              v-for="option in VIEW_TYPE_OPTIONS"
              :key="option.value"
              :label="option.value"
            >
              {{ option.label }}
            </el-radio-button>
          </el-radio-group>
        </div>
      </div>

      <!-- 第二行：查询条件和导出按钮 -->
      <div class="document-review-content">
        <el-form
          :model="queryForm"
          inline
          class="bidding-process-search-form"
        >
          <div class="form-content">
            <div class="form-item-wrapper">
              <label class="form-label">选择报价轮次</label>
              <el-select
                v-model="queryForm.roundNo"
                placeholder="请选择"
                class="search-select"
                @change="loadData"
              >
                <el-option
                  v-for="item in quotationRounds"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </div>
            <div
              class="form-item-wrapper"
              v-if="viewMode === VIEW_TYPE.MATERIAL"
            >
              <label class="form-label">物料名称</label>
              <el-input
                v-model="queryForm.materialName"
                placeholder="请输入物料名称"
                clearable
                class="search-input"
                @change="loadData"
              />
            </div>
            <!-- <div
            class="form-item-wrapper"
            v-if="viewMode === VIEW_TYPE.MATERIAL"
          >
            <label class="form-label">物料编码</label>
            <el-input
              v-model="queryForm.materialName"
              placeholder="请输入物料名称"
              clearable
              class="search-input"
              @change="loadData"
            />
          </div> -->
            <div
              class="form-item-wrapper"
              v-if="viewMode === VIEW_TYPE.SUPPLIER"
            >
              <label class="form-label">供应商名称</label>
              <el-input
                v-model="queryForm.supplierName"
                placeholder="请输入供应商名称"
                clearable
                class="search-input"
                @change="loadData"
              />
            </div>
            <div>
              <el-button
                @click="loadData"
                type="primary"
              >
                搜索
              </el-button>
              <el-button @click="handleReset"> 重置 </el-button>
              <el-button @click="handleDownloadTemplate"> 数据导出 </el-button>
            </div>
          </div>
        </el-form>
      </div>
      <!-- 第三行：提示信息 -->
      <div
        class="alert-section"
        v-if="viewMode === VIEW_TYPE.MATERIAL && quoteLessThanThreeMaterialCount"
      >
        <img
          src="https://oss-public.yunlizhi.cn/frontend/fe-procurement-platform/alert_icon.svg"
          alt=""
        />
        <div style="">
          <el-button
            type="text"
            disabled
            >提醒：{{ quoteLessThanThreeMaterialCount }}条物料报价供应商不足3家，</el-button
          >
          <el-button
            @click="getQuoteLessThanThreeMaterialCode"
            type="text"
            >请查看</el-button
          >
        </div>
      </div>

      <!-- 第四行：表格物料 -->
      <el-table
        :data="tableData"
        class="editable-table"
        v-if="viewMode === VIEW_TYPE.MATERIAL"
        :span-method="objectSpanMethod"
        height="70vh"
      >
        <template
          v-for="item in dynamicColumn"
          :key="item.prop || item.label"
        >
          <template v-if="item.children?.length">
            <el-table-column
              :key="item.prop"
              :label="item.label"
              :prop="item.prop"
              :min-width="item.width"
              :show-overflow-tooltip="item.showOverflowTooltip"
            >
              <el-table-column
                v-for="child in item.children"
                :key="child.prop"
                :label="child.label"
                :prop="child.prop"
                :min-width="child.width"
                :show-overflow-tooltip="child.showOverflowTooltip"
              >
              </el-table-column>
            </el-table-column>
          </template>
          <el-table-column
            v-else
            :key="item.prop || item.label"
            :label="item.label"
            :prop="item.prop"
            :min-width="item.width"
            :show-overflow-tooltip="item.showOverflowTooltip"
          >
          </el-table-column>
        </template>
        <el-table-column
          label="投标IP"
          prop="quoteIp"
          width="140"
        />
      </el-table>

      <!-- 第五行：表格供应商 -->
      <el-table
        :data="tableSupplierData"
        class="editable-table"
        height="70vh"
        v-else
      >
        <el-table-column
          v-for="item in SUPPLIER_COLUMN"
          :key="item.prop"
          :label="item.label"
          :prop="item.prop"
          :type="item.type"
          :width="item.width || undefined"
        >
          <template #default="{ row, $index }">
            <template v-if="item.type === 'index'">
              {{ $index + 1 }}
            </template>
            <template v-else-if="item.prop === 'lookUp'">
              <el-button
                type="text"
                @click="handleViewRegister(row)"
                >查看</el-button
              >
            </template>
            <template v-else-if="item.prop === 'action'">
              <el-button
                type="text"
                @click="handleViewDetail(row)"
                >报价明细</el-button
              >
            </template>
            <template v-else>
              {{ row?.[item?.prop as string] || '--' }}
            </template>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <!-- 报价详情抽屉 -->
    <QuotationDetail ref="quotationDetailRef" />

    <!-- 报名资料 -->
    <AuditDrawer
      v-model:visible="auditDrawerVisible"
      :row="currentRow"
      :btnType="BTN_O_TYPE.DETAIL"
    />
  </yun-drawer>
</template>

<script lang="ts" setup>
import { ref, computed, onMounted, onBeforeMount } from 'vue';
import AuditDrawer from '@/views/procurementSourcing/biddingProcess/components/bidding/DocumentReview/AuditDrawer.vue';
import QuotationDetail from '../QuotationDetail/index.vue';
import { queryQuoteListByMaterial, queryQuoteListBySupplier, getQuoteLessThanThreeMaterialCodeList } from '@/api/purchasing/bid';
import { VIEW_TYPE_OPTIONS, VIEW_TYPE, SUPPLIER_COLUMN, BTN_O_TYPE } from '@/views/procurementSourcing/biddingProcess/constants';
import { numberToChinese } from '@/views/procurementSourcing/biddingProcess/utils';
import { useBiddingStore } from '@/views/procurementSourcing/biddingProcess/stores';
import { ElMessage } from 'yun-design';
// @ts-ignore
import { useDynamicTable } from '@/views/procurementSourcing/biddingProcess/hooks/useDynamicTable.jsx';
import moment from 'moment';
// @ts-ignore
import { useExcel } from '@/views/procurementSourcing/biddingProcess/hooks/useExcel.jsx';

// 详情抽屉控制
const quotationDetailRef = ref<any>(null);
const { dynamicColumn, tableData, setDynamicColumn, handleTableData, objectSpanMethod } = useDynamicTable();
const { exportToExcel } = useExcel();

const biddingStore = useBiddingStore();
const noticeInfo = computed(() => biddingStore?.noticeInfo);
const projectId = computed(() => biddingStore?.projectId);
// const isPublic = computed(() => biddingStore?.projectDetail?.inviteMethod === 'PUBLICITY');
const noticeId = computed(() => biddingStore?.noticeId);

const showVisible = ref(false);

// 报名资料抽屉控制
const auditDrawerVisible = ref(false);
const currentRow = ref<any>(null);
// 表格数据
const currentSectionId = ref('');
const tableSupplierData = ref<any[]>([]);
// 查看模式
const viewMode = ref<any>(VIEW_TYPE.MATERIAL);
// Tab 切换相关
// 报价轮次选项
const quotationRounds = ref<any[]>([]);
// 已经发起多少轮
const hasQuotation = ref(0);
// 查询表单
const initFormData = (ext = {}) => {
  return {
    roundNo: '',
    materialName: '',
    supplierName: '',
    ...ext,
  };
};
const queryForm = ref<any>(initFormData());

const handleDownloadMaterialTemplate = async () => {
  try {
    const blob = await exportToExcel(
      tableData.value,
      dynamicColumn.value?.concat({
        label: '投标IP',
        prop: 'quoteIp',
        fieldName: '投标IP',
        fieldCode: 'quoteIp',
      })
    );
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `物料数据_${moment().format('YYYY-MM-DD HH:mm:ss')}.xlsx`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    window.URL.revokeObjectURL(url);
  } catch (error) {
    ElMessage.error('导出失败');
  }
};

const handleDownloadSupplierTemplate = async () => {
  try {
    const blob = await exportToExcel(
      tableSupplierData.value,
      SUPPLIER_COLUMN?.filter((item: any) => !item.ingoreExport)?.map((item: any) => ({
        label: item.label,
        prop: item.prop,
        fieldName: item.label,
        fieldCode: item.prop,
      }))
    );
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `供应商数据_${moment().format('YYYY-MM-DD HH:mm:ss')}.xlsx`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    window.URL.revokeObjectURL(url);
  } catch (error) {
    ElMessage.error('导出失败');
  }
};

// 处理下载模板
async function handleDownloadTemplate() {
  if (viewMode.value === VIEW_TYPE.MATERIAL) {
    handleDownloadMaterialTemplate();
  } else if (viewMode.value === VIEW_TYPE.SUPPLIER) {
    handleDownloadSupplierTemplate();
  }
}
function handleReset() {
  queryForm.value = initFormData({
    roundNo: quotationRounds.value?.[0]?.value || '',
  });
  loadData();
}

const isNewQuotation = computed(() => {
  return queryForm.value?.roundNo === quotationRounds.value?.length;
});

// 条物料报价供应商不足3家
const quoteLessThanThreeMaterialCount = computed(() => {
  const projectItemIds = [...new Set(tableData.value?.map((item: any) => item?.projectItemId) || [])];
  let count = 0;
  projectItemIds?.forEach((item: any) => {
    const quoteList = tableData.value?.filter((i: any) => i?.projectItemId === item);
    if (quoteList?.length < 3) {
      count++;
    }
  });
  return count;
});

// 按物料查询报价列表
async function queryQuoteListByMaterialData(extParams = {}) {
  try {
    const res = await queryQuoteListByMaterial(
      {
        sectionId: currentSectionId.value,
        noticeId: noticeId.value,
        projectId: projectId.value,
        ...queryForm.value,
        ...extParams,
      },
      {
        current: 1,
        size: 200,
      }
    );
    // eslint-disable-next-line no-console

    const list = res?.data?.records || [];

    setDynamicColumn(list);
    handleTableData(list, isNewQuotation.value);
  } catch (error) {
    // eslint-disable-next-line no-console
    console.log('error:', error);
  }
}
// 按供应商查询报价列表
async function queryQuoteListBySupplierData() {
  try {
    const res = await queryQuoteListBySupplier({
      sectionId: currentSectionId.value,
      noticeId: noticeId.value,
      projectId: projectId.value,
      ...queryForm.value,
    });
    // eslint-disable-next-line no-console
    const list = res?.data || [];
    tableSupplierData.value = list?.map((row: any) => {
      const { quoteMaterialCount, totalMaterialCount } = row;
      return {
        ...row,
        count: totalMaterialCount ? `${quoteMaterialCount || 0}/${totalMaterialCount}` : quoteMaterialCount || 0,
      };
    });
  } catch (error) {
    // eslint-disable-next-line no-console
    console.log('error:', error);
    tableSupplierData.value = [];
  }
}
// 切换查看模式
function handleViewModeChange(mode: string) {
  initRoundsData();
  if (mode === VIEW_TYPE.MATERIAL) {
    queryQuoteListByMaterialData();
  } else if (mode === VIEW_TYPE.SUPPLIER) {
    queryQuoteListBySupplierData();
  }
}

async function getQuoteLessThanThreeMaterialCode() {
  try {
    const res = await getQuoteLessThanThreeMaterialCodeList({
      noticeId: noticeId.value,
      sectionId: currentSectionId.value,
      quoteRound: hasQuotation.value,
    });
    if (res?.data?.length) {
      await queryQuoteListByMaterialData({
        materialCodeList: res?.data,
      });
      ElMessage.success('查询成功');
    } else {
      ElMessage.error('暂无数据');
    }
    // eslint-disable-next-line no-empty
  } catch (error) {}
}

const quoteRoundCount = computed(() => {
  const list = noticeInfo?.value?.quoteRoundVoList || [];
  const findRound = list.find((item: any) => String(item?.sectionId) === String(currentSectionId.value)) || {};
  return findRound?.currentQuoteRound || 1;
});

// 初始化轮次数据
function initRoundsData() {
  const count = quoteRoundCount.value;
  hasQuotation.value = count;
  quotationRounds.value = Array.from({ length: count }, (_, i) => i + 1).map((item) => {
    return {
      label: `第${numberToChinese(item)}轮报价`,
      value: item,
    };
  });
  const lastRound = quotationRounds.value?.[quotationRounds.value?.length - 1]?.value || '';
  queryForm.value = initFormData({
    roundNo: lastRound,
  });
}

// 加载数据
async function loadData() {
  if (viewMode.value === VIEW_TYPE.MATERIAL) {
    queryQuoteListByMaterialData();
  } else if (viewMode.value === VIEW_TYPE.SUPPLIER) {
    queryQuoteListBySupplierData();
  }
}

// 查看详情
function handleViewDetail(row: any) {
  quotationDetailRef.value.show({
    ...row,
    roundNo: quoteRoundCount.value,
    sectionId: currentSectionId.value,
  });
}

// 报名资料
function handleViewRegister(row: any) {
  currentRow.value = { ...row, sectionId: currentSectionId.value };
  auditDrawerVisible.value = true;
}

onMounted(async () => {});
onBeforeMount(async () => {});

function close() {
  currentRow.value = {};
  showVisible.value = false;
  currentSectionId.value = '';
}
function show(row: any) {
  currentRow.value = {};
  showVisible.value = true;
  currentSectionId.value = row?.sectionId;
  initRoundsData();
  loadData();
}

defineExpose({
  show,
  close,
});
</script>

<style lang="scss" scoped>
@import '@/views/procurementSourcing/biddingProcess/styles/collapse-panel.scss';
.alert-section {
  display: flex;
  padding: 8px 16px;
  align-items: center;
  gap: 8px;
  align-self: stretch;
  border-radius: var(--Radius-border-radius-small, 2px);
  background: var(--Color-Warning-color-warning-light-9, #fff9e8);
  color: var(--Color-Text-text-color-primary, #1d2129);
  font-family: 'PingFang SC';
  font-size: 13px;
  font-style: normal;
  font-weight: 400;
  line-height: 22px;
}
</style>
