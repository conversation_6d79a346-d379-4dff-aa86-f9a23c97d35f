<template>
  <yun-dialog
    v-model="visible"
    title="评审结论"
    :size="'small'"
    :confirm-button-handler="confirmHandler"
    :confirm-button-text="'确定'"
    :cancel-button-text="'取消'"
    :before-close="handleClose"
  >
    <yun-pro-form
      ref="formRef"
      custom-class="batch-form"
      :form="form"
      :columns="schema"
      :config="config"
      :form-props="{ labelPosition: 'top', labelWidth: '240px' }"
    />
  </yun-dialog>
</template>
<script setup>
import { ref, computed } from 'vue';
import { ElMessage } from 'yun-design';
import { useForm } from '@/hooks/useForm';
import { inviteSupplier } from '@/api/purchasing/bid';
import { useBiddingStore } from '@/views/procurementSourcing/biddingProcess/stores';

const biddingStore = useBiddingStore();

const emit = defineEmits(['refresh']);
const visible = ref(false);
const { formRef, form, config, validateBasicForm, resetForm, setForm } = useForm();
const schema = computed(() => [
  {
    prop: 'remark',
    type: 'input',
    // label: '选择供应商',
    attrs: {
      placeholder: '请输入评审结论',
      type: 'textarea',
      rows: 8,
      // maxlength: 64,
    },
    colProps: { span: 24 },
    rules: [{ trigger: 'blur', required: true, message: '请输入评审结论' }],
  },
]);

const show = (row) => {
  setForm({ ...row });
  visible.value = true;
};
function handleClose() {
  resetForm();
  visible.value = false;
  emit('onClose');
}

const confirmHandler = async () => {
  await validateBasicForm();
  try {
    await inviteSupplier({
      ...form.value,
      supplierInfoList: (biddingStore?.supplierInfoList || [])
        ?.filter((item) => form.value?.supplierInfoList?.includes(item.id))
        ?.map((item) => ({ tenantSupplierId: item.id, supplierName: item.supplierName })),
    });
    ElMessage.success('操作成功');
    handleClose();
    emit('refresh');
  } catch (e) {
    // eslint-disable-next-line no-console
    console.log(e);
  }
};

defineExpose({
  show,
});
</script>
<style lang="scss" scoped>
.close-title {
  margin: 0 0 8px 0;
}
</style>
