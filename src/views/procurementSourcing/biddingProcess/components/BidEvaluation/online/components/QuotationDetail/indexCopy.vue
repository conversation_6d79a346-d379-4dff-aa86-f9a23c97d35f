<template>
  <yun-drawer
    v-model="showVisible"
    title="报价明细"
    size="X-large"
    :with-header="true"
    append-to-body
    destroy-on-close
    :show-footer="false"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    custom-class="bid-custom-drawer"
  >
    <div
      class="flex flex-col"
      v-loading="loading"
      style="height: 98%"
    >
      <!-- 报价轮次和基本信息 -->
      <div>
        <el-tabs
          v-model="activeTab"
          @tab-click="handleTabClick"
        >
          <el-tab-pane
            v-for="item in quotationRounds"
            :key="item.value"
            :label="item.label"
            :name="item.value"
          />
        </el-tabs>
      </div>
      <div
        class="other-info-wrapper"
        v-if="tableData?.length"
      >
        <div class="flex items-center gap-2">
          <div class="item-label">第{{ numberToChinese(activeTab) }}次报价</div>
          <div class="item-block">
            <span class="label">报价时间:</span>
            <span class="value">{{ tableDataInfo?.quoteTime ? moment(tableDataInfo?.quoteTime).format('YYYY-MM-DD HH:mm:ss') : '--' }}</span>
          </div>
          <div class="item-block">
            <span class="label">报价IP:</span>
            <span class="value">{{ tableDataInfo?.quoteIp || '--' }}</span>
          </div>
        </div>
        <div class="flex items-center gap-2">
          <div class="item-block">
            <span class="label">报价文件:</span>
            <template v-if="quoteAttachments">
              <el-tooltip
                class="item"
                effect="dark"
                placement="top"
                :content="quoteAttachments.fileName"
              >
                <span class="value file-value">{{ quoteAttachments.fileName }}</span>
              </el-tooltip>
              <span class="download-btn">
                <el-link
                  :href="quoteAttachments.filePath"
                  target="_blank"
                  type="primary"
                >
                  下载
                </el-link>
              </span>
            </template>
            <span
              class="value"
              v-else
              >--</span
            >
          </div>
          <div class="item-block">
            <span class="label">其他附件:</span>
            <template v-if="otherAttachments">
              <el-tooltip
                class="item"
                effect="dark"
                placement="top"
                :content="otherAttachments.fileName"
              >
                <span class="value file-value">{{ otherAttachments.fileName }}</span>
              </el-tooltip>
              <span class="download-btn">
                <el-link
                  :href="otherAttachments.filePath"
                  target="_blank"
                  type="primary"
                >
                  下载
                </el-link>
              </span>
            </template>
            <span
              class="value"
              v-else
              >--</span
            >
          </div>
        </div>
      </div>

      <el-form
        :model="queryForm"
        inline
        class="bidding-process-search-form"
      >
        <div class="form-content">
          <div class="form-item-wrapper">
            <label class="form-label">物料名称</label>
            <el-input
              v-model="queryForm.materialName"
              placeholder="请输入物料名称"
              clearable
              class="search-input"
            />
          </div>
          <div class="form-item-wrapper">
            <label class="form-label">物料编码</label>
            <el-input
              v-model="queryForm.materialCode"
              placeholder="请输入物料编码"
              clearable
              class="search-input"
            />
          </div>
          <div>
            <el-button
              @click="queryQuoteListByMaterialData"
              type="primary"
            >
              搜索
            </el-button>
            <el-button @click="handleReset"> 重置 </el-button>
          </div>
        </div>
      </el-form>

      <div class="need-hide-table-card flex-1">
        <yun-pro-table
          ref="proTableRef"
          :table-columns="dynamicColumn"
          v-model:tableData="tableData"
          :auto-height="true"
          layout="whole"
          :default-fetch="false"
          :table-props="{
            ...tablePropsObj,
            'span-method': objectSpanMethod,
          }"
        >
        </yun-pro-table>
      </div>
    </div>
  </yun-drawer>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import { queryQuoteDetailByMaterial } from '@/api/purchasing/evaluation';
import { useBiddingStore } from '@/views/procurementSourcing/biddingProcess/stores';
import { numberToChinese } from '@/views/procurementSourcing/biddingProcess/utils';
import moment from 'moment';
// @ts-ignore
import { useDynamicTable } from '@/views/procurementSourcing/biddingProcess/hooks/useDynamicTable.jsx';

const props = defineProps({
  isFixedRound: {
    type: Boolean,
    default: false,
  },
});

// 查询表单
const initFormData = () => {
  return {
    materialName: '',
    materialCode: '',
  };
};
const queryForm = ref<any>(initFormData());

const tablePropsObj = computed(() => ({
  // ...tableProps.value,
  stripe: true,
  border: true,
  headerCellStyle: {
    backgroundColor: '#f5f7fa',
    color: '#303133',
    fontWeight: 'bold',
    'vertical-align': 'middle',
    // height: '40px',
  },
  cellStyle: {
    padding: '0',
    // height: '40px',
    'vertical-align': 'middle',
  },
  rowStyle: {
    // height: '40px',
  },
  rowHeight: 40,
  // rowKey: 'phone',
}));

const { dynamicColumn, tableData, setDynamicColumn, handleTableData, objectSpanMethod } = useDynamicTable();
const biddingStore = useBiddingStore();
const noticeId = computed(() => biddingStore?.noticeId);
const projectId = computed(() => biddingStore?.projectId);
const loading = ref(false);
const roundList = ref<any>([]);
const showVisible = ref(false);
const currentRow = ref<any>(null);

const activeTab = ref(0);
// 报价轮次
const quotationRounds = ref<any>([]);
// const quoteRoundCount = ref(0);
const tableDataInfo = computed(() => (roundList.value || []).find((item: any) => item.roundNo === activeTab.value) || {});

const otherAttachments = computed(() => (tableDataInfo?.value?.otherAttachments || [])?.[0] || null);
const quoteAttachments = computed(() => (tableDataInfo?.value?.quoteAttachments || [])?.[0] || null);

async function initRoundData() {
  const lastRound = currentRow.value?.roundNo || 0;

  // 固定轮次只会有一轮报价
  if (props.isFixedRound) {
    activeTab.value = lastRound;
    quotationRounds.value = [
      {
        label: `第${numberToChinese(lastRound)}轮报价`,
        value: lastRound,
      },
    ];
    return;
  } else {
    quotationRounds.value = Array.from({ length: lastRound }, (_, i) => i + 1)
      .map((item) => {
        return {
          label: `第${numberToChinese(item)}次报价`,
          value: item,
        };
      })
      .reverse();
    activeTab.value = quotationRounds.value[0]?.value || null;
  }
}

// 初始化数据
async function initData() {
  // if (props.isFixedRound) {
    const data = tableDataInfo?.value?.materialList || [];
    setDynamicColumn(data);
    handleTableData(data);
  // }
}

function handleReset() {
  queryForm.value = initFormData();

  queryQuoteListByMaterialData();
}

// 按物料查询报价列表
async function queryQuoteListByMaterialData() {
  loading.value = true;
  try {
    const res = await queryQuoteDetailByMaterial({
      noticeId: noticeId.value,
      projectId: projectId.value,
      sectionId: currentRow?.value?.sectionId,
      tenantSupplierId: currentRow.value?.tenantSupplierId || null,
      ...queryForm.value,
    });

    roundList.value = res?.data?.roundList || [];

    initRoundData();
    initData();
  } catch (error) {
    // eslint-disable-next-line no-console
    console.log('error:', error);
    roundList.value = [];
  }
  loading.value = false;
}

function handleTabClick() {
  loading.value = true;
  setTimeout(() => {
    loading.value = false;
  }, 300);
}

function show(row: any) {
  currentRow.value = row;
  activeTab.value = 0;

  showVisible.value = true;
  queryQuoteListByMaterialData();
}

defineExpose({
  show,
});
</script>

<style scoped lang="scss">
@import '@/views/procurementSourcing/biddingProcess/styles/collapse-panel.scss';

.other-info-wrapper {
  display: flex;
  align-items: center;
  justify-content: space-between;
  // gap: 8px;
  // margin: 20px 0;
  margin-bottom: 20px;
  margin-top: 12px;
  .item-label {
    color: var(--Color-Text-text-color-primary, #1d2129);
    /* bold/medium */
    font-family: 'PingFang SC';
    font-size: 16px;
    font-style: normal;
    font-weight: 600;
    line-height: 24px; /* 150% */
    position: relative;
    padding-left: 10px;
    &::before {
      content: ' ';
      position: absolute;
      left: 0;
      top: 50%;
      transform: translateY(-50%);
      width: 2px;
      height: 14px;
      background: var(--Color-Primary-color-primary, #0069ff);
    }
  }
  .item-block {
    display: inline-flex;
    align-items: center;
    border-radius: var(--Radius-border-radius-small, 2px);
    border: 1px solid var(--Color-Border-border-color-light, #e4e7ed);
    background: var(--color-white, #fff);
    height: 24px;
    padding: 2px 8px;
    box-sizing: border-box;
    gap: 8px;
    .label {
      color: var(--Color-Text-text-color-secondary, #86909c);
      /* regular/base */
      font-family: 'PingFang SC';
      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      line-height: 22px; /* 157.143% */
    }
    .value {
      color: var(--Color-Text-text-color-primary, #1d2129);
      /* regular/base */
      font-family: 'PingFang SC';
      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      line-height: 22px;
    }
    .file-value {
      max-width: 160px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      word-break: break-all;
    }
    .download-btn {
      color: var(--Color-Primary-color-primary, #0069ff);
      /* regular/base */
      font-family: 'PingFang SC';
      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      line-height: 22px;
    }
  }
}
</style>
