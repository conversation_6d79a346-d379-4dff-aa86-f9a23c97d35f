<template>
  <div
    class="front-router-view-container overflow-hidden"
    v-loading="loading"
  >
    <div
      class="flex flex-col flex-1 box-border"
      style="background-color: #fff"
    >
      <div
        class="score-weight"
        v-if="isScoreItem"
      >
        <div class="score-weight-item">
          <span class="score-weight-item-label">节点总分:</span>
          <span class="score-weight-item-value">{{ countInfo.totalScore }}</span>
        </div>
        <div class="score-weight-item">
          <span class="score-weight-item-label">权重:</span>
          <span class="score-weight-item-value">{{ countInfo.totalWeight || 0 }}%</span>
        </div>
      </div>
      <div class="need-hide-table-card pt-5 pl-5 pr-5">
        <el-table
          :data="displayTableData"
          style="width: 100%"
          fit
          v-bind="tablePropsObj"
        >
          <el-table-column
            fixed="left"
            type="index"
            label="序号"
            width="60"
          >
            <template #default="{ row, $index }">
              <span v-if="row.isOverallSummary"> 合计 </span>
              <span v-else> {{ $index + 1 }} </span>
            </template>
          </el-table-column>
          <el-table-column
            prop="itemName"
            label="评审项"
            show-overflow-tooltip
            fixed="left"
          />
          <el-table-column
            prop="itemDescription"
            label="评审项详情"
            show-overflow-tooltip
            fixed="left"
          />
          <el-table-column
            v-for="column in dynamicColumns"
            :key="column.prop"
            :prop="column.prop"
            :label="column.label"
            :minWidth="column.minWidth"
          >
            <template #default="{ row }">
              <div class="flex items-center gap-2">
                <template v-if="isEvaluationItem">
                  <template v-if="!row.isOverallSummary">
                    <el-radio-group
                      :disabled="isView"
                      v-model="row[`isConform${speator}${column?.tenantSupplierId}`]"
                    >
                      <el-radio
                        v-for="item in CONFORMITY_RESULT_OPTIONS"
                        :key="item.value"
                        :label="String(item.value)"
                      >
                        {{ item.label }}
                      </el-radio>
                    </el-radio-group>
                    <el-popover
                      placement="bottom"
                      title=""
                      width="200px"
                      trigger="click"
                      v-model:visible="popoverVisible[`${row.standardId}${speator}${column.tenantSupplierId}`]"
                      :teleported="true"
                      popper-class="table-popover"
                    >
                      <div class="flex flex-col items-center gap-2">
                        <el-input
                          class="w-full transparent-input"
                          placeholder="请输入备注"
                          size="small"
                          style="border: none; outline: none; background-color: transparent"
                          v-model="remark"
                          :maxlength="200"
                          :disabled="isView"
                        ></el-input>
                        <div
                          class="flex items-center justify-end mt-2 w-full"
                          v-if="!isView"
                        >
                          <el-button
                            size="small"
                            @click="handleCancelRemark(row, column)"
                            >取消</el-button
                          >
                          <el-button
                            size="small"
                            type="primary"
                            @click="handleRemark(row, column)"
                            border
                            >提交</el-button
                          >
                        </div>
                      </div>
                      <template #reference>
                        <el-button
                          type="text"
                          class="ml-6"
                          @click="handleEdit(row, column)"
                        >
                          <span>备注</span>
                          <el-icon class="ml-2">
                            <EditPen />
                          </el-icon>
                        </el-button>
                      </template>
                    </el-popover>
                  </template>
                  <template v-else>
                    <el-radio-group
                      :disabled="true"
                      v-model="row[`isConform${speator}${column?.tenantSupplierId}`]"
                    >
                      <el-radio
                        v-for="item in CONFORMITY_RESULT_OPTIONS"
                        :key="item.value"
                        :label="item.value"
                      >
                        {{ item.label }}
                      </el-radio>
                    </el-radio-group>
                  </template>
                </template>
                <template v-if="isScoreItem">
                  <template v-if="!row.isOverallSummary">
                    <el-input-number
                      v-model="row[`score${speator}${column?.tenantSupplierId}`]"
                      step-strictly
                      :step="1"
                      :max="row?.maxScore"
                      :min="!isEmptyValue(row[`score${speator}${column?.tenantSupplierId}`]) ? row?.minScore || 1 : undefined"
                      :precision="0"
                      :disabled="isView"
                      :placeholder="`请输入${row?.minScore || 1}～${row?.maxScore}分`"
                    />
                  </template>

                  <span v-else>
                    {{ row[`score${speator}${column?.tenantSupplierId}`] || 0 }}
                  </span>
                </template>
              </div>
            </template>
          </el-table-column>
        </el-table>
        <!-- <yun-pro-table
          ref="proTableRef"
          :table-columns="tableColumns"
          v-model:tableData="displayTableData"
          :auto-height="true"
          :table-props="tablePropsObj"
          layout="whole"
          :default-fetch="false"
          border
        >
          <template #t_index="{ row, $index }">
            <span v-if="row.isOverallSummary"> 合计 </span>
            <span v-else> {{ $index + 1 }} </span>
          </template>
          <template #t_itemName="{ row }">
            <span v-if="row.isOverallSummary"> </span>
            <span v-else> {{ row.itemName }} </span>
          </template>
          <template #t_itemDescription="{ row }">
            <span v-if="row.isOverallSummary"> </span>
            <span v-else> {{ row.itemDescription }} </span>
          </template>
          <template
            v-for="column in dynamicColumns"
            :key="column.prop"
            v-slot:[`t_${column.prop}`]="{ row }: { row: any }"
          >
            <div class="flex items-center gap-2">
              <template v-if="isEvaluationItem">
                <template v-if="!row.isOverallSummary">
                  <el-radio-group
                    :disabled="isView"
                    v-model="row[`isConform${speator}${column?.tenantSupplierId}`]"
                  >
                    <el-radio
                      v-for="item in CONFORMITY_RESULT_OPTIONS"
                      :key="item.value"
                      :label="item.value"
                    >
                      {{ item.label }}
                    </el-radio>
                  </el-radio-group>
                  <el-popover
                    placement="bottom"
                    title=""
                    width="200px"
                    trigger="click"
                    v-model:visible="popoverVisible[`${row.standardId}${speator}${column.tenantSupplierId}`]"
                    :teleported="true"
                    popper-class="table-popover"
                  >
                    <div class="flex flex-col items-center gap-2">
                      <el-input
                        class="w-full transparent-input"
                        placeholder="请输入备注"
                        size="small"
                        style="border: none; outline: none; background-color: transparent"
                        v-model="remark"
                        :maxlength="200"
                        :disabled="isView"
                      ></el-input>
                      <div
                        class="flex items-center justify-end mt-2 w-full"
                        v-if="!isView"
                      >
                        <el-button
                          size="small"
                          @click="handleCancelRemark(row, column)"
                          >取消</el-button
                        >
                        <el-button
                          size="small"
                          type="primary"
                          @click="handleRemark(row, column)"
                          border
                          >提交</el-button
                        >
                      </div>
                    </div>
                    <template #reference>
                      <el-button
                        type="text"
                        class="ml-6"
                        @click="handleEdit(row, column)"
                      >
                        <span>备注</span>
                        <el-icon class="ml-2">
                          <EditPen />
                        </el-icon>
                      </el-button>
                    </template>
                  </el-popover>
                </template>
                <template v-else>
                  <el-radio-group
                    :disabled="true"
                    v-model="row[`isConform${speator}${column?.tenantSupplierId}`]"
                  >
                    <el-radio
                      v-for="item in CONFORMITY_RESULT_OPTIONS"
                      :key="item.value"
                      :label="item.value"
                    >
                      {{ item.label }}
                    </el-radio>
                  </el-radio-group>
                </template>
              </template>
              <template v-if="isScoreItem">
                <el-input-number
                  v-if="!row.isOverallSummary"
                  v-model="row[`score${speator}${column?.tenantSupplierId}`]"
                  step-strictly
                  :step="1"
                  :max="row?.maxScore"
                  :min="row?.minScore || 1"
                  :precision="0"
                  :disabled="isView"
                  :placeholder="`请输入${row?.minScore || 1}～${row?.maxScore}分`"
                />
                <span v-else>
                  {{ row[`score${speator}${column?.tenantSupplierId}`] || 0 }}
                </span>
              </template>
            </div>
          </template>
        </yun-pro-table> -->
      </div>
      <div class="flex items-center fixed-bottom-container">
        <el-button
          type="primary"
          :loading="btnLoading"
          :disabled="btnLoading || tableData?.length === 0"
          @click="confirmHandler(SUBMIT_STATUS_ENUM.SUBMITTED)"
          v-if="!isView"
        >
          提交
        </el-button>
        <el-button
          type="primary"
          border
          :loading="btnLoading"
          :disabled="btnLoading || tableData?.length === 0"
          @click="confirmHandler(SUBMIT_STATUS_ENUM.WAIT_SUBMIT)"
          v-if="!isView"
        >
          保存
        </el-button>
        <el-button
          @click="close"
          :disabled="btnLoading"
        >
          取消
        </el-button>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed, onMounted, onUnmounted, nextTick } from 'vue';
import { ElMessage } from 'yun-design';
import { EditPen } from '@yun-design/icons-vue';
import {
  CONFORMITY_RESULT_OPTIONS,
  SUBMIT_STATUS_ENUM,
  OPERATION_TYPE_ENUM,
} from '@/views/procurementSourcing/biddingProcess/components/BidEvaluation/online/const';
import { useBiddingStore, useEvaluationStore } from '@/views/procurementSourcing/biddingProcess/stores';
import { useRoute } from 'vue-router';
import { getExpertScoringTableByNode, batchSaveScoring } from '@/api/purchasing/evaluation';
import mittBus from '/@/utils/mitt';
// import { EVALUATION_MEMBER_ROLE_ENUM } from '@/views/procurementSourcing/biddingProcess/components/BidEvaluation/committee/const';
import { isEmptyValue } from '@/views/procurementSourcing/biddingProcess/utils/bid';

const evaluationStore = useEvaluationStore();
const biddingStore = useBiddingStore();
const projectId = computed(() => biddingStore?.projectId);
const noticeId = computed(() => biddingStore?.noticeId);

const sectionId = computed(() => route.query.sectionId);
const uuid = computed(() => route.query.uuid);

const isView = computed(() => route.query?.viewType === OPERATION_TYPE_ENUM.VIEW);

const currentEvaluationItem = computed<any>(() => {
  return evaluationStore?.currentEvaluationItem?.find((i: any) => uuid.value === i?.uuid) || {};
});

// const typeName = computed(() => currentEvaluationItem?.value?.column?.typeName);
const typeName = computed(() => currentEvaluationItem?.value?.column?.type);
const nodeName = computed(() => currentEvaluationItem?.value?.column?.nodeName);
const nodeType = computed(() => currentEvaluationItem?.value?.column?.type);
const evaluationId = computed(() => currentEvaluationItem?.value?.row?.evaluationId);
// const isCompleted = computed(() => currentEvaluationItem?.value?.row[nodeName.value]?.isCompleted || false);
const userId = computed(() => currentEvaluationItem?.value?.row?.userId);
// const currentUserRole = computed(() => currentEvaluationItem?.value?.row?.currentUserRole);
// const isLeader = computed(() => !!(EVALUATION_MEMBER_ROLE_ENUM.EVALUATION_LEADER === currentUserRole.value));

// const isEvaluationItem = computed(() => !!(typeName.value === '评审项'));
// const isScoreItem = computed(() => !!(typeName.value === '评分项'));
const isEvaluationItem = computed(() => !!(typeName.value === 'REVIEW'));
const isScoreItem = computed(() => !!(typeName.value === 'SCORE'));

const countInfo = ref({
  totalScore: 0,
  totalWeight: 0,
});

// 分隔符
const speator = ref('__');
const remark = ref();
// 控制每个 popover 的显示状态
const popoverVisible = ref<Record<string, boolean>>({});

const route = useRoute();
const tableData = ref([]);
const tableColumns = computed<any>(() => {
  const list = tableData.value || [];
  const s: any = list?.find((item: any) => item?.supplierScorings?.length > 0) || {};
  const supplierList = s?.supplierScorings || [];
  const temp = supplierList?.map((item: any) => {
    return {
      ...item,
      label: `${item?.tenantSupplierName}`,
      prop: `tenantSupplierId${speator.value}${item?.tenantSupplierId}`,
      isDynamic: true,
      minWidth: '200px',
      showOverflowTooltip: false,
    };
  });

  return [
    {
      label: '序号',
      // type: 'index',
      prop: 'index',
      width: '60px',
      fixed: 'left',
    },
    {
      label: '评审项',
      prop: 'itemName',
      fixed: 'left',
    },
    {
      label: '评审项详情',
      prop: 'itemDescription',
      fixed: 'left',
    },
    ...temp,
  ];
});
interface DynamicColumn {
  tenantSupplierId: string;
  tenantSupplierName: string;
  label: string;
  prop: string;
  isDynamic: boolean;
  minWidth: string;
}

const dynamicColumns = computed<DynamicColumn[]>(() => {
  return tableColumns.value?.filter((item: any) => item?.isDynamic) || [];
});

const loading = ref(false);
const btnLoading = ref(false);

const tablePropsObj = computed(() => ({
  stripe: false,
  border: true,
  headerCellStyle: {
    backgroundColor: '#FAFBFC',
    color: '#505762',
    fontWeight: 'bold',
    height: '40px',
    borderColor: '#DCDFE6',
  },
  cellStyle: {
    padding: '0',
    height: '40px',
    'vertical-align': 'middle',
    borderColor: '#DCDFE6',
  },
  rowStyle: {
    height: '40px',
    borderColor: '#DCDFE6',
  },
  rowHeight: 40,
}));

// 计算合计行
const summaryRow = computed<any>(() => {
  if (tableData.value.length === 0) return null;

  const row: any = { isOverallSummary: true };

  dynamicColumns.value.forEach((col) => {
    const key = `${isScoreItem.value ? 'score' : 'isConform'}${speator.value}${col.tenantSupplierId}`;
    if (isScoreItem.value) {
      // 评分项：统计每列的和
      row[key] = tableData.value.reduce((sum, item) => {
        const val = Number(item[key]);
        return sum + (isNaN(val) ? 0 : val);
      }, 0);
    } else if (isEvaluationItem.value) {
      const allOne = tableData.value.every((item) => item[key] === 1);
      if (allOne) {
        row[key] = 1;
      } else {
        row[key] = 0;
      }
    }
  });

  return row;
});

// 3. 渲染用的数据，拼接合计行
const displayTableData = computed(() => {
  if (summaryRow.value) {
    return [...tableData.value, summaryRow.value];
  }
  return tableData.value;
});

// 点击外部关闭所有 popover
function handleClickOutside(event: Event) {
  const target = event.target as HTMLElement;

  // 检查是否点击在 popover 内部或者 reference 按钮上
  const isPopoverContent =
    target.closest('.el-popover') || target.closest('.el-button') || target.closest('.el-input') || target.closest('.el-radio-group');

  if (!isPopoverContent) {
    // 关闭所有打开的 popover
    Object.keys(popoverVisible.value).forEach((key) => {
      popoverVisible.value[key] = false;
    });
    remark.value = '';
  }
}

function close() {
  mittBus.emit('onCurrentContextmenuClick', { contextMenuClickId: 1, ...route });
}

function handleCancelRemark(row: any, column: any) {
  remark.value = '';
  // 关闭对应的 popover
  const popoverKey = `${row.standardId}${speator.value}${column.tenantSupplierId}`;
  popoverVisible.value[popoverKey] = false;
}

function handleRemark(row: any, column: any) {
  const key = `conclusion${speator.value}${column?.tenantSupplierId}`;
  row[key] = remark.value;
  remark.value = '';
  // 关闭对应的 popover
  const popoverKey = `${row.standardId}${speator.value}${column.tenantSupplierId}`;
  popoverVisible.value[popoverKey] = false;
}

function handleEdit(row: any, column: any) {
  // 先关闭所有其他的 popover
  Object.keys(popoverVisible.value).forEach((key) => {
    popoverVisible.value[key] = false;
  });

  // 设置当前行的备注内容
  remark.value = row[`conclusion${speator.value}${column?.tenantSupplierId}`];

  // 打开当前的 popover
  const popoverKey = `${row.standardId}${speator.value}${column.tenantSupplierId}`;
  nextTick(() => {
    popoverVisible.value[popoverKey] = true;
  });
}

// 处理参数
function handleParams() {
  const list = (tableData.value || [])?.filter((item: any) => !item?.isOverallSummary);
  const scoringList: any[] = [];

  list.forEach((item: any) => {
    const supplierScorings = item?.supplierScorings || [];

    supplierScorings.forEach((supplier: any) => {
      const tenantSupplierId = supplier.tenantSupplierId;

      // 获取该供应商的评审结果和备注
      const isConform = item[`isConform${speator.value}${tenantSupplierId}`];
      const conclusion = item[`conclusion${speator.value}${tenantSupplierId}`];

      // 评分项
      const score = item[`score${speator.value}${tenantSupplierId}`];

      const obj = {
        id: supplier.scoringId || null,
        projectId: projectId.value,
        noticeId: noticeId.value,
        sectionId: sectionId.value,
        userId: userId.value,
        type: typeName.value,
        scoringDetailId: item.standardId, // 使用standardId作为评分详情id
        tenantSupplierId: tenantSupplierId,
        responseId: supplier.responseId,
        evaluationId: evaluationId.value,
      };
      if (isScoreItem.value) {
        scoringList.push({
          ...obj,
          score: score || null,
        });
      }
      if (isEvaluationItem.value) {
        scoringList.push({
          ...obj,
          conclusion: conclusion || null,
          isConform: isConform || null,
        });
      }
    });
  });

  return {
    scoringList,
  };
}

function confirmHandler(status: string) {
  // eslint-disable-next-line no-console
  const params = {
    ...handleParams(),
    status,
  };

  if (params?.scoringList?.length === 0) {
    ElMessage.error('请先填写完整');
    return;
  }
  // eslint-disable-next-line no-console
  console.log(params, 'submitParams');

  if (nodeType.value === 'SCORE' && status === SUBMIT_STATUS_ENUM.SUBMITTED) {
    const hasEmpty = params?.scoringList?.some((item: any) => isEmptyValue(item?.score));
    if (hasEmpty) {
      ElMessage.error('请先填写完整评分项');
      return;
    }
  }
  if (nodeType.value === 'REVIEW' && status === SUBMIT_STATUS_ENUM.SUBMITTED) {
    const hasEmpty = params?.scoringList?.some((item: any) => isEmptyValue(item?.isConform));
    if (hasEmpty) {
      ElMessage.error('请先填写完整评审项');
      return;
    }
  }

  btnLoading.value = true;

  // 调用API提交数据
  batchSaveScoring(params)
    .then((res) => {
      // eslint-disable-next-line no-console
      console.log('提交成功', res);
      // 可以在这里添加成功提示
      ElMessage.success(status === SUBMIT_STATUS_ENUM.WAIT_SUBMIT ? '保存成功' : '提交成功');
      biddingStore?.checkEvaluationAllCompleted();

      // 如果是提交操作，关闭弹窗
      if (status === SUBMIT_STATUS_ENUM.SUBMITTED) {
        close();
      } else if (status === SUBMIT_STATUS_ENUM.WAIT_SUBMIT) {
        // 如果是保存操作，刷新数据
        loadData();
      }
    })
    .catch((error) => {
      // eslint-disable-next-line no-console
      console.error('提交失败', error);
      // ElMessage.error(type === 'SUBMIT' ? '提交失败' : '保存失败');
    })
    .finally(() => {
      btnLoading.value = false;
    });
}

async function loadData() {
  loading.value = true;
  try {
    const res = await getExpertScoringTableByNode({
      projectId: projectId.value,
      noticeId: noticeId.value,
      sectionId: sectionId.value,
      nodeName: nodeName.value,
      nodeType: nodeType.value,
      userId: userId.value,
      evaluationId: evaluationId.value,
    });

    countInfo.value = {
      totalScore: res?.data?.nodeTotalScore || 0,
      totalWeight: res?.data?.nodeWeight || 0,
    };

    const list = (res?.data?.evaluationStandards || []).filter((item: any) => !item?.isOverallSummary);
    tableData.value = list?.map((item: any) => {
      const temp = (item?.supplierScorings || [])?.reduce((cur: any, next: any) => {
        cur[`tenantSupplierName${speator.value}${next?.tenantSupplierName}`] = next?.tenantSupplierName || null;
        cur[`tenantSupplierId${speator.value}${next?.tenantSupplierId}`] = next?.tenantSupplierId || null;
        cur[`conclusion${speator.value}${next?.tenantSupplierId}`] = next?.conclusion || null;
        // 确保isConform为数字类型，与CONFORMITY_RESULT_OPTIONS中的value类型一致
        cur[`isConform${speator.value}${next?.tenantSupplierId}`] = isEmptyValue(next?.isConform) ? null : String(next?.isConform);
        cur[`score${speator.value}${next?.tenantSupplierId}`] = next?.score || undefined;
        return cur;
      }, {});
      return {
        ...item,
        ...temp,
      };
    });
    // eslint-disable-next-line no-console
    console.log(tableData.value, 'tableData');
    // eslint-disable-next-line no-empty
  } catch (error) {}
  loading.value = false;
}

onMounted(() => {
  loadData();
  // 添加全局点击事件监听
  document.addEventListener('click', handleClickOutside);
  // eslint-disable-next-line no-console
  console.log(currentEvaluationItem?.value, 'currentEvaluationItem');
});

onUnmounted(() => {
  // 移除全局点击事件监听
  document.removeEventListener('click', handleClickOutside);
});
</script>

<style lang="scss" scoped>
@import '@/views/procurementSourcing/biddingProcess/styles/collapse-panel.scss';

.transparent-input {
  :deep(.el-input__inner) {
    border: none;
    outline: none;
    background-color: transparent;
    appearance: none;
    padding: 0;
    box-shadow: none;
    &:focus {
      border: none;
      outline: none;
      background-color: transparent;
      appearance: none;
      box-shadow: none;
    }
  }
}

.front-router-view-container {
  padding: 20px 20px 0 20px;
  display: flex;
  flex-direction: column;

  .need-hide-table-card {
    flex: 1;
    overflow: visible;
    position: relative;
  }

  :deep(.el-table__cell) {
    z-index: unset !important;
  }
  :deep(.el-radio__label) {
    padding-top: 2px;
  }

  .fixed-bottom-container {
    padding: 16px 24px;
    background-color: #fff;
    border-top: 1px solid var(--Color-Border-border-color-light, #e4e7ed);
  }

  .score-weight {
    display: flex;
    align-items: center;
    gap: 16px;
    padding-top: 20px;
    padding-left: 20px;
    .score-weight-item {
      border-radius: 2px;
      background: var(--Color-Fill-fill-color-light, #f5f7fa);
      display: flex;
      height: 28px;
      padding: 0px 6px;
      align-items: center;
      color: #4e5969;
      /* medium/base */
      font-family: 'PingFang SC';
      font-size: 14px;
      font-style: normal;
      line-height: 22px; /* 157.143% */
      gap: 8px;
      .score-weight-item-label {
        color: #4e5969;
      }
      .score-weight-item-value {
        color: #1d2129;
        font-weight: 500;
      }
    }
  }
}
</style>

<style lang="scss">
// 全局样式，确保 popover 能够正确显示在表格之上
.table-popover {
  z-index: 9999 !important;
}
</style>
