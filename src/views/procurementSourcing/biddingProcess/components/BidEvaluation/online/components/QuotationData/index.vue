<template>
  <yun-drawer
    v-model="showVisible"
    title="查看报价数据"
    size="X-large"
    :with-header="true"
    append-to-body
    destroy-on-close
    :show-footer="false"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    custom-class="bid-custom-drawer"
  >
    <div
      class="flex flex-col"
      v-loading="loading"
      style="height: 98%"
    >
      <!-- 报价轮次和基本信息 -->
      <div>
        <el-tabs
          v-model="activeTab"
          @tab-click="handleTabClick"
        >
          <el-tab-pane
            v-for="item in quotationRounds"
            :key="item.value"
            :label="item.label"
            :name="item.value"
          />
        </el-tabs>
      </div>
      <div
        class="other-info-wrapper"
        v-if="tableData?.length"
      >
        <div class="item-label">第{{ numberToChinese(activeTab) }}次报价</div>
        <div class="item-block">
          <span class="label">报价截止时间:</span>
          <span class="value">{{ tableDataInfo?.quoteEndTime ? moment(tableDataInfo?.quoteEndTime).format('YYYY-MM-DD HH:mm:ss') : '--' }}</span>
        </div>
        <div class="item-block">
          <span class="label">开标状态:</span>
          <span class="value">{{
            tableDataInfo?.openStatus ? PURCHASER_STATUS_LABELS[tableDataInfo?.openStatus as keyof typeof PURCHASER_STATUS_LABELS] : '--'
          }}</span>
        </div>
      </div>
      <div class="need-hide-table-card flex-1">
        <yun-pro-table
          ref="proTableRef"
          :table-columns="columns"
          v-model:tableData="tableData"
          :auto-height="true"
          :table-props="tablePropsObj"
          layout="whole"
          :default-fetch="false"
        >
          <template #t_action="{ row }">
            <el-button
              type="text"
              v-if="row.quoteStatus === QUOTE_STATUS_ENUM.COMPLETED"
              @click="handleViewDetail(row)"
              >报价明细</el-button
            >
          </template>
        </yun-pro-table>
      </div>
    </div>
    <!-- 报价详情抽屉 -->
    <QuotationDetail
      ref="quotationDetailRef"
      :is-fixed-round="true"
    />
  </yun-drawer>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import { queryQuoteDetailBySupplier } from '@/api/purchasing/evaluation';
import { useBiddingStore } from '@/views/procurementSourcing/biddingProcess/stores';
import { numberToChinese } from '@/views/procurementSourcing/biddingProcess/utils';
import QuotationDetail from '@/views/procurementSourcing/biddingProcess/components/BidEvaluation/online/components/QuotationDetail/index.vue';
import { QUOTE_STATUS_ENUM } from './const';
import { useColumns } from './hooks/useColumns';
import moment from 'moment';
import { PURCHASER_STATUS_LABELS } from '@/views/procurementSourcing/biddingProcess/constants';

const tablePropsObj = computed(() => ({
  // ...tableProps.value,
  stripe: true,
  border: true,
  headerCellStyle: {
    backgroundColor: '#f5f7fa',
    color: '#303133',
    fontWeight: 'bold',
    height: '40px',
  },
  cellStyle: {
    padding: '0',
    height: '40px',
    'vertical-align': 'middle',
  },
  rowStyle: {
    height: '40px',
  },
  rowHeight: 40,
  rowKey: 'phone',
}));

const { columns } = useColumns();
const biddingStore = useBiddingStore();
const noticeId = computed(() => biddingStore?.noticeId);
const projectId = computed(() => biddingStore?.projectId);
const loading = ref(false);
const roundList = ref<any>([]);
const showVisible = ref(false);
const currentSectionId = ref('');
const quotationDetailRef = ref<any>(null);

const activeTab = ref(0);
// 报价轮次
const quotationRounds = ref<any>([]);
const quoteRoundCount = ref(0);

const tableDataInfo = computed(() => (roundList.value || []).find((item: any) => item.roundNo === activeTab.value) || {});
const tableData = computed(() => tableDataInfo?.value?.supplierList || []);

async function initData() {
  quotationRounds.value = Array.from({ length: quoteRoundCount.value }, (_, i) => i + 1)
    .map((item) => {
      return {
        label: `第${numberToChinese(item)}轮报价`,
        value: item,
      };
    })
    .reverse();
}

// 按物料查询报价列表
async function queryQuoteListByMaterialData() {
  loading.value = true;
  try {
    const res = await queryQuoteDetailBySupplier({
      sectionId: currentSectionId.value,
      noticeId: noticeId.value,
      projectId: projectId.value,
    });

    const list = (res?.data?.roundList || []).reverse();
    roundList.value = list;

    // 初始话轮次
    const lastRound = list[0]?.roundNo || 0;
    quoteRoundCount.value = lastRound;
    activeTab.value = lastRound;

    initData();
  } catch (error) {
    // eslint-disable-next-line no-console
    console.log('error:', error);
    roundList.value = [];
  }
  loading.value = false;
}

function handleTabClick() {
  loading.value = true;
  setTimeout(() => {
    loading.value = false;
  }, 300);
}

// 查看详情
function handleViewDetail(row: any) {
  quotationDetailRef.value.show({ ...row, sectionId: currentSectionId.value });
}

function show(row: any) {
  activeTab.value = 0;
  quotationRounds.value = [];
  quoteRoundCount.value = 0;

  currentSectionId.value = row?.sectionId;
  showVisible.value = true;
  queryQuoteListByMaterialData();
}

defineExpose({
  show,
});
</script>

<style scoped lang="scss">
@import '@/views/procurementSourcing/biddingProcess/styles/collapse-panel.scss';

.other-info-wrapper {
  display: flex;
  align-items: center;
  gap: 8px;
  // margin: 20px 0;
  margin-bottom: 20px;
  margin-top: 12px;
  .item-label {
    color: var(--Color-Text-text-color-primary, #1d2129);
    /* bold/medium */
    font-family: 'PingFang SC';
    font-size: 16px;
    font-style: normal;
    font-weight: 600;
    line-height: 24px; /* 150% */
    position: relative;
    padding-left: 10px;
    &::before {
      content: ' ';
      position: absolute;
      left: 0;
      top: 50%;
      transform: translateY(-50%);
      width: 2px;
      height: 14px;
      background: var(--Color-Primary-color-primary, #0069ff);
    }
  }
  .item-block {
    display: inline-flex;
    align-items: center;
    border-radius: var(--Radius-border-radius-small, 2px);
    border: 1px solid var(--Color-Border-border-color-light, #e4e7ed);
    background: var(--color-white, #fff);
    height: 24px;
    padding: 2px 8px;
    box-sizing: border-box;
    .label {
      color: var(--Color-Text-text-color-secondary, #86909c);
      /* regular/base */
      font-family: 'PingFang SC';
      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      line-height: 22px; /* 157.143% */
      padding-right: 8px;
    }
    .value {
      color: var(--Color-Text-text-color-primary, #1d2129);
      /* regular/base */
      font-family: 'PingFang SC';
      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      line-height: 22px;
    }
  }
}
</style>
