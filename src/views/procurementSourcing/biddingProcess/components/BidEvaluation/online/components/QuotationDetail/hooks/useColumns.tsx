import { ref, computed } from 'vue';

export function useColumns() {
  const searchData = ref<any>({});
  const columns = ref([]);

  const searchFields = computed(() => {
    const commonFields = [
      {
        label: '物料名称',
        prop: 'materialName',
        type: 'input',
        componentAttrs: {
          clearable: true,
        },
      },
      {
        label: '物料编码',
        prop: 'materialCode',
        type: 'input',
        componentAttrs: {
          clearable: true,
        },
      },
    ];
    return commonFields;
  });

  return {
    columns,
    searchFields,
    searchData,
  };
}
