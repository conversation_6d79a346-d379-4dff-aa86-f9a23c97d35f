<template>
  <div
    class="front-router-view-container overflow-hidden"
    v-loading="loading"
  >
    <div
      class="flex flex-col flex-1 box-border"
      style="background-color: #fff"
    >
      <div
        class="score-weight"
        v-if="isScoreItem"
      >
        <div class="score-weight-item">
          <span class="score-weight-item-label">节点总分:</span>
          <span class="score-weight-item-value">{{ countInfo.totalScore }}</span>
        </div>
        <div class="score-weight-item">
          <span class="score-weight-item-label">权重:</span>
          <span class="score-weight-item-value">{{ countInfo.totalWeight || 0 }}%</span>
        </div>
      </div>
      <div class="need-hide-table-card pt-5 pl-5 pr-5">
        <el-table
          :data="tableData"
          style="width: 100%"
          fit
          v-bind="tablePropsObj"
        >
          <el-table-column
            fixed="left"
            type="index"
            label="序号"
            width="60"
          ></el-table-column>
          <el-table-column
            prop="expertName"
            label="评审项"
            fixed="left"
            show-overflow-tooltip
          />
          <el-table-column
            v-for="column in dynamicColumns"
            :key="column.prop"
            :prop="column.prop"
            :label="column.label"
            :minWidth="column.minWidth"
          >
            <template #default="{ row }">
              <div class="flex items-center gap-2">
                <template v-if="isEvaluationItem">
                  <el-radio-group
                    :disabled="true"
                    v-model="row[`isConform${speator}${column?.tenantSupplierId}`]"
                  >
                    <el-radio
                      v-for="item in CONFORMITY_RESULT_OPTIONS"
                      :key="item.value"
                      :label="String(item.value)"
                    >
                      {{ item.label }}
                    </el-radio>
                  </el-radio-group>
                  <template v-if="!row.isOverallSummary">
                    <el-popover
                      placement="bottom"
                      title=""
                      width="200px"
                      trigger="click"
                      v-model:visible="popoverVisible[`${row.userId}${speator}${column.tenantSupplierId}`]"
                      :teleported="true"
                      popper-class="table-popover"
                    >
                      <div class="flex flex-col items-center gap-2">
                        <el-input
                          class="w-full transparent-input"
                          placeholder="请输入备注"
                          size="small"
                          style="border: none; outline: none; background-color: transparent"
                          :value="remark || '暂未填写备注内容'"
                          :maxlength="200"
                          :disabled="true"
                        ></el-input>
                      </div>
                      <template #reference>
                        <el-button
                          type="text"
                          class="ml-6"
                          @click="handleEdit(row, column)"
                        >
                          <span>备注</span>
                          <el-icon class="ml-2">
                            <EditPen />
                          </el-icon>
                        </el-button>
                      </template>
                    </el-popover>
                  </template>
                </template>
                <span v-if="isScoreItem">
                  {{ row[`totalScore${speator}${column?.tenantSupplierId}`] || 0 }}
                </span>
              </div>
            </template>
          </el-table-column>
        </el-table>
        <!-- <yun-pro-table
          ref="proTableRef"
          :table-columns="tableColumns"
          v-model:tableData="tableData"
          :auto-height="true"
          :table-props="tablePropsObj"
          layout="whole"
          :default-fetch="false"
        >
          <template
            v-for="column in dynamicColumns"
            :key="column.prop"
            v-slot:[`t_${column.prop}`]="{ row }: { row: any }"
          >
            <div class="flex items-center gap-2">
              <template v-if="isEvaluationItem">
                <el-radio-group
                  :disabled="true"
                  v-model="row[`isConform${speator}${column?.tenantSupplierId}`]"
                >
                  <el-radio
                    v-for="item in CONFORMITY_RESULT_OPTIONS"
                    :key="item.value"
                    :label="item.value"
                  >
                    {{ item.label }}
                  </el-radio>
                </el-radio-group>
              </template>
              <span v-if="isScoreItem">
                {{ row[`totalScore${speator}${column?.tenantSupplierId}`] || 0 }}
              </span>
            </div>
          </template>
        </yun-pro-table> -->
      </div>
      <div class="flex items-center fixed-bottom-container">
        <el-button
          @click="close"
          :disabled="btnLoading"
        >
          关闭
        </el-button>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed, onMounted, onUnmounted } from 'vue';
import { CONFORMITY_RESULT_OPTIONS } from '@/views/procurementSourcing/biddingProcess/components/BidEvaluation/online/const';
import { useBiddingStore, useEvaluationStore } from '@/views/procurementSourcing/biddingProcess/stores';
import { useRoute } from 'vue-router';
import { getNodeEvaluationSummary } from '@/api/purchasing/evaluation';
import mittBus from '/@/utils/mitt';

const evaluationStore = useEvaluationStore();
const biddingStore = useBiddingStore();
const projectId = computed(() => biddingStore?.projectId);
const noticeId = computed(() => biddingStore?.noticeId);

const uuid = computed(() => route.query.uuid);
const sectionId = computed(() => route.query.sectionId);

const currentEvaluationItem = computed<any>(() => {
  return evaluationStore?.currentEvaluationItem?.find((i: any) => uuid.value === i?.uuid) || {};
});

const typeName = computed(() => currentEvaluationItem?.value?.column?.type);
// const typeName = computed(() => currentEvaluationItem?.value?.column?.typeName);
const nodeName = computed(() => currentEvaluationItem?.value?.column?.nodeName);
const nodeType = computed(() => currentEvaluationItem?.value?.column?.type);
const evaluationId = computed(() => currentEvaluationItem?.value?.row?.evaluationId);

// const isEvaluationItem = computed(() => !!(typeName.value === '评审项'));
// const isScoreItem = computed(() => !!(typeName.value === '评分项'));
const isEvaluationItem = computed(() => !!(typeName.value === 'REVIEW'));
const isScoreItem = computed(() => !!(typeName.value === 'SCORE'));

const countInfo = ref({
  totalScore: 0,
  totalWeight: 0,
});

// 分隔符
const speator = ref('__');
const remark = ref();
// 控制每个 popover 的显示状态
const popoverVisible = ref<Record<string, boolean>>({});

const route = useRoute();
const tableData = ref<any[]>([]);
const tableColumns = computed<any>(() => {
  const list = tableData.value || [];
  const s: any = list?.find((item: any) => item?.supplierScorings?.length > 0) || {};
  const supplierList = s?.supplierScorings || [];
  const temp = supplierList?.map((item: any) => {
    return {
      ...item,
      label: `${item?.tenantSupplierName}`,
      prop: `tenantSupplierId${speator.value}${item?.tenantSupplierId}`,
      isDynamic: true,
      minWidth: '200px',
      showOverflowTooltip: false,
    };
  });

  return [
    {
      label: '序号',
      type: 'index',
      width: '60px',
      fixed: 'left',
    },
    {
      label: '专家名称',
      prop: 'expertName',
      fixed: 'left',
    },
    ...temp,
  ];
});
interface DynamicColumn {
  tenantSupplierId: string;
  tenantSupplierName: string;
  label: string;
  prop: string;
  isDynamic: boolean;
  minWidth: string;
}

const dynamicColumns = computed<DynamicColumn[]>(() => {
  return tableColumns.value?.filter((item: any) => item?.isDynamic) || [];
});

const loading = ref(false);
const btnLoading = ref(false);

const tablePropsObj = computed(() => ({
  stripe: false,
  border: true,
  headerCellStyle: {
    backgroundColor: '#FAFBFC',
    color: '#505762',
    fontWeight: 'bold',
    height: '40px',
    borderColor: '#DCDFE6',
  },
  cellStyle: {
    padding: '0',
    height: '40px',
    'vertical-align': 'middle',
    borderColor: '#DCDFE6',
  },
  rowStyle: {
    height: '40px',
    borderColor: '#DCDFE6',
  },
  rowHeight: 40,
}));

function handleEdit(row: any, column: any) {
  // 先关闭所有其他的 popover
  Object.keys(popoverVisible.value).forEach((key) => {
    popoverVisible.value[key] = false;
  });

  // 设置当前行的备注内容
  remark.value = row[`conclusion${speator.value}${column?.tenantSupplierId}`];

  // 打开当前的 popover
  const popoverKey = `${row.userId}${speator.value}${column.tenantSupplierId}`;
  nextTick(() => {
    popoverVisible.value[popoverKey] = true;
  });
}

// 点击外部关闭所有 popover
function handleClickOutside(event: Event) {
  const target = event.target as HTMLElement;

  // 检查是否点击在 popover 内部或者 reference 按钮上
  const isPopoverContent =
    target.closest('.el-popover') || target.closest('.el-button') || target.closest('.el-input') || target.closest('.el-radio-group');

  if (!isPopoverContent) {
    // 关闭所有打开的 popover
    Object.keys(popoverVisible.value).forEach((key) => {
      popoverVisible.value[key] = false;
    });
    remark.value = '';
  }
}

function close() {
  mittBus.emit('onCurrentContextmenuClick', { contextMenuClickId: 1, ...route });
}

// 数据转换函数
function transformSupplierDataToExpertTable(supplierData: any[]) {
  if (!supplierData || supplierData.length === 0) {
    return [];
  }

  // 收集所有专家信息
  const expertsMap = new Map();

  // 遍历所有供应商，收集专家信息
  supplierData.forEach((supplier) => {
    supplier.expertDetails?.forEach((expert: any) => {
      if (!expertsMap.has(expert.userId)) {
        expertsMap.set(expert.userId, {
          userId: expert.userId,
          expertName: expert.expertName,
        });
      }
    });
  });

  const experts = Array.from(expertsMap.values());

  // 构建表格数据 - 以专家为行
  const tableData = experts.map((expert: any) => {
    const row: any = {
      userId: expert.userId,
      expertName: expert.expertName,
      supplierScorings: supplierData.map((supplier: any) => ({
        tenantSupplierId: supplier.tenantSupplierId,
        tenantSupplierName: supplier.supplierName,
      })), // 保存供应商信息用于构建列
    };

    // 为每个供应商添加对应的专家评审数据
    supplierData.forEach((supplier) => {
      const expertDetail = supplier.expertDetails?.find((detail: any) => detail.userId === expert.userId);
      const supplierId = supplier.tenantSupplierId;

      if (expertDetail) {
        // 使用分隔符构建动态属性名，与原代码保持一致
        row[`result${speator.value}${supplierId}`] = expertDetail.result;
        row[`conclusion${speator.value}${supplierId}`] = expertDetail.conclusion;
        row[`totalScore${speator.value}${supplierId}`] = expertDetail.totalScore;
        row[`status${speator.value}${supplierId}`] = expertDetail.status;
        row[`isConform${speator.value}${supplierId}`] = expertDetail.result === '通过' ? '1' : '0';
      } else {
        // 如果没有对应的专家数据，设置默认值
        row[`result${speator.value}${supplierId}`] = null;
        row[`conclusion${speator.value}${supplierId}`] = null;
        row[`totalScore${speator.value}${supplierId}`] = null;
        row[`status${speator.value}${supplierId}`] = null;
        row[`isConform${speator.value}${supplierId}`] = null;
      }
    });

    return row;
  });

  return tableData;
}

// 转换结论数据函数
function transformConclusionDataToTableRows(conclusionDataList: any[], supplierData: any[]) {
  if (!conclusionDataList || conclusionDataList.length === 0) {
    return [];
  }

  // 获取供应商信息用于构建列结构
  const supplierScorings = supplierData.map((supplier: any) => ({
    tenantSupplierId: supplier.tenantSupplierId,
    tenantSupplierName: supplier.supplierName,
  }));

  return conclusionDataList.map((conclusionItem: any) => {
    const row: any = {
      userId: conclusionItem.dataType, // 使用 dataType 作为唯一标识
      expertName: conclusionItem.dataType, // dataType 赋值为 expertName
      supplierScorings: supplierScorings,
      isOverallSummary: true, // 标记为汇总行
    };

    // 遍历 supplierDataMap，将数据分配到对应的供应商列
    Object.entries(conclusionItem.supplierDataMap || {}).forEach(([supplierId, value]: any) => {
      // 构建与专家数据相同的属性名格式
      row[`result${speator.value}${supplierId}`] = value;
      row[`conclusion${speator.value}${supplierId}`] = value;
      // 如果 conclusionItem.dataType === '权重' 需要处理成百分比
      if (conclusionItem.dataType === '权重') {
        row[`totalScore${speator.value}${supplierId}`] = `${Number(value || 0)}%`;
      } else {
        row[`totalScore${speator.value}${supplierId}`] = value;
      }
      row[`isConform${speator.value}${supplierId}`] = value === '通过' ? '1' : '0';
    });

    return row;
  });
}

async function loadData() {
  loading.value = true;
  try {
    const res = await getNodeEvaluationSummary({
      projectId: projectId.value,
      noticeId: noticeId.value,
      sectionId: sectionId.value,
      nodeName: nodeName.value,
      nodeType: nodeType.value,
      evaluationId: evaluationId.value,
      // userId: userId.value,
    });

    countInfo.value = {
      totalScore: res?.data?.nodeTotalScore || 0,
      totalWeight: res?.data?.nodeWeight || 0,
    };

    // 使用转换函数处理数据
    const supplierData = res?.data?.supplierResults || [];
    const conclusionDataList = res?.data?.conclusionDataList || [];

    // 转换专家数据
    const expertTableData = transformSupplierDataToExpertTable(supplierData);

    // 转换结论数据
    const conclusionTableData = transformConclusionDataToTableRows(conclusionDataList, supplierData);

    // 合并数据：专家数据 + 结论数据
    tableData.value = [...expertTableData, ...conclusionTableData];
  } catch (error) {
    // eslint-disable-next-line no-console
    console.error('加载数据失败:', error);
  }
  loading.value = false;
}

onMounted(() => {
  loadData();
  // 添加全局点击事件监听
  document.addEventListener('click', handleClickOutside);
  // eslint-disable-next-line no-console
  console.log(currentEvaluationItem?.value, 'currentEvaluationItem');
});

onUnmounted(() => {
  // 移除全局点击事件监听
  document.removeEventListener('click', handleClickOutside);
});
</script>

<style lang="scss" scoped>
@import '@/views/procurementSourcing/biddingProcess/styles/collapse-panel.scss';

.transparent-input {
  :deep(.el-input__inner) {
    border: none;
    outline: none;
    background-color: transparent;
    appearance: none;
    padding: 0;
    box-shadow: none;
    &:focus {
      border: none;
      outline: none;
      background-color: transparent;
      appearance: none;
      box-shadow: none;
    }
  }
}

.front-router-view-container {
  padding: 20px 20px 0 20px;
  display: flex;
  flex-direction: column;

  .need-hide-table-card {
    flex: 1;
    overflow: visible;
    position: relative;
  }
  :deep(.el-radio__label) {
    padding-top: 2px;
  }
  :deep(.el-table__cell) {
    z-index: unset !important;
  }

  .fixed-bottom-container {
    padding: 16px 24px;
    background-color: #fff;
    border-top: 1px solid var(--Color-Border-border-color-light, #e4e7ed);
  }

  .score-weight {
    display: flex;
    align-items: center;
    gap: 16px;
    // margin-bottom: 20px;
    padding-top: 20px;
    padding-left: 20px;
    .score-weight-item {
      border-radius: 2px;
      background: var(--Color-Fill-fill-color-light, #f5f7fa);
      display: flex;
      height: 28px;
      padding: 0px 6px;
      align-items: center;
      color: #4e5969;
      /* medium/base */
      font-family: 'PingFang SC';
      font-size: 14px;
      font-style: normal;
      line-height: 22px; /* 157.143% */
      gap: 8px;
      .score-weight-item-label {
        color: #4e5969;
      }
      .score-weight-item-value {
        color: #1d2129;
        font-weight: 500;
      }
    }
  }
}
</style>

<style lang="scss">
// 全局样式，确保 popover 能够正确显示在表格之上
.table-popover {
  z-index: 9999 !important;
}
</style>
