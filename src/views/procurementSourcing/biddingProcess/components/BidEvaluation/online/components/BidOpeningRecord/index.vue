<template>
  <yun-drawer
    v-model="showVisible"
    size="X-large"
    title="查看开标记录"
    append-to-body
    destroy-on-close
    :show-footer="false"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    @close="close"
  >
    <div class="quotation-management">
      <!-- 第一行：视图切换和报价信息 -->
      <div class="view-control">
        <div class="left-section">
          <el-radio-group
            v-model="viewMode"
            class="view-tabs"
            @change="loadData"
          >
            <el-radio-button
              v-for="option in VIEW_TYPE_OPTIONS"
              :key="option.value"
              :label="option.value"
            >
              {{ option.label }}
            </el-radio-button>
          </el-radio-group>
        </div>
      </div>
      <div class="table-section">
        <!-- 物料表格 -->
        <CommonMaterialTable
          v-if="viewMode === VIEW_TYPE.MATERIAL"
          style="padding: 0"
          scene="JZTP_ZB_KB"
          ref="materialTableRef"
        />
        <!-- 供应商表格 -->
        <CommonSupplierTable
          v-if="viewMode === VIEW_TYPE.SUPPLIER"
          style="padding: 0"
          scene="JZTP_ZB_KB"
          ref="supplierTableRef"
        />
      </div>
    </div>
  </yun-drawer>
</template>

<script lang="ts" setup>
import { ref, onMounted, onBeforeMount } from 'vue';
import { VIEW_TYPE_OPTIONS, VIEW_TYPE } from '@/views/procurementSourcing/biddingProcess/constants';
// @ts-ignore
import CommonMaterialTable from '@/views/procurementSourcing/biddingProcess/components/bidding/CommonMaterialTable/index.vue';
// @ts-ignore
import CommonSupplierTable from '@/views/procurementSourcing/biddingProcess/components/bidding/CommonSupplierTable/index.vue';

const materialTableRef = ref<any>(null);
const supplierTableRef = ref<any>(null);
const showVisible = ref(false);
const currentRow = ref<any>(null);
// 表格数据
const currentSectionId = ref('');
const viewMode = ref<any>(VIEW_TYPE.MATERIAL);
function loadData() {
  materialTableRef?.value?.handleSectionChange(currentSectionId.value);
  supplierTableRef?.value?.handleSectionChange(currentSectionId.value);
}

onMounted(async () => {});
onBeforeMount(async () => {});

function close() {
  currentRow.value = {};
  showVisible.value = false;
  currentSectionId.value = '';
  viewMode.value = VIEW_TYPE.MATERIAL;
}
function show(row: any) {
  currentRow.value = {};
  showVisible.value = true;
  currentSectionId.value = row?.sectionId;
  setTimeout(loadData, 100);
}

defineExpose({
  show,
  close,
});
</script>

<style lang="scss" scoped>
.quotation-management {
  height: 98%;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  .table-section {
    flex: 1;
    overflow: auto;
    // height: calc(100% - 40px);
    // overflow: hidden;
    // height: 80vh;
  }
}
</style>
