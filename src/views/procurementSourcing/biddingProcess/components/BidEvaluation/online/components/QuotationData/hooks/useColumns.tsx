import { ref, computed } from 'vue';
import { QUOTE_STATUS_OPTIONS } from '../const';
export function useColumns() {
  const searchData = ref({});
  const columns = computed(() => {
    return [
      {
        type: 'index',
        label: '序号',
        width: 52,
        // fixed: 'left',
      },
      {
        prop: 'supplierName',
        label: '供应商名称',
      },
      {
        prop: 'contactPerson',
        label: '联系人',
      },
      {
        prop: 'contactPhone',
        label: '联系电话',
      },
      {
        label: '报价状态',
        prop: 'quoteStatus',
        enums: QUOTE_STATUS_OPTIONS,
      },
      {
        prop: 'quoteIp',
        label: '报价IP',
      },
      {
        prop: 'quoteTime',
        label: '报价时间',
        width: 160,
      },
      {
        prop: 'ranking',
        label: '报价排名',
      },
      {
        label: '操作',
        prop: 'action',
        width: 100,
      },
    ];
  });

  const searchFields = ref([]);

  return {
    columns,
    searchFields,
    searchData,
  };
}
