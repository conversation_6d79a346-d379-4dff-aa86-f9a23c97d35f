import { ref, computed } from 'vue';
export function useColumns() {
  const searchData = ref({});
  const columns = computed(() => {
    return [
      // {
      //   label: '序号',
      //   type: 'index',
      //   width: '60px',
      // },
      {
        label: '序号',
        prop: 'index',
      },
      {
        label: '评标专家',
        prop: 'expertName',
      },
      // {
      //   label: '资格性审查（评审项）',
      //   prop: 'zgxps',
      // },
      // // {
      // //   label: '符合性审查（评审项）',
      // //   prop: 'fhxps',
      // // },
      // {
      //   label: '资格部分（评分项）',
      //   prop: 'zg',
      // },
      // {
      //   label: '技术评审（评分项）',
      //   prop: 'js',
      // },
      // {
      //   label: '商务评审（评分项）',
      //   prop: 'sw',
      // },
    ];
  });

  const searchFields = ref([]);

  return {
    columns,
    searchFields,
    searchData,
  };
}
