<template>
  <div
    class="bid-evaluation-online-container"
    v-loading="loading"
  >
    <StageTabs
      v-model="activeTabIndex"
      @update:sectionId="handleSectionChange"
    />
    <div class="flex justify-between items-center">
      <el-select
        v-model="currentRound"
        placeholder="选择评标轮次"
        @change="loadData"
      >
        <el-option
          v-for="item in quotationRounds"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        />
      </el-select>
      <div>
        <el-button @click="handleViewBidOpeningRecord">查看开标记录</el-button>
        <el-button @click="handleViewQuotationData">查看报价数据</el-button>
      </div>
    </div>
    <div class="bid-evaluation-online-table need-hide-table-card">
      <yun-pro-table
        ref="proTableRef"
        :table-columns="tableColumns"
        v-model:tableData="tableData"
        :auto-height="true"
        :table-props="tablePropsObj"
        layout="whole"
        :default-fetch="false"
      >
        <template #t_index="{ row, $index }">
          <span v-if="row.isSummarized"> 汇总 </span>
          <span v-else> {{ $index + 1 }} </span>
        </template>
        <template #t_expertName="{ row }">
          <span>
            {{ row.expertName }}
          </span>
          <span v-if="row?.role === EVALUATION_MEMBER_ROLE_ENUM.EVALUATION_LEADER"> 【组长】 </span>
        </template>
        <template
          v-for="column in dynamicColumns"
          :key="column.nodeName"
          v-slot:[`t_${column.nodeName}`]="{ row }"
        >
          <!-- 判断是否是统计行 -->
          <template v-if="row.isSummarized">
            <el-button
              type="text"
              v-if="row?.[column?.nodeName]?.isSummarized"
              @click="handleSummary(row, column)"
            >
              <span>已汇总</span>
            </el-button>
            <span v-else>未汇总</span>
          </template>
          <template v-else>
            <div class="opt-container">
              <div
                class="inline-flex items-center mr-4"
                v-if="row[column.nodeName]?.['isCompleted'] && !isLMSEMember && !(isEvaluationCommitteeMember && isOwner(row))"
              >
                <span class="green-dot"></span>
                <span>评审完成</span>
              </div>
              <el-button
                type="text"
                v-if="row[column.nodeName]?.['isCompleted'] && ((isEvaluationCommitteeMember && isOwner(row)) || isLMSEMember)"
                @click="handleAudit(row, column, isEvaluationCommitteeMember && isOwner(row) ? '' : OPERATION_TYPE_ENUM.VIEW)"
              >
                <span>评审完成</span>
              </el-button>
              <el-button
                type="text"
                @click="handleAudit(row, column)"
                v-if="!row[column.nodeName]?.['isCompleted'] && (isEvaluationCommitteeMember || isEvaluationMember) && isOwner(row)"
              >
                <span>点击评审</span>
                <el-icon class="ml-2">
                  <Right />
                </el-icon>
              </el-button>
              <el-link
                class="ml-3"
                type="danger"
                :underline="false"
                :icon="RefreshLeft"
                v-if="row[column.nodeName]?.['isCompleted'] && isEvaluationCommitteeMember && isOwner(row)"
                @click="handleRevokeAudit(row, column)"
              >
                <span class="ml-1">撤回</span>
              </el-link>
            </div>
          </template>
        </template>
      </yun-pro-table>
    </div>
    <BidOpeningRecord ref="bidOpeningRecordRef" />
    <QuotationData ref="quotationDataRef" />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onBeforeMount } from 'vue';
import StageTabs from '@/views/procurementSourcing/biddingProcess/components/bidding/StageTabs.vue';
import { ElMessageBox, ElMessage } from 'yun-design';
import { useBiddingStore, useEvaluationStore } from '@/views/procurementSourcing/biddingProcess/stores';
import { RefreshLeft, Right } from '@yun-design/icons-vue';
import { useColumns } from './hooks/useColumns';
import { useRouter } from 'vue-router';
import { getEvaluationProgress, revokeScoringResult } from '@/api/purchasing/evaluation';
import { useUserInfo } from '/@/stores/userInfo';
import { EVALUATION_MEMBER_ROLE_ENUM } from '@/views/procurementSourcing/biddingProcess/components/BidEvaluation/committee/const';
import { OPERATION_TYPE_ENUM } from '@/views/procurementSourcing/biddingProcess/components/BidEvaluation/online/const';
import BidOpeningRecord from '@/views/procurementSourcing/biddingProcess/components/BidEvaluation/online/components/BidOpeningRecord/index.vue';
import QuotationData from '@/views/procurementSourcing/biddingProcess/components/BidEvaluation/online/components/QuotationData/index.vue';
// import { generateUUID } from '/@/utils/other';
// import { utf8ToBase64, base64ToUtf8 } from '@/views/procurementSourcing/biddingProcess/utils/bid';
import { numberToChinese } from '@/views/procurementSourcing/biddingProcess/utils';

const bidOpeningRecordRef = ref<any>();
const quotationDataRef = ref<any>();

const evaluationStore = useEvaluationStore();
const biddingStore = useBiddingStore();
const stores = useUserInfo();
const loginUserId = computed(() => stores?.userInfos?.user?.userId);
const noticeId = computed(() => biddingStore?.noticeId);
const projectId = computed(() => biddingStore?.projectId);
// const isEvaluationCommitteeMember = computed(() => biddingStore?.isEvaluationCommitteeMember);
const isEvaluationCommitteeMember = computed(() => {
  return biddingStore?.isEvaluationCommitteeMember || biddingStore?.isEvaluationMember;
});
const isEvaluationMember = computed(() => biddingStore?.isEvaluationMember);
const currentSectionId = ref('');
const activeTabIndex = ref(0);
const currentRound = ref(1);

interface DynamicColumn {
  nodeName: string;
  typeName: string;
}

const router = useRouter();

const loading = ref(false);
const tableData = ref([]);
const quotationRounds = ref<any>([]);
const { columns } = useColumns();
const dynamicColumns = ref<DynamicColumn[]>([]);
const tableColumns = computed(() => {
  const temp = dynamicColumns.value.map((item: any) => {
    const { typeName, nodeName } = item || {};
    return {
      label: `${nodeName}(${typeName})`,
      prop: nodeName,
    };
  });
  return [...columns.value, ...temp];
});

// // 判断是否具有权限
// function hasPermission(row: any) {
//   const { currentUserRole, userId } = row || {};
//   // 是负责人
//   if (EVALUATION_MEMBER_ROLE_ENUM.EVALUATION_LEADER === currentUserRole) {
//     return true;
//   }
//   // 是成员且是本人
//   if (EVALUATION_MEMBER_ROLE_ENUM.EVALUATION_MEMBER === currentUserRole && userId === loginUserId?.value) {
//     return true;
//   }
//   return false;
// }

// 项目负责人+项目小组成员+监督人员+评标组长 有权限查看
const isLMSEMember = computed(() => {
  return biddingStore?.isProjectMemberLeader || biddingStore?.isProjectMember || biddingStore?.isSupervision || biddingStore?.isEvaluationMember;
});

// 是否是自己的评审项
function isOwner(row: any) {
  const { userId } = row || {};
  // 是成员且是本人
  if (userId === loginUserId?.value) {
    return true;
  }
  return false;
}

// 是否是组长
// function isLeader(row: any) {
//   const { currentUserRole } = row || {};
//   // 是负责人
//   if (EVALUATION_MEMBER_ROLE_ENUM.EVALUATION_LEADER === currentUserRole) {
//     return true;
//   }
//   return false;
// }

const tablePropsObj = computed(() => ({
  stripe: true,
  border: true,
  headerCellStyle: {
    backgroundColor: '#f5f7fa',
    color: '#303133',
    fontWeight: 'bold',
    height: '40px',
  },
  cellStyle: {
    padding: '0',
    height: '40px',
    'vertical-align': 'middle',
  },
  rowStyle: {
    height: '40px',
  },
  rowHeight: 40,
}));

function handleRevokeAudit(row: any, column: any) {
  ElMessageBox.confirm('是否确定撤回已评审完成内容?', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  })
    .then(async () => {
      try {
        await revokeScoringResult({
          projectId: projectId.value,
          noticeId: noticeId.value,
          sectionId: currentSectionId.value,
          nodeName: column.nodeName,
          userId: row.userId,
          evaluationId: row.evaluationId,
        });
        ElMessage.success('操作成功');
        loadData();
      } catch (error) {
        // ElMessage.error('操作失败');
      }
    })
    .catch(() => {});
}

function generateUUID() {
  return Math.random().toString(36).substring(2);
}

function handleSummary(row: any, column: any) {
  const uuid = generateUUID();
  evaluationStore.setCurrentEvaluationItem({ row, column, uuid });
  // eslint-disable-next-line no-console
  setTimeout(() => {
    router.push({
      path: '/evaluationItemSummary',
      query: {
        tagsViewName: `已汇总-${column.typeName}`,
        sectionId: currentSectionId.value,
        uuid,
      },
    });
  }, 200);
}
function handleAudit(row: any, column: any, viewType?: string) {
  const uuid = generateUUID();
  evaluationStore.setCurrentEvaluationItem({ row, column, uuid });
  // eslint-disable-next-line no-console

  setTimeout(() => {
    router.push({
      path: '/evaluationItem',
      query: {
        tagsViewName: `${column.nodeName}(${column.typeName})`,
        sectionId: currentSectionId.value,
        uuid,
        viewType,
      },
    });
  }, 200);
}

const EVALUATION_TYPE_ENUM: Record<string, string> = {
  REVIEW: '评审项',
  SCORE: '评分项',
};

// 初始化轮次数据
function initRoundsData() {
  const quoteRoundVoList = biddingStore?.noticeInfo?.quoteRoundVoList || [];
  const quoteRoundCount =
    (quoteRoundVoList?.find((item: any) => String(item?.sectionId) === String(currentSectionId.value)) || {})?.currentQuoteRound || 1;
  quotationRounds.value = Array.from({ length: quoteRoundCount }, (_, i) => i + 1).map((item) => {
    return {
      label: `第${numberToChinese(item)}轮报价`,
      value: item,
    };
  });
  currentRound.value = quotationRounds.value[quotationRounds.value.length - 1]?.value || 1;
}

async function loadData() {
  loading.value = true;
  try {
    const result = await getEvaluationProgress({
      sectionId: currentSectionId.value || '28',
      noticeId: noticeId.value || '26',
      currentRound: currentRound.value,
    });

    const { evaluatorProgressList, evaluationNodes, currentUserRole, committeeInfo } = result.data || {};

    const nodeNames = evaluationNodes?.map((item: any) => item?.nodeName);

    dynamicColumns.value = (evaluationNodes || [])?.map((item: any) => {
      return {
        ...item,
        typeName: EVALUATION_TYPE_ENUM[item?.type] || item?.type,
      };
    });

    const temp = (evaluatorProgressList || [])
      ?.map((item: any) => {
        return {
          ...item,
          currentUserRole,
          ...(item.nodeCompletionMap || {}),
          evaluationId: committeeInfo?.id,
        };
      })
      ?.sort((a: any, b: any) => b?.role?.localeCompare(a?.role));

    // 如果存在组长  则需要插入一行统计数据
    const leader = temp.find((item: any) => item.role === EVALUATION_MEMBER_ROLE_ENUM.EVALUATION_LEADER);
    if (leader) {
      const t =
        nodeNames?.reduce((cur: any, item: any) => {
          // cur[item] = temp?.every((i: any) => i[item]?.isSummarized === true);
          cur[item] = {
            isCompleted: temp?.every((i: any) => i[item]?.isCompleted === true),
            isSummarized: temp?.every((i: any) => i[item]?.isSummarized === true),
          };
          return cur;
        }, {}) || {};
      temp.push({
        ...leader,
        isSummarized: true,
        // expertName: `${leader?.expertName}【组长】`,
        ...t,
      });
    }

    console.log('temp', temp);
    tableData.value = temp;
    // eslint-disable-next-line no-empty
  } catch (error) {
    console.log('error', error);
  }
  loading.value = false;
}

// 标段变化事件
async function handleSectionChange(sectionId: string) {
  loading.value = true;
  currentSectionId.value = sectionId;
  setTimeout(async () => {
    if (currentSectionId.value) {
      await initRoundsData();
      await loadData();
      loading.value = false;
    }
  }, 300);
}

function handleViewBidOpeningRecord() {
  bidOpeningRecordRef.value?.show({ sectionId: currentSectionId.value });
}

function handleViewQuotationData() {
  quotationDataRef.value?.show({ sectionId: currentSectionId.value });
}

onMounted(async () => {});
onBeforeMount(async () => {});
</script>

<style lang="scss" scoped>
@import '@/views/procurementSourcing/biddingProcess/styles/collapse-panel.scss';
.bid-evaluation-online-container {
  flex: 1;
  padding: 20px;
  position: relative;
  height: 100%;
  display: flex;
  flex-direction: column;
  gap: 20px;
  .bid-evaluation-online-table {
    flex: 1;
    overflow: hidden;
    .green-dot {
      display: inline-block;
      width: 6px;
      height: 6px;
      background-color: var(---el-color-success, #00b42a);
      border-radius: 50%;
      margin-right: 8px;
    }

    .opt-container:empty::before {
      content: '--';
      color: rgb(29, 33, 41);
    }
  }
}
</style>
