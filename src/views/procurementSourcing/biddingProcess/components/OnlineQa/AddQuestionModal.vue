<template>
  <el-dialog
    v-model="visible"
    title="新增问题"
    width="600px"
    :before-close="handleClose"
    destroy-on-close
    :close-on-click-modal="false"
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="rules"
      label-width="100px"
      class="add-question-form"
    >
      <el-form-item label="问题标题" prop="questionTitle" required>
        <el-input
          v-model="formData.questionTitle"
          placeholder="请输入问题标题"
          maxlength="100"
          show-word-limit
        />
      </el-form-item>

      <el-form-item label="问题描述" prop="questionContent" required>
        <el-input
          v-model="formData.questionContent"
          type="textarea"
          placeholder="请详细描述您的问题"
          :rows="6"
          maxlength="1000"
          show-word-limit
        />
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="loading">
          提交
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, computed } from 'vue'
import { ElMessage } from 'yun-design'
import type { FormInstance, FormRules } from 'element-plus'
import { addQuestion } from './api/onlineQa'
import type { AddQuestionForm } from './types/onlineQa'

interface Props {
  modelValue: boolean
  searchData: any
}

interface Emits {
  (e: 'update:modelValue', value: boolean): void
  (e: 'success'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const formRef = ref<FormInstance>()
const loading = ref(false)

// 表单数据
const formData = reactive<AddQuestionForm>({
  questionTitle: '',
  questionContent: ''
})

// 表单验证规则
const rules: FormRules = {
  questionTitle: [
    { required: true, message: '请输入问题标题', trigger: 'blur' },
    { min: 1, max: 100, message: '问题标题长度在1到100个字符', trigger: 'blur' }
  ],
  questionContent: [
    { required: true, message: '请输入问题描述', trigger: 'blur' },
    { min: 1, max: 1000, message: '问题描述长度在1到1000个字符', trigger: 'blur' }
  ]
}

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return

  try {
    const valid = await formRef.value.validate()
    if (!valid) return

    loading.value = true
    await addQuestion({
      ...formData,
      ...props.searchData
    })

    ElMessage.success('问题提交成功')
    emit('success')
    handleClose()
  } catch (error) {
    console.log(error);
  } finally {
    loading.value = false
  }
}

// 关闭对话框
const handleClose = () => {
  formRef.value?.resetFields()
  visible.value = false
}
</script>

<style scoped lang="scss">
.add-question-form {
  padding: 0 24px;
}

.dialog-footer {
  display: flex;
  justify-content: center;
  gap: 16px;
}
</style>
