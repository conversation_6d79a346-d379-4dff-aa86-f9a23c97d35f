/**
 * 在线答疑相关类型定义
 */

// 回复状态枚举
export enum AnswerStatus {
  PENDING = 'PENDING', // 待回复
  REPLIED = 'REPLIED'  // 已回复
}

// 回复状态选项
export const ANSWER_STATUS_OPTIONS = [
  { label: '待回复', value: AnswerStatus.PENDING },
  { label: '已回复', value: AnswerStatus.REPLIED }
]

// 问答记录接口
export interface OnlineQaRecord {
  id: string
  questionTitle: string
  questionContent: string
  supplierName: string
  submitBy: string
  submitTime: string
  answerStatus: AnswerStatus
  answerContent?: string
  answerBy?: string
  answerTime?: string
}

// 新增问题表单数据
export interface AddQuestionForm {
  questionTitle: string
  questionContent: string
}

// 回复问题表单数据
export interface ReplyQuestionForm {
  id: string
  answerContent: string
}
