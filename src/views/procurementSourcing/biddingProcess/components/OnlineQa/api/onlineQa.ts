import request from '/@/utils/request'

/**
 * 分页查询在线答疑列表
 * @param data 查询参数
 * @returns
 */
export function fetchOnlineQaPage(data: any) {
  return request({
    url: `/admin/onlineQa/findQuestion`,
    method: 'post',
    data
  })
}

/**
 * 新增问题（供应商使用）
 * @param data 问题数据
 * @returns
 */
export function addQuestion(data: any) {
  return request({
    url: '/admin/onlineQa/addQuestion',
    method: 'post',
    data
  })
}

/**
 * 回复问题（采购方使用）
 * @param data 回复数据
 * @returns
 */
export function replyQuestion(data: any) {
  return request({
    url: `/admin/onlineQa/replyQuestion`,
    method: 'post',
    data
  })
}
