<template>
  <el-dialog
    v-model="visible"
    title="回复问题"
    width="600px"
    :before-close="handleClose"
    destroy-on-close
    :close-on-click-modal="false"
  >
    <div class="reply-modal-content">
      <!-- 回复表单 -->
      <div>
        <el-form
          ref="formRef"
          :model="formData"
          :rules="rules"
          label-width=""
        >
          <el-form-item label="" prop="answerContent" required>
            <el-input
              v-model="formData.answerContent"
              type="textarea"
              placeholder="请输入回复内容"
              :rows="6"
              maxlength="1000"
              show-word-limit
            />
          </el-form-item>
        </el-form>
      </div>
    </div>
    <div class="dialog-footer">
      <el-button @click="handleClose">取消</el-button>
      <el-button type="primary" @click="handleSubmit" :loading="loading">
        提交
      </el-button>
    </div>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch } from 'vue'
import { ElMessage } from 'yun-design'
import type { FormInstance, FormRules } from 'element-plus'
import { replyQuestion } from './api/onlineQa'
import type { OnlineQaRecord, ReplyQuestionForm } from './types/onlineQa'

interface Props {
  modelValue: boolean
  questionData?: OnlineQaRecord | null
  searchData: any
}

interface Emits {
  (e: 'update:modelValue', value: boolean): void
  (e: 'success'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const formRef = ref<FormInstance>()
const loading = ref(false)

// 表单数据
const formData = reactive<ReplyQuestionForm>({
  id: '',
  answerContent: ''
})

// 表单验证规则
const rules: FormRules = {
  answerContent: [
    { required: true, message: '请输入回复内容', trigger: 'blur' },
    { min: 1, max: 1000, message: '回复内容长度在1到1000个字符', trigger: 'blur' }
  ]
}

// 监听问题数据变化，更新表单ID
watch(() => props.questionData, (newData) => {
  if (newData) {
    formData.id = newData.id
  }
}, { immediate: true })

// 提交回复
const handleSubmit = async () => {
  if (!formRef.value) return

  try {
    const valid = await formRef.value.validate()
    if (!valid) return

    loading.value = true
    await replyQuestion({
      ...formData,
      ...props.searchData
    })

    ElMessage.success('回复提交成功')
    emit('success')
    handleClose()
  } catch (error) {
    ElMessage.error('提交回复失败，请重试')
  } finally {
    loading.value = false
  }
}

// 关闭对话框
const handleClose = () => {
  formRef.value?.resetFields()
  formData.answerContent = ''
  visible.value = false
}
</script>

<style scoped lang="scss">
.reply-modal-content {
  .question-info {
    margin-bottom: 24px;

    h4 {
      margin: 0 0 16px 0;
      color: #303133;
      font-size: 16px;
      font-weight: 600;
    }

    .question-content {
      white-space: pre-wrap;
      word-break: break-word;
      line-height: 1.6;
    }
  }

  .reply-form {
    h4 {
      margin: 0 0 16px 0;
      color: #303133;
      font-size: 16px;
      font-weight: 600;
    }
  }
}

.dialog-footer {
  display: flex;
  justify-content: end;
  gap: 16px;
}
</style>
