<template>
  <el-drawer
    v-model="visible"
    title="在线问答"
    size="80%"
    :before-close="handleClose"
    destroy-on-close
  >
    <div class="online-qa-drawer">
      <!-- 操作区域 -->
      <div class="action-section" v-if="isSupplier">
        <el-button type="primary" border @click="handleAddQuestion">
          新增问答
        </el-button>
      </div>

      <!-- 表格区域 -->
      <div class="table-section">
        <el-table
          :data="tableData"
          style="width: 100%"
          border
          stripe
          v-loading="loading"
        >
          <el-table-column
            label="序号"
            type="index"
            width="60"
            align="center"
          />
          <el-table-column
            label="问题标题"
            prop="questionTitle"
            min-width="150"
          />
          <el-table-column
            label="问题描述"
            prop="questionContent"
            min-width="200"
          />
          <el-table-column
            label="公司名称"
            prop="supplierName"
            width="150"
          />
          <el-table-column
            label="提问人"
            prop="submitBy"
            width="120"
            align="center"
          />
          <el-table-column
            label="提出时间"
            prop="submitTime"
            width="160"
            align="center"
          />
          <el-table-column
            label="回复状态"
            prop="answerStatus"
            width="100"
            align="center"
          >
            <template #default="{ row }">
              <el-tag
                :type="row.answerStatus === 'REPLIED' ? 'success' : 'warning'"
              >
                {{ getAnswerStatusLabel(row.answerStatus) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column
            label="回复详情"
            prop="answerContent"
            min-width="200"
          >
            <template #default="{ row }">
              <span v-if="row.answerContent">{{ row.answerContent }}</span>
              <span v-else class="no-reply">暂无回复</span>
            </template>
          </el-table-column>
          <el-table-column
            v-if="isPurchaser"
            label="操作"
            width="100"
            align="center"
            fixed="right"
          >
            <template #default="{ row }">
              <el-button
                type="text"
                @click="handleReply(row)"
                :disabled="row.answerStatus === 'REPLIED'"
              >
                回复
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- 分页 -->
      <div class="pagination-section">
        <el-pagination
          v-model:current-page="pagination.current"
          v-model:page-size="pagination.size"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>

    <!-- 新增问题模态框 -->
    <AddQuestionModal
      v-model="addQuestionModalVisible"
      :searchData="searchForm"
      @success="handleAddSuccess"
    />

    <!-- 回复问题模态框 -->
    <ReplyModal
      v-model="replyModalVisible"
      :question-data="currentQuestionData"
      :searchData="searchForm"
      @success="handleReplySuccess"
    />
  </el-drawer>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, watch } from 'vue'
import { ElMessage } from 'yun-design'
import { useUserRole } from '../../utils/useUserRole'
import { fetchOnlineQaPage } from './api/onlineQa'
import type { OnlineQaRecord, OnlineQaPageParams, AnswerStatus } from './types/onlineQa'
import { ANSWER_STATUS_OPTIONS } from './types/onlineQa'
import AddQuestionModal from './AddQuestionModal.vue'
import ReplyModal from './ReplyModal.vue'

interface Props {
  modelValue: boolean
}

interface Emits {
  (e: 'update:modelValue', value: boolean): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

// 用户角色
const { isPurchaser, isSupplier } = useUserRole()

// 表格数据
const tableData = ref<OnlineQaRecord[]>([])
const loading = ref(false)

// 搜索表单
const searchForm = reactive<any>({
  noticeId: '19',
  projectId: '20',
})

// 分页数据
const pagination = reactive({
  current: 1,
  size: 10,
  total: 0
})

// 模态框控制
const addQuestionModalVisible = ref(false)
const replyModalVisible = ref(false)
const currentQuestionData = ref<OnlineQaRecord | null>(null)

// 获取回复状态标签
const getAnswerStatusLabel = (status: AnswerStatus) => {
  const option = ANSWER_STATUS_OPTIONS.find(item => item.value === status)
  return option?.label || status
}

// 获取列表数据
const getTableData = async () => {
  try {
    loading.value = true
    const params: OnlineQaPageParams = {
      ...searchForm,
      current: pagination.current,
      size: pagination.size
    }

    const response = await fetchOnlineQaPage(params)
    tableData.value = response.data.records || []
    pagination.total = response.data.total || 0
  } catch (error) {
    console.error('获取在线答疑列表失败:', error)
    ElMessage.error('获取数据失败，请重试')
  } finally {
    loading.value = false
  }
}

// 新增问题
const handleAddQuestion = () => {
  addQuestionModalVisible.value = true
}

// 回复问题
const handleReply = (row: OnlineQaRecord) => {
  currentQuestionData.value = row
  replyModalVisible.value = true
}

// 新增成功回调
const handleAddSuccess = () => {
  getTableData()
}

// 回复成功回调
const handleReplySuccess = () => {
  getTableData()
}

// 分页大小改变
const handleSizeChange = (size: number) => {
  pagination.size = size
  pagination.current = 1
  getTableData()
}

// 当前页改变
const handleCurrentChange = (current: number) => {
  pagination.current = current
  getTableData()
}

// 关闭抽屉
const handleClose = () => {
  visible.value = false
}

// 组件挂载时获取数据
onMounted(() => {
  if (visible.value) {
    getTableData()
  }
})

// 监听抽屉打开，获取数据
watch(visible, (newVal) => {
  if (newVal) {
    getTableData()
  }
})
</script>

<style scoped lang="scss">
.online-qa-drawer {
  padding: 0 24px 24px;
  height: 100%;
  display: flex;
  flex-direction: column;

  .search-section {
    margin-bottom: 16px;
    padding: 16px;
    background: #f8f9fa;
    border-radius: 4px;
  }

  .action-section {
    margin-bottom: 16px;
    display: flex;
    justify-content: flex-end;
  }

  .table-section {
    flex: 1;
    margin-bottom: 16px;

    .no-reply {
      color: #909399;
      font-style: italic;
    }
  }

  .pagination-section {
    display: flex;
    justify-content: end;
    padding-top: 16px;
  }
}
</style>
