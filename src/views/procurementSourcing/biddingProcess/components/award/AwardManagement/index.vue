<template>
  <div class="award-management-container">
    <!-- 步骤指示器 -->
    <StepsIndicator
      v-if="!isAuditMode"
      :steps="stepsList"
      @step-click="handleStepClick"
    />

    <!-- 动态组件渲染区域 -->
    <div class="component-content">
      <component :is="currentComponent" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import StepsIndicator, { type StepItem } from '../../StepsIndicator/index.vue';
import { useRoute } from 'vue-router';
import AwardResult from '../AwardResult/index.vue';
import WinnerPublicity from '../WinnerPublicity/index.vue';
import WinnerPublicityView from '../WinnerPublicity/view.vue';
import WinnerAnnouncement from '../WinnerAnnouncement/index.vue';
import WinnerAnnouncementView from '../WinnerAnnouncement/view.vue';
import WinnerNotification from '../WinnerNotification/index.vue';
import WinnerNotificationView from '../WinnerNotification/view.vue';
import { useUserRole } from '@/views/procurementSourcing/biddingProcess/utils';
import { useAwardStore, useBiddingStore } from '@/views/procurementSourcing/biddingProcess/stores';

const { isPurchaser } = useUserRole();
const awardStore = useAwardStore();
const biddingStore = useBiddingStore();
const projectDetail = computed(() => biddingStore?.projectDetail);
const route = useRoute()

// 审核模式判断
const isAuditMode = computed(() => {
  return route.query.audit === 'true' || route.query.mode === 'audit';
});


// 当前激活的步骤索引
const activeStepIndex = ref(0);

// 根据邀请方式动态生成组件映射
const componentMap = computed(() => {
  const isPublicity = projectDetail.value?.inviteMethod === "PUBLICITY";

  if (isPublicity) {
    // PUBLICITY 模式：包含所有组件
    return {
      0: AwardResult,
      1: WinnerPublicity,
      2: WinnerAnnouncement,
      3: WinnerNotification,
      10: WinnerPublicityView,
      11: WinnerAnnouncementView,
      12: WinnerNotificationView,
    };
  } else {
    // 非 PUBLICITY 模式：只包含定标结果和中标通知书
    return {
      0: AwardResult,
      3: WinnerNotification,
      12: WinnerNotificationView,
    };
  }
});

// 当前显示的组件
const currentComponent = computed(() => {
  const component = componentMap.value[activeStepIndex.value as keyof typeof componentMap.value];
  if (!component) {
    return null;
  }
  return component;
});

// 初始化当前步骤索引到最新可用的节点
const initActiveStepIndex = () => {
  // 获取当前步骤列表
  const currentSteps = stepsList.value;
  if (currentSteps && currentSteps.length > 0) {
    // 找到最新一个可用的节点
    const lastAvailableStep = currentSteps.findLast(step => step.accessible);
    if (lastAvailableStep && lastAvailableStep.id !== undefined) {
      // 设置到该节点对应的组件索引（使用步骤的id）
      activeStepIndex.value = lastAvailableStep.id as number;
      return;
    }
  }

  // 如果没有找到可用节点，使用默认逻辑
  if (isPurchaser.value) {
    activeStepIndex.value = 0;
  } else {
    if (projectDetail.value?.inviteMethod === "PUBLICITY") {
      activeStepIndex.value = 10;
    } else {
      activeStepIndex.value = 12; // 非PUBLICITY模式下，供应商只能看到中标通知书
    }
  }
};

// 步骤列表 - 使用 store 中的流程控制逻辑，并根据邀请方式调整
const stepsList = computed(() => {
  if (isPurchaser.value) {
    return awardStore.getPurchaserSteps(activeStepIndex.value);
  } else {
    return awardStore.getSupplierSteps(activeStepIndex.value);
  }
});


// 处理步骤点击事件 - 带流程控制
function handleStepClick(step: StepItem, index: number) {
  // 检查步骤是否可访问
  if (!awardStore.isStepAccessible(step.id as number)) {
    return;
  }

  activeStepIndex.value = step.id as number;
}

// 初始化数据
async function initData() {
  try {
    // 获取定标详情数据（用于流程控制）
    if (biddingStore.noticeId && biddingStore.projectId) {
      await awardStore.getTenderBidDetail(biddingStore.noticeId, biddingStore.projectId);
      // 初始化完成后设置正确的步骤索引
      initActiveStepIndex();
    }
  } catch (error) {
    console.error('初始化定标管理数据失败:', error);
  }
}

// 组件挂载时初始化
onMounted(() => {
  initData();
});
</script>

<style lang="scss" scoped>
.award-management-container {
  min-height: 100%;
  display: flex;
  flex-direction: column;

  .component-content {
    flex: 1;
    margin-top: 12px;
    border-radius: 6px;
  }
}

.form-actions-wrapper {
  width: 100%;
  box-sizing: border-box;
  background-color: #fff;
  z-index: 1000;
  position: sticky;
  bottom: -20px;
  left: 0;
  right: 0;
  padding: 16px 24px;
  display: flex;
  gap: 12px;
  border-top: 1px solid var(--Color-Border-border-color-light, #e4e7ed);

  .form-actions {
    display: flex;
    justify-content: flex-start;
    gap: 12px;
    width: 100%;
  }
}
</style>
