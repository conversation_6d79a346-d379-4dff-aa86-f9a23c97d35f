<template>
	<yun-drawer
		v-model="visible"
		:title="currentTitle"
		size="large"
		destroy-on-close
		:show-cancel-button="!readonly"
		:confirm-button-text="readonly ? '' : '保存'"
		:cancel-button-text="readonly ? '关闭' : '取消'"
		@confirm="handleSave"
		@cancel="handleCancel"
	>
		<div class="editor-container">
			<Toolbar
				v-if="!readonly"
				style="border-bottom: 1px solid #ccc"
				:editor="editorRef"
				:defaultConfig="toolbarConfig"
				mode="default"
			/>
			<Editor
				:style="readonly ? 'height: calc(100vh - 120px); overflow-y: hidden;' : 'height: calc(100vh - 200px); overflow-y: hidden;'"
				v-model="content"
				:defaultConfig="editorConfig"
				mode="default"
				@onCreated="handleCreated"
				@onChange="handleChange"
			/>
		</div>
	</yun-drawer>
</template>

<script setup lang="ts">
import { ref, shallowRef, onBeforeUnmount, toRefs } from 'vue';
import { Editor, Toolbar } from '@wangeditor/editor-for-vue';
import '@wangeditor/editor/dist/css/style.css';
import type { IDomEditor, IEditorConfig, IToolbarConfig } from '@wangeditor/editor';

const props = defineProps({
	title1: {
		type: String,
		default: '编辑公告内容'
	},
	readonly: {
		type: Boolean,
		default: false
	}
});

const { readonly } = toRefs(props);

// 定义事件
const emit = defineEmits<{
	save: [content: string];
}>();

// 组件属性
const visible = ref(false);
const content = ref('');
const editorRef = shallowRef<IDomEditor>();
const currentTitle = ref(props.title1);

// 编辑器配置
const editorConfig: Partial<IEditorConfig> = {
	placeholder: '请输入公告内容...',
	autoFocus: false,
	scroll: true,
	readOnly: readonly.value,
	MENU_CONF: {
		uploadImage: {
			server: '/api/upload',
			fieldName: 'file',
		},
	},
};

// 工具栏配置
const toolbarConfig: Partial<IToolbarConfig> = {
	excludeKeys: [
		'group-video',
		'todo',
		'emotion',
		'fullScreen',
	],
};

// 编辑器生命周期
function handleCreated(editor: IDomEditor) {
	editorRef.value = editor;
}

function handleChange(editor: IDomEditor) {
	content.value = editor.getHtml();
}

// 处理保存
function handleSave(done: () => void) {
	// 触发保存事件，将内容传递给父组件
	emit('save', content.value);
	done();
}

// 处理取消
function handleCancel() {
	// 可以在这里添加取消逻辑
}

// 显示抽屉
function show(initialContent: string = '', title: string = '') {
	visible.value = true;
	content.value = initialContent;

	// 如果传入了标题，更新标题
	if (title) {
		currentTitle.value = title;
	} else {
		currentTitle.value = props.title1;
	}

	// 等待编辑器创建完成后设置内容
	setTimeout(() => {
		if (editorRef.value) {
			editorRef.value.setHtml(initialContent);
		}
	}, 100);
}

// 组件卸载时销毁编辑器
onBeforeUnmount(() => {
	const editor = editorRef.value;
	if (editor == null) return;
	editor.destroy();
});

// 暴露方法
defineExpose({
	show,
});
</script>

<style lang="scss" scoped>
.editor-container {
	height: 100%;
	display: flex;
	flex-direction: column;
	border: 1px solid #ccc;
	border-radius: 4px;
	overflow: hidden;
}

:deep(.w-e-toolbar) {
	border-bottom: 1px solid #ccc !important;
}

:deep(.w-e-text-container) {
	flex: 1;
}

:deep(.w-e-text-placeholder) {
	color: #999;
	font-style: italic;
}
</style>
