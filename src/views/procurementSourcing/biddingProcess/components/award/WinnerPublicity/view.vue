<template>
  <div
    class="winner-publicity-container"
    v-loading="loading"
  >
    <div class="bg-[#fff] p-5 mb-3">
      <div class="section-header">
        <div class="header-title">中标信息</div>
      </div>
      <!-- 标签栏 -->
      <StageTabs
        class="my-4"
        v-model="activeTabIndex"
        @update:sectionId="handleSectionChange"
      />

      <!-- 中标信息表格 -->
      <el-table
        :data="winnerTableData"
        :loading="tableLoading"
        style="width: 100%"
        class="editable-table"
      >
        <el-table-column
          label="序号"
          type="index"
          width="60"
          align="center"
        />
        <el-table-column
          label="供应商名称"
          prop="supplierName"
          min-width="200"
        />
        <el-table-column
          label="联系人"
          prop="contactPerson"
          width="120"
          align="center"
        />
        <el-table-column
          label="联系方式"
          prop="contactPhone"
          width="140"
          align="center"
        />
        <el-table-column
          label="成交价格"
          prop="totalPrice"
          width="120"
          align="center"
        >
          <template #default="{ row }">
            <span>{{ formatPrice(row.totalPrice) }}</span>
          </template>
        </el-table-column>
        <el-table-column
          label="操作"
          width="140"
          align="center"
        >
          <template #default="{ row }">
            <el-button
              v-if="row.showDetail"
              type="text"
              @click="handleViewDetail(row)"
            >
              查看中标明细
            </el-button>
            <div v-else>-</div>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 公示要求 -->
    <div class="publicity-requirements-section">
      <div class="section-header">
        <div class="header-title">中标公示</div>
      </div>
      <div class="publicity-content">
        <div v-html="publicityForm.content"></div>
      </div>

      <div
        class="flex mt-5"
        v-if="publicityForm.attachmentList.length > 0"
      >
        <div>附件：</div>
        <div class="flex flex-col gap-2">
          <div
            v-for="(attachment, index) in publicityForm.attachmentList"
            :key="index"
            class="flex items-center gap-2 text-[#1d2129]"
          >
            <span>{{ attachment.name }}</span>
            <el-icon
              size="12"
              class="color-primary cursor-pointer"
              @click="handleDownload(attachment)"
            >
              <download />
            </el-icon>
          </div>
        </div>
      </div>

      <!-- <div
        class="flex mt-3"
        v-if="publicityForm.platformList.length > 0"
      >
        <div>发送媒体：</div>
        <div class="text-[#1d2129]">{{ publicityForm.platformList.join('、') }}</div>
      </div> -->
    </div>
  </div>

  <!-- 中标明细抽屉 -->
  <AwardDetailDrawer
    v-model:visible="awardDetailDrawerVisible"
    :supplier-data="selectedSupplierData"
  />
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue';
import { Download } from '@element-plus/icons-vue';
import StageTabs from '@/views/procurementSourcing/biddingProcess/components/bidding/StageTabs.vue';
import { getTenderBidById } from '../../../api/award';
import { getAwardedSuppliers } from '../../../api/WinnerPublicity';
import type { TenderBidPublicityDetail } from '../../../types/award';
import type { AwardedSupplierInfo } from '../../../types/winnerPublicity';
import { useBiddingStore } from '@/views/procurementSourcing/biddingProcess/stores';
import AwardDetailDrawer from './AwardDetailDrawer.vue';
import downloadUrlFile from '@/utils/downloadUrl.js';

const biddingStore = useBiddingStore();
const noticeId = computed(() => biddingStore?.noticeId);
const projectId = computed(() => biddingStore?.projectId);
const ownSupplier = computed(() => biddingStore?.ownSupplier || {});

biddingStore.initData();

// 中标公示详情数据
const tenderBidDetail = ref<TenderBidPublicityDetail | null>(null);

// 数据加载状态
const loading = ref(false);

// 当前选中的标段ID
const currentSectionId = ref<number | null>(null);

// 中标信息表格数据（原始数据）
const allWinnerData = ref<AwardedSupplierInfo[]>([]);

// 过滤后的表格数据（用于显示）
const winnerTableData = ref<any[]>([]);

// 表格加载状态
const tableLoading = ref(false);

const publicityForm = reactive({
  title: '',
  content: '',
  attachmentList: [] as any[],
  platformList: [] as string[],
});

const activeTabIndex = ref(0);

// 格式化价格
function formatPrice(price: number): string {
  return `¥${price.toLocaleString('zh-CN', { minimumFractionDigits: 2 })}`;
}

const awardDetailDrawerVisible = ref(false);
const selectedSupplierData = ref<AwardedSupplierInfo | null>(null);
// 处理查看中标明细
function handleViewDetail(row: any) {
  selectedSupplierData.value = row;
  awardDetailDrawerVisible.value = true;
}

const handleDownload = (attachment?: any) => {
  downloadUrlFile(attachment.filePath, attachment.fileName);
};

// 处理标段变化
function handleSectionChange(sectionId: number) {
  currentSectionId.value = sectionId;
  filterWinnerDataBySectionId();
}

// 根据标段ID过滤中标供应商数据
function filterWinnerDataBySectionId() {
  if (currentSectionId.value === null) {
    // 显示所有数据，转换为表格需要的格式
    winnerTableData.value = convertToTableFormat(allWinnerData.value);
  } else {
    // 过滤指定标段的数据，转换为表格需要的格式
    const filteredData = allWinnerData.value.filter((item) => item.sectionId === currentSectionId.value && item.awardReportStatus === 'APPROVE');
    winnerTableData.value = convertToTableFormat(filteredData);
  }
}

// 将中标供应商数据转换为表格显示格式
function convertToTableFormat(suppliers: AwardedSupplierInfo[]) {
  return suppliers.map((supplier) => ({
    id: supplier.tenantSupplierId,
    supplierName: supplier.supplierName,
    contactPerson: supplier.contactName, // 映射 contactName 到 contactPerson
    contactPhone: supplier.contactPhone,
    totalPrice: supplier.totalAwardedAmount || 0, // 映射 totalAwardedAmount 到 totalPrice
    sectionId: supplier.sectionId,
    tenantSupplierId: supplier.tenantSupplierId,
    showDetail: supplier.showDetail,
  }));
}

// 获取中标供应商列表
async function getWinnerData() {
  try {
    tableLoading.value = true;
    const { data } = await getAwardedSuppliers(noticeId.value, projectId.value);

    if (data && Array.isArray(data)) {
      // 存储原始数据
      // allWinnerData.value = data.filter((item: any) => item.supplierCode === ownSupplier.value.supplierCode);
      allWinnerData.value = (data ?? []).map((item: any) => ({ ...item, showDetail: item.tenantSupplierId === ownSupplier.value.id }));

      // 根据当前选中的标段进行过滤并转换格式
      filterWinnerDataBySectionId();
    } else {
      allWinnerData.value = [];
      winnerTableData.value = [];
    }
  } catch (error) {
    console.error('获取中标供应商列表失败:', error);
    allWinnerData.value = [];
    winnerTableData.value = [];
  } finally {
    tableLoading.value = false;
  }
}

// 获取中标公示详情数据
async function getTenderBidDetails() {
  try {
    loading.value = true;
    const { data } = await getTenderBidById(noticeId.value, projectId.value);

    if (data) {
      tenderBidDetail.value = data;

      // 填充公示表单数据（注意：公示使用 publicity 字段，公示使用 notice 字段）
      publicityForm.title = data.publicityTitle || '';
      publicityForm.content = data.publicityContent || '';

      // 处理附件数据（注意：公示使用 publicityAttachments，公示使用 noticeAttachments）
      if (data.publicityAttachments && Array.isArray(data.publicityAttachments)) {
        publicityForm.attachmentList = data.publicityAttachments.map((attachment: any) => ({
          id: attachment.id || Date.now().toString(),
          name: attachment.name || attachment.fileName || '',
          url: attachment.url || attachment.filePath || '',
          size: attachment.size || attachment.fileSize || 0,
          type: attachment.type || attachment.fileType || '',
        }));
      }

      // 处理平台信息（如果有的话）
      publicityForm.platformList = ['优质来云采购平台（https://www.youzhicai.com)'];
    }
  } catch (error) {
    console.error('获取中标公示详情失败:', error);
  } finally {
    loading.value = false;
  }
}

// 初始化函数
async function init() {
  // 并行获取中标公示详情数据和中标供应商数据
  await Promise.all([getTenderBidDetails(), getWinnerData()]);
}

// 组件挂载时初始化
onMounted(() => {
  init();
});
</script>

<style lang="scss" scoped>
@import '../../../styles/collapse-panel.scss';
.winner-publicity-container {
  color: #4e5969;
  font-size: 14px;
}

.page-header {
  margin-bottom: 16px;

  .company-info {
    display: flex;
    align-items: center;
    gap: 8px;

    .company-icon {
      color: #0069ff;
      font-size: 18px;
    }

    .company-name {
      font-size: 18px;
      font-weight: 600;
      color: #1d2129;
    }

    .status-tag {
      margin-left: 8px;
    }
  }
}

.winner-table-section,
.publicity-requirements-section,
.publicity-details-section,
.publicity-attachments-section,
.publicity-platform-section {
  margin-bottom: 12px;
  background: #fff;
  border-radius: 6px;
  padding: 20px;

  .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
  }
}

.publicity-form {
  :deep(.el-form-item) {
    margin-bottom: 20px;
  }

  :deep(.el-form-item__label) {
    //color: #86909c;
    //font-weight: 400;
  }
}

.winner-table {
  :deep(.el-table__header) {
    th {
      background: #f5f7fa;
      padding: 12px 0;
      border-bottom: 1px solid #ebeef5;

      .cell {
        color: #505762;
        font-size: 14px;
        font-weight: 500;
      }
    }
  }

  :deep(.el-table__body) {
    tr {
      td {
        border-bottom: 1px solid #ebeef5;
        padding: 12px 0;
      }
    }
  }
}

.platform-checkboxes {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

:deep(.el-range-input) {
  background: #fff;
}

.publicity-content {
  display: flex;
  padding: 32px 40px;
  flex-direction: column;
  align-items: center;
  gap: 16px;
  align-self: stretch;
  background: var(--Color-Fill-fill-color-light, #f5f7fa);
}
</style>
