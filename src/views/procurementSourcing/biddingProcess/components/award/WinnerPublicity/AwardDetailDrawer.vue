<template>
  <el-drawer
    v-model="showVisible"
    :title="title"
    size="70vw"
    :with-header="true"
    append-to-body
    destroy-on-close
  >
    <div
      v-loading="loading"
      class="award-detail-container"
    >
      <!-- 供应商信息 -->
      <div class="form-section">
        <div class="label-header">供应商信息</div>
        <el-descriptions
          :column="2"
          class="supplier-info"
        >
          <el-descriptions-item label="供应商名称">
            {{ supplierInfo.supplierName || '--' }}
          </el-descriptions-item>
          <el-descriptions-item label="供应商编号">
            {{ supplierInfo.supplierCode || '--' }}
          </el-descriptions-item>
          <el-descriptions-item label="联系人">
            {{ supplierInfo.contactName || '--' }}
          </el-descriptions-item>
          <el-descriptions-item label="联系方式">
            {{ supplierInfo.contactPhone || '--' }}
          </el-descriptions-item>
          <el-descriptions-item label="中标总金额">
            <span class="amount-text">{{ formatPrice(supplierInfo.totalAwardedAmount) }}</span>
          </el-descriptions-item>
          <el-descriptions-item label="中标物料数量">
            {{ supplierInfo.awardedMaterialCount || '--' }}
          </el-descriptions-item>
        </el-descriptions>
      </div>

      <!-- 中标明细清单 -->
      <div class="form-section">
        <div class="label-header">中标明细清单</div>
        <div class="form-content">
          <div class="form-item-wrapper">
            <label class="form-label">物料名称</label>
            <el-input
              v-model="queryForm.materialName"
              placeholder="请输入物料名称"
              clearable
              class="search-input"
            />
          </div>
          <div class="form-item-wrapper">
            <label class="form-label">物料编码</label>
            <el-input
              v-model="queryForm.materialCode"
              placeholder="请输入物料编码"
              clearable
              class="search-input"
            />
          </div>
          <div>
            <el-button
              @click="searchHandler"
              type="primary"
            >
              搜索
            </el-button>
            <el-button @click="resetSearch">重置</el-button>
          </div>
          <div>
            <el-button
              @click="handleExport"
              :disabled="awardDetailList.length === 0"
            >
              <el-icon style="margin-right: 6px">
                <Download />
              </el-icon>
              导出中标明细
            </el-button>
          </div>
        </div>
      </div>

      <!-- 中标明细表格 -->
      <el-table
        :data="awardDetailList"
        style="width: 100%"
        class="award-detail-table"
        row-key="id"
        v-loading="tableLoading"
      >
        <template
          v-for="item in dynamicColumn"
          :key="item.prop || item.label"
        >
          <template v-if="item.children?.length">
            <el-table-column
              :key="item.prop"
              :label="item.label"
              :prop="item.prop"
              :min-width="item.width"
              :show-overflow-tooltip="item.showOverflowTooltip"
            >
              <el-table-column
                v-for="child in item.children"
                :key="child.prop"
                :label="child.label"
                :prop="child.prop"
                :min-width="child.width"
                :show-overflow-tooltip="child.showOverflowTooltip"
              />
            </el-table-column>
          </template>
          <el-table-column
            v-else
            :key="item.prop || item.label"
            :label="item.label"
            :prop="item.prop"
            :min-width="item.width"
            :show-overflow-tooltip="item.showOverflowTooltip"
          >
          </el-table-column>
        </template>


        <el-table-column
          label="中标价格"
          prop="bidAmount"
          min-width="200"
          fixed="right"
        >
          <template #default="{ row }">
            <div>{{ (row.paymentList?.find((item: any) => item.value === row.procurementProjectPaymentId)?.price ?? 0) * row.awardedQuantity }}</div>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <template #footer>
      <div class="flex justify-end gap-4">
        <el-button @click="showVisible = false">关闭</el-button>
      </div>
    </template>
  </el-drawer>
</template>

<script setup lang="ts">
import { ref, watch, computed } from 'vue';
import { Download } from '@element-plus/icons-vue';
import { ElMessage } from 'yun-design';
import moment from 'moment';
import { useBiddingStore, useAwardStore } from '@/views/procurementSourcing/biddingProcess/stores';
import { useDynamicTable } from '@/views/procurementSourcing/biddingProcess/hooks/useDynamicTable.jsx';
import { useExcel } from '@/views/procurementSourcing/biddingProcess/hooks/useExcel.jsx';
import { getAwardedItems } from '../../../api/WinnerPublicity';
import type { AwardedSupplierInfo } from '../../../types/winnerPublicity';

const biddingStore = useBiddingStore();
const awardStore = useAwardStore();
const noticeId = computed(() => biddingStore?.noticeId);
const projectId = computed(() => biddingStore?.projectId);
const { dynamicColumn, tableData, setDynamicColumn, handleTableData, objectSpanMethod } = useDynamicTable();
const { exportToExcel } = useExcel();

const loading = ref(false);
const tableLoading = ref(false);

// 组件属性
const props = defineProps<{
  visible: boolean;
  supplierData?: AwardedSupplierInfo;
}>();

const emit = defineEmits(['update:visible']);

const showVisible = computed({
  get: () => props.visible,
  set: (v: boolean) => emit('update:visible', v),
});

// 标题
const title = computed(() => {
  return `${props.supplierData?.supplierName || '供应商'} - 中标明细`;
});

// 供应商信息
const supplierInfo = ref<AwardedSupplierInfo>({} as AwardedSupplierInfo);

// 查询表单
const initQueryForm = () => ({
  materialName: '',
  materialCode: '',
});
const queryForm = ref<any>(initQueryForm());

// 中标明细列表
const awardDetailList = ref<any[]>([]);

// 附件信息
const attachmentInfo = ref<{
  quoteAttachment: any[];
  otherAttachment: any[];
}>({
  quoteAttachment: [],
  otherAttachment: [],
});

// 监听抽屉显示状态
watch(
  () => showVisible.value,
  async (v) => {
    if (v && props.supplierData) {
      supplierInfo.value = { ...props.supplierData };
      await awardStore.loadForAwardResult(noticeId.value, projectId.value, true);
      resetSearch();
      getAwardDetailData();
    }
  }
);

// 格式化价格
function formatPrice(price: number): string {
  if (!price && price !== 0) return '--';
  return `¥${price.toLocaleString('zh-CN', { minimumFractionDigits: 2 })}`;
}

// 格式化字段值
function formatFieldValue(value: any, column: any): string {
  if (!value && value !== 0) return '--';

  // 根据字段类型格式化
  switch (column.fieldType) {
    case 'NUM':
      return typeof value === 'number' ? value.toFixed(2) : value.toString();
    case 'ENUM':
    case 'LINK_FORM':
      // 如果有枚举选项，显示对应的标签
      if (column.enumValues) {
        try {
          const options = JSON.parse(column.enumValues);
          const option = options.find((opt: any) => opt.value === value);
          return option ? option.label : value;
        } catch {
          return value;
        }
      }
      return value;
    default:
      return value.toString();
  }
}

// 处理文件下载
function handleDownloadFile(file: any) {
  try {
    if (file.filePath || file.url) {
      const downloadUrl = file.filePath || file.url;
      const fileName = file.fileName || file.name || '附件';

      // 创建一个临时的下载链接
      const link = document.createElement('a');
      link.href = downloadUrl;
      link.download = fileName;
      link.target = '_blank';

      // 触发下载
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      ElMessage.success(`正在下载：${fileName}`);
    } else {
      ElMessage.warning('文件地址不存在，无法下载');
    }
  } catch (error) {
    ElMessage.error('下载失败');
  }
}

// 搜索处理
function searchHandler() {
  getAwardDetailData();
}

// 重置搜索
function resetSearch() {
  queryForm.value = initQueryForm();
}

// 导出处理
const handleExport = async () => {
  if (!awardDetailList.value || awardDetailList.value.length === 0) {
    ElMessage.warning('没有可导出的数据');
    return;
  }
  try {
    const blob = await exportToExcel(awardDetailList.value, dynamicFields.value);
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `${supplierInfo.value.supplierName}_中标明细_${moment().format('YYYY-MM-DD HH:mm:ss')}.xlsx`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    window.URL.revokeObjectURL(url);
    ElMessage.success('导出成功');
  } catch (error) {
    ElMessage.error('导出失败');
  }
};

// 获取中标明细数据
async function getAwardDetailData() {
  if (!props.supplierData) return;

  try {
    tableLoading.value = true;
    const { data } = await getAwardedItems({
      noticeId: noticeId.value,
      projectId: projectId.value,
      tenantSupplierId: props.supplierData.tenantSupplierId,
      sectionId: props.supplierData.sectionId,
      ...queryForm.value,
    });

    setDynamicColumn(data.srmTenderBidderQuoteItems);
    handleTableData(data.srmTenderBidderQuoteItems);

    const amountList = awardStore.tenderBidDetail?.projectItemList?.filter(
      (item: any) => item.sectionId === supplierInfo.value.sectionId && item.tenantSupplierId === supplierInfo.value.tenantSupplierId
    );

    amountList.forEach((item: any) => {
      const target = tableData.value.find((tableItem: any) => tableItem.materialCode === item.materialCode);
      target && (target.bidAmount = item.bidAmount);
    });

    awardDetailList.value = tableData.value;
    attachmentInfo.value = {
      quoteAttachment: data.srmProjectAttachments || [],
      otherAttachment: data.otherSrmProjectAttachments || [],
    };
  } catch (error) {
    console.error('获取中标明细失败:', error);
    awardDetailList.value = [];
    ElMessage.error('获取中标明细失败');
  } finally {
    tableLoading.value = false;
  }
}
</script>

<style scoped lang="scss">
@import '../../../styles/collapse-panel.scss';

.award-detail-container {
  padding: 0;
}

.form-section {
  border-radius: 6px;

  .label-header {
    font-size: 16px;
    font-weight: 600;
    color: #1d2129;
  }
}

.supplier-info {
  :deep(.el-descriptions__label) {
    font-weight: 500;
    color: #606266;
  }

  .amount-text {
    color: #e6a23c;
    font-weight: 600;
    font-size: 14px;
  }
}

.form-content {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 16px;
  flex-wrap: wrap;

  .form-item-wrapper {
    display: flex;
    align-items: center;
    gap: 8px;

    .form-label {
      font-size: 14px;
      color: #606266;
      white-space: nowrap;
      min-width: 80px;
    }

    .search-input {
      width: 200px;
    }
  }
}

.award-detail-table {
  margin-bottom: 20px;

  :deep(.el-table__header) {
    th {
      background: #f5f7fa;
      padding: 12px 0;
      border-bottom: 1px solid #ebeef5;

      .cell {
        color: #505762;
        font-size: 14px;
        font-weight: 500;
      }
    }
  }

  :deep(.el-table__body) {
    tr {
      td {
        border-bottom: 1px solid #ebeef5;
        padding: 12px 0;
      }
    }
  }
}

.attachment-section {
  .attachment-title {
    font-size: 14px;
    font-weight: 500;
    color: #303133;
    margin-bottom: 12px;
    padding-bottom: 8px;
    border-bottom: 1px solid #ebeef5;
  }

  .file-list {
    display: flex;
    flex-direction: column;
    gap: 12px;
  }

  .file-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px;
    border-radius: 6px;
    transition: all 0.2s ease;

    .file-info {
      display: flex;
      align-items: center;
      gap: 8px;
      flex: 1;

      .file-icon {
        color: #409eff;
        font-size: 16px;
      }

      .file-name {
        color: #303133;
        font-size: 14px;
        font-weight: 500;
        flex: 1;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      .file-size {
        color: #909399;
        font-size: 12px;
        margin-left: auto;
        min-width: 60px;
        text-align: right;
      }
    }
  }

  .no-files {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 20px;
    color: #909399;
    font-size: 14px;
    border: 1px dashed #e4e7ed;
    border-radius: 6px;
    background-color: #fafbfc;
  }
}
</style>
