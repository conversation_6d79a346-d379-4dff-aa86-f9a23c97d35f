<template>
  <div class="winner-publicity-container">
    <div class="bg-[#fff] p-5 mb-3">
      <!-- 页面标题 -->
      <div class="page-header">
        <div class="company-info">
          <el-icon class="company-icon"><office-building /></el-icon>
          <el-tag
            v-if="isEditMode && publicityForm.isPublicity"
            type="info"
            size="small"
            class="status-tag"
          >
            {{ getPublicityStatusText() }}
          </el-tag>
        </div>
      </div>

      <!-- 标签栏 -->
      <StageTabs
        v-model="activeTabIndex"
        @update:sectionId="handleSectionChange"
      />

      <!-- 中标信息表格 -->
      <el-table
        :data="winnerTableData"
        :loading="tableLoading"
        style="width: 100%"
        class="editable-table mt-4"
        v-loading="tableLoading"
      >
        <el-table-column
          label="序号"
          type="index"
          width="60"
          align="center"
        />
        <el-table-column
          label="供应商名称"
          prop="supplierName"
          min-width="200"
          show-overflow-tooltip
        />
        <el-table-column
          label="供应商编号"
          prop="supplierCode"
          width="140"
          align="center"
          show-overflow-tooltip
        />
        <el-table-column
          label="联系人"
          prop="contactName"
          width="120"
          align="center"
        />
        <el-table-column
          label="联系方式"
          prop="contactPhone"
          width="140"
          align="center"
        />
        <el-table-column
          label="中标总金额"
          prop="totalAwardedAmount"
          width="130"
          align="center"
        >
          <template #default="{ row }">
            <span>{{ formatPrice(row.totalAwardedAmount) }}</span>
          </template>
        </el-table-column>
        <el-table-column
          label="中标物料数量"
          prop="awardedMaterialCount"
          width="120"
          align="center"
        />
        <el-table-column
          label="中标总数量"
          prop="totalAwardedQuantity"
          width="120"
          align="center"
        />
        <el-table-column
          label="供应商类型"
          prop="supplierType"
          width="120"
          align="center"
          show-overflow-tooltip
        >
          <template #default="{ row }">
            {{ SUPPLY_TYPE_OBJ[row.supplierType] || '-' }}
          </template>
        </el-table-column>
        <el-table-column
          label="企业性质"
          prop="enterpriseNature"
          width="120"
          align="center"
          show-overflow-tooltip
        >
          <template #default="{ row }">
            {{ getBusinessNature(row.enterpriseNature) || '-' }}
          </template>
        </el-table-column>
        <el-table-column
          label="法人代表"
          prop="legalPerson"
          width="120"
          align="center"
        />
        <el-table-column
          label="操作"
          width="140"
          align="center"
          fixed="right"
        >
          <template #default="{ row }">
            <el-button
              type="text"
              @click="handleViewDetail(row)"
            >
              查看中标明细
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 公示要求 -->
    <div class="publicity-requirements-section">
      <div class="section-header">
        <div class="header-title">公示要求</div>
      </div>

      <el-form
        ref="publicityFormRef"
        :model="publicityForm"
        :rules="formRules"
        label-width="120px"
        class="publicity-form"
      >
        <el-form-item
          label="是否发布公示"
          prop="isPublicity"
        >
          <el-radio-group
            v-model="publicityForm.isPublicity"
            @change="handlePublicityChange"
          >
            <el-radio :label="true">系统发布公示</el-radio>
            <el-radio :label="false">不发布公示</el-radio>
          </el-radio-group>
        </el-form-item>

        <!-- 只有选择"是"时才显示以下字段 -->
        <template v-if="publicityForm.isPublicity">
          <el-row>
            <el-col :span="8">
              <el-form-item
                label="公示起止时间"
                prop="publicityTimeRange"
              >
                <el-date-picker
                  v-model="publicityForm.publicityTimeRange"
                  type="datetimerange"
                  placeholder=""
                  format="YYYY-MM-DD HH:mm:ss"
                  value-format="YYYY-MM-DD HH:mm:ss"
                  range-separator="至"
                  start-placeholder="开始时间"
                  end-placeholder="结束时间"
                  style="width: 100%"
                  @change="handleTimeRangeChange"
                />
              </el-form-item>
            </el-col>
          </el-row>

          <el-row>
            <el-col :span="8">
              <el-form-item
                label="公示标题"
                prop="publicityTitle"
              >
                <el-input
                  v-model="publicityForm.publicityTitle"
                  placeholder="请输入公示标题"
                  maxlength="50"
                  show-word-limit
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
          </el-row>
        </template>
      </el-form>
    </div>

    <!-- 公示详情 - 只有选择"是"时才显示 -->
    <div
      v-if="publicityForm.isPublicity"
      class="publicity-details-section"
    >
      <div class="section-header">
        <div class="header-title">公示详情</div>
      </div>

      <el-form
        ref="publicityFormRef"
        :model="publicityForm"
        :rules="formRules"
        label-width="120px"
        class="publicity-form"
      >
        <el-row>
          <el-col :span="8">
            <el-form-item
              label="引用模版"
              prop="publicityTemplateId"
            >
              <el-select
                v-model="publicityForm.publicityTemplateId"
                placeholder="请选择公示模版"
                style="width: 200px"
                @change="handleTemplateChange"
              >
                <el-option
                  v-for="template in templateOptions"
                  :key="template.id"
                  :label="template.templateName"
                  :value="template.id"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item
          v-if="isEditMode"
          label="公示内容"
          prop="publicityContent"
        >
          <el-link
            type="primary"
            @click="handleEditContent"
          >
            <el-icon class="mr-[6px]"><edit-pen /></el-icon>
            查看
          </el-link>
        </el-form-item>
      </el-form>
    </div>

    <!-- 公示附件 - 只有选择"是"时才显示 -->
    <div
      v-if="publicityForm.isPublicity"
      class="publicity-attachments-section"
    >
      <div class="section-header">
        <div class="header-title">公示附件</div>
      </div>

      <el-form
        label-width="120px"
        class="publicity-form"
      >
        <el-form-item
          v-if="!isEditMode"
          label="公示附件"
        >
          <!-- 非编辑模式：显示上传组件 -->
          <div class="upload-wrapper">
            <YunUpload
              v-model="publicityFileList"
              @change="handleUploadChange"
              :limit="5"
              :multiple="true"
            />
          </div>
        </el-form-item>

        <div v-if="isEditMode">
          <div class="flex gap-4 mt-3">
            <div class="file-label">定标公示附件：</div>
            <div
              class="flex flex-col gap-2"
              v-if="publicityFileList.length > 0"
            >
              <div
                class="flex gap-4 items-center"
                v-for="(item, index) in publicityFileList"
                :key="index"
              >
                <div
                  class="file-item-label"
                  @click="handleDownload(item)"
                >
                  {{ item.fileName || item.name }}
                </div>
              </div>
            </div>
            <div
              v-else
              class="no-files"
            >
              <span>暂无附件</span>
            </div>
          </div>
        </div>
      </el-form>
    </div>

    <!-- 公示平台 - 只有选择"是"时才显示 -->
    <div
      v-if="publicityForm.isPublicity"
      class="publicity-platform-section"
    >
      <div class="section-header">
        <div class="header-title">公示平台</div>
      </div>

      <el-form
        label-width="120px"
        class="publicity-form"
      >
        <el-form-item label="公示平台选择">
          <div class="platform-checkboxes">
            <el-checkbox-group v-model="publicityForm.platforms">
              <el-checkbox
                v-for="platform in platformOptions"
                :key="platform.value"
                :label="platform.value"
              >
                {{ platform.label }}
              </el-checkbox>
            </el-checkbox-group>
          </div>
        </el-form-item>
      </el-form>
    </div>
  </div>

  <div
    v-if="!isEditMode && !props.isChangeMode && isProjectLeader"
    class="form-actions-wrapper"
  >
    <div class="form-actions">
      <el-button
        type="primary"
        @click="handleSubmit"
      >
        {{ '生成中标公示' }}
      </el-button>
      <el-button @click="handleCancel">取消</el-button>
    </div>
  </div>

  <!-- 公示内容编辑抽屉 -->
  <AnnouncementEditor
    ref="announcementEditorRef"
    :readonly="isEditMode"
    @save="handleSaveContent"
  />

  <!-- 中标明细抽屉 -->
  <AwardDetailDrawer
    v-model:visible="awardDetailDrawerVisible"
    :supplier-data="selectedSupplierData"
  />
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, watch } from 'vue';
import { ElMessage, type FormInstance, type FormRules } from 'element-plus';
import { OfficeBuilding, Document } from '@element-plus/icons-vue';
import StageTabs from '@/views/procurementSourcing/biddingProcess/components/bidding/StageTabs.vue';
import AnnouncementEditor from '../AwardResult/components/AnnouncementEditor.vue';
import YunUpload from '@/components/YunUpload/index.vue';
import AwardDetailDrawer from './AwardDetailDrawer.vue';
import { getTemplateList, getTenderBidById } from '../../../api/award';
import downloadUrlFile from '@/utils/downloadUrl.js';
import type { TemplateOption, TenderBidPublicityDetail } from '../../../types/award';
import { getAwardedSuppliers, saveBidPublicity } from '../../../api/WinnerPublicity';
import type {
  AwardedSupplierInfo,
  InviteSupplierInfo,
  SaveBidPublicityParams,
  AttachmentInfo as ApiAttachmentInfo,
} from '../../../types/winnerPublicity';
import { getTemplateDetail } from '../../../api/announcement';
import { templateContentFormatter } from '../../announcement/ProcurementDocument/templateFormatter';
import { useBiddingStore } from '@/views/procurementSourcing/biddingProcess/stores';
import { getNoticeChangeDetailInfo } from '@/api/purchasing/noticeChange';
import moment from 'moment';
import { SUPPLY_TYPE_OBJ } from '@/views/lowcode/supply/const';
import { BUSINESS_NATURE_OPTIONS } from '@/api/supplier';


// 定义组件 Props
interface Props {
  isChangeMode?: boolean; // 是否为变更模式
  formData?: any; // 变更模式下的表单数据
  isViewMode?: boolean; // 是否为查看模式
}

const props = withDefaults(defineProps<Props>(), {
  isChangeMode: false,
  formData: null,
  isViewMode: false,
});

const emit = defineEmits<{
  (e: 'update:form-data', data: any): void;
  (e: 'change-submit', data: { content: string; submitData: any }): void;
}>();

const biddingStore = useBiddingStore();

const noticeId = computed(() => biddingStore?.noticeId);
const projectId = computed(() => biddingStore?.projectId);
const projectDetail = computed(() => biddingStore?.projectDetail);
const isProjectLeader = computed(() => biddingStore?.isProjectLeader);
// 定义附件信息接口
interface AttachmentInfo {
  id: string;
  fileName: string;
  filePath: string;
  fileSize: number;
  fileType: string;
}

// 定义公示表单数据结构
interface PublicityFormData {
  noticeId: number;
  projectId: number;
  isPublicity: boolean; // 变更中标公示--是否发布中标公示
  publicityStartTime: string; // 变更中标公示--公示开始时间
  publicityEndTime: string; // 变更中标公示--公示结束时间
  publicityTimeRange: [string, string] | null; // 公示时间范围（用于表单验证）
  publicityTitle: string; // 公示标题
  publicityTemplateId: string; // 引用模版ID
  publicityContent: string; // 公示内容
  attachmentInfos: AttachmentInfo[]; // 附件信息
  platforms: string[]; // 公示平台（非必需字段，根据业务需要）
}

// 表单引用
const publicityFormRef = ref<FormInstance>();

// 抽屉组件引用
const announcementEditorRef = ref();

const activeTabIndex = ref(0);

// 当前选中的标段ID
const currentSectionId = ref<number | null>(null);

// 中标信息表格数据（原始数据）
const allWinnerData = ref<AwardedSupplierInfo[]>([]);

// 过滤后的表格数据（用于显示）
const winnerTableData = ref<AwardedSupplierInfo[]>([]);

// 表格加载状态
const tableLoading = ref(false);

// 公示表单数据 - 固定写死 noticeId 和 projectId
const publicityForm = reactive<PublicityFormData>({
  noticeId: noticeId.value, // 固定写死的公示ID
  projectId: projectId.value, // 固定写死的采购立项ID
  isPublicity: true, // 默认选中"是"
  publicityStartTime: '',
  publicityEndTime: '',
  publicityTimeRange: null, // 时间范围
  publicityTitle: '',
  publicityTemplateId: '',
  publicityContent: '',
  attachmentInfos: [],
  platforms: [],
});

// 保存状态
const saving = ref(false);

// 页面状态：编辑模式还是新增模式
const isEditMode = ref(false);

// 定标详情数据
const tenderBidDetail = ref<TenderBidPublicityDetail | null>(null);


const getBusinessNature = (value: string) => {
  return BUSINESS_NATURE_OPTIONS.find(item => item.value === value)?.label || '-';
}

// 公示状态枚举
const PUBLICITY_STATUS = {
  PENDING: 'PENDING',    // 待公示
  PUBLISHED: 'PUBLISHED', // 公示中
  EXPIRED: 'EXPIRED'     // 公示结束
} as const;

// 获取公示状态文本
function getPublicityStatusText(): string {
  if (!publicityForm.publicityTimeRange || !Array.isArray(publicityForm.publicityTimeRange) || publicityForm.publicityTimeRange.length !== 2) {
    return '待公示';
  }

  const [startTime, endTime] = publicityForm.publicityTimeRange;
  const now = new Date();
  const startDate = new Date(startTime);
  const endDate = new Date(endTime);

  if (now < startDate) {
    return '待公示';
  } else if (now >= startDate && now <= endDate) {
    return '公示中';
  } else {
    return '公示结束';
  }
}



// 表单验证规则
const formRules: FormRules = {
  isPublicity: [{ required: true, message: '请选择是否发布公示', trigger: 'change' }],
  publicityTimeRange: [
    {
      required: true,
      message: '请选择公示起止时间',
      trigger: 'change',
    },
    {
      validator: (rule, value, callback) => {
        if (!publicityForm.isPublicity) {
          callback();
          return;
        }

        // 检查 publicityTimeRange 的值
        const timeRangeValue = publicityForm.publicityTimeRange;
        if (!timeRangeValue || !Array.isArray(timeRangeValue) || timeRangeValue.length !== 2) {
          callback(new Error('请选择公示起止时间'));
          return;
        }

        const [startTime, endTime] = timeRangeValue;
        if (!startTime || !endTime) {
          callback(new Error('请选择公示起止时间'));
          return;
        }

        if (new Date(endTime) <= new Date(startTime)) {
          callback(new Error('公示结束时间必须大于开始时间'));
          return;
        }

        callback();
      },
    },
  ],
  publicityTitle: [
    {
      required: true,
      message: '请输入公示标题',
      trigger: 'blur',
    },
    {
      validator: (rule, value, callback) => {
        if (!publicityForm.isPublicity) {
          callback();
          return;
        }

        // 检查实际的标题值
        const titleValue = publicityForm.publicityTitle;
        if (!titleValue || titleValue.trim() === '') {
          callback(new Error('请输入公示标题'));
          return;
        }

        if (titleValue.length > 50) {
          callback(new Error('公示标题不能超过50个字符'));
          return;
        }

        callback();
      },
    },
  ],
  publicityTemplateId: [
    {
      required: true,
      message: '请选择引用模版',
      trigger: 'change',
    },
    {
      validator: (rule, value, callback) => {
        if (!publicityForm.isPublicity) {
          callback();
          return;
        }

        // 检查实际的模板值
        const templateValue = publicityForm.publicityTemplateId;
        if (!templateValue || templateValue === '') {
          callback(new Error('请选择引用模版'));
          return;
        }

        callback();
      },
    },
  ],
  publicityContent: [
    {
      validator: (rule, value, callback) => {
        if (!publicityForm.isPublicity) {
          callback();
          return;
        }

        // 检查实际的公示内容值
        const contentValue = publicityForm.publicityContent;
        if (!contentValue || contentValue.trim() === '') {
          callback(new Error('请编辑公示内容'));
          return;
        }

        callback();
      },
    },
  ],
};

// 模板选项
const templateOptions = ref<TemplateOption[]>([]);

// 平台选项
const platformOptions = ref([
  { label: '中国采购与招标网', value: 'procurement' },
  { label: '中国招标投标公共服务平台', value: 'quality-procurement' },
]);

// 公示附件文件列表
const publicityFileList = ref<any[]>([]);

// 处理是否发布公示切换
function handlePublicityChange(value: boolean) {
  // 清除所有表单验证错误
  if (publicityFormRef.value) {
    publicityFormRef.value.clearValidate();
  }

  // 如果切换为不发布公示，清空相关字段
  if (!value) {
    publicityForm.publicityTimeRange = null;
    publicityForm.publicityStartTime = '';
    publicityForm.publicityEndTime = '';
    publicityForm.publicityTitle = '';
    publicityForm.publicityContent = '';
    publicityForm.attachmentInfos = [];
    publicityForm.platforms = [];
  }
}

// 处理时间范围变化
function handleTimeRangeChange(value: [string, string] | null) {
  publicityForm.publicityTimeRange = value;
  if (value && Array.isArray(value)) {
    publicityForm.publicityStartTime = value[0];
    publicityForm.publicityEndTime = value[1];
  } else {
    publicityForm.publicityStartTime = '';
    publicityForm.publicityEndTime = '';
  }
}

// 处理标段变化
function handleSectionChange(sectionId: number) {
  currentSectionId.value = sectionId;
  filterWinnerDataBySectionId();
}

// 根据标段ID过滤中标供应商数据
function filterWinnerDataBySectionId() {
  if (currentSectionId.value === null) {
    winnerTableData.value = allWinnerData.value;
  } else {
    winnerTableData.value = allWinnerData.value.filter((item) => item.sectionId === currentSectionId.value);
  }
}

// 格式化价格
function formatPrice(price: number): string {
  return `¥${price.toLocaleString('zh-CN', { minimumFractionDigits: 2 })}`;
}

// 处理文件上传变化（参考AwardResult的实现）
function handleUploadChange(fileList: any[]) {
  // 更新附件信息数组，使用fileName和filePath作为属性名
  publicityForm.attachmentInfos = fileList.map((file) => ({
    id: file.id || Date.now().toString(),
    fileName: file.name,
    filePath: file.url || '',
    fileSize: file.size || 0,
    fileType: file.type || '',
  }));
}

// 中标明细抽屉相关
const awardDetailDrawerVisible = ref(false);
const selectedSupplierData = ref<AwardedSupplierInfo | undefined>(undefined);

// 处理查看中标明细
function handleViewDetail(row: AwardedSupplierInfo) {
  selectedSupplierData.value = row;
  awardDetailDrawerVisible.value = true;
}

// 验证必要数据
function validateRequiredData(): boolean {
  // 1. 检查是否有中标供应商数据
  if (!allWinnerData.value || allWinnerData.value.length === 0) {
    ElMessage.error('请先获取中标供应商数据');
    return false;
  }

  // 2. 检查是否有具有有效金额的中标项目
  const validAwardedItems = allWinnerData.value;
  // .filter(item =>
  //   item.totalAwardedAmount && item.totalAwardedAmount > 0
  // );

  if (validAwardedItems.length === 0) {
    ElMessage.error('无有效的中标数据，请确认中标信息');
    return false;
  }

  // 3. 检查是否选择了模板（当选择发布公示时）
  if (publicityForm.isPublicity && !publicityForm.publicityTemplateId) {
    ElMessage.error('请选择引用模版');
    return false;
  }

  return true;
}

// 验证模板选择并获取模板内容
async function verifyTemplateSelection(): Promise<boolean> {
  if (!publicityForm.isPublicity) {
    return true; // 如果不发布公示，跳过模板验证
  }

  if (!publicityForm.publicityTemplateId) {
    ElMessage.error('请先选择引用模版');
    return false;
  }

  try {
    const { data } = await getTemplateDetail(publicityForm.publicityTemplateId);
    if (!data || !data.content) {
      ElMessage.error('模板内容为空，请选择其他模板');
      return false;
    }
    return true;
  } catch (error) {
    return false;
  }
}

// 转换数据为模板变量格式
function transformAwardDataForTemplate() {
  // 1. 按供应商分组统计
  const supplierGroupData = allWinnerData.value.reduce((acc, item) => {
    const key = `${item.supplierName}_${item.sectionId}`;
    if (!acc[key]) {
      acc[key] = {
        supplierName: item.supplierName,
        supplierCode: item.supplierCode,
        contactName: item.contactName,
        contactPhone: item.contactPhone,
        materials: [],
        totalAmount: 0,
      };
    }
    acc[key].materials.push(item);
    acc[key].totalAmount += item.totalAwardedAmount || 0;
    return acc;
  }, {} as Record<string, any>);

  // 2. 计算汇总数据
  const totalAwardAmount = allWinnerData.value.reduce((sum, item) => sum + (item.totalAwardedAmount || 0), 0);
  const totalSupplierCount = Object.keys(supplierGroupData).length;
  const totalMaterialCount = allWinnerData.value.length;

  // 3. 构建模板数据对象
  return {
    // 项目基本信息
    publicityTitle: publicityForm.publicityTitle,

    projectName: projectDetail.value?.projectName || '',
    projectCode: projectDetail.value?.projectCode || '',
    procurementMethod: projectDetail.value?.procurementMethod || '',
    budgetAmount: projectDetail.value?.budgetAmount || 0,

    // 公示相关信息
    publicityStartTime: publicityForm.publicityStartTime,
    publicityEndTime: publicityForm.publicityEndTime,

    // 统计数据
    totalAwardAmount: totalAwardAmount,
    totalSupplierCount: totalSupplierCount,
    totalMaterialCount: totalMaterialCount,

    // 格式化金额
    formattedTotalAmount: `¥${totalAwardAmount.toLocaleString('zh-CN', { minimumFractionDigits: 2 })}`,
    // 供应商列表
    supplierList: Object.values(supplierGroupData),

    // 时间信息
    generateTime: moment().format('YYYY-MM-DD HH:mm:ss'),
    currentDate: moment().format('YYYY年MM月DD日'),
  };
}

// 处理模板变化事件
async function handleTemplateChange() {
  if (!publicityForm.publicityTemplateId) return;

  try {
    // 检查是否已有内容且不是默认内容
    if (!publicityForm.publicityContent || publicityForm.publicityContent.includes('<h2>XDMY-LYMG')) {
      const { data } = await getTemplateDetail(publicityForm.publicityTemplateId);
      publicityForm.publicityContent = data.content;
    }
  } catch (error) {}
}

// 处理编辑公示内容
async function handleEditContent() {
  try {
    // 检查是否有已保存的公示内容
    if (!publicityForm.publicityContent) {
      ElMessage.error('暂无公示内容');
      return;
    }

    // 直接打开富文本编辑器，显示已保存的内容
    announcementEditorRef.value?.show(publicityForm.publicityContent, '编辑中标公示内容');
  } catch (error) {}
}

// 处理保存公示内容
async function handleSaveContent(content: string) {
  if (isEditMode.value) {
    return;
  }

  // 变更模式下，通过 emit 通知父组件处理
  if (props.isChangeMode) {
    // 1. 先校验数据
    if (!validateRequiredData()) {
      return;
    }

    // 2. 验证表单
    if (publicityFormRef.value) {
      await publicityFormRef.value.validate();
    }

    // 3. 更新公示内容
    publicityForm.publicityContent = content;

    // 4. 组装提交数据（使用相同的转换器）
    const submitData = assembleSubmitData();

    // 5. 通知父组件处理变更提交
    emit('change-submit', {
      content,
      submitData,
    });
    return;
  }

  // 原有的公示模式逻辑
  try {
    saving.value = true;

    // 1. 验证必要数据
    if (!validateRequiredData()) {
      return;
    }

    // 2. 验证表单
    if (publicityFormRef.value) {
      await publicityFormRef.value.validate();
    }

    // 3. 更新内容
    publicityForm.publicityContent = content;

    // 4. 准备提交数据
    const submitData = assembleSubmitData();

    // 5. 调用接口保存
    const { code, msg } = await saveBidPublicity(submitData);

    if (code === 0) {
      ElMessage.success(isEditMode.value ? '中标公示更新成功' : '中标公示保存成功');

      // 提交成功后重新加载数据
      if (!isEditMode.value) {
        // 如果是新增模式，提交成功后切换到编辑模式
        await loadTenderBidDetail();
      }
    } else {
      ElMessage.error(msg || (isEditMode.value ? '更新失败' : '保存失败'));
    }
  } catch (error) {
  } finally {
    saving.value = false;
  }
}

// 从列表数据中提取并去重邀请供应商信息
function getInviteSupplierList(): InviteSupplierInfo[] {
  // 创建一个Map来去重，key为 sectionId + tenantSupplierId 的组合
  const supplierMap = new Map<string, InviteSupplierInfo>();

  allWinnerData.value.forEach((supplier) => {
    const key = `${supplier.sectionId}_${supplier.tenantSupplierId}`;

    // 如果还没有这个供应商，或者当前数据更完整，则更新
    if (!supplierMap.has(key)) {
      supplierMap.set(key, {
        sectionId: supplier.sectionId,
        tenantSupplierId: supplier.tenantSupplierId,
        supplierName: supplier.supplierName,
        contactPerson: supplier.contactName,
        contactPhone: supplier.contactPhone,
        dealPrice: supplier.totalAwardedAmount,
      });
    }
  });

  return Array.from(supplierMap.values());
}

// 组装提交数据
function assembleSubmitData(): SaveBidPublicityParams {
  // 获取去重后的邀请供应商列表
  const inviteSupplierList = getInviteSupplierList();

  // 转换附件信息格式
  const attachmentInfos: AttachmentInfo[] = publicityForm.attachmentInfos.map((file) => ({
    id: file.id,
    fileName: file.fileName,
    filePath: file.filePath,
    fileSize: file.fileSize,
    fileType: file.fileType,
  }));

  return {
    noticeId: publicityForm.noticeId,
    projectId: publicityForm.projectId,
    inviteSupplierList,
    isPublicity: publicityForm.isPublicity,
    publicityStartTime: publicityForm.publicityStartTime,
    publicityEndTime: publicityForm.publicityEndTime,
    publicityTitle: publicityForm.publicityTitle,
    publicityTemplateId: parseInt(publicityForm.publicityTemplateId),
    publicityContent: publicityForm.publicityContent,
    attachmentInfos,
  };
}

// 处理提交
async function handleSubmit() {
  try {
    // 1. 验证必要数据
    if (!validateRequiredData()) {
      return;
    }

    // 2. 验证表单
    if (publicityFormRef.value) {
      await publicityFormRef.value.validate();
    }

    // 3. 检查是否选择不发布公示
    if (!publicityForm.isPublicity) {
      // 不发布公示：直接保存数据，不打开模板编辑器
      try {
        saving.value = true;

        // 清空模板相关字段
        publicityForm.publicityTemplateId = '';
        publicityForm.publicityContent = '';
        publicityForm.publicityTitle = '';
        publicityForm.publicityStartTime = '';
        publicityForm.publicityEndTime = '';
        publicityForm.publicityTimeRange = null;

        // 准备提交数据
        const submitData = assembleSubmitData();

        // 变更模式下，通过 emit 通知父组件处理
        if (props.isChangeMode) {
          // 通知父组件处理变更提交
          emit('change-submit', {
            content: '',
            submitData,
          });
          return;
        }

        // 非变更模式：直接调用接口保存
        const { code, msg } = await saveBidPublicity(submitData);

        if (code === 0) {
          ElMessage.success('中标公示保存成功');
          // 提交成功后重新加载数据
          await loadTenderBidDetail();
        } else {
          ElMessage.error(msg || '保存失败');
        }
      } catch (error) {
        console.error('保存失败:', error);
        ElMessage.error('保存失败');
      } finally {
        saving.value = false;
      }
      return;
    }

    // 4. 验证模板选择（只有发布公示时才验证）
    if (!(await verifyTemplateSelection())) {
      return;
    }

    // 5. 转换数据为模板变量格式
    const templateData = transformAwardDataForTemplate();

    // 6. 获取并格式化模板内容
    if (!publicityForm.publicityContent || publicityForm.publicityContent.includes('<h2>XDMY-LYMG')) {
      const { data } = await getTemplateDetail(publicityForm.publicityTemplateId);
      publicityForm.publicityContent = data.content;
    }

    const formattedContent = templateContentFormatter(publicityForm.publicityContent, templateData, {
      removeEmptyLoops: true,
      removeEmptyTags: true,
      debug: true,
    });

    // 7. 打开富文本编辑器进行最终编辑和保存
    announcementEditorRef.value?.show(formattedContent, '编辑中标公示');
  } catch (error) {
    console.error('数据转换失败:', error);
  }
}

// 处理取消
function handleCancel() {}

// 获取中标供应商列表
async function getWinnerData() {
  try {
    tableLoading.value = true;
    const { data } = await getAwardedSuppliers(publicityForm.noticeId, publicityForm.projectId);
    if (data && Array.isArray(data)) {
      // 存储原始数据
      allWinnerData.value = data;
      // 根据当前选中的标段进行过滤
      filterWinnerDataBySectionId();
    } else {
      allWinnerData.value = [];
      winnerTableData.value = [];
    }
  } catch (error) {
    allWinnerData.value = [];
    winnerTableData.value = [];
  } finally {
    tableLoading.value = false;
  }
}

// 获取模版列表
async function getTemplateOptions() {
  try {
    const { data } = await getTemplateList({
      type: 'AWARD_PUBLICITY', // 中标公示模版类型
    });

    if (data?.records && data.records.length > 0) {
      templateOptions.value = data.records;
      // 如果选择了发布公示且还没有选择模版，自动选择第一个
      if (publicityForm.isPublicity && !publicityForm.publicityTemplateId) {
        publicityForm.publicityTemplateId = data.records[0]?.id || '';
      }
    }
  } catch (error) {}
}

// 查询定标详情
async function loadTenderBidDetail() {
  try {
    let response;
    if (props.isViewMode) {
      const params = {
        noticeId: noticeId.value,
        projectId: projectId.value,
        changeTypeEnum: 'TO_APPROVE',
      };
      response = await getNoticeChangeDetailInfo(params);
    } else {
      response = await getTenderBidById(Number(publicityForm.noticeId), Number(publicityForm.projectId));
    }
    if (response.code === 0 && response.data) {
      tenderBidDetail.value = response.data;

      if (props.isViewMode) {
        isEditMode.value = false;
      } else if (response.data.publicityContent) {
        isEditMode.value = true;
      } else {
        isEditMode.value = false;
      }
      // 回填中标公示数据
      fillPublicityFormData(response.data);
    } else {
      // 没有数据，显示新增模式
      isEditMode.value = false;
    }
  } catch (error) {
    // 查询失败也显示新增模式
    isEditMode.value = false;
  }
}

// 回填中标公示表单数据
function fillPublicityFormData(data: TenderBidPublicityDetail) {
  // 回填公示基本信息
  if (data.publicityStatus && data.publicityStatus !== 'UNPUBLISHED') {
    publicityForm.isPublicity = true;
  }

  // 回填公示模板
  if (data.publicityTemplateId) {
    publicityForm.publicityTemplateId = data.publicityTemplateId.toString();
  }

  // 回填公示内容
  if (data.publicityContent) {
    publicityForm.publicityContent = data.publicityContent;
  }

  // 回填公示标题
  if (data.publicityTitle) {
    publicityForm.publicityTitle = data.publicityTitle;
  }

  // 回填公示时间
  if (data.publicityStartTime && data.publicityEndTime) {
    publicityForm.publicityStartTime = data.publicityStartTime;
    publicityForm.publicityEndTime = data.publicityEndTime;
    publicityForm.publicityTimeRange = [data.publicityStartTime, data.publicityEndTime];
  }

  // 回填附件信息
  if (data.publicityAttachments && data.publicityAttachments.length > 0) {
    publicityForm.attachmentInfos = data.publicityAttachments.map((file: any) => ({
      id: file.id || Date.now().toString(),
      fileName: file.fileName || file.name,
      filePath: file.filePath || file.url,
      fileSize: file.fileSize || file.size || 0,
      fileType: file.fileType || file.type || '',
    }));
    publicityFileList.value = publicityForm.attachmentInfos;
  }
}

// 变更模式专用的验证和提交方法
const validatePublicityData = async (): Promise<boolean> => {
  try {
    handleSubmit();
    return true;
  } catch (error) {
    console.error('验证失败:', error);
    return false;
  }
};

// 获取当前组件的数据用于变更提交
const getPublicityData = () => {
  return {
    publicityForm: { ...publicityForm },
    allWinnerData: [...allWinnerData.value],
    winnerTableData: [...winnerTableData.value],
  };
};

const handleDownload = (item: any) => {
  downloadUrlFile(item.filePath, item.fileName);
};

const init = async () => {
  // 获取模版列表
  getTemplateOptions();

  // await biddingStore.initProjectDetail();

  loadTenderBidDetail();

  getWinnerData();
};

// 暴露方法给父组件调用
defineExpose({
  validatePublicityData,
  getPublicityData,
  handleSaveContent,
  handleSubmit,
});

// 组件挂载时初始化
onMounted(() => {
  init();
});
</script>

<style lang="scss" scoped>
@import '../../../styles/collapse-panel.scss';
.winner-publicity-container {
  //padding: 20px;
  //background: #f5f7fa;
  //min-height: 100vh;
}

.page-header {
  margin-bottom: 16px;

  .company-info {
    display: flex;
    align-items: center;
    gap: 8px;

    .company-icon {
      color: #0069ff;
      font-size: 18px;
    }

    .company-name {
      font-size: 18px;
      font-weight: 600;
      color: #1d2129;
    }
  }
}

.winner-table-section,
.publicity-requirements-section,
.publicity-details-section,
.publicity-attachments-section,
.publicity-platform-section {
  margin-bottom: 12px;
  background: #fff;
  border-radius: 6px;
  padding: 20px;

  .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
  }
}

.publicity-form {
  :deep(.el-form-item) {
    margin-bottom: 20px;
  }

  :deep(.el-form-item__label) {
    //color: #86909c;
    //font-weight: 400;
  }
}

.winner-table {
  :deep(.el-table__header) {
    th {
      background: #f5f7fa;
      padding: 12px 0;
      border-bottom: 1px solid #ebeef5;

      .cell {
        color: #505762;
        font-size: 14px;
        font-weight: 500;
      }
    }
  }

  :deep(.el-table__body) {
    tr {
      td {
        border-bottom: 1px solid #ebeef5;
        padding: 12px 0;
      }
    }
  }
}

.platform-checkboxes {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.upload-wrapper {
  width: 100%;
}

:deep(.el-range-input) {
  background: #fff;
}

.form-actions-wrapper {
  width: 100%;
  box-sizing: border-box;
  background-color: #fff;
  z-index: 1000;
  position: sticky;
  bottom: -20px;
  left: 0;
  right: 0;
  padding: 16px 24px;
  display: flex;
  gap: 12px;
  border-top: 1px solid var(--Color-Border-border-color-light, #e4e7ed);

  .form-actions {
    display: flex;
    justify-content: flex-start;
    gap: 12px;
    width: 100%;
  }
}

.file-label {
  color: var(--Color-Text-text-color-regular, #4e5969);
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 22px;
}

.file-item-label {
  color: var(--Color-Primary-color-primary, #0069ff);
  font-size: 14px;
  font-weight: 400;
  cursor: pointer;
}

.header-title {
  color: var(--Color-Text-text-color-primary, #1d2129);
  font-family: 'PingFang SC';
  font-size: 16px;
  font-style: normal;
  font-weight: 600;
  line-height: 24px;
  padding-left: 10px;
  position: relative;
  &::before {
    content: '';
    display: inline-block;
    width: 2px;
    height: 14px;
    background: var(--Color-Primary-color-primary, #0069ff);
    margin-right: 8px;
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
  }
}
</style>
