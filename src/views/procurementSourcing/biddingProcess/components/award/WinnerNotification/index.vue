<template>
  <div class="winner-notification-container">
    <div class="bg-[#fff] p-5 mb-3">
      <!-- 标签栏 -->
      <StageTabs
        v-model="activeTabIndex"
        @update:sectionId="handleSectionChange"
      />

      <div class="mb-4">
        <el-button
          v-if="isProjectLeader"
          @click="handleBatchSendTemp"
          :loading="batchSendLoading"
          type="primary"
        >
          {{ batchSendLoading ? '发送中...' : '一键发送' }}
        </el-button>
      </div>

      <!-- 中标信息表格 -->
      <el-table
        :data="winnerTableData"
        :loading="tableLoading"
        style="width: 100%"
        class="editable-table"
        v-loading="tableLoading"
      >
        <el-table-column
          label="序号"
          type="index"
          width="60"
          align="center"
        />
        <el-table-column
          label="中标公司"
          prop="supplierName"
          min-width="200"
          show-overflow-tooltip
        />
        <el-table-column
          label="供应商编号"
          prop="supplierCode"
          width="140"
          align="center"
          show-overflow-tooltip
        />
        <el-table-column
          label="联系人"
          prop="contactName"
          width="120"
          align="center"
        />
        <el-table-column
          label="联系方式"
          prop="contactPhone"
          width="140"
          align="center"
        />
        <el-table-column
          label="中标总金额"
          prop="totalAwardedAmount"
          width="130"
          align="center"
        >
          <template #default="{ row }">
            <span>{{ formatPrice(row.totalAwardedAmount) }}</span>
          </template>
        </el-table-column>
        <el-table-column
          label="通知状态"
          prop="noticeStatus"
          width="120"
          align="center"
        >
          <template #default="{ row }">
            <el-tag
              :type="getNoticeStatusType(row.noticeStatus)"
              size="small"
            >
              {{ getNoticeStatusText(row.noticeStatus) }}
            </el-tag>
          </template>
        </el-table-column>
        <!-- <el-table-column
          label="签章状态"
          prop="signatureStatus"
          width="120"
          align="center"
        >
          <template #default="{ row }">
            <el-tag
              :type="getSignatureStatusType(row.signatureStatus)"
              size="small"
            >
              {{ getSignatureStatusText(row.signatureStatus) }}
            </el-tag>
          </template>
        </el-table-column> -->
        <el-table-column
          label="操作"
          width="300"
          align="center"
          fixed="right"
        >
          <template #default="{ row }">
            <el-button
              type="text"
              v-if="isProjectLeader && row.noticeStatus !== 'SENT'"
              @click="handleEditContent(row)"
            >
              编辑中标通知书
            </el-button>
            <!-- <el-button
              type="text"
              @click="handleElectronicSignature(row)"
            >
              电子签章
            </el-button> -->
            <el-button
              type="text"
              v-if="row.noticeStatus === 'UNSENT' && isProjectLeader"
              @click="handleSendNotification(row)"
              :loading="row.sending"
            >
              {{ row.sending ? '发送中...' : '发送中标通知' }}
            </el-button>
            <el-button
              type="text"
              v-else-if="isProjectLeader"
              @click="handleSendNotification(row)"
              :loading="row.sending"
            >
              {{ row.sending ? '发送中...' : '重新发送通知' }}
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
  </div>

  <!-- 中标通知书编辑抽屉 -->
  <AnnouncementEditor
    ref="announcementEditorRef"
    @save="handleSaveContent"
  />
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue';
import { ElMessage } from 'yun-design';
import StageTabs from '@/views/procurementSourcing/biddingProcess/components/bidding/StageTabs.vue';
import AnnouncementEditor from '../AwardResult/components/AnnouncementEditor.vue';
import { getAwardedSuppliers, sendWinnerNotification, editNotificationContent, electronicSignature } from '../../../api/WinnerNotification';
import type { AwardedSupplierInfo, SendNotificationParams, EditNotificationContentParams } from '../../../types/winnerNotification';
import { NoticeStatus, SignatureStatus } from '../../../types/winnerNotification';
import { useBiddingStore } from '@/views/procurementSourcing/biddingProcess/stores';

// 抽屉组件引用
const announcementEditorRef = ref();

const activeTabIndex = ref(0);

// 当前选中的标段ID
const currentSectionId = ref<number | null>(null);

// 中标信息表格数据（原始数据）
const allWinnerData = ref<AwardedSupplierInfo[]>([]);

// 过滤后的表格数据（用于显示）
const winnerTableData = ref<AwardedSupplierInfo[]>([]);

// 表格加载状态
const tableLoading = ref(false);

// 批量发送加载状态
const batchSendLoading = ref(false);

// Store
const biddingStore = useBiddingStore();
const noticeId = computed(() => biddingStore?.noticeId);
const projectId = computed(() => biddingStore?.projectId);
const isProjectLeader = computed(() => biddingStore?.isProjectLeader);

// 中标通知表单数据
const notificationForm = reactive({
  noticeId: noticeId.value, // 从store获取或使用默认值
  projectId: projectId.value, // 从store获取或使用默认值
});

const currentEditingSupplier = ref<any>(null);

// 处理标段变化
function handleSectionChange(sectionId: number) {
  currentSectionId.value = sectionId;
  filterWinnerDataBySectionId();
}

// 根据标段ID过滤中标供应商数据
function filterWinnerDataBySectionId() {
  if (currentSectionId.value === null) {
    winnerTableData.value = allWinnerData.value;
  } else {
    winnerTableData.value = allWinnerData.value.filter((item) => item.sectionId === currentSectionId.value);
  }
}

// 格式化价格
function formatPrice(price: number): string {
  return `¥${price.toLocaleString('zh-CN', { minimumFractionDigits: 2 })}`;
}

// 获取通知状态文本
function getNoticeStatusText(status: NoticeStatus | string): string {
  const statusMap: Record<string, string> = {
    [NoticeStatus.UNSENT]: '未发送',
    [NoticeStatus.SENT]: '已发送',
  };
  return statusMap[status] || '未知';
}

// 获取通知状态类型
function getNoticeStatusType(status: NoticeStatus | string): string {
  const typeMap: Record<string, string> = {
    [NoticeStatus.UNSENT]: 'info',
    [NoticeStatus.SENT]: 'success',
  };
  return typeMap[status] || '';
}

// 获取签章状态文本
function getSignatureStatusText(status: SignatureStatus | string): string {
  const statusMap: Record<string, string> = {
    [SignatureStatus.UNSIGNED]: '未签章',
    [SignatureStatus.SIGNING]: '签章中',
    [SignatureStatus.SIGNED]: '已签章',
  };
  return statusMap[status] || '未知';
}

// 获取签章状态类型
function getSignatureStatusType(status: SignatureStatus | string): string {
  const typeMap: Record<string, string> = {
    [SignatureStatus.UNSIGNED]: 'warning',
    [SignatureStatus.SIGNING]: 'primary',
    [SignatureStatus.SIGNED]: 'success',
  };
  return typeMap[status] || '';
}

// 处理编辑中标通知书内容
async function handleEditContent(row: any) {
  currentEditingSupplier.value = row;
  // 直接打开抽屉编辑器，显示已有的通知书内容
  announcementEditorRef.value?.show(row.notificationContent || '', `编辑 ${row.supplierName} 中标通知书`);
}

// 处理电子签章
async function handleElectronicSignature(row: any) {
  try {
    const data = {
      noticeId: notificationForm.noticeId,
      projectId: notificationForm.projectId,
      tenantSupplierId: row.tenantSupplierId,
      supplierName: row.supplierName,
    };

    await electronicSignature(data);

    // 刷新数据
    await getWinnerData();
  } catch (error) {}
}

// 发送中标通知的通用方法
async function sendNotification(data: SendNotificationParams) {
  try {
    await sendWinnerNotification(data);
    ElMessage.success('中标通知书发送成功');
    // 刷新数据
    await getWinnerData();
  } catch (error) {
    ElMessage.error('发送失败，请重试');
  }
}

// 处理发送中标通知（单个供应商）
async function handleSendNotification(row: any) {
  try {
    if (!row.notificationContent) {
      ElMessage.error('该供应商暂无通知书内容，请先在定标结果页面生成通知书');
      return;
    }

    // 设置发送状态
    row.sending = true;

    // 构建单个供应商的数据
    const data = {
      noticeId: notificationForm.noticeId,
      projectId: notificationForm.projectId,
      bidCompanyList: [
        {
          tenantSupplierId: row.tenantSupplierId,
          supplierName: row.supplierName,
          sectionId: row.sectionId,
        },
      ],
    };

    await sendNotification(data);
  } catch (error) {
    ElMessage.error('发送失败，请重试');
  } finally {
    row.sending = false;
  }
}

// 处理保存通知内容
async function handleSaveContent(content: string) {
  try {
    if (!currentEditingSupplier.value) {
      ElMessage.error('未找到当前编辑的供应商信息');
      return;
    }

    // 构建编辑通知内容的请求参数
    const data: EditNotificationContentParams = {
      noticeId: notificationForm.noticeId,
      projectId: notificationForm.projectId,
      sectionId: currentEditingSupplier.value.sectionId,
      tenantSupplierId: currentEditingSupplier.value.tenantSupplierId,
      noticeContent: content,
    };

    // 调用API保存通知书内容
    await editNotificationContent(data);

    // 更新本地数据
    currentEditingSupplier.value.notificationContent = content;

    ElMessage.success(`${currentEditingSupplier.value.supplierName} 中标通知书内容保存成功`);

    // 刷新列表数据
    await getWinnerData();
  } catch (error) {
    ElMessage.error('保存失败，请重试');
  }
}

// 获取中标供应商列表
async function getWinnerData() {
  try {
    tableLoading.value = true;
    const { data } = await getAwardedSuppliers(notificationForm.noticeId, notificationForm.projectId);
    if (data && Array.isArray(data)) {
      // 存储原始数据，添加通知和签章状态以及通知书内容
      allWinnerData.value = data.map((item: any) => ({
        ...item,
        noticeStatus: item.awardNotice?.noticeStatus || NoticeStatus.UNSENT,
        signatureStatus: item.signatureStatus || SignatureStatus.UNSIGNED,
        notificationContent: item.awardNotice?.noticeContent || '', // 添加通知书内容字段
        sending: false, // 初始化发送状态
      }));

      // 根据当前选中的标段进行过滤
      filterWinnerDataBySectionId();
    } else {
      allWinnerData.value = [];
      winnerTableData.value = [];
      ElMessage.warning('暂无中标供应商数据');
    }
  } catch (error) {
    allWinnerData.value = [];
    winnerTableData.value = [];
  } finally {
    tableLoading.value = false;
  }
}

// 处理一键发送（所有供应商）
async function handleBatchSendTemp() {
  try {
    // 检查是否有数据
    if (!winnerTableData.value || winnerTableData.value.length === 0) {
      ElMessage.warning('暂无中标供应商数据');
      return;
    }
    batchSendLoading.value = true;
    // 构建所有供应商的数据
    const bidCompanyList = winnerTableData.value.map((item) => ({
      tenantSupplierId: item.tenantSupplierId,
      supplierName: item.supplierName,
      sectionId: item.sectionId,
    }));

    const data = {
      noticeId: notificationForm.noticeId,
      projectId: notificationForm.projectId,
      bidCompanyList,
    };

    await sendNotification(data);
  } catch (error) {
    ElMessage.error('一键发送失败，请重试');
  } finally {
    batchSendLoading.value = false;
  }
}

// 初始化函数
async function init() {
  try {
    await getWinnerData();
  } catch (error) {}
}

// 组件挂载时初始化
onMounted(() => {
  init();
});
</script>

<style lang="scss" scoped>
@import '../../../styles/collapse-panel.scss';

.page-header {
  margin-bottom: 16px;

  .company-info {
    display: flex;
    align-items: center;
    gap: 8px;

    .company-icon {
      color: #0069ff;
      font-size: 18px;
    }

    .company-name {
      font-size: 18px;
      font-weight: 600;
      color: #1d2129;
    }

    .status-tag {
      margin-left: 8px;
    }
  }
}

.winner-table-section,
.notification-section {
  margin-bottom: 12px;
  background: #fff;
  border-radius: 6px;
  padding: 20px;

  .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;

    .header-title {
      font-size: 16px;
      font-weight: 600;
      color: #1d2129;
    }
  }
}

.form-actions-wrapper {
  width: 100%;
  box-sizing: border-box;
  background-color: #fff;
  z-index: 1000;
  position: sticky;
  bottom: -20px;
  left: 0;
  right: 0;
  padding: 16px 24px;
  display: flex;
  gap: 12px;
  border-top: 1px solid var(--Color-Border-border-color-light, #e4e7ed);

  .form-actions {
    display: flex;
    justify-content: flex-start;
    gap: 12px;
    width: 100%;
  }
}
</style>
