<template>
  <div class="winner-publicity-container">
    <StageTabs
      v-model="activeTabIndex"
      @update:sectionId="handleSectionChange"
    />
    <!-- 公示要求 -->
    <div class="publicity-requirements-section">
      <div class="section-header">
        <div class="header-title">中标通知书</div>
      </div>
      <div
        v-if="publicityForm.content"
        class="publicity-content"
      >
        <div v-html="publicityForm.content"></div>
      </div>
      <div
        v-else
        class="publicity-content"
      >
        暂无中标通知书
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { reactive, computed, onMounted } from 'vue';
import { useBiddingStore } from '@/views/procurementSourcing/biddingProcess/stores';
import { getAwardNoticeDetail } from '../../../api/WinnerNotification';
import StageTabs from '@/views/procurementSourcing/biddingProcess/components/bidding/StageTabs.vue';

const biddingStore = useBiddingStore();
const noticeId = computed(() => biddingStore?.noticeId);
const ownSupplier = computed(() => biddingStore?.ownSupplier || {});


const activeTabIndex = ref(0);
const currentSectionId = ref(0);

const publicityForm = reactive({
  content: '',
});

// 获取中标通知书详情
async function getAwardNoticeDetailData() {
  try {
    if (!noticeId.value) {
      return;
    }

    if (!ownSupplier.value?.id) {
      return;
    }

    const response = await getAwardNoticeDetail(noticeId.value, currentSectionId.value, ownSupplier.value?.id);
    if (response.data && response.data.noticeContent) {
      publicityForm.content = response.data.noticeContent;
    }
  } catch (error) {
    console.error('获取中标通知书详情失败:', error);
  }
}


function handleSectionChange(sectionId: number) {
  currentSectionId.value = sectionId;
  getAwardNoticeDetailData();
}

onMounted(async () => {
  await biddingStore.initData();
  getAwardNoticeDetailData(); 
});


</script>

<style lang="scss" scoped>
.winner-publicity-container {
  color: #4e5969;
  font-size: 14px;
}

.page-header {
  margin-bottom: 16px;

  .company-info {
    display: flex;
    align-items: center;
    gap: 8px;

    .company-icon {
      color: #0069ff;
      font-size: 18px;
    }

    .company-name {
      font-size: 18px;
      font-weight: 600;
      color: #1d2129;
    }

    .status-tag {
      margin-left: 8px;
    }
  }
}

.publicity-requirements-section {
  margin-top: 12px;
}

.winner-table-section,
.publicity-requirements-section,
.publicity-details-section,
.publicity-attachments-section,
.publicity-platform-section {
  margin-bottom: 12px;
  background: #fff;
  border-radius: 6px;
  padding: 20px;

  .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
  }
}

.publicity-form {
  :deep(.el-form-item) {
    margin-bottom: 20px;
  }

  :deep(.el-form-item__label) {
    //color: #86909c;
    //font-weight: 400;
  }
}

.winner-table {
  :deep(.el-table__header) {
    th {
      background: #f5f7fa;
      padding: 12px 0;
      border-bottom: 1px solid #ebeef5;

      .cell {
        color: #505762;
        font-size: 14px;
        font-weight: 500;
      }
    }
  }

  :deep(.el-table__body) {
    tr {
      td {
        border-bottom: 1px solid #ebeef5;
        padding: 12px 0;
      }
    }
  }
}

.platform-checkboxes {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

:deep(.el-range-input) {
  background: #fff;
}

.publicity-content {
  display: flex;
  padding: 32px 40px;
  flex-direction: column;
  align-items: center;
  gap: 16px;
  align-self: stretch;
  background: var(--Color-Fill-fill-color-light, #f5f7fa);
}
</style>
