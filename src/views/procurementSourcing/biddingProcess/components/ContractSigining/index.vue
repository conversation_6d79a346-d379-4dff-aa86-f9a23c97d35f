<template>
  <div class="contract-signing-container">

    <StageTabs
        v-model="activeStageIndex"
        @update:sectionId="handleStageChange"
      />

    <div class="contract-signing-table">
      <yun-pro-table
        ref="proTableRef"
        v-model:pagination="pagination"
        v-model:searchData="searchData"
        :search-fields="searchFields"
        :layout="'whole'"
        :auto-height="true"
        :table-columns="tableColumns"
        :remote-method="remoteMethod"
        :table-props="tableProps"
        :default-fetch="true"
      >
        <template #t_contractStatus="{ row }">
          <el-tag
            :type="getContractStatusType(row.contractStatus)"
            size="small"
          >
            {{ getContractStatusText(row.contractStatus) }}
          </el-tag>
        </template>
        <template #t_approvalStatus="{ row }">
          <el-tag
            :type="getApprovalStatusType(row.approvalStatus)"
            size="small"
          >
            {{ getApprovalStatusText(row.approvalStatus) }}
          </el-tag>
        </template>
        <template #t_action="{ row }">
          <yun-rest limit="1">
            <el-button
              type="text"
              @click="handleView(row)"
            >
              查看
            </el-button>
          </yun-rest>
        </template>
      </yun-pro-table>
    </div>

    <!-- 合同列表 -->
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, computed, watch } from 'vue';
import { useUserRole } from '@/views/procurementSourcing/biddingProcess/utils';
import { useProTable } from '@ylz-use/core';
import { useContractSigningOptions } from './hooks';
import { useContractHandler } from '@/views/contractMangement/hooks';
import { getContractList } from '@/api/contract';
import { getOwnSupplier } from '@/api/purchasing/bid';
import { useBiddingStore } from '@/views/procurementSourcing/biddingProcess/stores';
import StageTabs from '../bidding/StageTabs.vue';

// 用户角色判断
const { isPurchaser, isSupplier } = useUserRole();

// 投标状态管理
const biddingStore = useBiddingStore();
const projectId = computed(() => biddingStore?.projectId);

// 标段相关
const activeStageIndex = ref(0);
const currentSectionId = ref<string>('');

// 供应商信息
const ownSupplier = ref<any>(null);

// 使用自定义hooks
const {
  getContractStatusType,
  getContractStatusText,
  getApprovalStatusType,
  getApprovalStatusText,
  handleView,
  formatDate,
  formatDateTime,
  formatSignDate,
  formatAmount,
} = useContractHandler(isPurchaser.value);

const { searchFields, tableColumns, tablePropsObj } = useContractSigningOptions(isPurchaser.value, {
  formatDate,
  formatDateTime,
  formatSignDate,
  formatAmount,
});

// 搜索数据
const searchData = ref({});

// 获取供应商信息
const getOwnSupplierData = async () => {
  if (!isSupplier.value) return;

  try {
    const response = await getOwnSupplier();
    if (response.code === 0) {
      ownSupplier.value = response.data;
    } else {
      ownSupplier.value = null;
    }
  } catch (error) {
    ownSupplier.value = null;
  }
};

// 标段切换处理
const handleStageChange = (sectionId: string) => {
  currentSectionId.value = sectionId;
  // 刷新列表数据
  if (proTableRef.value) {
    proTableRef.value.getData();
  }
};

// 自定义远程方法，支持供应商ID过滤和标段过滤
const customRemoteMethod = async (params: any) => {
  try {
    const requestParams = { ...params, isPurchase: isPurchaser.value };

    // 如果是供应商，添加supplierIds参数
    if (isSupplier.value && ownSupplier.value?.id) {
      requestParams.supplierIds = [ownSupplier.value.id];
    }

    // 添加标段ID
    if (currentSectionId.value) {
      requestParams.sectionId = currentSectionId.value;
    }

    // 采购方登录时，设置项目ID为默认参数
    if (isPurchaser.value && projectId.value) {
      requestParams.projectId = projectId.value;
    }

    const response = await getContractList(requestParams);
    return response;
  } catch (error) {
    return { data: { records: [], total: 0 } };
  }
};

const { pagination, remoteMethod, tableProps, proTableRef, filterTableData, reLoad } = useProTable({
  apiFn: customRemoteMethod,
  defaultParams: {
    current: 1,
    size: 20,
  },
  tableProps: tablePropsObj,
  responseHandler(result: any) {
    return result.data;
  },
  plugins: {
    config: {
      columns: tableColumns.value,
      searchFields: searchFields.value,
    },
    list: ['SEARCH_PLUS'],
  },
});

// 监听标段变化，刷新数据
watch(currentSectionId, () => {
  if (proTableRef.value) {
    proTableRef.value.getData();
  }
});

// 组件挂载时初始化
onMounted(async () => {
  // 如果是供应商，先获取供应商信息
  if (isSupplier.value) {
    await getOwnSupplierData();
  }
});

// 组件卸载时清理
onUnmounted(() => {
  ownSupplier.value = null;
});
</script>

<style lang="scss" scoped>
.contract-signing-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;

  .contract-signing-table {
    flex: 1;
    width: 100%;
  }
}
</style>
