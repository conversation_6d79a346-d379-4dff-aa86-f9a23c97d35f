/**
 * 合同签订配置选项Hook
 */
import { computed, ref } from 'vue';
import type { ContractListItem } from '@/types/contract';
import { deptTree } from '@/api/admin/dept';

export function useContractSigningOptions(isPurchaser: boolean, handlers?: {
  formatDate: (date: string | null | undefined) => string;
  formatDateTime: (dateTime: string | null | undefined) => string;
  formatSignDate: (date: string | null | undefined) => string;
  formatAmount: (amount: number | null | undefined) => string;
}) {
  // 部门树数据
  const treeDeptData = ref([]);
  
  // 获取部门树数据
  const getDeptTree = async () => {
    try {
      const resp = await deptTree();
      treeDeptData.value = resp.data;
    } catch (error) {
      console.error('获取部门树失败:', error);
      treeDeptData.value = [];
    }
  };
  
  // 初始化获取部门树
  getDeptTree();
  
  // 获取树形数据中的项目
  const getTreeItem = (tree: any[], val: string | number, config?: { value?: string; children?: string }): any => {
    const { value = 'value', children = 'children' } = config || {};
    let item = {};
    for (let i = 0; i < tree.length; i += 1) {
      if (tree[i][value] === val) {
        item = tree[i];
        break;
      } else {
        if (tree[i]?.[children]?.length) {
          item = getTreeItem(tree[i][children], val, config);
          if (Object.keys(item).length) {
            break;
          }
        }
      }
    }
    return item;
  };
  // 搜索字段配置
  const searchFields = computed(() => {
    const baseFields = [
      {
        label: '合同名称',
        prop: 'contractName',
        type: 'input',
        placeholder: '请输入',
      },
      {
        label: '采购方',
        prop: 'purchaserDeptId',
        component: 'el-cascader',
        componentAttrs: {
          options: treeDeptData.value,
          filterable: true,
          clearable: true,
          placeholder: '请选择采购方',
          props: {
            value: 'id',
            label: 'name',
            children: 'children',
            checkStrictly: true,
            emitPath: false,
            showAllLevels: false,
          },
        },
      },
      {
        label: '签约时间',
        prop: 'signDate',
        component: 'el-date-picker',
        componentAttrs: {
          type: 'date',
          placeholder: '请选择签约时间',
          clearable: true,
          format: 'YYYY-MM-DD',
          valueFormat: 'YYYY-MM-DD',
          style: 'width: 100%'
        }
      },
      {
        label: '项目名称',
        prop: 'projectName',
        type: 'input',
        placeholder: '请输入',
      },
    ];

    // 采购方登录时添加供应商查询选项
    if (isPurchaser) {
      baseFields.splice(2, 0, {
        label: '供应商',
        prop: 'supplierName',
        type: 'input',
        placeholder: '请输入供应商名称',
      });
    }

    return baseFields;
  });

  // 表格列配置
  const tableColumns = computed(() => {
    const baseColumns: any[] = [
      {
        label: '序号',
        type: 'index',
      },
      {
        label: '合同编号',
        prop: 'contractCode',
      },
      {
        label: '合同名称',
        prop: 'contractName',
      },
      {
        label: '项目名称',
        prop: 'projectName',
      },
      {
        label: '标段名称',
        prop: 'sectionName',
      },
      {
        label: '采购方',
        prop: 'purchaseDeptName',
      },
      {
        label: '供应商',
        prop: 'supplierName',
      },
      {
        label: '含税合同总金额',
        prop: 'totalAmount',
        formatter: (row: ContractListItem) => handlers?.formatAmount(row.totalAmount) || '--',
      },
      {
        label: '签约时间',
        prop: 'signDate',
        formatter: (row: ContractListItem) => {
          return handlers?.formatSignDate(row.signDate) || '--';
        },
      },
      {
        label: '合同状态',
        prop: 'contractStatus',
        slot: true,
      },
    ];

    // 采购方登录时显示审批状态、创建人、修改人等字段
    if (isPurchaser) {
      baseColumns.push(
        {
          label: '审批状态',
          prop: 'approvalStatus',
          slot: true,
        },
        {
          label: '创建人',
          prop: 'createByName',
        },
        {
          label: '创建时间',
          prop: 'createTime',
          formatter: (row: ContractListItem) => {
            return handlers?.formatDateTime(row.createTime) || '--';
          },
        },
        {
          label: '修改人',
          prop: 'updateByName',
        },
        {
          label: '修改时间',
          prop: 'updateTime',
          formatter: (row: ContractListItem) => {
            return handlers?.formatDateTime(row.updateTime) || '--';
          },
        }
      );
    }

    baseColumns.push({
      label: '操作',
      prop: 'action',
      fixed: 'right',
      slot: true,
    });

    return baseColumns;
  });

  // 表格属性配置
  const tablePropsObj = {
    stripe: true,
    border: true,
    'show-summary': false,
  };

  return {
    searchFields,
    tableColumns,
    tablePropsObj,
  };
} 