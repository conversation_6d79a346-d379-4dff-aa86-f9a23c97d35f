<template>
  <div class="project-archive-container">
    <!-- 项目归档状态 -->
    <div class="archive-status-section">
      <div class="section-header">
        <div class="header-title">项目归档</div>
      </div>
      <div class="archive-status-content">
        <div class="archive-label">归档内容</div>

        <div class="archive-status-list">
          <div
            v-for="item in archiveItems"
            :key="item.key"
            class="archive-status-item"
          >
            <div class="status-checkbox">
              <el-checkbox
                :model-value="item.completed"
                disabled
                class="status-checkbox-disabled"
              />
            </div>
            <div class="status-label">{{ item.label }}</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 过程附件 -->
    <div class="process-attachments-section">
      <div class="section-header">
        <div class="header-title">过程附件</div>
      </div>

      <!-- 采购人附件 -->
      <div class="attachments-subsection">
        <div class="subsection-title">采购人附件</div>
        <el-descriptions
          :column="1"
          :border="false"
          class="archive-descriptions"
        >
          <el-descriptions-item
            v-for="item in processAttachmentItems"
            :key="item.key"
            :label="item.label"
          >
            <!-- 询价公告/邀请函特殊处理 -->
            <div
              v-if="item.key === 'inquiry' || item.key === 'invitation'"
              class="announcement-detail"
            >
              <div class="announcement-preview">
                <span>{{archiveData?.noticeTitle}}</span>
                <el-button
                  type="text"
                  @click="previewAnnouncement"
                >
                  预览
                </el-button>
              </div>
              <div class="attachment-list">
                <div
                  v-for="attachment in archiveData?.noticeAttachmentList || []"
                  :key="attachment.fileName"
                  class="attachment-item"
                >
                  <span
                    class="file-link"
                    @click="downloadAttachment(attachment.filePath, attachment.fileName)"
                  >
                    {{ attachment.fileName }}
                  </span>
                </div>
              </div>
            </div>
            <!-- 定标公示特殊处理 -->
            <div
              v-else-if="item.key === 'awardPubilc'"
              class="announcement-detail"
            >
              <div class="announcement-preview">
                <span>{{archiveData?.awardPublicityTitle}}</span>
                <el-button
                  type="text"
                  @click="previewAwardPublicity"
                >
                  预览
                </el-button>
              </div>
              <div class="attachment-list">
                <div
                  v-for="attachment in archiveData?.awardPublicityAttachmentList || []"
                  :key="attachment.fileName"
                  class="attachment-item"
                >
                  <span
                    class="file-link"
                    @click="downloadAttachment(attachment.filePath, attachment.fileName)"
                  >
                    {{ attachment.fileName }}
                  </span>
                </div>
              </div>
            </div>
            <!-- 定标公告特殊处理 -->
            <div
              v-else-if="item.key === 'awardAnnouncement'"
              class="announcement-detail"
            >
              <div class="announcement-preview">
                <span>{{archiveData?.awardNoticeTitle}}</span>
                <el-button
                  type="text"
                  @click="previewAwardNotice"
                >
                  预览
                </el-button>
              </div>
              <div class="attachment-list">
                <div
                  v-for="attachment in archiveData?.awardNoticeAttachmentList || []"
                  :key="attachment.fileName"
                  class="attachment-item"
                >
                  <span
                    class="file-link"
                    @click="downloadAttachment(attachment.filePath, attachment.fileName)"
                  >
                    {{ attachment.fileName }}
                  </span>
                </div>
              </div>
            </div>
            <!-- 合同附件特殊处理 -->
            <div
              v-else-if="item.key === 'contract'"
              class="announcement-detail"
            >
              <div class="announcement-preview">
                <el-button
                  type="text"
                  @click="previewContract"
                >
                  预览
                </el-button>
              </div>
              <div class="attachment-list">
                <div
                  v-for="attachment in archiveData?.contractAttachmentList || []"
                  :key="attachment.fileName"
                  class="attachment-item"
                >
                  <span
                    class="file-link"
                    @click="downloadAttachment(attachment.filePath, attachment.fileName)"
                  >
                    {{ attachment.fileName }}
                  </span>
                </div>
              </div>
            </div>
            <!-- 定标结果特殊处理 -->
            <div
              v-else-if="item.key === 'award'"
              class="award-detail"
            >
              <div
                v-for="section in sectionList"
                :key="section.sectionId"
                class="award-section"
              >
                <div class="section-title">{{ section.sectionName }}</div>
                <div
                  v-for="result in [((archiveData?.evaluationResultList?.filter((item: any) => item.sectionId === section.sectionId) || [])?.[0])]"
                  :key="result.id || result.sectionId"
                  class="section-content"
                >
                  <div class="award-preview">
                    <span>{{result.awardNoticeTitle}}</span>
                    <el-button
                      type="text"
                      @click="previewAwardReport(result)"
                      >预览</el-button
                    >
                  </div>
                  <div class="attachment-list">
                    <div
                      v-for="attachment in result.awardAttachmentList || []"
                      :key="attachment.fileName"
                      class="attachment-item"
                    >
                      <span
                        class="file-link"
                        @click="downloadAttachment(attachment.filePath, attachment.fileName)"
                      >
                        {{ attachment.fileName }}
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <!-- 其他项目默认显示 -->
            <span
              v-else
              class="file-link"
              >查看附件</span
            >
          </el-descriptions-item>
        </el-descriptions>
      </div>

      <!-- 投标人附件 -->
      <div class="attachments-subsection">
        <div class="subsection-title">投标人附件</div>

        <StageTabs
          v-if="archiveData"
          v-model="activeTabIndex"
          @update:sectionId="handleSectionChange"
        />

        <el-table
          :data="bidderAttachments"
          class="editable-table"
          size="small"
          style="margin-top: 12px"
        >
          <!-- 投标人名称 - 固定列 -->
          <el-table-column
            label="投标人名称"
            prop="supplierName"
            min-width="200"
          />

          <!-- 邀请回执附件 - 条件显示 -->
          <el-table-column
            v-if="isNeedInviteReceipt"
            label="邀请回执附件"
            min-width="150"
          >
            <template #default="{ row }">
              <div class="attachment-links">
                <span
                  v-for="attachment in row.inviteAttachmentList || []"
                  :key="attachment.fileName"
                  class="file-link"
                  @click="downloadAttachment(attachment.filePath, attachment.fileName)"
                >
                  {{ attachment.fileName }}
                </span>
                <span
                  v-if="!row.inviteAttachmentList?.length"
                  class="no-data"
                  >无</span
                >
              </div>
            </template>
          </el-table-column>

          <!-- 报名附件 - 条件显示 -->
          <el-table-column
            v-if="isNeedPreQualification"
            label="报名附件"
            min-width="150"
          >
            <template #default="{ row }">
              <div class="attachment-links">
                <span
                  v-for="attachment in parseJsonField(row.responseContent)"
                  :key="attachment.name"
                  class="file-link"
                  @click="downloadAttachment(attachment.url, attachment.name)"
                >
                  {{ attachment.name }}
                </span>
                <span
                  v-if="!parseJsonField(row.responseContent).length"
                  class="no-data"
                  >无</span
                >
              </div>
            </template>
          </el-table-column>

          <!-- 保证金附件 - 条件显示 -->
          <el-table-column
            v-if="isNeedDeposit"
            label="保证金附件"
            min-width="150"
          >
            <template #default="{ row }">
              <div class="attachment-links">
                <span
                  v-for="attachment in parseJsonField(row.depositRespContent)"
                  :key="attachment.name"
                  class="file-link"
                  @click="downloadAttachment(attachment.url, attachment.name)"
                >
                  {{ attachment.name }}
                </span>
                <span
                  v-if="!parseJsonField(row.depositRespContent).length"
                  class="no-data"
                  >无</span
                >
              </div>
            </template>
          </el-table-column>

          <!-- 报价附件 - 固定列 -->
          <el-table-column
            label="报价附件"
            min-width="150"
          >
            <template #default="{ row }">
              <div class="attachment-links">
                <span
                  v-for="attachment in row.quoteAttachmentList || []"
                  :key="attachment.fileName"
                  class="file-link"
                  @click="downloadAttachment(attachment.filePath, attachment.fileName)"
                >
                  {{ attachment.fileName }}
                </span>
                <span
                  v-if="!row.quoteAttachmentList?.length"
                  class="no-data"
                  >无</span
                >
              </div>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>
  </div>

  <!-- 公告预览组件 -->
  <AnnouncementEditor
    ref="announcementEditorRef"
    :readonly="true"
  />
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { ElMessage } from 'yun-design';
import { useBiddingStore } from '@/views/procurementSourcing/biddingProcess/stores';
import { getProjectArchive } from '@/views/procurementSourcing/biddingProcess/api/archive';
import AnnouncementEditor from '../../components/award/AwardResult/components/AnnouncementEditor.vue';
import StageTabs from '@/views/procurementSourcing/biddingProcess/components/bidding/StageTabs.vue';

const biddingStore = useBiddingStore();

// 归档数据状态
const archiveData = ref<any>(null);
const loading = ref(false);
const activeTabIndex = ref(0);
const bidderAttachments = ref<any>([]);

// 公告预览相关状态
const announcementEditorRef = ref();

// 标段变更处理
function handleSectionChange(sectionId: number) {
  bidderAttachments.value =
    archiveData.value?.sectionSupplierAttachmentList?.find((bidder: any) => bidder.sectionId === sectionId)?.srmTenderSupplierResponseList ?? [];
}

// 组件挂载时初始化标段
onMounted(() => {
  fetchArchiveData();
});

// 获取项目归档信息
const fetchArchiveData = async () => {
  try {
    loading.value = true;
    const projectCode = biddingStore?.projectDetail?.projectCode;
    if (!projectCode) {
      console.warn('项目编码不存在');
      return;
    }

    const response = await getProjectArchive(projectCode);
    archiveData.value = response.data;
  } catch (error) {
    console.error('获取项目归档信息失败:', error);
  } finally {
    loading.value = false;
  }
};

// 预览公告
const previewAnnouncement = () => {
  const content = archiveData.value?.noticeContent || '';
  const title = isNeedInviteReceipt.value ? '邀请函预览' : '询价公告预览';

  // 调用子组件的 show 方法
  announcementEditorRef.value?.show(content, title);
};

// 预览合同
const previewContract = () => {
  const content = archiveData.value?.contractContent || '';
  const title = '合同详情预览';

  // 调用子组件的 show 方法
  announcementEditorRef.value?.show(content, title);
};

// 预览定标结果报告
const previewAwardReport = (section: any) => {
  const content = section?.awardReportContent || '';
  const title = `标段${archiveData.value?.evaluationResultList?.indexOf(section) + 1}定标结果预览`;

  // 调用子组件的 show 方法
  announcementEditorRef.value?.show(content, title);
};

// 预览定标公示
const previewAwardPublicity = () => {
  const content = archiveData.value?.awardPublicityContent || '';
  const title = '定标公示预览';

  // 调用子组件的 show 方法
  announcementEditorRef.value?.show(content, title);
};

// 预览定标公告
const previewAwardNotice = () => {
  const content = archiveData.value?.awardNoticeContent || '';
  const title = '定标公告预览';

  // 调用子组件的 show 方法
  announcementEditorRef.value?.show(content, title);
};

// 下载附件
const downloadAttachment = (filePath: string, fileName: string) => {
  if (!filePath) {
    ElMessage.warning('文件路径不存在');
    return;
  }

  // 创建下载链接
  const link = document.createElement('a');
  link.href = filePath;
  link.download = fileName;
  link.target = '_blank';
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
};

// 资格预审标识 1-需要 其他-不需要
const isNeedPreQualification = computed(() => [1, true]?.includes(biddingStore?.projectDetail?.preQualification));

// 邀请回执标识 1-需要 其他-不需要
const isNeedInviteReceipt = computed(() => [1, true]?.includes(biddingStore?.projectDetail?.inviteReceipt));

// 保证金管理标识 1-需要 其他-不需要
const isNeedDeposit = computed(() => [1, true]?.includes(biddingStore?.noticeInfo?.needDeposit));

// 归档项目状态数据
const archiveItems = computed(() => {
  const items = [];

  // 询价公告或邀请函
  if (isNeedInviteReceipt.value) {
    items.push({ key: 'invitation', label: '邀请函', completed: true });
  } else {
    items.push({ key: 'inquiry', label: '询价公告', completed: true });
  }

  // 资格预审 - 只有需要时才显示
  if (isNeedPreQualification.value) {
    items.push({ key: 'prequalification', label: '资格预审', completed: true });
  }

  // 保证金管理 - 只有需要时才显示
  if (isNeedDeposit.value) {
    items.push({ key: 'deposit', label: '保证金管理', completed: true });
  }

  // 报价情况
  items.push({ key: 'quotation', label: '报价情况', completed: true });

  // 开标记录
  // items.push({ key: 'bidOpening', label: '开标记录', completed: true });

  // 定标结果
  items.push({ key: 'award', label: '定标结果', completed: true });

  // 中标公示和中标公告 - 只有不需要邀请回执时才显示
  if (!isNeedInviteReceipt.value) {
    items.push({ key: 'awardPubilc', label: '中标公示', completed: true });
    items.push({ key: 'awardAnnouncement', label: '中标公告', completed: true });
  }

  // 合同附件
  items.push({ key: 'contract', label: '合同附件', completed: true });

  return items;
});

// 过程附件项目数据 - 过滤掉保证金管理和报价情况
const processAttachmentItems = computed(() => {
  return archiveItems.value.filter((item) => item.key !== 'deposit' && item.key !== 'quotation');
});

// 计算所有唯一标段
const sectionList = computed(() => {
  const list = archiveData.value?.evaluationResultList || [];
  const map = new Map();
  list.forEach((item: any) => {
    if (!map.has(item.sectionId)) {
      map.set(item.sectionId, {
        sectionId: item.sectionId,
        sectionName: item.sectionName || `标段${item.sectionId}`,
      });
    }
  });
  return Array.from(map.values());
});

// 投标人附件数据 - 根据标段过滤
// const bidderAttachments = computed(() => {
//   const allBidders = archiveData.value?.srmTenderSupplierBidderResponseList || [];

//   // 根据当前选中的标段过滤数据
//   return allBidders.filter((bidder: any) => bidder.sectionId === activeTabIndex.value);
// });

// 解析JSON字符串的辅助函数
const parseJsonField = (jsonString: string) => {
  if (!jsonString) return [];

  try {
    const parsed = JSON.parse(jsonString);

    // 如果是数组，直接返回
    if (Array.isArray(parsed)) {
      return parsed.map((item) => ({
        name: item.name || item.fileName || '未知文件',
        url: item.url || item.filePath || '',
      }));
    }

    // 如果是对象，转换为数组
    if (typeof parsed === 'object' && parsed !== null) {
      return [
        {
          name: parsed.name || parsed.fileName || '未知文件',
          url: parsed.url || parsed.filePath || '',
        },
      ];
    }

    return [];
  } catch (error) {
    console.error('解析JSON字段失败:', error);
    return [];
  }
};
</script>

<style scoped lang="scss">
@import '../../styles/collapse-panel.scss';
.project-archive-container {
  .archive-status-section,
  .process-attachments-section {
    margin-bottom: 20px;
    background: #fff;
    border-radius: 4px;
    padding: 20px;

    .section-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;

      .header-actions {
        display: flex;
        gap: 8px;
      }
    }
  }

  .archive-status-content {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    gap: 12px;

    .archive-label {
      width: 120px;
      color: var(--Color-Text-text-color-regular, #4e5969);
      font-family: 'PingFang SC';
      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      line-height: 22px;
      text-align: right;
    }

    .archive-status-list {
      flex: 1;
      display: flex;
      flex-wrap: wrap;
      gap: 24px;

      .archive-status-item {
        display: flex;
        align-items: center;
        gap: 8px;

        .status-checkbox {
          .status-checkbox-disabled {
            :deep(.el-checkbox__input.is-checked .el-checkbox__inner) {
              background-color: #0069ff;
              border-color: #0069ff;
            }

            :deep(.el-checkbox__input.is-checked .el-checkbox__inner::after) {
              border-color: #fff;
            }

            :deep(.el-checkbox__input.is-disabled .el-checkbox__inner) {
              background-color: #0069ff;
              border-color: #0069ff;
            }
          }
        }

        .status-label {
          color: var(--Color-Primary-color-primary, #0069ff);
          text-align: center;
          font-family: 'PingFang SC';
          font-size: 14px;
          font-style: normal;
          font-weight: 500;
          line-height: 22px;
        }
      }
    }
  }

  .attachments-subsection {
    margin-bottom: 24px;

    &:last-child {
      margin-bottom: 0;
    }

    .subsection-title {
      color: var(--Color-Text-text-color-primary, #1d2129);
      font-family: 'PingFang SC';
      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      line-height: 22px;
      margin-bottom: 16px;
    }
  }

  .file-link {
    color: #0069ff;
    font-size: 14px;
    font-weight: 400;
    cursor: pointer;
    text-decoration: none;

    &:hover {
      text-decoration: underline;
    }
  }

  .no-data {
    color: #86909c;
    font-size: 14px;
    font-weight: 400;
  }

  .attachment-links {
    display: flex;
    flex-direction: column;
    gap: 4px;

    .file-link {
      color: #0069ff;
      font-size: 14px;
      font-weight: 400;
      cursor: pointer;
      text-decoration: none;

      &:hover {
        text-decoration: underline;
      }
    }
  }

  .archive-descriptions {
    :deep(.el-descriptions__body) {
      background: transparent;
    }

    :deep(.el-descriptions__label) {
      color: var(--Color-Text-text-color-regular, #4e5969);
      font-family: 'PingFang SC';
      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      line-height: 22px;
    }

    :deep(.el-descriptions__content) {
      color: var(--Color-Text-text-color-primary, #1d2129);
      font-family: 'PingFang SC';
      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      line-height: 22px;
    }
  }

  .announcement-detail {
    display: inline-flex;
    align-items: center;
    gap: 20px;

    .announcement-preview {
      flex-shrink: 0;
      display: flex;
      align-items: center;
      gap: 10px;
    }

    .attachment-list {
      flex: 1;
      display: flex;
      flex-direction: column;
      gap: 8px;

      .attachment-item {
        .file-link {
          color: #0069ff;
          font-size: 14px;
          font-weight: 400;
          cursor: pointer;
          text-decoration: none;

          &:hover {
            text-decoration: underline;
          }
        }
      }
    }
  }

  .award-detail {
    display: inline-flex;
    gap: 16px;

    .award-section {
      .section-title {
        color: var(--Color-Text-text-color-primary, #1d2129);
        font-family: 'PingFang SC';
        font-size: 14px;
        font-style: normal;
        font-weight: 500;
        line-height: 22px;
      }

      .section-content {
        display: inline-flex;
        align-items: center;
        gap: 20px;

        .award-preview {
          flex-shrink: 0;
        }

        .attachment-list {
          flex: 1;
          display: flex;
          flex-direction: column;
          gap: 8px;

          .attachment-item {
            .file-link {
              color: #0069ff;
              font-size: 14px;
              font-weight: 400;
              cursor: pointer;
              text-decoration: none;

              &:hover {
                text-decoration: underline;
              }
            }
          }
        }
      }
    }
  }

  // 确保 el-descriptions 内容区域有足够的高度
  .archive-descriptions {
    :deep(.el-descriptions__body) {
      background: transparent;
    }

    :deep(.el-descriptions__table) {
      width: 100%;
      border-collapse: collapse;
    }

    :deep(.el-descriptions__cell) {
      padding: 12px 16px;
      border: none;
    }

    :deep(.el-descriptions__label) {
      color: var(--Color-Text-text-color-regular, #4e5969);
      font-family: 'PingFang SC';
      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      line-height: 22px;
      width: 120px;
      text-align: right;
      padding-right: 16px;
      vertical-align: middle;
      background: transparent;
      border: none;
    }

    :deep(.el-descriptions__content) {
      color: var(--Color-Text-text-color-primary, #1d2129);
      font-family: 'PingFang SC';
      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      line-height: 22px;
      vertical-align: middle;
      text-align: left;
      background: transparent;
      border: none;
    }
  }

  .header-title {
    color: #1d2129;
    font-family: 'PingFang SC';
    font-size: 16px;
    font-style: normal;
    font-weight: 600;
    line-height: 24px;
    padding-left: 10px;
    position: relative;

    &::before {
      content: '';
      display: inline-block;
      width: 2px;
      height: 14px;
      background: #0069ff;
      margin-right: 8px;
      position: absolute;
      left: 0;
      top: 50%;
      transform: translateY(-50%);
    }
  }
}
</style>
