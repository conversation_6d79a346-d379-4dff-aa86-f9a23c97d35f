/**
 * 竞谈文件相关API
 */
import request from '@/utils/request';
import type { CNDocumentApiData, NegotiationDocApiData } from './types';

// API路径前缀
const API_PREFIX = '/admin/cnDocument';

/**
 * 保存竞谈文件数据
 * @param data 竞谈文件数据
 */
export function saveCNDocumentData(data: CNDocumentApiData) {
  return request({
    url: `${API_PREFIX}/save`,
    method: 'post',
    data
  });
}

/**
 * 更新竞谈文件数据
 * @param data 竞谈文件数据
 */
export function updateCNDocumentData(data: CNDocumentApiData) {
  return request({
    url: `${API_PREFIX}/update`,
    method: 'put',
    data
  });
}

/**
 * 获取竞谈文件详情 - 旧接口
 * @param id 竞谈文件ID
 */
export function getCNDocumentDetail(id: string) {
  return request({
    url: `${API_PREFIX}/detail/${id}`,
    method: 'get'
  });
}

/**
 * 获取竞谈文件详情 - 新接口
 * @param noticeId 招标公告ID
 */
export function getNegotiationDocDetail(noticeId: number | string) {
  return request({
    url: `/admin/negotiationDoc/detail/${noticeId}`,
    method: 'post'
  });
}

/**
 * 根据项目ID获取竞谈文件
 * @param projectId 项目ID
 */
export function getCNDocumentByProjectId(projectId: string) {
  return request({
    url: `${API_PREFIX}/project/${projectId}`,
    method: 'get'
  });
}

/**
 * 删除竞谈文件
 * @param id 竞谈文件ID
 */
export function deleteCNDocument(id: string) {
  return request({
    url: `${API_PREFIX}/delete/${id}`,
    method: 'delete'
  });
}

/**
 * 提交竞谈文件审核
 * @param id 竞谈文件ID
 */
export function submitCNDocumentForReview(id: string) {
  return request({
    url: `${API_PREFIX}/submit/${id}`,
    method: 'post'
  });
}

/**
 * 审核竞谈文件
 * @param id 竞谈文件ID
 * @param status 审核状态 'APPROVED' | 'REJECTED'
 * @param comment 审核意见
 */
export function reviewCNDocument(id: string, status: string, comment?: string) {
  return request({
    url: `${API_PREFIX}/review/${id}`,
    method: 'post',
    data: {
      status,
      comment
    }
  });
}

/**
 * 获取竞谈文件列表
 * @param params 查询参数
 */
export function getCNDocumentList(params: {
  projectId?: string;
  status?: string;
  pageNum?: number;
  pageSize?: number;
}) {
  return request({
    url: `${API_PREFIX}/list`,
    method: 'get',
    params
  });
}

/**
 * 保存或更新竞谈文件数据 - 新接口
 * @param data 竞谈文件数据
 */
export function upsertNegotiationDoc(data: NegotiationDocApiData) {
  return request({
    url: '/admin/negotiationDoc/upsert',
    method: 'post',
    data
  });
}
