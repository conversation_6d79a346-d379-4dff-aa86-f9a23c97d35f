<template>
  <div class="scoring-rules-component">
    <!-- 多标段选择 -->
    <div class="flex" v-if="lotList.length > 1">
      <div
        class="px-5"
        v-for="item in lotList"
        :key="item.id"
      >
        <div
          class="py-[9px] lot-item mb-4"
          :class="{ active: activeLotId === item.id }"
          @click="handleLotClick(item.id)"
        >
          {{ item.sectionName }}
        </div>
      </div>
    </div>
    <AddScoringRules
      v-model="currentScoringRules"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue';
import type { LotInfo } from '../types';
import type { ScoringRule } from '@/views/procurementSourcing/BidEvaluationTemplate/types';
import AddScoringRules from '@/views/procurementSourcing/BidEvaluationTemplate/components/addScoringRules.vue';

const props = withDefaults(defineProps<{
  modelValue: ScoringRule[];
  lotList: LotInfo[];
  activeLotId?: string;
}>(), {
  activeLotId: ''
});

const emits = defineEmits<{
  'update:modelValue': [value: ScoringRule[]];
  'update:activeLotId': [value: string];
}>();

const activeLotId = ref(props.activeLotId || (props.lotList.length > 0 ? props.lotList[0].id : ''));

const currentScoringRules = computed<ScoringRule[]>({
  get() {
    return props.modelValue.filter(item => item.sectionId === activeLotId.value);
  },
  set(val) {
    // 替换当前 sectionId 的规则，其它 sectionId 的规则保持不变
    const newValue = [
      ...props.modelValue.filter(item => item.sectionId !== activeLotId.value),
      ...val.map(rule => ({ ...rule, sectionId: activeLotId.value }))
    ];
    emits('update:modelValue', newValue);
  }
});

watch(
  () => props.lotList,
  (newList) => {
    if (newList.length > 0 && !activeLotId.value) {
      activeLotId.value = newList[0].id;
    }
  },
  { immediate: true, deep: false }
);

const handleLotClick = (lotId: string) => {
  activeLotId.value = lotId;
  emits('update:activeLotId', lotId);
}
</script>

<style lang="scss" scoped>
// 标段信息样式
.lot-item {
  cursor: pointer;
  &.active {
    border-bottom: 2px solid var(--Color-Primary-color-primary, #0069ff);
  }
}

// 评分细则样式
.scoring-actions {
  display: flex;
  justify-content: flex-start;

  .el-button {
    display: flex;
    align-items: center;
    gap: 6px;
  }
}

.scoring-rules-container {
  .scoring-group {
    margin-bottom: 16px;
    border: 1px solid var(--Color-Border-border-color-light, #E4E7ED);

    &:last-child {
      margin-bottom: 0;
    }
  }

  .group-header {
    padding: 12px 16px;
    box-shadow: 0px 0px 6px 0px rgba(0, 0, 0, 0.12);
    border: 1px solid var(--Color-Border-border-color-light, #EBEEF5);
    border-radius: var(--Radius-border-radius-small, 2px);

    .group-title {
      display: flex;
      align-items: center;
      gap: 12px;
      justify-content: space-between;
      width: 100%;

      .group-delete-btn {
        margin-left: auto;
        transition: all 0.3s ease;

        &:hover {
          transform: scale(1.1);
          color: #FF3B30 !important;
        }
      }
    }

    .group-type-badge {
      display: inline-flex;
      padding: 0px 8px;
      justify-content: center;
      align-items: center;
      gap: 4px;
      color: var(--Color-Text-text-color-regular, #4E5969);
      text-align: center;
      font-size: 12px;
      font-style: normal;
      font-weight: 400;
      line-height: 20px;
      border-radius: var(--Radius-border-radius-small, 2px);
      border: 1px solid var(--Color-Border-border-color, #DCDFE6);
      background: var(--Color-Border-border-color, #DCDFE6);
    }

    .group-type-badge-title {
      color: var(--Color-Text-text-color-primary, #1D2129);
      text-align: center;
      font-size: 14px;
      font-style: normal;
      font-weight: 600;
      line-height: 22px;
    }
  }

  .group-content {
    padding: 16px;
  }

  .rule-section {
    margin-bottom: 24px;

    &:last-child {
      margin-bottom: 0;
    }
  }

  .rule-node-header {
    margin-bottom: 12px;
    padding: 8px 12px;
    background-color: #f8f9fa;
    border-radius: 4px;
    border-left: 3px solid var(--Color-Primary-color-primary, #0069ff);

    .node-info {
      display: flex;
      align-items: center;
      justify-content: space-between;

      .node-name {
        font-weight: 600;
        color: var(--Color-Text-text-color-primary, #1D2129);
      }

      .el-icon {
        transition: all 0.3s ease;

        &:hover {
          transform: scale(1.1);
        }
      }
    }
  }
}

// 表格样式
.scoring-items-table {
  .table-actions {
    display: flex;
    gap: 8px;
    justify-content: center;

    .action-btn {
      font-size: 12px;
    }
  }

  .score-range-container {
    display: flex;
    align-items: center;
    gap: 8px;

    .score-input {
      flex: 1;
      min-width: 80px;
    }

    .range-separator {
      color: var(--Color-Text-text-color-regular, #4E5969);
      font-weight: 500;
    }

    .score-unit {
      color: var(--Color-Text-text-color-regular, #4E5969);
      font-size: 14px;
    }
  }
}

// 空状态样式
.empty-state {
  text-align: center;
  padding: 40px 20px;
  color: var(--Color-Text-text-color-regular, #4E5969);

  .empty-icon {
    margin-bottom: 16px;
    color: var(--Color-Text-text-color-disabled, #C0C4CC);
  }

  .empty-text {
    font-size: 16px;
    font-weight: 500;
    margin-bottom: 8px;
    color: var(--Color-Text-text-color-primary, #1D2129);
  }

  .empty-desc {
    font-size: 14px;
    color: var(--Color-Text-text-color-regular, #4E5969);
  }
}

// 响应式样式
@media (max-width: 768px) {
  .scoring-rules-container {
    .group-header {
      padding: 8px 12px;

      .group-title {
        gap: 8px;

        .group-type-badge-title {
          font-size: 12px;
        }
      }
    }

    .group-content {
      padding: 12px;
    }

    .rule-node-header {
      padding: 6px 8px;

      .node-info .node-name {
        font-size: 14px;
      }
    }
  }

  .scoring-items-table {
    font-size: 12px;

    .table-actions {
      flex-direction: column;
      gap: 4px;
    }

    .score-range-container {
      gap: 4px;

      .score-input {
        min-width: 60px;
      }
    }
  }

  .empty-state {
    padding: 24px 16px;

    .empty-text {
      font-size: 14px;
    }

    .empty-desc {
      font-size: 12px;
    }
  }
}
</style>
