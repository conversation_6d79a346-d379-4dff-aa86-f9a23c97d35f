// 竞谈文件表单数据结构

// 标书费信息（按标段）
export interface BidFeeInfo {
  lotId: string;
  bidFeeAmount: string;
  payAccount: string;
  payBank: string;
  openAccountBank: string;
}

// 标段标书文件信息
export interface LotBidDocuments {
  lotId: string;
  bidDocuments: AttachmentInfo[]; // 该标段的标书文件
}

// 评分项目
export interface ScoringItem {
  itemName: string;
  itemDetail: string;
  minScore?: string; // 仅评分项使用
  maxScore?: string; // 仅评分项使用
}

// 评分规则
export interface ScoringRule {
  lotId?: string; // 标段ID（用于表单数据）
  sectionId?: string; // 标段id（用于API数据）
  type?: 'REVIEW' | 'SCORE'; // 环节类别：评审项或评分项
  nodeName?: string; // 环节名称
  itemName?: string; // 评审/评分项名称
  itemDescription?: string; // 评审/评分项详情
  maxScore?: number; // 分值最大值（仅评分项有效）
  minScore?: number; // 分值最小值（仅评分项有效）
  weight?: number; // 权重（仅评分项有效）
  vetoThreshold?: number; // 否决投标数量阈值（仅评审项有效）
  totalScore?: number; // 总分（仅评分项有效）
  items?: ScoringItem[]; // 兼容老结构，AddScoringRules内部表格用
}

// 时间要求
export interface TimeRequirements {
  documentObtainTimeRange: string[]; // 文件获取起止时间
  bidFeePaymentTimeRange: string[]; // 标书费缴纳起止时间
  quotationTimeRange: string[]; // 报价起止时间
}

// 附件信息
export interface AttachmentInfo {
  fileName: string;
  filePath: string;
}

// 竞谈文件表单数据
export interface CNDocumentFormData {
  deliveryAddress: string; // 文件递交地址
  bidFeeCollection: boolean; // 全局标书费收取设置：true-收取，false-不收取
  bidFeeSegments: BidFeeInfo[]; // 标书费信息（按标段，仅在bidFeeCollection为true时有效）
  bidDocuments: AttachmentInfo[]; // 标书文件（兼容旧版本）
  lotBidDocuments: LotBidDocuments[]; // 按标段存储的标书文件
  quotationNotice: string; // 投标须知
  evaluationMethod: string; // 评审规则
  scoringRules: ScoringRule[]; // 评分细则（按标段）
  timeRequirements: TimeRequirements; // 采购时间要求
}

// 标段信息
export interface LotInfo {
  id: string;
  sectionName: string;
}

// 评审规则选项
export interface EvaluationRuleOption {
  label: string;
  value: string;
}

// API数据结构
export interface CNDocumentApiData {
  id?: string;
  projectId: string;
  deliveryAddress: string;
  bidFeeSegments: BidFeeInfo[];
  bidDocuments: AttachmentInfo[];
  quotationNotice: string;
  evaluationMethod: string;
  scoringRules: ScoringRule[]; // 评分细则
  documentObtainStartTime: string;
  documentObtainEndTime: string;
  bidFeePaymentStartTime: string;
  bidFeePaymentEndTime: string;
  quotationStartTime: string;
  quotationEndTime: string;
}

// 新接口数据结构 - 投标段信息
export interface BidsSegment {
  requirementType?: 'BID_DOCUMENT'; // 只使用 BID_DOCUMENT 类型，包含标书文件和标书费信息
  sectionId?: number;
  requirementName?: string;
  requirementContent?: string; // JSON字符串，包含 bidDocuments 和 bidFeeInfo
  bidFeeInfo?: {
    bidFee?: number | string;
    bankAccount?: string;
    bank?: string;
    openBank?: string;
  };
  attachmentInfos?: Array<{
    businessId?: number;
    businessType?: 'PROJECT'|'NOTICE'|'BID_DOC';
    fileName?: string;
    filePath?: string;
    fileType?: string;
    fileSize?: number;
  }>;
}

// 新接口数据结构 - 评标标准信息
export interface EvaluationStandardInfo {
  sectionId?: number;
  type?: 'REVIEW'|'SCORE';
  nodeName?: string;
  itemName?: string;
  itemDescription?: string;
  totalScore?: number;
  maxScore?: number;
  minScore?: number;
  weight?: number;
  vetoThreshold?: number;
  sortOrder?: number;
  isRequired?: number;
}

// 统一的竞谈文件数据结构 - 用于初始化和提交
export interface NegotiationDocApiData {
  projectId: number;
  noticeId: number;
  fileSubmissionAddress: string;
  bidFeeCollection: boolean;
  bidsSegments?: BidsSegment[];
  biddingNotice?: string;
  evaluationMethod: string;
  fileObtainStartTime: string;
  fileObtainEndTime: string;
  bidDocPayStartTime: string;
  bidDocPayEndTime: string;
  quoteStartTime: string;
  quoteEndTime: string;
  evaluationStandardInfos: EvaluationStandardInfo[];
}



// 表单验证规则类型
export interface FormRules {
  [key: string]: Array<{
    required?: boolean;
    message: string;
    trigger: string;
    validator?: (rule: any, value: any, callback: Function) => void;
  }>;
}
