<template>
  <div class="announcement-info-container">
    <template v-if="isCompetitiveBidding">
      <!-- 步骤指示器 -->
      <StepsIndicator
        :steps="stepsData"
        @step-click="handleStepClick"
      />
      <div class="component-content bg-[#fff]" style="margin-top: 12px !important">
        <component :is="currentComponent" />
      </div>
    </template>
    <ProcurementProject v-else></ProcurementProject>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue';
import StepsIndicator, { type StepItem } from '../../StepsIndicator/index.vue';
import ProcurementProject from '../ProcurementProject/index.vue';
import CNDocument from '../CNDocument/index.vue';
import { useBiddingStore } from '@/views/procurementSourcing/biddingProcess/stores';

const biddingStore = useBiddingStore();
const isCompetitiveBidding = computed(() => biddingStore?.isCompetitiveBidding);

// 当前激活的步骤索引
const activeStepIndex = ref(0);

// 项目进度状态
const cndDocumentInfo = computed(() => {
  return biddingStore.cndDocumentInfo;
});

// 步骤数据
const stepsData = ref<StepItem[]>([
  {
    id: 0,
    number: 1,
    label: '竞谈公告',
    completed: false,
    current: true,
    accessible: true, // 竞谈公告始终可访问
  },
  {
    id: 1,
    number: 2,
    label: '竞谈文件',
    completed: false,
    current: false,
    accessible: false, // 竞谈文件初始不可访问，需要公告完成后才能访问
  },
]);

// 根据项目详情更新步骤完成状态和可访问性
const updateStepsCompletedStatus = () => {
  const projectDetailValue = biddingStore?.projectDetail;

  // 重置所有步骤的完成状态
  stepsData.value.forEach((step) => {
    step.completed = false;
  });

  // 判断竞谈公告是否已完成：通过 effectNoticeId 判断
  const isAnnouncementCompleted = !!projectDetailValue?.effectNoticeId;

  // 判断竞谈文件是否已完成：通过 cndDocumentInfo.docStatus 判断
  // 只有当审核状态为 APPROVE（审批通过）时，竞谈文件才算完成
  const isTenderDocCompleted = cndDocumentInfo.value?.docStatus === 'APPROVE';

  if (isAnnouncementCompleted) {
    // 竞谈公告已完成
    stepsData.value[0].completed = true;
  }

  if (isTenderDocCompleted) {
    // 竞谈文件已完成
    stepsData.value[1].completed = true;
  }

  // 设置竞谈文件的可访问性：只有在公告完成后才能访问
  stepsData.value[1].accessible = isAnnouncementCompleted;
};

// 组件映射
const componentMap = {
  0: ProcurementProject,
  1: CNDocument,
};

// 当前显示的组件
const currentComponent = computed(() => {
  return componentMap[activeStepIndex.value as keyof typeof componentMap];
});

// 处理步骤点击事件 - 切换显示的组件并更新步骤状态
function handleStepClick(step: StepItem, index: number) {
  // 检查步骤是否可访问
  if (step.accessible === false) {
    return; // 阻止访问不可用的步骤
  }

  activeStepIndex.value = index;
  // 更新当前步骤状态
  stepsData.value.forEach((step) => {
    step.current = false;
  });
  stepsData.value[index].current = true;
}

// 根据项目详情自动切换步骤（仅在初始化时调用）
const initializeStepsByProgress = () => {
  const projectDetailValue = biddingStore?.projectDetail;

  // 更新步骤完成状态和可访问性
  updateStepsCompletedStatus();

  // 判断竞谈公告是否已完成：通过 effectNoticeId 判断
  const isAnnouncementCompleted = !!projectDetailValue?.effectNoticeId;

  // 设置初始激活步骤和当前状态
  if (isAnnouncementCompleted) {
    // 如果已完成竞谈公告，自动切换到竞谈文件
    activeStepIndex.value = 1;
    stepsData.value[0].current = false;
    stepsData.value[1].current = true;
  } else {
    // 默认显示竞谈公告
    activeStepIndex.value = 0;
    stepsData.value[0].current = true;
    stepsData.value[1].current = false;
  }
};

// 监听项目详情和竞谈文件信息变化，只更新完成状态，不影响当前选中状态
watch(
  [() => biddingStore?.projectDetail, cndDocumentInfo],
  () => {
    updateStepsCompletedStatus();
  },
  { deep: true }
);

// 组件挂载时初始化
onMounted(() => {
  initializeStepsByProgress();
});
</script>

<style lang="scss" scoped>
.announcement-info-container {
  min-height: 100%;
  display: flex;
  flex-direction: column;
  .component-content {
    position: relative;
    flex: 1;
    // margin-top: 12px;
    border-radius: 12px;
    //background: var(--Color-Fill-fill-color-blank, #fff);
  }
}
</style>
