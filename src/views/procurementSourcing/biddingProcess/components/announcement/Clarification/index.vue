<template>
	<div class="procurement-announcement">
		<div class="procurement-header">
			<div class="procurement-header-title">澄清公告列表</div>
			<div class="procurement-header-button">
				<el-button
					type="primary"
					:icon="Plus"
					@click="openCreateDrawer"
					>新建澄清公告</el-button
				>
			</div>
		</div>
		<div class="procurement-table">
			<el-table
				:data="clarificationList"
				:loading="listLoading"
				style="width: 100%"
				class="editable-table"
			>
				<el-table-column
					label="澄清公告标题"
					prop="clarifyTitle"
					min-width="200"
					show-overflow-tooltip
				/>
				<el-table-column
					label="澄清内容"
					prop="clarifyContent"
					min-width="250"
					show-overflow-tooltip
				>
					<template #default="{ row }">
						<div v-html="row.clarifyContent" class="content-preview"></div>
					</template>
				</el-table-column>
				<el-table-column
					label="创建人"
					prop="createByName"
					width="120"
					show-overflow-tooltip
				/>
				<el-table-column
					label="创建时间"
					prop="createTime"
					width="160"
					show-overflow-tooltip
				/>
				<el-table-column
					label="发布时间"
					prop="publishNoticeTime"
					width="160"
					show-overflow-tooltip
				/>
				<el-table-column
					label="审批状态"
					prop="approveStatus"
					width="120"
					align="center"
				>
					<template #default="{ row }">
						<el-tag
							:type="getStatusType(row.approveStatus)"
							size="small"
						>
							{{ getStatusText(row.approveStatus) }}
						</el-tag>
					</template>
				</el-table-column>
				<el-table-column
					label="附件下载数"
					prop="attachmentDownloadSupplierCount"
					width="120"
					align="center"
				>
					<template #default="{ row }">
						<el-link
							v-if="row.attachmentDownloadSupplierCount > 0"
							type="primary"
							@click="handleShowDownloadInfo(row)"
						>
							{{ row.attachmentDownloadSupplierCount }}
						</el-link>
						<span v-else>0</span>
					</template>
				</el-table-column>
				<el-table-column
					label="操作"
					width="120"
					fixed="right"
					align="center"
				>
					<template #default="{ row }">
						<div class="table-actions">
							<el-link
								type="primary"
								size="small"
								@click="handleDetail(row)"
							>
								详情
							</el-link>
							<el-link
								v-if="canDelete(row)"
								type="danger"
								size="small"
								@click="handleDelete(row)"
							>
								删除
							</el-link>
						</div>
					</template>
				</el-table-column>
			</el-table>
		</div>

		<!-- 新建澄清公告抽屉 -->
		<el-drawer
			v-model="createDrawerVisible"
			title="新建澄清公告"
			size="65%"
			direction="rtl"
		>
			<div class="drawer-content">
				<el-form
					ref="createFormRef"
					:model="createForm"
					:rules="createFormRules"
					label-width="120px"
					class="create-form"
				>
					<el-form-item
						label="公告标题"
						prop="title"
						required
					>
						<el-input
							v-model="createForm.title"
							placeholder="请输入公告标题"
						/>
					</el-form-item>

					<el-form-item
						label="澄清内容"
						prop="content"
						required
					>
						<div class="rich-editor-container">
							<Toolbar
								:editor="editorRef"
								:defaultConfig="toolbarConfig"
								mode="default"
							/>
							<Editor
								style="height: 450px; overflow-y: hidden;"
								v-model="createForm.content"
								:defaultConfig="editorConfig"
								mode="default"
								@onCreated="handleCreated"
							/>
						</div>
					</el-form-item>

					<el-form-item
						label="发布媒体"
						prop="publishMedia"
						required
					>
						<el-checkbox-group v-model="createForm.publishMedia">
							<el-checkbox
								v-for="media in mediaOptions"
								:key="media.value"
								:label="media.value"
							>
								{{ media.label }}
							</el-checkbox>
						</el-checkbox-group>
					</el-form-item>

					<el-form-item
						label="澄清附件"
						prop="attachments"
					>
						<YunUpload
							v-model="createForm.attachments"
							@change="handleUploadChange"
							:limit="10"
							:multiple="true"
						/>
					</el-form-item>
				</el-form>
			</div>

			<template #footer>
				<div class="drawer-footer">
          <el-button
						type="primary"
						@click="handleSubmitCreate"
						:loading="submitLoading"
					>
						提交
					</el-button>
					<el-button @click="closeCreateDrawer">取消</el-button>
				</div>
			</template>
		</el-drawer>

		<!-- 附件下载供应商抽屉 -->
		<el-drawer
			v-model="downloadInfoDrawerVisible"
			title="附件下载供应商"
			size="60%"
			direction="rtl"
		>
			<div>
				<!-- 搜索区域 -->
        <el-form :inline="true" class="search-form">
          <el-form-item label="供应商名称：">
            <el-input
              v-model="searchForm.supplierName"
              placeholder="请输入"
              style="width: 200px;"
              clearable
            />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleSearch">搜索</el-button>
            <el-button @click="handleReset">重置</el-button>
          </el-form-item>
        </el-form>

				<!-- 统计信息 -->
				<div class="stats-section">
					<span class="flex justify-end">总计下载次数：{{ totalDownloadCount }}次</span>
				</div>

				<!-- 供应商下载信息表格 -->
				<el-table
					:data="supplierDownloadList"
					:loading="downloadInfoLoading"
					style="width: 100%"
					class="editable-table"
				>
					<el-table-column
						label="序号"
						type="index"
						width="60"
						align="center"
					/>
					<el-table-column
						label="供应商名称"
						prop="supplierName"
						min-width="200"
						show-overflow-tooltip
					/>
					<el-table-column
						label="下载次数"
						prop="downloadCount"
						width="120"
						align="center"
					/>
					<el-table-column
						label="最新下载人"
						prop="downloadUserName"
						width="120"
						show-overflow-tooltip
					/>
					<el-table-column
						label="最新下载时间"
						prop="lastDownloadTime"
						width="160"
						show-overflow-tooltip
					/>
				</el-table>
			</div>
		</el-drawer>
	</div>
</template>

<script setup lang="ts">
import { ref, reactive, shallowRef, onBeforeUnmount, computed, onMounted } from 'vue';
import { Plus, UploadFilled } from '@element-plus/icons-vue';
import { Editor, Toolbar } from '@wangeditor/editor-for-vue';
import '@wangeditor/editor/dist/css/style.css';
import type { IDomEditor, IEditorConfig, IToolbarConfig } from '@wangeditor/editor';
import type { FormInstance, FormRules } from 'element-plus';
import { useRoute } from 'vue-router';
import { useBiddingStore } from '../../../stores/bidding-store';
import { ElMessage } from 'yun-design';
import { createClarifyNotice, getClarifyNoticeList, getSupplierDownloadInfo, type ClarifyNoticeItem, type SupplierDownloadInfo } from './api';
import YunUpload from '/@/components/YunUpload/index.vue';

// 获取路由和store信息
const route = useRoute();
const biddingStore = useBiddingStore();

// 获取项目ID和公告ID
const projectId = computed(() => biddingStore?.projectId);
const noticeId = computed(() => {
  return route.query.noticeId || biddingStore?.noticeId || biddingStore?.projectDetail?.tenderNotice?.id;
});

// 澄清公告列表数据
const clarificationList = ref<ClarifyNoticeItem[]>([]);
const listLoading = ref(false);

// 附件下载供应商抽屉相关
const downloadInfoDrawerVisible = ref(false);
const supplierDownloadList = ref<SupplierDownloadInfo[]>([]);
const downloadInfoLoading = ref(false);
const currentClarifyNoticeId = ref<number | null>(null);

// 搜索表单
const searchForm = reactive({
	supplierName: ''
});

// 总下载次数
const totalDownloadCount = computed(() => {
	return supplierDownloadList.value.reduce((total, item) => total + (item.downloadCount || 0), 0);
});

// 获取审批状态样式类型
function getStatusType(status?: string): string {
	switch (status) {
		case 'APPROVED':
			return 'success';
		case 'PENDING':
			return 'warning';
		case 'REJECTED':
			return 'danger';
		case 'DRAFT':
			return 'info';
		default:
			return 'info';
	}
}

// 获取审批状态文本
function getStatusText(status?: string): string {
	switch (status) {
		case 'APPROVED':
			return '审批通过';
		case 'PENDING':
			return '待审批';
		case 'REJECTED':
			return '已拒绝';
		case 'DRAFT':
			return '草稿';
		default:
			return '未知';
	}
}

// 判断是否可以删除
function canDelete(row: ClarifyNoticeItem): boolean {
	// 只有草稿状态的公告可以删除
	return row.approveStatus === 'DRAFT';
}

// 查看详情
function handleDetail(row: ClarifyNoticeItem) {
	console.log('查看详情:', row);
	// TODO: 实现详情查看功能
}

// 删除公告
function handleDelete(row: ClarifyNoticeItem) {
	console.log('删除公告:', row);
	// TODO: 实现删除确认逻辑和API调用
}

// 显示附件下载信息
function handleShowDownloadInfo(row: ClarifyNoticeItem) {
	if (!row.id) {
		ElMessage.error('澄清公告ID不存在');
		return;
	}

	currentClarifyNoticeId.value = row.id;
	downloadInfoDrawerVisible.value = true;
	loadSupplierDownloadInfo();
}

// 加载供应商下载信息
async function loadSupplierDownloadInfo() {
	if (!currentClarifyNoticeId.value) {
		return;
	}

	downloadInfoLoading.value = true;
	try {
		const response = await getSupplierDownloadInfo(
			currentClarifyNoticeId.value,
			searchForm.supplierName || undefined
		);

		if (response.code === 0) {
			supplierDownloadList.value = response.data || [];
		} else {
			ElMessage.error(response.msg || '获取下载信息失败');
		}
	} catch (error) {
		console.error('获取下载信息失败:', error);
		ElMessage.error('获取下载信息失败，请重试');
	} finally {
		downloadInfoLoading.value = false;
	}
}

// 搜索处理
function handleSearch() {
	loadSupplierDownloadInfo();
}

// 重置搜索
function handleReset() {
	searchForm.supplierName = '';
	loadSupplierDownloadInfo();
}

// 新建澄清公告抽屉相关逻辑
const createDrawerVisible = ref(false);
const createFormRef = ref<FormInstance>();
const submitLoading = ref(false);
const editorRef = shallowRef<IDomEditor>();

const createForm = reactive({
	title: '',
	content: '',
	publishMedia: [] as string[],
	attachments: [] as any[],
});

const createFormRules: FormRules = {
	title: [{ required: true, message: '请输入公告标题', trigger: 'blur' }],
	content: [{ required: true, message: '请输入澄清内容', trigger: 'blur' }],
	publishMedia: [{ required: true, message: '请选择发布媒体', trigger: 'change' }],
};

const toolbarConfig: Partial<IToolbarConfig> = {
	excludeKeys: ['fullScreen'],
};

const editorConfig: Partial<IEditorConfig> = {
	placeholder: '请输入内容...',
};

const mediaOptions = [
	{ value: 'media1', label: '中国采购与招标网' },
	{ value: 'media2', label: '中国招标投标公共服务平台' },
];

// 加载澄清公告列表
async function loadClarificationList() {
	if (!projectId.value || !noticeId.value) {
		console.warn('缺少必要的项目信息，无法加载澄清公告列表');
		return;
	}

	listLoading.value = true;
	try {
		const response = await getClarifyNoticeList({
			projectId: Number(projectId.value),
			noticeId: Number(noticeId.value)
		});

		if (response.code === 0) {
			clarificationList.value = response.data || [];
		} else {
			ElMessage.error(response.msg || '获取澄清公告列表失败');
		}
	} catch (error) {
		console.error('获取澄清公告列表失败:', error);
		ElMessage.error('获取澄清公告列表失败，请重试');
	} finally {
		listLoading.value = false;
	}
}

// YunUpload 组件的文件变化处理
function handleUploadChange(fileList: any[]) {
	createForm.attachments = fileList;
	console.log('文件列表更新:', fileList);
}

// 打开新建抽屉
function openCreateDrawer() {
	createDrawerVisible.value = true;
}

// 关闭新建抽屉
function closeCreateDrawer() {
	createDrawerVisible.value = false;
	createFormRef.value?.resetFields();
	createForm.title = '';
	createForm.content = '';
	createForm.publishMedia = [];
	createForm.attachments = [];
}

// 富文本编辑器创建完成
function handleCreated(editor: IDomEditor) {
	editorRef.value = editor;
}

// 处理附件数据格式 - 适配 YunUpload 组件的数据结构
function formatAttachments(attachments: any[]) {
	return attachments
		.filter(file => file.url) // 只处理有URL的文件
		.map(file => ({
			fileName: file.name || '',
			filePath: file.url || '',
			fileType: file.name?.split('.').pop()?.toLowerCase() || '',
			fileSize: file.size || 0
		}));
}



// 提交表单
async function handleSubmitCreate() {
	if (!projectId.value || !noticeId.value) {
		ElMessage.error('缺少必要的项目信息，请刷新页面重试');
		return;
	}

	createFormRef.value?.validate(async (valid) => {
		if (valid) {
			submitLoading.value = true;
			try {
				const submitData = {
					projectId: Number(projectId.value),
					noticeId: Number(noticeId.value),
					clarifyTitle: createForm.title,
					clarifyContent: createForm.content,
					attachmentList: formatAttachments(createForm.attachments)
				};

				console.log('提交澄清公告数据:', submitData);

				await createClarifyNotice(submitData);

				ElMessage.success('澄清公告创建成功');
				closeCreateDrawer();
				// 刷新列表数据
				await loadClarificationList();
			} catch (error) {
				console.error('创建澄清公告失败:', error);
				ElMessage.error('创建澄清公告失败，请重试');
			} finally {
				submitLoading.value = false;
			}
		}
	});
}

// 组件挂载时初始化
onMounted(() => {
	// 延迟加载，确保store数据已经初始化
	setTimeout(() => {
		loadClarificationList();
	}, 100);
});

// 组件卸载时销毁编辑器
onBeforeUnmount(() => {
	const editor = editorRef.value;
	if (editor == null) return;
	editor.destroy();
});
</script>

<style scoped lang="scss">
@import '../../../styles/collapse-panel.scss';

.procurement-announcement {
	padding: 20px;
	margin-bottom: 12px;
	flex: 1;

	.procurement-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 20px;

		.procurement-header-title {
			font-size: 16px;
			color: var(--Color-Text-text-color-primary, #1d2129);
			font-family: 'PingFang SC';
			font-style: normal;
			font-weight: 600;
			line-height: 24px;
		}

		.procurement-header-button {
			margin-left: 20px;
		}
	}

	.procurement-table {
		.table-actions {
			display: flex;
			gap: 8px;
			align-items: center;
			justify-content: center;
		}
	}


	.drawer-footer {
		display: flex;
		justify-content: flex-end;
		gap: 12px;
	}

	// 内容预览样式
	.content-preview {
		max-height: 60px;
		overflow: hidden;
		text-overflow: ellipsis;
		display: -webkit-box;
		-webkit-line-clamp: 3;
		-webkit-box-orient: vertical;

		:deep(p) {
			margin: 0;
			line-height: 1.4;
		}

		:deep(img) {
			display: none;
		}
	}

	// 附件下载供应商抽屉样式
	.download-info-content {
		.download-info-table {
			:deep(.el-table__header) {
				th {
					background-color: #f5f7fa;
				}
			}
		}
	}
}


// 可编辑表格样式
.editable-table {
  :deep(.el-table__header) {
    th {
      padding: 8px 0;
      .cell {
        color: var(--Color-Text-text-color-primary, #1d2129);
        font-family: 'PingFang SC';
        font-size: 14px;
        font-style: normal;
        font-weight: 500;
        line-height: 22px;
      }
    }
  }

  :deep(.el-table__body) {
    tr {
      td {
        padding: 8px 0;
      }
    }
  }
}
</style>
