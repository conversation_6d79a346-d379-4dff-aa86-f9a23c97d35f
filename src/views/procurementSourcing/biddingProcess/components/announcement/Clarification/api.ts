/**
 * 澄清公告相关API
 */
import request from '@/utils/request';

// API路径前缀
const API_PREFIX = '/admin/negotiationDoc/clarifyNotice';

/**
 * 澄清公告新增/更新接口
 * @param data 澄清公告数据
 */
export interface ClarifyNoticeData {
  projectId: number; // 采购立项ID
  noticeId: number; // 招标公告ID
  clarifyTitle: string; // 澄清公告标题
  clarifyContent: string; // 澄清公告内容
  attachmentList?: AttachmentInfo[]; // 附件信息列表
}

export interface AttachmentInfo {
  fileName: string; // 文件名称
  filePath: string; // 文件路径
  fileType: string; // 文件类型
  fileSize: number; // 文件大小(字节)
}

export function createClarifyNotice(data: ClarifyNoticeData) {
  return request({
    url: `${API_PREFIX}/upsert`,
    method: 'post',
    data
  });
}

/**
 * 澄清公告列表数据类型
 */
export interface ClarifyNoticeItem {
  id?: number;
  tenantId?: number;
  deptId?: number;
  projectId?: number;
  noticeId?: number;
  clarifyTitle?: string;
  clarifyContent?: string;
  approveStatus?: string;
  publishNoticeTime?: string;
  publishOwnerId?: number;
  publishOwner?: string;
  delFlag?: number;
  createBy?: string;
  createByName?: string;
  createTime?: string;
  updateBy?: string;
  updateByName?: string;
  updateTime?: string;
  attachmentDownloadSupplierCount?: number;
  traceId?: string;
}

/**
 * 获取澄清公告列表
 * @param data 查询参数
 */
export function getClarifyNoticeList(data: {
  projectId: number;
  noticeId: number;
}) {
  return request({
    url: `${API_PREFIX}/list`,
    method: 'post',
    data
  });
}

/**
 * 获取澄清公告详情
 * @param id 澄清公告ID
 */
export function getClarifyNoticeDetail(id: string) {
  return request({
    url: `${API_PREFIX}/detail/${id}`,
    method: 'get'
  });
}

/**
 * 删除澄清公告
 * @param id 澄清公告ID
 */
export function deleteClarifyNotice(id: string) {
  return request({
    url: `${API_PREFIX}/delete/${id}`,
    method: 'delete'
  });
}

/**
 * 附件下载供应商信息类型
 */
export interface SupplierDownloadInfo {
  tenantId?: number;
  deptId?: number;
  tenantSupplierId?: number;
  supplierCode?: string;
  supplierName?: string;
  clarifyNoticeId?: number;
  downloadUserId?: string;
  downloadUserName?: string;
  lastDownloadTime?: string;
  downloadCount?: number;
  traceId?: string;
}

/**
 * 获取附件下载供应商信息
 * @param clarifyNoticeId 澄清公告ID
 * @param supplierName 供应商名称(可选，模糊查询)
 */
export function getSupplierDownloadInfo(clarifyNoticeId: number, supplierName?: string) {
  return request({
    url: `${API_PREFIX}/supplier/downloadInfo`,
    method: 'get',
    params: {
      clarifyNoticeId,
      supplierName
    }
  });
}
