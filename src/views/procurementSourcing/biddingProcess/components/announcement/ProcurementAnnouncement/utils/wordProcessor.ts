import Docxtemplater from 'docxtemplater';
import <PERSON><PERSON><PERSON><PERSON> from 'pizzip';
import { saveAs } from 'file-saver';
import type { WordTemplate, TemplateData, DocumentGenerationOptions, TemplateVariable } from '../types';

/**
 * Word文档处理工具类
 */
export class WordProcessor {
  /**
   * 解析Word模板，提取模板变量
   */
  static async extractTemplateVariables(file: File): Promise<TemplateVariable[]> {
    try {
      const arrayBuffer = await file.arrayBuffer();
      const zip = new PizZip(arrayBuffer);
      const doc = new Docxtemplater(zip, {
        paragraphLoop: true,
        linebreaks: true,
      });

      // 获取模板中的所有变量
      const tags = doc.getFullText().match(/\{[^}]+\}/g) || [];
      const uniqueTags = [...new Set(tags)];
      
      const variables: TemplateVariable[] = uniqueTags.map(tag => {
        const key = tag.replace(/[{}]/g, '');
        return {
          key,
          label: this.generateLabelFromKey(key),
          type: this.inferTypeFromKey(key),
          required: true,
          description: `模板变量: ${key}`,
        };
      });

      return variables;
    } catch (error) {
      console.error('解析模板变量失败:', error);
      throw new Error('解析模板变量失败');
    }
  }

  /**
   * 根据模板和数据生成Word文档
   */
  static async generateDocument(options: DocumentGenerationOptions): Promise<Blob> {
    try {
      const { template, data, outputFileName } = options;
      
      // 获取模板文件
      const response = await fetch(template.fileUrl);
      const arrayBuffer = await response.arrayBuffer();
      
      const zip = new PizZip(arrayBuffer);
      const doc = new Docxtemplater(zip, {
        paragraphLoop: true,
        linebreaks: true,
      });

      // 处理数据，确保所有模板变量都有值
      const processedData = this.processTemplateData(data, template.variables);
      
      // 渲染文档
      doc.render(processedData);
      
      // 生成文档
      const output = doc.getZip().generate({
        type: 'blob',
        mimeType: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      });

      return output;
    } catch (error) {
      console.error('生成Word文档失败:', error);
      throw new Error('生成Word文档失败');
    }
  }

  /**
   * 下载生成的文档
   */
  static downloadDocument(blob: Blob, fileName: string = '采购公告.docx'): void {
    saveAs(blob, fileName);
  }

  /**
   * 验证模板数据完整性
   */
  static validateTemplateData(data: TemplateData, variables: TemplateVariable[]): {
    isValid: boolean;
    missingFields: string[];
    errors: string[];
  } {
    const missingFields: string[] = [];
    const errors: string[] = [];

    variables.forEach(variable => {
      if (variable.required && !data[variable.key]) {
        missingFields.push(variable.key);
      }

      // 类型验证
      if (data[variable.key] !== undefined) {
        const isValidType = this.validateFieldType(data[variable.key], variable.type);
        if (!isValidType) {
          errors.push(`字段 ${variable.key} 类型不匹配，期望类型: ${variable.type}`);
        }
      }
    });

    return {
      isValid: missingFields.length === 0 && errors.length === 0,
      missingFields,
      errors,
    };
  }

  /**
   * 处理模板数据，确保格式正确
   */
  private static processTemplateData(data: TemplateData, variables: TemplateVariable[]): TemplateData {
    const processedData: TemplateData = { ...data };

    variables.forEach(variable => {
      const value = processedData[variable.key];
      
      if (value === undefined || value === null) {
        processedData[variable.key] = variable.defaultValue || '';
        return;
      }

      // 根据类型处理数据
      switch (variable.type) {
        case 'date':
          processedData[variable.key] = this.formatDate(value);
          break;
        case 'number':
          processedData[variable.key] = this.formatNumber(value);
          break;
        case 'array':
          processedData[variable.key] = Array.isArray(value) ? value : [];
          break;
        default:
          processedData[variable.key] = String(value);
      }
    });

    return processedData;
  }

  /**
   * 从变量名生成显示标签
   */
  private static generateLabelFromKey(key: string): string {
    const labelMap: Record<string, string> = {
      announcementTitle: '公告标题',
      announcementNumber: '公告编号',
      publishDate: '发布日期',
      deadline: '截止日期',
      purchaserName: '采购方名称',
      purchaserAddress: '采购方地址',
      contactPerson: '联系人',
      contactPhone: '联系电话',
      contactEmail: '联系邮箱',
      projectName: '项目名称',
      projectDescription: '项目描述',
      projectAddress: '项目地址',
      budgetAmount: '预算金额',
      currency: '币种',
      participationFee: '参与费用',
      guaranteeDeposit: '保证金',
      evaluationCriteria: '评审标准',
      specialTerms: '特殊条款',
      legalNotices: '法律声明',
    };

    return labelMap[key] || key;
  }

  /**
   * 从变量名推断数据类型
   */
  private static inferTypeFromKey(key: string): TemplateVariable['type'] {
    if (key.includes('Date') || key.includes('Time')) return 'date';
    if (key.includes('Amount') || key.includes('Fee') || key.includes('Deposit')) return 'number';
    if (key.includes('Requirements') || key.includes('Items')) return 'array';
    return 'text';
  }

  /**
   * 验证字段类型
   */
  private static validateFieldType(value: any, expectedType: TemplateVariable['type']): boolean {
    switch (expectedType) {
      case 'text':
        return typeof value === 'string';
      case 'number':
        return typeof value === 'number' && !isNaN(value);
      case 'date':
        return value instanceof Date || typeof value === 'string';
      case 'array':
        return Array.isArray(value);
      case 'image':
        return typeof value === 'string' || value instanceof File;
      default:
        return true;
    }
  }

  /**
   * 格式化日期
   */
  private static formatDate(value: any): string {
    if (value instanceof Date) {
      return value.toLocaleDateString('zh-CN');
    }
    if (typeof value === 'string') {
      const date = new Date(value);
      return isNaN(date.getTime()) ? value : date.toLocaleDateString('zh-CN');
    }
    return String(value);
  }

  /**
   * 格式化数字
   */
  private static formatNumber(value: any): string {
    const num = Number(value);
    return isNaN(num) ? '0' : num.toLocaleString('zh-CN');
  }

  /**
   * 生成默认的采购公告模板变量
   */
  static getDefaultProcurementVariables(): TemplateVariable[] {
    return [
      { key: 'announcementTitle', label: '公告标题', type: 'text', required: true },
      { key: 'announcementNumber', label: '公告编号', type: 'text', required: true },
      { key: 'publishDate', label: '发布日期', type: 'date', required: true },
      { key: 'deadline', label: '截止日期', type: 'date', required: true },
      { key: 'purchaserName', label: '采购方名称', type: 'text', required: true },
      { key: 'purchaserAddress', label: '采购方地址', type: 'text', required: true },
      { key: 'contactPerson', label: '联系人', type: 'text', required: true },
      { key: 'contactPhone', label: '联系电话', type: 'text', required: true },
      { key: 'contactEmail', label: '联系邮箱', type: 'text', required: false },
      { key: 'projectName', label: '项目名称', type: 'text', required: true },
      { key: 'projectDescription', label: '项目描述', type: 'text', required: true },
      { key: 'projectAddress', label: '项目地址', type: 'text', required: true },
      { key: 'budgetAmount', label: '预算金额', type: 'number', required: true },
      { key: 'currency', label: '币种', type: 'text', required: true, defaultValue: '人民币' },
      { key: 'qualificationRequirements', label: '资格要求', type: 'array', required: false },
      { key: 'quotationRequirements', label: '报价要求', type: 'array', required: false },
      { key: 'registrationStart', label: '报名开始时间', type: 'date', required: true },
      { key: 'registrationEnd', label: '报名结束时间', type: 'date', required: true },
      { key: 'submissionDeadline', label: '提交截止时间', type: 'date', required: true },
      { key: 'openingTime', label: '开标时间', type: 'date', required: true },
      { key: 'participationFee', label: '参与费用', type: 'number', required: false, defaultValue: 0 },
      { key: 'guaranteeDeposit', label: '保证金', type: 'number', required: false, defaultValue: 0 },
      { key: 'evaluationCriteria', label: '评审标准', type: 'text', required: true },
      { key: 'specialTerms', label: '特殊条款', type: 'text', required: false },
      { key: 'legalNotices', label: '法律声明', type: 'text', required: false },
    ];
  }
} 