<template>
    <div class="h-full">
        <yun-pro-table
          :auto-height="true"
          :table-columns="tableColumns"
          :remote-method="remoteMethod"
          :layout="'whole'"
          :table-props="{
				    showTableSetting: true,
			    }"
        >
            <template #t_action="{ row }">
                <el-button type="primary" @click="handleEdit(row)" >
                    修改
                </el-button>
            </template>
        </yun-pro-table>
    </div>
    <div>
       <el-drawer
        v-model="isDrawerOpen"
        size="80%"
      >
        <template #title>
          <span>当前模板: {{ wordStateRef.currentTemplate?.name || '' }}</span>
        </template>
          <RichTextEditor
            v-model="wordStateRef.editorContent"
            :variables="wordStateRef.currentTemplate?.variables || []"
            placeholder="请编辑模板内容..."
            @save="handleSave"
          />
      </el-drawer>
    </div>
</template>

<script lang="ts" setup>

import { ref } from 'vue';
import { WordTemplate } from '../types';
import RichTextEditor from './RichTextEditor.vue';
import { useWordState } from '/@/stores/wordState';
import { storeToRefs } from 'pinia';


const isDrawerOpen = ref(false);
const wordState = useWordState();
const { wordState:wordStateRef } = storeToRefs(wordState);



const handleEdit = () => {
  isDrawerOpen.value = true;
  // 获取服务端
  console.log(wordStateRef.value.currentTemplate, 'props.templates');
  
}

const handleSave = (content: string) => {
  // 保存服务端
  wordStateRef.value.editorContent = content;
}


const tableColumns = ref([
  {
    label: '默认名称',
    prop: 'name',
  },
  {
    label: '描述',
    prop: 'description',
  },
  {
    label: '操作',
    prop: 'action',
  }
])

const remoteMethod = async ({ searchData, filterData, pagination }) => {
  return await new Promise((resolve, reject) => {
    setTimeout(() => {
      resolve(wordStateRef.value.templates)
    }, 2000)
  })
}


</script>

<style lang="scss" scoped>

</style>