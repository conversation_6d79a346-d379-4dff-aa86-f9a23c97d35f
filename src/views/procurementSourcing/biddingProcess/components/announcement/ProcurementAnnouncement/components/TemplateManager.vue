<template>
  <div class="template-manager">

    <!-- 模板列表 -->
    <div class="template-list">
      <el-table :data="templates" style="width: 100%" v-loading="loading">
        <el-table-column prop="name" label="模板名称" min-width="200">
          <template #default="{ row }">
            <div class="template-name">
              <el-icon class="template-icon"><Document /></el-icon>
              <span>{{ row.name }}</span>
              <el-tag v-if="row.id === currentTemplateId" type="success" size="small">
                当前使用
              </el-tag>
            </div>
          </template>
        </el-table-column>
        
        <el-table-column prop="description" label="描述" show-overflow-tooltip />
        
        <el-table-column prop="variables" label="变量数量" width="100">
          <template #default="{ row }">
            <el-tag type="info" size="small">{{ row.variables.length }}</el-tag>
          </template>
        </el-table-column>
        
        <el-table-column prop="updatedAt" label="更新时间" width="180">
          <template #default="{ row }">
            {{ formatDate(row.updatedAt) }}
          </template>
        </el-table-column>
        
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button-group>
              <el-button size="small" @click="useTemplate(row)" :icon="Check">
                使用
              </el-button>
              <el-button size="small" @click="editTemplate(row)" :icon="Edit">
                编辑
              </el-button>
              <el-button 
                size="small" 
                type="danger" 
                @click="deleteTemplate(row)"
                :icon="Delete"
              >
                删除
              </el-button>
            </el-button-group>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 上传模板对话框 -->
    <el-dialog
      v-model="uploadDialogVisible"
      title="上传Word模板"
      width="600px"
    >
      <el-form :model="uploadForm" :rules="uploadRules" ref="uploadFormRef" label-width="100px">
        <el-form-item label="模板名称" prop="name">
          <el-input v-model="uploadForm.name" placeholder="请输入模板名称" />
        </el-form-item>
        
        <el-form-item label="模板描述" prop="description">
          <el-input
            v-model="uploadForm.description"
            type="textarea"
            :rows="3"
            placeholder="请输入模板描述"
          />
        </el-form-item>
        
        <el-form-item label="模板文件" prop="file">
          <el-upload
            ref="uploadRef"
            :auto-upload="false"
            :show-file-list="true"
            :limit="1"
            accept=".docx,.doc"
            :on-change="handleFileChange"
            :on-remove="handleFileRemove"
          >
            <el-button :icon="Upload">选择Word文件</el-button>
            <template #tip>
              <div class="el-upload__tip">
                只能上传.docx或.doc文件，且不超过10MB
              </div>
            </template>
          </el-upload>
        </el-form-item>
        
        <el-form-item v-if="extractedVariables.length > 0" label="模板变量">
          <div class="variable-list">
            <el-tag
              v-for="variable in extractedVariables"
              :key="variable.key"
              type="info"
              class="variable-tag"
            >
              {{ variable.label }} ({{ variable.key }})
            </el-tag>
          </div>
        </el-form-item>
      </el-form>
      
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="uploadDialogVisible = false">取消</el-button>
          <el-button 
            type="primary" 
            @click="confirmUpload"
            :loading="uploading"
          >
            上传
          </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 编辑模板对话框 -->
    <el-drawer
      v-model="editDrawerVisible"
      title="编辑模板"
      size="80%"
    >
      <el-form :model="editForm" label-width="100px">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="模板名称">
              <el-input v-model="editForm.name" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="模板描述">
              <el-input v-model="editForm.description" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      
      <!-- 变量管理 -->
      <div class="variable-manager">
        <div class="flex gap-[1rem]">
          <h5>模板变量管理</h5>
          <el-button type="plain" size="small" @click="addVariable" :icon="Plus">
            添加变量
          </el-button>
        </div>
        <el-table :data="editForm.variables" style="width: 100%">
          <el-table-column prop="key" label="变量名" width="150" >
              <template #default="{ row, $index }">
              <el-input 
                v-model="row.key" 
                size="small"
                class="variable-key-input"
                @change="updateVariable($index, 'key', row.key)"
              />
            </template>
          </el-table-column>
          <el-table-column prop="label" label="显示名称" width="150">
            <template #default="{ row, $index }">
              <el-input 
                v-model="row.label" 
                size="small"
                @change="updateVariable($index, 'label', row.label)"
              />
            </template>
          </el-table-column>
          <el-table-column prop="type" label="类型" width="120">
            <template #default="{ row, $index }">
              <el-select 
                v-model="row.type" 
                size="small"
                @change="updateVariable($index, 'type', row.type)"
              >
                <el-option label="文本" value="text" />
                <el-option label="数字" value="number" />
                <el-option label="日期" value="date" />
                <el-option label="数组" value="array" />
                <el-option label="图片" value="image" />
              </el-select>
            </template>
          </el-table-column>
          <el-table-column prop="required" label="必填" width="80">
            <template #default="{ row, $index }">
              <el-switch 
                v-model="row.required"
                @change="updateVariable($index, 'required', row.required)"
              />
            </template>
          </el-table-column>
          <el-table-column prop="defaultValue" label="默认值">
            <template #default="{ row, $index }">
              <el-input 
                v-model="row.defaultValue" 
                size="small"
                @change="updateVariable($index, 'defaultValue', row.defaultValue)"
              />
            </template>
          </el-table-column>
          <el-table-column prop="description" label="描述">
            <template #default="{ row, $index }">
              <el-input 
                v-model="row.description" 
                size="small"
                @change="updateVariable($index, 'description', row.description)"
              />
            </template>
          </el-table-column>
          <el-table-column label="操作" width="120">
            <template #default="{ row, $index }">
              <el-button
                size="small"
                type="danger"
                @click="removeVariable($index)"
                :icon="Delete"
                circle
              >
              </el-button>
            </template>
          </el-table-column>
        </el-table>
        <div class="w-full">
          <el-button class="w-full" type="plain" size="medium" @click="addVariable" :icon="Plus">
            添加变量
          </el-button>
        </div>
      </div>
      
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="editDrawerVisible = false">取消</el-button>
          <el-button 
            type="primary" 
            @click="confirmEdit"
            :loading="editing"
          >
            保存
          </el-button>
        </span>
      </template>
    </el-drawer>
    <el-dialog
      v-model="addVariableDialogVisible"
      title="添加变量"
      width="400px"
    >
      <el-form :model="newVariableForm" :rules="variableRules" ref="variableFormRef">
        <el-form-item label="变量名" prop="key">
          <el-input v-model="newVariableForm.key" placeholder="请输入变量名" />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="addVariableDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="confirmAddVariable">确定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, nextTick, onMounted } from 'vue';
import { ElMessage, ElMessageBox } from 'yun-design';
import { Upload, Plus, Document, Check, Edit, Delete } from '@element-plus/icons-vue';
import type { UploadFile, FormInstance } from 'element-plus';
import type { WordTemplate, TemplateVariable } from '../types';
import { WordProcessor } from '../utils/wordProcessor';

interface Props {
  templates: WordTemplate[];
  currentTemplateId?: string;
  loading?: boolean;
}

interface Emits {
  (e: 'template-uploaded', template: WordTemplate): void;
  (e: 'template-updated', template: WordTemplate): void;
  (e: 'template-deleted', templateId: string): void;
  (e: 'template-selected', template: WordTemplate): void;
  (e: 'refresh'): void;
}

const props = withDefaults(defineProps<Props>(), {
  templates: () => [],
  loading: false,
});

const emit = defineEmits<Emits>();

// 上传相关
const uploadDialogVisible = ref(false);
const uploadFormRef = ref<FormInstance>();
const uploadRef = ref();
const uploading = ref(false);

const uploadForm = ref({
  name: '',
  description: '',
  file: null as File | null,
});

const addVariableDialogVisible = ref(false);
const variableFormRef = ref<FormInstance>();
const newVariableForm = ref({
  key: ''
});

const variableRules = {
  key: [{ required: true, message: '请输入变量名', trigger: 'blur' }]
};

const uploadRules = {
  name: [{ required: true, message: '请输入模板名称', trigger: 'blur' }],
  file: [{ required: true, message: '请选择模板文件', trigger: 'change' }],
};

const extractedVariables = ref<TemplateVariable[]>([]);

// 编辑相关
const editDrawerVisible = ref(false);
const editing = ref(false);
const editForm = ref<WordTemplate>({
  id: '',
  name: '',
  description: '',
  fileName: '',
  fileUrl: '',
  variables: [],
  createdAt: '',
  updatedAt: '',
});

const addVariable = () => {
  addVariableDialogVisible.value = true;
  newVariableForm.value.key = '';
};

const removeVariable = (index: number) => {
  editForm.value.variables.splice(index, 1); 
}

const confirmAddVariable = () => {
  if (!variableFormRef.value) return;
  
  variableFormRef.value.validate((valid) => {
    if (valid) {
      const newVar: TemplateVariable = {
        key: newVariableForm.value.key,
        label: '',
        type: 'text',
        required: false,
        defaultValue: '',
        description: '',
      };
      
      editForm.value.variables.push(newVar);
      addVariableDialogVisible.value = false;
    }
  });
};

// 文件变化处理
async function handleFileChange(file: UploadFile) {
  if (!file.raw) return;
  
  const maxSize = 10 * 1024 * 1024; // 10MB
  if (file.raw.size > maxSize) {
    ElMessage.error('文件大小不能超过10MB');
    return;
  }

  uploadForm.value.file = file.raw;
  
  // 自动填充模板名称
  if (!uploadForm.value.name) {
    const fileName = file.name.replace(/\.(docx?|doc)$/i, '');
    uploadForm.value.name = fileName;
  }

  // 提取模板变量
  try {
    const variables = await WordProcessor.extractTemplateVariables(file.raw);
    extractedVariables.value = variables;
    ElMessage.success(`成功提取${variables.length}个模板变量`);
  } catch (error) {
    console.error('提取模板变量失败:', error);
    ElMessage.warning('无法提取模板变量，请手动配置');
    extractedVariables.value = [];
  }
}

function handleFileRemove() {
  uploadForm.value.file = null;
  extractedVariables.value = [];
}

// 确认上传
async function confirmUpload() {
  if (!uploadFormRef.value) return;
  
  try {
    await uploadFormRef.value.validate();
    
    uploading.value = true;
    
    // 模拟上传到服务器
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    const newTemplate: WordTemplate = {
      id: Date.now().toString(),
      name: uploadForm.value.name,
      description: uploadForm.value.description,
      fileName: uploadForm.value.file?.name || '',
      fileUrl: URL.createObjectURL(uploadForm.value.file!),
      variables: extractedVariables.value,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };
    
    emit('template-uploaded', newTemplate);
    ElMessage.success('模板上传成功');
    
    // 重置表单
    uploadForm.value = { name: '', description: '', file: null };
    extractedVariables.value = [];
    uploadFormRef.value.resetFields();
    uploadDialogVisible.value = false;
  } catch (error) {
    console.error('上传模板失败:', error);
    ElMessage.error('上传模板失败');
  } finally {
    uploading.value = false;
  }
}

// 新建模板
function createNewTemplate() {
  const newTemplate: WordTemplate = {
    id: Date.now().toString(),
    name: '新建模板',
    description: '',
    fileName: '',
    fileUrl: '',
    variables: WordProcessor.getDefaultProcurementVariables(),
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  };
  
  emit('template-uploaded', newTemplate);
  editTemplate(newTemplate);
}

// 使用模板
function useTemplate(template: WordTemplate) {
  emit('template-selected', template);
  ElMessage.success(`已切换到模板：${template.name}`);
}

// 编辑模板
function editTemplate(template: WordTemplate) {
  editForm.value = { ...template };
  editDrawerVisible.value = true;
}

// 更新变量
function updateVariable(index: number, field: keyof TemplateVariable, value: any) {
  if (editForm.value.variables[index]) {
    (editForm.value.variables[index] as any)[field] = value;
  }
}

// 确认编辑
async function confirmEdit() {
  try {
    editing.value = true;
    
    const updatedTemplate = {
      ...editForm.value,
      updatedAt: new Date().toISOString(),
    };
    
    emit('template-updated', updatedTemplate);
    ElMessage.success('模板更新成功');
    editDrawerVisible.value = false;
  } catch (error) {
    console.error('更新模板失败:', error);
    ElMessage.error('更新模板失败');
  } finally {
    editing.value = false;
  }
}

// 删除模板
async function deleteTemplate(template: WordTemplate) {
  try {
    await ElMessageBox.confirm(
      `确定要删除模板"${template.name}"吗？此操作不可撤销。`,
      '删除确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    );
    
    emit('template-deleted', template.id);
    ElMessage.success('模板删除成功');
  } catch {
    // 用户取消删除
  }
}

// 格式化日期
function formatDate(dateString: string): string {
  return new Date(dateString).toLocaleString('zh-CN');
}

onMounted(() => {
  if (props.templates.length === 0) {
    emit('refresh');
  }
});
</script>

<style lang="scss" scoped>
.template-manager {
  .manager-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 20px;
    padding-bottom: 16px;
    border-bottom: 1px solid #e8e8e8;

    .header-left {
      h4 {
        margin: 0 0 4px 0;
        font-size: 16px;
        font-weight: 600;
        color: #1d2129;
      }

      p {
        margin: 0;
        font-size: 14px;
        color: #86909c;
      }
    }

    .header-right {
      display: flex;
      gap: 12px;
    }
  }

  .template-list {
    .template-name {
      display: flex;
      align-items: center;
      gap: 8px;

      .template-icon {
        color: #1890ff;
      }
    }
  }

  .variable-list {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;

    .variable-tag {
      margin: 0;
    }
  }

  .variable-manager {
    margin-top: 24px;
    padding-top: 16px;
    border-top: 1px solid #e8e8e8;

    h5 {
      margin: 0 0 16px 0;
      font-size: 14px;
      font-weight: 600;
      color: #1d2129;
    }
  }
}

:deep(.el-upload__tip) {
  margin-top: 8px;
  font-size: 12px;
  color: #86909c;
}

:deep(.el-table) {
  .el-input,
  .el-select {
    width: 100%;
  }
}
</style> 