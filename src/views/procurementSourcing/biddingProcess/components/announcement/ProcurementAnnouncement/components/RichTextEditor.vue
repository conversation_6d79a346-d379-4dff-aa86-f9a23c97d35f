<template>
  <div class="rich-text-editor">
    <div class="editor-toolbar">
      <el-button-group>
        <el-button size="small" @click="insertVariable" :icon="Plus">
          插入变量
        </el-button>
        <el-button size="small" @click="insertLoopVariable" :icon="Operation">
          插入循环变量
        </el-button>
        <el-button size="small" @click="previewTemplate" :icon="View">
          预览模板
        </el-button>
        <!-- <el-button size="small" @click="saveTemplate" :icon="Document">
          保存模板
        </el-button> -->
      </el-button-group>
    </div>

    <div class="editor-container" @dragover.prevent @drop="onDrop">
      <Toolbar
        style="border-bottom: 1px solid #ccc"
        :editor="editorRef"
        :defaultConfig="toolbarConfig"
        mode="default"
      />
      <Editor
        style="height: 400px; overflow-y: hidden"
        v-model="content"
        :defaultConfig="editorConfig"
        mode="default"
        @onCreated="handleCreated"
        @onChange="handleChange"
      />
    </div>

    <!-- 变量插入对话框 -->
    <el-dialog
      v-model="variableDialogVisible"
      title="插入模板变量"
      width="600px"
      lock-scroll
      :modal="true"
      :modal-append-to-body="true"
      :append-to-body="true"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :show-close="true"
      :destroy-on-close="false"
    >
      <el-form :model="variableForm" label-width="100px">
        <el-form-item label="选择变量">
          <el-select
            v-model="variableForm.selectedVariable"
            placeholder="请选择要插入的变量"
            style="width: 100%"
          >
            <el-option
              v-for="variable in availableVariables"
              :key="variable.key"
              :label="variable.label"
              :value="variable.key"
            >
              <span>{{ variable.label }}</span>
              <span style="color: #8492a6; font-size: 13px"> ({{ variable.key }}) </span>
            </el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="显示格式">
          <el-radio-group v-model="variableForm.format">
            <el-radio label="simple">简单格式: {变量名}</el-radio>
            <el-radio label="label">带标签: 标签名：{变量名}</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="variableDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="confirmInsertVariable"> 插入 </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 循环变量插入对话框 -->
    <el-dialog
      v-model="loopVariableDialogVisible"
      title="插入循环变量"
      width="700px"
      lock-scroll
      :modal="true"
      :modal-append-to-body="true"
      :append-to-body="true"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :show-close="true"
      :destroy-on-close="false"
    >
      <el-form :model="loopVariableForm" label-width="120px">
        <el-form-item label="选择数组变量">
          <el-select
            v-model="loopVariableForm.selectedVariable"
            placeholder="请选择要循环的数组变量"
            style="width: 100%"
            @change="handleArrayVariableChange"
          >
            <el-option
              v-for="variable in arrayVariables"
              :key="variable.key"
              :label="variable.label"
              :value="variable.key"
            >
              <span>{{ variable.label }}</span>
              <span style="color: #8492a6; font-size: 13px"> ({{ variable.key }}) </span>
            </el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="循环模板类型">
          <el-radio-group v-model="loopVariableForm.templateType">
            <el-radio label="simple">简单循环</el-radio>
            <el-radio label="table">表格行循环</el-radio>
            <el-radio label="list">列表循环</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item label="预览模板">
          <el-input
            v-model="loopVariableForm.previewTemplate"
            type="textarea"
            :rows="6"
            placeholder="将根据选择的变量和模板类型自动生成..."
            readonly
          />
        </el-form-item>
      </el-form>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="loopVariableDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="confirmInsertLoopVariable">
            插入循环模板
          </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 模板预览对话框 -->
    <el-dialog
      v-model="previewDialogVisible"
      title="模板预览"
      width="80%"
      top="5vh"
      lock-scroll
      :modal="true"
      :modal-append-to-body="true"
      :append-to-body="true"
      :close-on-click-modal="true"
      :close-on-press-escape="true"
      :show-close="true"
      :destroy-on-close="false"
    >
      <div class="template-preview editor-content-view" v-html="previewContent"></div>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="previewDialogVisible = false">关闭</el-button>
          <el-button type="primary" @click="exportPreview"> 导出预览 </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onBeforeUnmount, shallowRef, watch } from 'vue';
import { Editor, Toolbar } from '@wangeditor/editor-for-vue';
import '@wangeditor/editor/dist/css/style.css';
import { ElMessage } from 'yun-design';
import { Plus, View, Operation } from '@element-plus/icons-vue';
import type { IDomEditor, IEditorConfig, IToolbarConfig } from '@wangeditor/editor';
import type { TemplateVariable } from '../types';
import { replaceTemplate } from '@/utils/replaceTemplate';

interface Props {
  modelValue: string;
  variables?: TemplateVariable[];
  placeholder?: string;
  readonly?: boolean;
}

interface Emits {
  (e: 'update:modelValue', value: string): void;
  (e: 'save', content: string): void;
  (e: 'preview', content: string): void;
}

const props = withDefaults(defineProps<Props>(), {
  variables: () => [],
  placeholder: '请输入模板内容...',
  readonly: false,
});

const emit = defineEmits<Emits>();

// 编辑器实例
const editorRef = shallowRef<IDomEditor>();

// 编辑器内容
const content = computed({
  get: () => props.modelValue,
  set: (value: string) => emit('update:modelValue', value),
});

// 编辑器配置
const editorConfig: Partial<IEditorConfig> = {
  placeholder: props.placeholder,
  readOnly: props.readonly,
  autoFocus: false,
  scroll: true,
  MENU_CONF: {
    uploadImage: {
      server: '/api/upload',
      fieldName: 'file',
    },
  },
};

// 工具栏配置
const toolbarConfig: Partial<IToolbarConfig> = {
  excludeKeys: ['group-video', 'todo', 'emotion', 'codeBlock', 'divider'], // 'insertTable',
};

// 变量相关
const variableDialogVisible = ref(false);
const variableForm = ref({
  selectedVariable: '',
  format: 'simple',
});

const availableVariables = computed(() => props.variables);

// 预览相关
const previewDialogVisible = ref(false);
const previewContent = ref('');

// 循环变量相关
const loopVariableDialogVisible = ref(false);
const loopVariableForm = ref({
  selectedVariable: '',
  templateType: 'simple',
  previewTemplate: '',
});

const arrayVariables = computed(() => props.variables.filter((v) => v.type === 'array'));

function createSimpleCode(variable: string): string {
  const paths = variable.split('.').filter(Boolean);
  if (!paths.length) return '';

  // 中文序号
  const levelNames = ['一', '二', '三', '四', '五', '六', '七', '八', '九', '十'];

  // 只有一个字段，直接循环
  if (paths.length === 1) {
    return `<strong>一级标题</strong>\n<p>#foreach((item, index) of ${paths[0]})</p>\n  <p>子标题{index}: {item}</p>\n<p>#end</p>\n`;
  }

  // 多字段，循环到倒数第二层
  let result = '';
  for (let i = 0; i < paths.length - 1; i++) {
    const varName = i === 0 ? 'item' : `item${i}`;
    const idxName = i === 0 ? 'index' : `index${i}`;
    const arrName = i === 0 ? paths[0] : `${'item' + (i - 1 ? i - 1 : '')}.${paths[i]}`;
    // 标题
    if (i === 0) {
      result += `<strong>${levelNames[i] || i + 1}级标题</strong>\n`;
    }
    // 只到倒数第二层才输出"n级标题"，最后一层循环体内不再输出标题
    if (i < paths.length - 2) {
      result += `${'  '.repeat(
        i
      )}<p>#foreach((${varName}, ${idxName}) of ${arrName})</p>\n`;
      result += `${'  '.repeat(i + 1)}<p><strong>${
        levelNames[i + 1] || i + 2
      }级标题{${idxName}}:</strong></p>\n`;
    } else {
      // 最后一层循环
      result += `${'  '.repeat(
        i
      )}<p>#foreach((${varName}, ${idxName}) of ${arrName})</p>\n`;
      // 只输出子标题
      const lastField = paths[paths.length - 1];
      result += `${'  '.repeat(
        i + 1
      )}<p>子标题{${idxName}}: {${varName}.${lastField}}</p>\n`;
    }
  }
  // 闭合
  for (let i = paths.length - 2; i >= 0; i--) {
    result += `${'  '.repeat(i)}<p>#end</p>\n`;
  }
  return result;
}

function createListCode(variable: string): string {
  const paths = variable.split('.').filter(Boolean);
  if (!paths.length) return '';

  // a
  if (paths.length === 1) {
    return `<ol>
  <p>#foreach((item) of ${paths[0]})</p>
    <li><strong>{item}</strong></li>
  <p>#end</p>
</ol>`;
  }

  // a.b
  if (paths.length >= 2) {
    return `<ol>
  <p>#foreach((item) of ${paths[0]})</p>
    <li>
      <strong>{item.${paths[1]}}</strong>
    </li>
  <p>#end</p>
</ol>`;
  }

  // 其它情况
  return '仅支持一层或两层数据（如 a 或 a.b）';
}

// 处理数组变量变化的函数
function handleArrayVariableChange() {
  const { selectedVariable, templateType } = loopVariableForm.value;
  if (!selectedVariable) return;

  const variable = arrayVariables.value.find((v) => v.key === selectedVariable);
  if (!variable) return;

  let template = '';
  switch (templateType) {
    case 'simple':
      template = createSimpleCode(selectedVariable);
      break;
    case 'table': {
      const paths = selectedVariable.split('.').filter(Boolean);
      if (paths.length === 1) {
        // a
        template = `<p>#table((item, index) of ${paths[0]})</p>
<table style="width: 100%; border-collapse: collapse;">
  <thead>
    <tr>
      <th style="border: 1px solid #ddd; padding: 8px;">序号</th>
      <th style="border: 1px solid #ddd; padding: 8px;">内容</th>
    </tr>
  </thead>
  <tbody>
    <tr>
      <td style="border: 1px solid #ddd; padding: 8px;">{index}</td>
      <td style="border: 1px solid #ddd; padding: 8px;">{item}</td>
    </tr>
  </tbody>
</table>
<p>#end</p>`;
      } else if (paths.length >= 2) {
        // a.b
        template = `<p>#table((item, index) of ${paths[0]})</p>
<table style="width: 100%; border-collapse: collapse;">
  <thead>
    <tr>
      <th style="border: 1px solid #ddd; padding: 8px;">序号</th>
      <th style="border: 1px solid #ddd; padding: 8px;">内容</th>
    </tr>
  </thead>
  <tbody>
    <tr>
      <td style="border: 1px solid #ddd; padding: 8px;">{index}</td>
      <td style="border: 1px solid #ddd; padding: 8px;">{item.${paths[1]}}</td>
    </tr>
  </tbody>
</table>
<p>#end</p>`;
      }
      break;
    }
    case 'list': {
      template = createListCode(selectedVariable);
      break;
    }
    default:
      template = '';
  }
  loopVariableForm.value.previewTemplate = template;
}

// 监听循环变量表单变化，自动更新预览
watch(
  () => [loopVariableForm.value.selectedVariable, loopVariableForm.value.templateType],
  () => {
    if (loopVariableForm.value.selectedVariable) {
      handleArrayVariableChange();
    }
  }
);

// 编辑器生命周期
function handleCreated(editor: IDomEditor) {
  editorRef.value = editor;
}

function handleChange(editor: IDomEditor) {
  emit('update:modelValue', editor.getHtml());
}

// 添加 onDrop 方法
function onDrop(event: DragEvent) {
  event.preventDefault();
  if (!editorRef.value) return;

  const type = event.dataTransfer?.getData('application/x-custom-type');
  if (!type) return;

  let insertHtml = '';
  switch (type) {
    case 'string':
      insertHtml = `<div style="background-color: #e6f7ff; color: #1890ff; padding: 2px 4px; border-radius: 3px; font-weight: bold;" contenteditable="false"><projectName>字符串变量</projectName></div>&nbsp;`;
      break;
    case 'array':
      insertHtml =
        '<div class="loop-template-block" contenteditable="false" data-variable-type="array">\n  <div class="loop-placeholder">这是一个数组循环模板占位符，请替换为具体的循环结构，例如：</div>\n  <pre>{#数组变量}\n    &lt;p&gt;{index}. {item.属性名}&lt;/p&gt;\n{/数组变量}</pre>\n</div>';
      break;
    case 'link':
      insertHtml =
        '<a href="#" style="background-color: #e6f7ff; color: #1890ff; padding: 2px 4px; border-radius: 3px; font-weight: bold;" contenteditable="false"><text-href>超链接变量</text-href></a>&nbsp;';
      break;
    default:
      insertHtml = '<span style="color: #1890ff;">未知组件</span>&nbsp;';
      break;
  }

  try {
    editorRef.value.focus();
    editorRef.value.dangerouslyInsertHtml(insertHtml);
    ElMessage.success(`已插入 ${type} 组件`);
  } catch (error) {
    // console.error('插入组件失败:', error);
    ElMessage.error('插入组件失败，请重试');
  }
}

// 插入变量
function insertVariable() {
  if (availableVariables.value.length === 0) {
    ElMessage.warning('暂无可用的模板变量');
    return;
  }
  variableDialogVisible.value = true;
}

function confirmInsertVariable() {
  const { selectedVariable, format } = variableForm.value;

  if (!selectedVariable) {
    ElMessage.warning('请选择要插入的变量');
    return;
  }

  const variable = availableVariables.value.find((v) => v.key === selectedVariable);
  if (!variable) return;
  let insertText = '';
  if (format === 'simple') {
    insertText = '{' + variable.key + '}';
  } else {
    insertText = variable.label + '：{' + variable.key + '}';
  }

  let insertHtml = '';
  // 根据变量类型插入不同的HTML结构
  if (variable.type === 'string') {
    insertHtml = `<projectName contenteditable="false" data-variable-key="${variable.key}">${insertText}</projectName>&nbsp;`;
  } else if (variable.type === 'link') {
    insertHtml = `<text-href contenteditable="false" data-variable-key="${variable.key}">${insertText}</text-href>&nbsp;`;
  } else {
    insertHtml = insertText.replace(
      /\{([^}]+)\}/g,
      '<span class="auto-highlight" style="background-color: #e6f7ff; color: #1890ff; padding: 2px 4px; border-radius: 3px; font-weight: bold;">{$1}</span>'
    );
  }

  if (editorRef.value) {
    try {
      editorRef.value.focus();
      editorRef.value.dangerouslyInsertHtml(insertHtml);
      ElMessage.success('已插入变量：' + insertText);
    } catch (error) {
      const currentContent = content.value;
      const newContent = currentContent + '<p>' + insertText + '</p>';
      content.value = newContent;
      ElMessage.success('已插入变量：' + insertText);
    }
  }

  variableForm.value = {
    selectedVariable: '',
    format: 'simple',
  };
  variableDialogVisible.value = false;
}

// 预览模板
function previewTemplate() {
  const htmlContent = content.value;
  if (!htmlContent.trim()) {
    ElMessage.warning('模板内容为空');
    return;
  }

  let processedContent = htmlContent;

  // 预览时对自定义标签进行处理
  // processedContent = processedContent.replace(
  //   /<projectName[^>]*>([^<]+)<\/projectName>/g,
  //   '<span class="preview-tag-string">$1</span>'
  // );
  // processedContent = processedContent.replace(
  //   /<text-href[^>]*>([^<]+)<\/text-href>/g,
  //   '<span class="preview-tag-link">$1</span>'
  // );
  // processedContent = processedContent.replace(
  //   /\{([^}]+)\}/g,
  //   '<span style="background-color: #e6f7ff; color: #1890ff; padding: 2px 4px; border-radius: 3px; font-weight: bold;">{$1}</span>'
  // );

  previewContent.value = processedContent;
  previewDialogVisible.value = true;
  // const finalData = replaceTemplate(htmlContent, {
  //   name: '测试标题',
  //   notice: [
  //     {
  //       type: '1111',
  //     },
  //     {
  //       type: '2222',
  //     },
  //   ],
  // });
  // previewContent.value = finalData!;
  // console.log(htmlContent, 'htmlContent');
  // console.log(finalData, 'finalData');
  emit('preview', processedContent);
}

// 插入循环变量
function insertLoopVariable() {
  if (arrayVariables.value.length === 0) {
    ElMessage.warning('暂无可用的数组变量');
    return;
  }
  loopVariableDialogVisible.value = true;
}

function confirmInsertLoopVariable() {
  const { selectedVariable, previewTemplate } = loopVariableForm.value;

  if (!selectedVariable) {
    ElMessage.warning('请选择要循环的数组变量');
    return;
  }

  if (!previewTemplate) {
    ElMessage.warning('模板内容为空');
    return;
  }

  if (editorRef.value) {
    try {
      editorRef.value.focus();

      const highlightedTemplate = previewTemplate.replace(
        /\{([^}]+)\}/g,
        '<span class="auto-highlight" style="background-color: #e6f7ff; color: #1890ff; padding: 2px 4px; border-radius: 3px; font-weight: bold;">{$1}</span>'
      );
      editorRef.value.dangerouslyInsertHtml(
        '<div class="loop-template-block">' + highlightedTemplate + '</div>'
      );
      ElMessage.success('已插入' + selectedVariable + '的循环模板');
    } catch (error) {
      ElMessage.error('插入循环模板失败，请重试');
    }
  }

  loopVariableForm.value = {
    selectedVariable: '',
    templateType: 'simple',
    previewTemplate: '',
  };
  loopVariableDialogVisible.value = false;
}

// 导出预览
function exportPreview() {
  // 创建隐藏的iframe用于打印
  const iframe = document.createElement('iframe');
  iframe.style.position = 'absolute';
  iframe.style.top = '-1000px';
  iframe.style.left = '-1000px';
  iframe.style.width = '0';
  iframe.style.height = '0';
  iframe.style.border = 'none';

  document.body.appendChild(iframe);

  const iframeDoc = iframe.contentDocument || iframe.contentWindow?.document;
  if (iframeDoc) {
    const printHtml = `
      <!DOCTYPE html>
      <html>
      <head>
        <title>模板预览</title>
        <meta charset="utf-8">
        <style>
          @media print {
            @page {
              margin: 20mm;
              size: A4;
            }
          }
          body { 
            font-family: "Microsoft YaHei", "SimSun", sans-serif; 
            margin: 0;
            padding: 20px;
            line-height: 1.6; 
            color: #333;
          } 
          table { 
            width: 100%; 
            border-collapse: collapse; 
            margin: 10px 0; 
          } 
          th, td { 
            border: 1px solid #ddd; 
            padding: 8px; 
            text-align: left; 
          } 
          th { 
            background-color: #f5f5f5; 
            font-weight: bold; 
          }
          p { 
            margin: 8px 0; 
          }
          div { 
            margin: 4px 0; 
          }
          .loop-template-block { 
            border: none !important; 
            background: none !important; 
            padding: 0 !important; 
            margin: 0 !important; 
          } 
          .loop-template-block::before { 
            display: none !important; 
          }
          projectName,
          text-href {
            background: transparent !important;
            color: inherit !important;
            padding: 0 !important;
            border: none !important;
          }
          .preview-tag-string,
          .preview-tag-link {
            background: transparent !important;
            color: inherit !important;
            padding: 0 !important;
          }
          .highlight {
            background: transparent !important;
            color: inherit !important;
            padding: 0 !important;
          }
          .data-highlight {
            background: transparent !important;
            color: inherit !important;
            padding: 0 !important;
          }
        </style>
      </head>
      <body>
        ${previewContent.value}
      </body>
      </html>
    `;

    iframeDoc.open();
    iframeDoc.write(printHtml);
    iframeDoc.close();

    // 等待内容加载完成后触发打印
    setTimeout(() => {
      iframe.contentWindow?.print();

      // 打印完成后清理iframe
      setTimeout(() => {
        document.body.removeChild(iframe);
      }, 1000);
    }, 500);
  }
}

// 组件销毁
onBeforeUnmount(() => {
  const editor = editorRef.value;
  if (editor) {
    editor.destroy();
  }
});
</script>
<style>
@import '../../../../asset/view.css';
</style>
<style lang="scss" scoped>
.rich-text-editor {
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  overflow: hidden;

  .editor-toolbar {
    padding: 8px 12px;
    background: #fafafa;
    border-bottom: 1px solid #d9d9d9;
  }

  .editor-container {
    background: #fff;

    @media screen {
      @page {
        margin: 0 !important;
      }
    }

    [data-slate-editor] {
      padding: 20px;
      min-height: 300px;

      &::before,
      &::after {
        display: none !important;
      }
    }

    // 稳定编辑器内容区域
    .w-e-text {
      position: relative;
      overflow-wrap: break-word;
      word-wrap: break-word;
      word-break: break-all;

      // 防止内容跳动
      p {
        margin: 0;
        padding: 0;
        line-height: 1.6;
      }

      // 稳定变量文本显示
      span {
        display: inline;
        white-space: nowrap;
      }
    }
  }
}

.template-preview {
  max-height: 60vh;
  overflow-y: auto;
  padding: 20px;
  background: #fff;
  border: 1px solid #e8e8e8;
  border-radius: 6px;

  @media print {
    @page {
      margin: 0;
      size: A4;
    }

    padding: 0;
    border: none;
    border-radius: 0;
    max-height: none;
  }

  :deep(p) {
    margin-bottom: 16px;
    line-height: 1.6;
  }

  :deep(h1, h2, h3, h4, h5, h6) {
    margin: 20px 0 16px 0;
    font-weight: 600;
  }

  :deep(ul, ol) {
    padding-left: 20px;
    margin-bottom: 16px;
  }

  :deep(blockquote) {
    margin: 16px 0;
    padding: 16px;
    background: #f6f8fa;
    border-left: 4px solid #d1d9e0;
  }
}

:deep(.w-e-text-placeholder) {
  color: #999;
  font-style: italic;
}

:deep(.w-e-text-container) {
  background: #fff;

  @media screen {
    @page {
      margin: 0 !important;
    }
  }

  [data-slate-editor] {
    padding: 20px;
    min-height: 300px;

    &::before,
    &::after {
      display: none !important;
    }
  }
}

:deep(.w-e-toolbar) {
  background: #fafafa;
}

// 自定义标签样式
:deep(projectName) {
  display: inline-block;
  background-color: #e6f7ff; /* Light blue */
  color: #1890ff; /* Dark blue */
  padding: 2px 4px;
  border-radius: 3px;
  font-weight: bold;
  white-space: nowrap;
  cursor: default;
  user-select: none;
  border: 1px solid #91d5ff; /* 更改为蓝色边框 */
}

:deep(text-href) {
  display: inline-block;
  background-color: #f6ffed; /* Light green */
  color: #1890ff; /* Dark blue */ /* 更改为蓝色 */
  padding: 2px 4px;
  border-radius: 3px;
  font-weight: bold;
  white-space: nowrap;
  cursor: default;
  user-select: none;
  border: 1px solid #91d5ff; /* 更改为蓝色边框 */
}

// 循环模板块样式
:deep(.loop-template-block) {
  border: 2px dashed #1890ff;
  padding: 10px;
  margin: 10px 0;
  background-color: #f6ffed;
  border-radius: 4px;
  position: relative;
  line-height: 1.4;
}

:deep(.loop-template-block::before) {
  content: '循环模板';
  position: absolute;
  top: -10px;
  left: 10px;
  background: #1890ff;
  color: white;
  padding: 2px 8px;
  font-size: 12px;
  border-radius: 10px;
}

:deep(.loop-template-block) {
  p {
    margin: 4px 0 !important;
    line-height: 1.4 !important;
  }

  div {
    margin: 4px 0 !important;
    line-height: 1.4 !important;
  }

  li {
    margin: 2px 0 !important;
    line-height: 1.4 !important;
  }

  table {
    margin: 8px 0 !important;
  }

  td,
  th {
    padding: 6px 8px !important;
    line-height: 1.3 !important;
  }
}

// 预览样式
:deep(.preview-tag-string) {
  background-color: #e6f7ff;
  color: #1890ff;
  padding: 2px 4px;
  border-radius: 3px;
  font-weight: bold;
}

:deep(.preview-tag-link) {
  background-color: #f6ffed;
  color: #1890ff; /* 更改为蓝色 */
  padding: 1px 3px;
  border-radius: 2px;
  font-weight: bold;
}

:deep(.w-e-text-placeholder) {
  color: #999;
  font-style: italic;
}

@media print {
  @page {
    margin: 0;
    size: A4;
  }

  html,
  body {
    margin: 0 !important;
    padding: 0 !important;
    -webkit-print-color-adjust: exact;
    color-adjust: exact;
  }

  body {
    margin: 0;
    padding: 20px;
  }

  :deep(.w-e-toolbar) {
    display: none !important;
  }

  :deep(.editor-toolbar) {
    display: none !important;
  }
}
</style>
