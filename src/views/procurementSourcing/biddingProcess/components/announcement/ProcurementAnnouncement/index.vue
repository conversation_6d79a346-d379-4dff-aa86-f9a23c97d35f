<template>
  <div class="procurement-announcement">
    <!-- Header -->
    <div class="header">
      <div>
        <el-button
          type="text"
          @click="handleBack"
          ><el-icon><ArrowLeftBold /></el-icon> <span>上一步</span>
        </el-button>
      </div>
      <div class="flex items-center">
        <!--				<el-button-->
        <!--					type="text"-->
        <!--					@click="onViewAttachment"-->
        <!--				>-->
        <!--					<span style="margin-right: 8px">查看附件</span>-->
        <!--					<el-icon><Link /></el-icon>-->
        <!--				</el-button>-->
        <el-button @click="onPrint"> 打印 </el-button>
<!--        <el-button @click="onGeneratePDF"> 生成PDF </el-button>-->
        <el-button
          v-if="!hasNotice && isApproved && hasAuth"
          type="primary"
          icon="Promotion"
          @click="onSubmit"
          :loading="loading"
          >提交审批</el-button
        >
      </div>
    </div>
    <!-- 富文本编辑器 -->
    <div class="flex-1 overflow-y-auto">
      <div class="w-full px-[200px] min-h-[400px]">
        <div class="w-full h-full rich-editor-container bg-[#fff]">
          <Toolbar
            :editor="editorRef"
            :defaultConfig="toolbarConfig"
            mode="default"
          />
          <Editor
            v-model="data.noticeContent"
            :defaultConfig="editorConfig"
            mode="default"
            @onCreated="handleCreated"
          />
        </div>
      </div>
      <div class="px-[200px] mt-5">
        <div class="bg-[#fff] p-5">
          <div class="sub-title">相关附件</div>
          <div class="flex gap-4 mt-3">
            <div class="file-label">采购公告附件：</div>
            <div class="flex flex-col gap-2">
              <div
                class="flex gap-4 items-center"
                v-for="(item, index) in data.attachmentInfos"
                :key="index"
              >
                <div
                  class="file-item-label"
                  @click="handleDownload(item)"
                >
                  {{ item.fileName }}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, shallowRef, onBeforeUnmount, computed } from 'vue';
import '@wangeditor/editor/dist/css/style.css';
import { Editor, Toolbar } from '@wangeditor/editor-for-vue';
import type { IDomEditor, IEditorConfig, IToolbarConfig } from '@wangeditor/editor';
import downloadUrlFile from '@/utils/downloadUrl.js';
import { useMessage, useMessageBox } from '@/hooks/message';
import { saveAnnouncementData } from '@/views/procurementSourcing/biddingProcess/api';
import { useBiddingStore } from '@/views/procurementSourcing/biddingProcess/stores';
import { useRouter } from 'vue-router';
import { generatePDFFromEditor } from '@/utils/pdfGenerator';

const editorRef = shallowRef<IDomEditor>();

const biddingStore = useBiddingStore()
const hasNotice = computed(() => biddingStore?.hasNotice);

const props = defineProps({
  data: {
    type: Object,
    default: () => ({}),
  },
});

const isApproved = computed(() => {
  return ['TO_APPROVE', 'APPROVE_REJECT', 'APPROVE_REVOKE'].includes(props.data?.noticeStatus)
})

const hasAuth = computed(() => {
  return true
  // return isProjectMember(biddingStore.projectDetail?.projectMemberList) === 'PROJECT_LEADER'
})

const router = useRouter()

const emits = defineEmits(['back']);
const loading = ref(false)

const toolbarConfig: Partial<IToolbarConfig> = {
  excludeKeys: ['fullScreen'],
};
const editorConfig: Partial<IEditorConfig> = {
  placeholder: '请输入公告内容...',
};

function handleCreated(editor: IDomEditor) {
  editorRef.value = editor;
}

function onPrint() {
  if (!editorRef.value) {
    console.warn('编辑器未初始化');
    return;
  }

  // 获取富文本编辑器的HTML内容
  const htmlContent = editorRef.value.getHtml();

  // 调试信息
  console.log('打印内容:', htmlContent);
  console.log('数据内容:', props.data.noticeContent);

  // 如果编辑器内容为空，尝试使用数据中的内容
  const finalContent = htmlContent || props.data.noticeContent || '<p>暂无内容</p>';

  if (!finalContent || finalContent === '<p><br></p>' || finalContent.trim() === '') {
    console.warn('没有可打印的内容');
    return;
  }

  console.log('最终打印内容:', finalContent);

  // 创建新窗口进行打印
  const printWindow = window.open(location.href, '_blank', 'width=1400,height=800');
  if (!printWindow) {
    console.error('无法打开打印窗口，请检查浏览器弹窗设置');
    return;
  }

  // 构建CSS样式
  const cssStyles = [
    '* { margin: 0; padding: 0; box-sizing: border-box; }',
    'body { font-family: "Microsoft YaHei", Arial, sans-serif; font-size: 14px; line-height: 1.6; color: #333; padding: 20px; background: white; }',
    '@media print { @page { margin: 2cm; size: A4; } body { margin: 0; padding: 20px; } }',
    '.print-content { max-width: 100%; word-wrap: break-word; }',
    '.print-content img { max-width: 100%; height: auto; }',
    '.print-content table { border-collapse: collapse; width: 100%; margin: 10px 0; }',
    '.print-content table td, .print-content table th { border: 1px solid #333; padding: 8px; text-align: left; }',
    '.print-content table th { background-color: #f5f5f5; font-weight: bold; }',
    '.print-content h1, .print-content h2, .print-content h3, .print-content h4, .print-content h5, .print-content h6 { margin: 20px 0 10px 0; font-weight: bold; }',
    '.print-content p { margin: 10px 0; }',
    '.print-content ul, .print-content ol { margin: 10px 0; padding-left: 30px; }',
    '.print-content li { margin: 5px 0; }',
  ].join(' ');

  // 构建完整的HTML文档
  const printDocument = printWindow.document;
  printDocument.open();
  printDocument.write('<!DOCTYPE html>');
  printDocument.write('<html><head>');
  printDocument.write('<meta charset="utf-8">');
  printDocument.write('<title>采购公告打印</title>');
  printDocument.write('<style>' + cssStyles + '</style>');
  printDocument.write('</head><body>');
  printDocument.write('<div class="print-content">' + finalContent + '</div>');

  // 分别写入script标签，避免Vue编译器解析问题
  printDocument.write('<' + 'script>');
  printDocument.write('window.onload = function() { window.print(); window.onafterprint = function() { window.close(); }; };');
  printDocument.write('</' + 'script>');
  printDocument.write('</body></html>');
  printDocument.close();
}

async function onGeneratePDF() {
  if (!editorRef.value) {
    useMessage().warning('编辑器未初始化');
    return;
  }

  try {
    useMessage().info('正在生成PDF，请稍候...');

    // 使用工具函数生成PDF
    await generatePDFFromEditor(editorRef.value, '采购公告', {
      useIframe: true,
      scale: 1.5,
      pageFormat: 'a4',
      orientation: 'p'
    });

    useMessage().success('PDF生成成功');

  } catch (error) {
    console.error('PDF生成失败:', error);
    useMessage().error(error.message || 'PDF生成失败，请重试');
  }
}

function onSubmit() {
  useMessageBox()
    .confirm('确认提交审批?')
    .then(async () => {
      try {
        loading.value = true;
        // await reviewApi({
        //   noticeId: props.data.id,
        // });
        await saveAnnouncementData(props.data)
        // biddingStore.initProjectDetail()
        useMessage().success('提交审批成功');
        router.back()
      } catch (e) {
        console.log(e);
      } finally {
        loading.value = false;
      }
    })
    .catch((err) => {});
}

const handleBack = () => {
  emits('back');
};

const handleDownload = (item: any) => {
  downloadUrlFile(item.filePath, item.fileName);
};

onBeforeUnmount(() => {
  if (editorRef.value) {
    editorRef.value.destroy();
  }
});
</script>

<style scoped lang="scss">
@import '../../../styles/collapse-panel.scss';
.procurement-announcement {
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  position: absolute;
  left: 20px;
  right: 20px;
  bottom: 0;
  top: 20px;
  overflow: hidden;

  .header {
    display: flex;
    align-items: flex-start;
    justify-content: space-between;
    border-bottom: 1px solid #dcdfe6;
    margin-bottom: 24px;
    padding-bottom: 8px;
    //background: #ffffff;
  }

  .rich-editor-container {
    height: 100%;
    display: flex;
    flex-direction: column;
  }
  :deep(.w-e-scroll) {
    overflow: hidden !important;
  }
}

.sub-title {
  color: var(--Color-Text-text-color-primary, #1d2129);
  font-size: 16px;
  font-weight: 600;
  line-height: 24px; /* 150% */
}

.file-label {
  color: var(--Color-Text-text-color-regular, #4e5969);
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 22px; /* 157.143% */
}

.file-item-label {
  color: var(--Color-Primary-color-primary, #0069ff);
  font-size: 14px;
  font-weight: 400;
  cursor: pointer;
}

:deep(.w-e-text-container) {
  background: #fff !important;
}

:deep(.w-e-bar) {
  background: #fff !important;
}
</style>
