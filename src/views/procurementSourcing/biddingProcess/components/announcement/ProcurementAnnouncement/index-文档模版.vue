<template>
	<div class="procurement-announcement-container">
		<div class="announcement-header">
			<h3 class="announcement-title">采购公告发布</h3>
			<p class="announcement-description">编辑和发布采购公告信息</p>
		</div>

		<!-- 功能导航 -->
		<el-tabs v-model="activeTab" type="card" class="announcement-tabs">
			<el-tab-pane label="模板管理" name="templates">
				<TemplateManager
					:templates="wordState.templates"
					:current-template-id="wordState.currentTemplate?.id"
					:loading="wordState.loading"
					@template-uploaded="handleTemplateUploaded"
					@template-updated="handleTemplateUpdated"
					@template-deleted="handleTemplateDeleted"
					@template-selected="handleTemplateSelected"
					@refresh="loadTemplates"
				/>
			</el-tab-pane>

			<el-tab-pane label="模板编辑" name="editor" :disabled="!wordState.currentTemplate">
				<div class="editor-section">
					<div class="section-header">
						<h4>当前模板：{{ wordState.currentTemplate?.name || '未选择' }}</h4>
						<div class="template-actions">

							<!-- <el-button type="primary" @click="generateDocument" :loading="wordState.isGenerating">
								生成文档
							</el-button> -->
						</div>
					</div>
					<div>
						<TemplateEditor
							:templates="wordState.templates"
							:current-template="wordState.currentTemplate"
						/>
					</div>
					<!-- <RichTextEditor
						v-model="wordState.editorContent"
						:variables="wordState.currentTemplate?.variables || []"
						placeholder="请编辑模板内容..."
						@save="handleTemplateSave"
						@preview="handleTemplatePreview"
					/> -->
				</div>
			</el-tab-pane>

			<el-tab-pane label="数据配置" name="data" :disabled="!wordState.currentTemplate">
				<div class="data-config-section">
					<div class="section-header">
						<h4>模板数据配置</h4>
						<div class="data-actions">
							<!-- <el-button @click="resetTemplateData" :icon="RefreshLeft">
								重置数据
							</el-button> -->
							<el-button @click="loadSampleData" :icon="DataLine">
								加载示例数据
							</el-button>
							<!-- <el-button type="primary" @click="validateAndGenerate" :loading="wordState.isGenerating">
								验证并生成
							</el-button> -->
						</div>
					</div>

					<!-- 数据表单 -->
					<el-form
						:model="wordState.templateData"
						label-width="120px"
						class="template-data-form"
					>
						<el-row :gutter="20">
							<el-col
								v-for="variable in wordState.currentTemplate?.variables || []"
								:key="variable.key"
								:span="variable.type === 'array' ? 24 : 12"
							>
								<el-form-item
									:label="variable.label"
									:required="variable.required"
								>
									<!-- 文本输入 -->
									<el-input
										v-if="variable.type === 'text'"
										v-model="wordState.templateData[variable.key]"
										:placeholder="variable.description"
									/>

									<!-- 数字输入 -->
									<el-input-number
										v-else-if="variable.type === 'number'"
										v-model="wordState.templateData[variable.key]"
										:placeholder="variable.description"
										style="width: 100%"
									/>

									<!-- 日期选择 -->
									<el-date-picker
										v-else-if="variable.type === 'date'"
										v-model="wordState.templateData[variable.key]"
										type="date"
										:placeholder="variable.description"
										style="width: 100%"
										format="YYYY-MM-DD"
										value-format="YYYY-MM-DD"
									/>

									<!-- 数组输入 -->
									<div v-else-if="variable.type === 'array'" class="array-input">
										<!-- 资格要求数组 -->
										<div v-if="variable.key === 'qualificationRequirements'" class="complex-array-input">
											<div
												v-for="(item, index) in (wordState.templateData[variable.key] || [])"
												:key="index"
												class="complex-array-item"
											>
												<el-card class="array-item-card">
													<div class="array-item-header">
														<span>第 {{ index + 1 }} 项要求</span>
														<el-button
															type="danger"
															size="small"
															:icon="Delete"
															@click="removeArrayItem(variable.key, index)"
														>
															删除
														</el-button>
													</div>
													<el-row :gutter="16">
														<el-col :span="12">
															<el-form-item label="要求项目">
																<el-input
																	v-model="wordState.templateData[variable.key][index].requirement"
																	placeholder="请输入要求项目"
																/>
															</el-form-item>
														</el-col>
														<el-col :span="12">
															<el-form-item label="具体描述">
																<el-input
																	v-model="wordState.templateData[variable.key][index].description"
																	placeholder="请输入具体描述"
																/>
															</el-form-item>
														</el-col>
													</el-row>
												</el-card>
											</div>
											<el-button
												type="primary"
												size="small"
												:icon="Plus"
												@click="addQualificationRequirement(variable.key)"
											>
												添加资格要求
											</el-button>
										</div>

										<!-- 报价要求数组 -->
										<div v-else-if="variable.key === 'quotationRequirements'" class="complex-array-input">
											<div
												v-for="(item, index) in (wordState.templateData[variable.key] || [])"
												:key="index"
												class="complex-array-item"
											>
												<el-card class="array-item-card">
													<div class="array-item-header">
														<span>第 {{ index + 1 }} 项报价要求</span>
														<el-button
															type="danger"
															size="small"
															:icon="Delete"
															@click="removeArrayItem(variable.key, index)"
														>
															删除
														</el-button>
													</div>
													<el-row :gutter="16">
														<el-col :span="6">
															<el-form-item label="报价项目">
																<el-input
																	v-model="wordState.templateData[variable.key][index].item"
																	placeholder="请输入报价项目"
																/>
															</el-form-item>
														</el-col>
														<el-col :span="8">
															<el-form-item label="规格要求">
																<el-input
																	v-model="wordState.templateData[variable.key][index].specification"
																	placeholder="请输入规格要求"
																/>
															</el-form-item>
														</el-col>
														<el-col :span="5">
															<el-form-item label="数量">
																<el-input-number
																	v-model="wordState.templateData[variable.key][index].quantity"
																	:min="0"
																	placeholder="数量"
																	style="width: 100%"
																/>
															</el-form-item>
														</el-col>
														<el-col :span="5">
															<el-form-item label="单位">
																<el-input
																	v-model="wordState.templateData[variable.key][index].unit"
																	placeholder="单位"
																/>
															</el-form-item>
														</el-col>
													</el-row>
												</el-card>
											</div>
											<el-button
												type="primary"
												size="small"
												:icon="Plus"
												@click="addQuotationRequirement(variable.key)"
											>
												添加报价要求
											</el-button>
										</div>

										<!-- 简单数组输入（兼容其他类型） -->
										<div v-else class="simple-array-input">
											<div
												v-for="(item, index) in (wordState.templateData[variable.key] || [])"
												:key="index"
												class="array-item"
											>
												<el-input
													v-model="wordState.templateData[variable.key][index]"
													:placeholder="`${variable.description} ${index + 1}`"
												/>
												<el-button
													type="danger"
													size="small"
													:icon="Delete"
													@click="removeArrayItem(variable.key, index)"
												/>
											</div>
											<el-button
												type="primary"
												size="small"
												:icon="Plus"
												@click="addArrayItem(variable.key)"
											>
												添加项目
											</el-button>
										</div>
									</div>

									<!-- 图片上传 -->
									<el-upload
										v-else-if="variable.type === 'image'"
										:auto-upload="false"
										:show-file-list="false"
										accept="image/*"
										@change="(file: UploadFile) => handleImageUpload(variable.key, file)"
									>
										<el-button :icon="Picture">
											{{ wordState.templateData[variable.key] ? '重新选择' : '选择图片' }}
										</el-button>
									</el-upload>
								</el-form-item>
							</el-col>
						</el-row>
					</el-form>
				</div>
			</el-tab-pane>

			<el-tab-pane label="文档生成" name="generate" :disabled="!wordState.currentTemplate">
				<div class="generate-section">
					<div class="section-header">
						<h4>文档生成与预览</h4>
						<div class="generate-actions">
							<el-button type="primary" @click="generateDocumentPreview" :loading="wordState.isGenerating">
								<el-icon><View /></el-icon>
								生成预览
							</el-button>
							<el-button 
								v-if="wordState.previewContent" 
								type="success" 
								@click="downloadFromPreview" 
								:loading="wordState.isDownloading"
							>
								<el-icon><Download /></el-icon>
								下载文档
							</el-button>
							<el-button 
								v-if="wordState.previewContent" 
								@click="clearPreview"
							>
								<el-icon><Close /></el-icon>
								清除预览
							</el-button>
						</div>
					</div>


					<!-- 预览编辑器 -->
					<div v-if="wordState.previewContent" class="preview-editor-section">
						<RichTextEditor
							v-model="wordState.previewContent"
							:variables="wordState.currentTemplate?.variables || []"
							placeholder="文档预览内容..."
							@save="handlePreviewSave"
							class="preview-editor"
						/>
					</div>

					<!-- 生成历史 -->
					<!-- <div class="generation-history">
						<h5>生成历史</h5>
						<el-table :data="wordState.generatedDocuments" style="width: 100%">
							<el-table-column prop="name" label="文档名称" />
							<el-table-column prop="templateId" label="使用模板">
								<template #default="{ row }">
									{{ getTemplateName(row.templateId) }}
								</template>
							</el-table-column>
							<el-table-column prop="generatedAt" label="生成时间" width="180">
								<template #default="{ row }">
									{{ formatDate(row.generatedAt) }}
								</template>
							</el-table-column>
							<el-table-column label="操作" width="120">
								<template #default="{ row }">
									<el-button
										size="small"
										type="primary"
										@click="downloadDocument(row)"
										:icon="Download"
									>
										下载
									</el-button>
								</template>
							</el-table-column>
						</el-table>
					</div> -->
				</div>
			</el-tab-pane>
		</el-tabs>

		<!-- 验证结果对话框 -->
		<el-dialog
			v-model="validationDialogVisible"
			title="数据验证结果"
			width="600px"
		>
			<div class="validation-result">
				<div v-if="validationResult.isValid" class="success-message">
					<el-icon class="success-icon"><SuccessFilled /></el-icon>
					<span>数据验证通过，可以生成文档</span>
				</div>

				<div v-else class="error-message">
					<el-icon class="error-icon"><WarningFilled /></el-icon>
					<span>数据验证失败，请检查以下问题：</span>

					<div v-if="validationResult.missingFields.length > 0" class="missing-fields">
						<h6>缺少必填字段：</h6>
						<ul>
							<li v-for="field in validationResult.missingFields" :key="field">
								{{ getFieldLabel(field) }}
							</li>
						</ul>
					</div>

					<div v-if="validationResult.errors.length > 0" class="validation-errors">
						<h6>数据格式错误：</h6>
						<ul>
							<li v-for="error in validationResult.errors" :key="error">
								{{ error }}
							</li>
						</ul>
					</div>
				</div>
			</div>

			<template #footer>
				<span class="dialog-footer">
					<el-button @click="validationDialogVisible = false">关闭</el-button>
					<el-button
						v-if="validationResult.isValid"
						type="primary"
						@click="proceedGeneration"
						:loading="wordState.isGenerating"
					>
						继续生成
					</el-button>
				</span>
			</template>
		</el-dialog>
	</div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue';
import { ElMessage } from 'yun-design';
import {
	Refresh,
	RefreshLeft,
	DataLine,
	Download,
	Delete,
	Plus,
	Picture,
	SuccessFilled,
	WarningFilled,
	View,
	Close,
} from '@element-plus/icons-vue';
import type { UploadFile } from 'element-plus';
import TemplateManager from './components/TemplateManager.vue';
import RichTextEditor from './components/RichTextEditor.vue';
import type {
	WordDocumentState,
	WordTemplate,
	TemplateData,
	GeneratedDocument,
	ProcurementTemplateVariables,
} from './types';
import { WordProcessor } from './utils/wordProcessor';
import TemplateEditor from './components/TemplateEditor.vue';

// 状态管理
const activeTab = ref('templates');
const wordState = reactive<WordDocumentState>({
	templates: [],
	currentTemplate: null,
	templateData: {},
	generatedDocuments: [],
	isGenerating: false,
	editorContent: '',
	loading: false,
	previewContent: '',
	isDownloading: false,
});

// 验证相关
const validationDialogVisible = ref(false);
const validationResult = ref({
	isValid: false,
	missingFields: [] as string[],
	errors: [] as string[],
});

// 模板管理事件处理
function handleTemplateUploaded(template: WordTemplate) {
	wordState.templates.push(template);
	if (!wordState.currentTemplate) {
		handleTemplateSelected(template);
	}
}

function handleTemplateUpdated(template: WordTemplate) {
	const index = wordState.templates.findIndex(t => t.id === template.id);
	if (index !== -1) {
		wordState.templates[index] = template;
		if (wordState.currentTemplate?.id === template.id) {
			wordState.currentTemplate = template;
		}
	}
}

function handleTemplateDeleted(templateId: string) {
	wordState.templates = wordState.templates.filter(t => t.id !== templateId);
	if (wordState.currentTemplate?.id === templateId) {
		wordState.currentTemplate = null;
		wordState.templateData = {};
		wordState.editorContent = '';
	}
}

function handleTemplateSelected(template: WordTemplate) {
	wordState.currentTemplate = template;
	initializeTemplateData();
	activeTab.value = 'editor';
}

// 模板数据初始化
function initializeTemplateData() {
	if (!wordState.currentTemplate) return;

	const data: TemplateData = {};
	wordState.currentTemplate.variables.forEach(variable => {
		if (variable.defaultValue !== undefined) {
			data[variable.key] = variable.defaultValue;
		} else {
			switch (variable.type) {
				case 'array':
					// 根据不同的数组类型初始化不同的数据结构
					if (variable.key === 'qualificationRequirements') {
						data[variable.key] = [];
					} else if (variable.key === 'quotationRequirements') {
						data[variable.key] = [];
					} else {
						data[variable.key] = [];
					}
					break;
				case 'number':
					data[variable.key] = 0;
					break;
				default:
					data[variable.key] = '';
			}
		}
	});

	wordState.templateData = data;
}

// 数据操作
function addArrayItem(key: string) {
	if (!wordState.templateData[key]) {
		wordState.templateData[key] = [];
	}
	wordState.templateData[key].push('');
}

function removeArrayItem(key: string, index: number) {
	if (wordState.templateData[key] && Array.isArray(wordState.templateData[key])) {
		wordState.templateData[key].splice(index, 1);
	}
}

// 添加资格要求
function addQualificationRequirement(key: string) {
	if (!wordState.templateData[key]) {
		wordState.templateData[key] = [];
	}
	wordState.templateData[key].push({
		requirement: '',
		description: ''
	});
}

// 添加报价要求
function addQuotationRequirement(key: string) {
	if (!wordState.templateData[key]) {
		wordState.templateData[key] = [];
	}
	wordState.templateData[key].push({
		item: '',
		specification: '',
		quantity: 1,
		unit: '项'
	});
}

function handleImageUpload(key: string, file: UploadFile) {
	if (file.raw) {
		// 这里应该上传到服务器，然后保存URL
		const imageUrl = URL.createObjectURL(file.raw);
		wordState.templateData[key] = imageUrl;
	}
}

// 模板编辑
function handleTemplateSave(content: string) {
	wordState.editorContent = content;
	// 这里可以保存到服务器
}

function handleTemplatePreview(content: string) {
	// 预览处理已在富文本编辑器中完成
}

// 数据管理
function loadTemplateData() {
	initializeTemplateData();
	ElMessage.success('模板数据已重新加载');
}

function resetTemplateData() {
	initializeTemplateData();
	ElMessage.success('模板数据已重置');
}

function loadSampleData() {
	if (!wordState.currentTemplate) return;

	// 加载示例数据
	const sampleData: Partial<ProcurementTemplateVariables> = {
		announcementTitle: '某市政工程建设项目采购公告',
		announcementNumber: '*********',
		publishDate: new Date().toISOString().split('T')[0],
		deadline: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
		purchaserName: '某市建设局',
		purchaserAddress: '某市中心区政府大楼',
		contactPerson: '张三',
		contactPhone: '010-12345678',
		contactEmail: '<EMAIL>',
		projectName: '市政道路改造工程',
		projectDescription: '对市中心区主要道路进行改造升级，包括路面重铺、交通设施更新等。',
		projectAddress: '某市中心区主要道路',
		budgetAmount: 5000000,
		currency: '人民币',
		participationFee: 500,
		guaranteeDeposit: 100000,
		evaluationCriteria: '综合评分法，技术分40%，商务分40%，信誉分20%',
		// 添加数组类型的示例数据
		qualificationRequirements: [
			{ requirement: '具有独立法人资格', description: '投标人必须是依法注册的企业法人' },
			{ requirement: '注册资本不低于500万元人民币', description: '具备足够的资金实力' },
			{ requirement: '具有市政工程施工总承包三级及以上资质', description: '具备相应的施工能力和经验' },
			{ requirement: '近三年无重大质量安全事故', description: '具有良好的施工记录' },
			{ requirement: '具有良好的银行资信和商业信誉', description: '财务状况良好，信誉可靠' },
			{ requirement: '项目经理具有市政工程专业二级及以上建造师执业资格', description: '项目管理能力符合要求' }
		],
		quotationRequirements: [
			{ item: '工程施工费', specification: '包含人工、机械等全部施工费用', quantity: 1, unit: '项' },
			{ item: '材料采购费', specification: '符合国家标准的建筑材料', quantity: 1, unit: '项' },
			{ item: '设备安装费', specification: '交通设施及照明设备安装', quantity: 1, unit: '项' },
			{ item: '质量保证费', specification: '工程质量保证期内的维护费用', quantity: 1, unit: '项' },
			{ item: '安全文明施工费', specification: '施工期间安全防护措施费用', quantity: 1, unit: '项' }
		],
		// 添加时间相关的示例数据
		registrationStart: new Date().toISOString().split('T')[0],
		registrationEnd: new Date(Date.now() + 15 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
		submissionDeadline: new Date(Date.now() + 20 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
		openingTime: new Date(Date.now() + 25 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
		specialTerms: '本项目要求使用环保材料，施工期间需确保交通正常通行',
		legalNotices: '投标人应严格按照招标文件要求提交相关材料，虚假材料将取消投标资格'
	};

	// 应用示例数据
	wordState.currentTemplate.variables.forEach(variable => {
		if (sampleData[variable.key as keyof ProcurementTemplateVariables] !== undefined) {
			wordState.templateData[variable.key] = sampleData[variable.key as keyof ProcurementTemplateVariables];
		}
	});

	ElMessage.success('示例数据已加载，包含循环变量示例');
}

// 文档生成
async function validateAndGenerate() {
	if (!wordState.currentTemplate) {
		ElMessage.error('请先选择模板');
		return;
	}

	const result = WordProcessor.validateTemplateData(
		wordState.templateData,
		wordState.currentTemplate.variables
	);

	validationResult.value = result;
	validationDialogVisible.value = true;

	if (result.isValid) {
		// 如果验证通过，可以直接生成
		setTimeout(() => {
			validationDialogVisible.value = false;
			generateDocument();
		}, 1000);
	}
}

async function proceedGeneration() {
	validationDialogVisible.value = false;
	await generateDocument();
}

async function generateDocument() {
	if (!wordState.currentTemplate) {
		ElMessage.error('请先选择模板');
		return;
	}

	try {
		wordState.isGenerating = true;

		// 检查是否有实际的Word模板文件
		if (!wordState.currentTemplate.fileUrl || wordState.currentTemplate.fileUrl === '') {
			// 如果没有Word模板文件，使用HTML生成方式
			generateDocumentFromHTML();
			return;
		}

		const options = {
			template: wordState.currentTemplate,
			data: wordState.templateData,
			outputFileName: `${wordState.templateData.announcementTitle || '采购公告'}.docx`,
		};

		const blob = await WordProcessor.generateDocument(options);

		// 自动下载
		WordProcessor.downloadDocument(blob, options.outputFileName);

		// 记录生成历史
		const generatedDoc: GeneratedDocument = {
			id: Date.now().toString(),
			name: options.outputFileName,
			templateId: wordState.currentTemplate.id,
			generatedAt: new Date().toISOString(),
			downloadUrl: URL.createObjectURL(blob),
			data: { ...wordState.templateData },
		};

		wordState.generatedDocuments.unshift(generatedDoc);

		ElMessage.success('Word文档生成成功（.doc格式，可用Word打开）');
	} catch (error) {
		console.error('生成文档失败:', error);
		ElMessage.error('生成文档失败，请检查模板和数据');
	} finally {
		wordState.isGenerating = false;
	}
}

// 从HTML生成Word文档的备用方法
function generateDocumentFromHTML() {
	try {
		// 构建HTML内容
		let htmlContent = wordState.editorContent || generateDefaultHTMLTemplate();
		
		// 替换模板变量 - 支持循环变量
		wordState.currentTemplate?.variables.forEach(variable => {
			const value = wordState.templateData[variable.key];
			if (value !== undefined && value !== null) {
				if (variable.type === 'array' && Array.isArray(value)) {
					// 处理循环变量
					htmlContent = processArrayVariable(htmlContent, variable.key, value);
				} else {
					// 处理普通变量
					const regex = new RegExp(`\\{${variable.key}\\}`, 'g');
					htmlContent = htmlContent.replace(regex, String(value));
				}
			}
		});

		// 创建Word兼容的HTML文档，不依赖外部库
		const fullHTML = `
			<!DOCTYPE html>
			<html xmlns:o='urn:schemas-microsoft-com:office:office' xmlns:w='urn:schemas-microsoft-com:office:word' xmlns='http://www.w3.org/TR/REC-html40'>
				<head>
					<meta charset="utf-8">
					<title>${wordState.templateData.announcementTitle || '采购公告'}</title>
					<!--[if gte mso 9]>
					<xml>
						<w:WordDocument>
							<w:View>Print</w:View>
							<w:Zoom>90</w:Zoom>
							<w:DoNotPromptForConvert/>
							<w:DoNotShowInsertionsAndDeletions/>
						</w:WordDocument>
					</xml>
					<![endif]-->
					<style>
						@page {
							margin: 1in;
							mso-page-orientation: portrait;
						}
						body { 
							font-family: "Microsoft YaHei", "SimSun", serif;
							line-height: 1.6;
							margin: 0;
							font-size: 14px;
							color: #333;
						}
						h1, h2, h3 { 
							color: #333; 
							page-break-after: avoid;
							font-weight: bold;
						}
						h1 { font-size: 24px; margin: 20px 0; }
						h2 { font-size: 18px; margin: 16px 0 12px 0; }
						h3 { font-size: 16px; margin: 14px 0 10px 0; }
						table { 
							border-collapse: collapse; 
							width: 100%; 
							margin: 20px 0;
							mso-table-lspace: 0pt;
							mso-table-rspace: 0pt;
						}
						th, td { 
							border: 1px solid #333; 
							padding: 8px 12px; 
							text-align: left;
							vertical-align: top;
							mso-border-alt: solid #333 0.5pt;
						}
						th { 
							background-color: #f5f5f5; 
							font-weight: bold;
						}
						.header { 
							text-align: center; 
							margin-bottom: 30px; 
						}
						.title { 
							font-size: 24px; 
							font-weight: bold; 
							margin: 20px 0;
						}
						.section { 
							margin: 20px 0; 
							page-break-inside: avoid;
						}
						p { 
							margin: 8px 0; 
							line-height: 1.6;
						}
						div {
							margin: 4px 0;
							line-height: 1.4;
						}
						/* 循环变量生成的div元素紧凑样式 */
						div[style*="margin: 4px 0"] {
							margin: 2px 0 !important;
							line-height: 1.3 !important;
						}
						strong { font-weight: bold; }
					</style>
				</head>
				<body>
					${htmlContent}
				</body>
			</html>
		`;

		// 创建Word兼容的HTML文档，不依赖外部库
		const blob = new Blob([fullHTML], { 
			type: 'application/msword' 
		});
		
		const fileName = `${wordState.templateData.announcementTitle || '采购公告'}.doc`;
		
		// 下载Word文件
		const link = document.createElement('a');
		link.href = URL.createObjectURL(blob);
		link.download = fileName;
		link.click();

		// 记录生成历史
		const generatedDoc: GeneratedDocument = {
			id: Date.now().toString(),
			name: fileName,
			templateId: wordState.currentTemplate!.id,
			generatedAt: new Date().toISOString(),
			downloadUrl: URL.createObjectURL(blob),
			data: { ...wordState.templateData },
		};

		wordState.generatedDocuments.unshift(generatedDoc);

		ElMessage.success('Word文档生成成功（.doc格式，可用Word打开）');
	} catch (error) {
		console.error('生成Word文档失败:', error);
		ElMessage.error('生成Word文档失败，请重试');
		
		// 如果Word转换失败，提供HTML备用方案
		generateHTMLFallback();
	}
}

// HTML备用方案
function generateHTMLFallback() {
	try {
		let htmlContent = wordState.editorContent || generateDefaultHTMLTemplate();
		
		// 替换模板变量 - 支持循环变量
		wordState.currentTemplate?.variables.forEach(variable => {
			const value = wordState.templateData[variable.key];
			if (value !== undefined && value !== null) {
				if (variable.type === 'array' && Array.isArray(value)) {
					// 处理循环变量
					htmlContent = processArrayVariable(htmlContent, variable.key, value);
				} else {
					// 处理普通变量
					const regex = new RegExp(`\\{${variable.key}\\}`, 'g');
					htmlContent = htmlContent.replace(regex, String(value));
				}
			}
		});

		const fullHTML = `
			<!DOCTYPE html>
			<html>
				<head>
					<meta charset="utf-8">
					<title>${wordState.templateData.announcementTitle || '采购公告'}</title>
					<style>
						body { 
							font-family: "Microsoft YaHei", "SimSun", serif;
							line-height: 1.6;
							margin: 40px;
							font-size: 14px;
						}
						h1, h2, h3 { color: #333; }
						table { border-collapse: collapse; width: 100%; margin: 20px 0; }
						th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
						th { background-color: #f2f2f2; }
						.header { text-align: center; margin-bottom: 30px; }
						.title { font-size: 24px; font-weight: bold; }
						.section { margin: 20px 0; }
					</style>
				</head>
				<body>
					${htmlContent}
				</body>
			</html>
		`;

		const blob = new Blob([fullHTML], { type: 'text/html;charset=utf-8' });
		const fileName = `${wordState.templateData.announcementTitle || '采购公告'}.html`;
		
		const link = document.createElement('a');
		link.href = URL.createObjectURL(blob);
		link.download = fileName;
		link.click();

		ElMessage.warning('Word转换失败，已生成HTML格式文档作为备用方案');
	} catch (error) {
		console.error('生成HTML备用文档失败:', error);
		ElMessage.error('文档生成完全失败');
	}
}

// 处理数组变量的循环模板
function processArrayVariable(htmlContent: string, variableKey: string, arrayValue: any[]): string {
	// 支持多种循环语法格式:
	// 1. 简单循环: {#variableKey} content {/variableKey}
	// 2. 带索引循环: {#variableKey:index} content {index} {item} {/variableKey}
	// 3. 表格行循环: <tr>{#variableKey}<td>{item}</td>{/variableKey}</tr>
	// 4. 对象属性访问: {item.property} 或 {item.property|default}

	let processedContent = htmlContent;

	// 1. 处理简单循环语法
	const simpleLoopRegex = new RegExp(`\\{#${variableKey}\\}([\\s\\S]*?)\\{\\/${variableKey}\\}`, 'g');
	processedContent = processedContent.replace(simpleLoopRegex, (match, template) => {
		if (arrayValue.length === 0) return '';
		
		return arrayValue.map((item, index) => {
			let itemContent = template;
			
			// 替换循环内的变量
			itemContent = itemContent.replace(/\{index\}/g, String(index + 1));
			itemContent = itemContent.replace(/\{index0\}/g, String(index));
			
			// 处理对象属性访问
			if (typeof item === 'object' && item !== null) {
				// 替换 {item.property} 格式
				itemContent = itemContent.replace(/\{item\.([^}|]+)(\|([^}]+))?\}/g, (propMatch: string, propName: string, defaultPart: string | undefined, defaultValue: string | undefined) => {
					const value = item[propName];
					if (value !== undefined && value !== null) {
						return String(value);
					}
					return defaultValue || '';
				});
				
				// 替换简单的 {item} 为对象的第一个属性或 JSON 字符串
				const firstProperty = Object.keys(item)[0];
				if (firstProperty) {
					itemContent = itemContent.replace(/\{item\}/g, String(item[firstProperty] || JSON.stringify(item)));
				}
			} else {
				// 简单值处理
				itemContent = itemContent.replace(/\{item\}/g, String(item));
			}
			
			return itemContent;
		}).join('');
	});

	// 2. 处理带索引的循环语法
	const indexedLoopRegex = new RegExp(`\\{#${variableKey}:([^}]+)\\}([\\s\\S]*?)\\{\\/${variableKey}\\}`, 'g');
	processedContent = processedContent.replace(indexedLoopRegex, (match, indexVar, template) => {
		if (arrayValue.length === 0) return '';
		
		return arrayValue.map((item, index) => {
			let itemContent = template;
			
			// 替换循环内的变量
			itemContent = itemContent.replace(new RegExp(`\\{${indexVar}\\}`, 'g'), String(index + 1));
			
			// 处理对象属性访问
			if (typeof item === 'object' && item !== null) {
				itemContent = itemContent.replace(/\{item\.([^}|]+)(\|([^}]+))?\}/g, (propMatch: string, propName: string, defaultPart: string | undefined, defaultValue: string | undefined) => {
					const value = item[propName];
					if (value !== undefined && value !== null) {
						return String(value);
					}
					return defaultValue || '';
				});
				
				const firstProperty = Object.keys(item)[0];
				if (firstProperty) {
					itemContent = itemContent.replace(/\{item\}/g, String(item[firstProperty] || JSON.stringify(item)));
				}
			} else {
				itemContent = itemContent.replace(/\{item\}/g, String(item));
			}
			
			return itemContent;
		}).join('');
	});

	// 3. 处理表格行循环 (更复杂的场景)
	const tableRowRegex = new RegExp(`(<tr[^>]*>)\\{#${variableKey}\\}([\\s\\S]*?)\\{\\/${variableKey}\\}(</tr>)`, 'g');
	processedContent = processedContent.replace(tableRowRegex, (match, openTr, cellTemplate, closeTr) => {
		if (arrayValue.length === 0) return `${openTr}<td colspan="100%">暂无数据</td>${closeTr}`;
		
		return arrayValue.map((item, index) => {
			let rowContent = cellTemplate;
			
			// 替换循环内的变量
			rowContent = rowContent.replace(/\{index\}/g, String(index + 1));
			rowContent = rowContent.replace(/\{index0\}/g, String(index));
			
			// 处理对象属性访问
			if (typeof item === 'object' && item !== null) {
				rowContent = rowContent.replace(/\{item\.([^}|]+)(\|([^}]+))?\}/g, (propMatch: string, propName: string, defaultPart: string | undefined, defaultValue: string | undefined) => {
					const value = item[propName];
					if (value !== undefined && value !== null) {
						return String(value);
					}
					return defaultValue || '';
				});
				
				const firstProperty = Object.keys(item)[0];
				if (firstProperty) {
					rowContent = rowContent.replace(/\{item\}/g, String(item[firstProperty] || JSON.stringify(item)));
				}
			} else {
				rowContent = rowContent.replace(/\{item\}/g, String(item));
			}
			
			return `${openTr}${rowContent}${closeTr}`;
		}).join('');
	});

	// 4. 处理无序列表循环
	const listItemRegex = new RegExp(`(<ul[^>]*>)\\{#${variableKey}\\}([\\s\\S]*?)\\{\\/${variableKey}\\}(</ul>)`, 'g');
	processedContent = processedContent.replace(listItemRegex, (match, openUl, itemTemplate, closeUl) => {
		if (arrayValue.length === 0) return `${openUl}<li>暂无数据</li>${closeUl}`;
		
		const listItems = arrayValue.map((item, index) => {
			let itemContent = itemTemplate;
			
			// 替换循环内的变量
			itemContent = itemContent.replace(/\{index\}/g, String(index + 1));
			itemContent = itemContent.replace(/\{index0\}/g, String(index));
			
			// 处理对象属性访问
			if (typeof item === 'object' && item !== null) {
				itemContent = itemContent.replace(/\{item\.([^}|]+)(\|([^}]+))?\}/g, (propMatch: string, propName: string, defaultPart: string | undefined, defaultValue: string | undefined) => {
					const value = item[propName];
					if (value !== undefined && value !== null) {
						return String(value);
					}
					return defaultValue || '';
				});
				
				const firstProperty = Object.keys(item)[0];
				if (firstProperty) {
					itemContent = itemContent.replace(/\{item\}/g, String(item[firstProperty] || JSON.stringify(item)));
				}
			} else {
				itemContent = itemContent.replace(/\{item\}/g, String(item));
			}
			
			return itemContent.startsWith('<li>') ? itemContent : `<li>${itemContent}</li>`;
		}).join('');
		
		return `${openUl}${listItems}${closeUl}`;
	});

	// 5. 处理有序列表循环
	const orderedListRegex = new RegExp(`(<ol[^>]*>)\\{#${variableKey}\\}([\\s\\S]*?)\\{\\/${variableKey}\\}(</ol>)`, 'g');
	processedContent = processedContent.replace(orderedListRegex, (match, openOl, itemTemplate, closeOl) => {
		if (arrayValue.length === 0) return `${openOl}<li>暂无数据</li>${closeOl}`;
		
		const listItems = arrayValue.map((item, index) => {
			let itemContent = itemTemplate;
			
			// 替换循环内的变量
			itemContent = itemContent.replace(/\{index\}/g, String(index + 1));
			itemContent = itemContent.replace(/\{index0\}/g, String(index));
			
			// 处理对象属性访问
			if (typeof item === 'object' && item !== null) {
				itemContent = itemContent.replace(/\{item\.([^}|]+)(\|([^}]+))?\}/g, (propMatch: string, propName: string, defaultPart: string | undefined, defaultValue: string | undefined) => {
					const value = item[propName];
					if (value !== undefined && value !== null) {
						return String(value);
					}
					return defaultValue || '';
				});
				
				const firstProperty = Object.keys(item)[0];
				if (firstProperty) {
					itemContent = itemContent.replace(/\{item\}/g, String(item[firstProperty] || JSON.stringify(item)));
				}
			} else {
				itemContent = itemContent.replace(/\{item\}/g, String(item));
			}
			
			return itemContent.startsWith('<li>') ? itemContent : `<li>${itemContent}</li>`;
		}).join('');
		
		return `${openOl}${listItems}${closeOl}`;
	});

	return processedContent;
}

// 生成默认HTML模板 - 添加循环变量示例
function generateDefaultHTMLTemplate(): string {
	return `
		<div class="header">
			<h1 class="title">{announcementTitle}</h1>
			<p>公告编号：{announcementNumber}</p>
		</div>

		<div class="section">
			<h2>一、采购项目基本信息</h2>
			<table>
				<tr><td><strong>项目名称</strong></td><td>{projectName}</td></tr>
				<tr><td><strong>项目地址</strong></td><td>{projectAddress}</td></tr>
				<tr><td><strong>项目描述</strong></td><td>{projectDescription}</td></tr>
				<tr><td><strong>预算金额</strong></td><td>{budgetAmount} {currency}</td></tr>
			</table>
		</div>

		<div class="section">
			<h2>二、采购方信息</h2>
			<table>
				<tr><td><strong>采购方名称</strong></td><td>{purchaserName}</td></tr>
				<tr><td><strong>采购方地址</strong></td><td>{purchaserAddress}</td></tr>
				<tr><td><strong>联系人</strong></td><td>{contactPerson}</td></tr>
				<tr><td><strong>联系电话</strong></td><td>{contactPhone}</td></tr>
				<tr><td><strong>联系邮箱</strong></td><td>{contactEmail}</td></tr>
			</table>
		</div>

		<div class="section">
			<h2>三、资格要求</h2>
			<table>
				<thead>
					<tr>
						<th>序号</th>
						<th>要求项目</th>
						<th>具体描述</th>
					</tr>
				</thead>
				<tbody>
					{#qualificationRequirements}
					<tr>
						<td>{index}</td>
						<td>{item.requirement}</td>
						<td>{item.description}</td>
					</tr>
					{/qualificationRequirements}
				</tbody>
			</table>
		</div>

		<div class="section">
			<h2>四、报价要求</h2>
			<table>
				<thead>
					<tr>
						<th>序号</th>
						<th>报价项目</th>
						<th>规格要求</th>
						<th>数量</th>
						<th>单位</th>
					</tr>
				</thead>
				<tbody>
					{#quotationRequirements}
					<tr>
						<td>{index}</td>
						<td>{item.item}</td>
						<td>{item.specification}</td>
						<td>{item.quantity}</td>
						<td>{item.unit}</td>
					</tr>
					{/quotationRequirements}
				</tbody>
			</table>
		</div>

		<div class="section">
			<h2>五、时间安排</h2>
			<table>
				<tr><td><strong>发布日期</strong></td><td>{publishDate}</td></tr>
				<tr><td><strong>报名开始时间</strong></td><td>{registrationStart}</td></tr>
				<tr><td><strong>报名结束时间</strong></td><td>{registrationEnd}</td></tr>
				<tr><td><strong>提交截止时间</strong></td><td>{submissionDeadline}</td></tr>
				<tr><td><strong>开标时间</strong></td><td>{openingTime}</td></tr>
				<tr><td><strong>截止日期</strong></td><td>{deadline}</td></tr>
			</table>
		</div>

		<div class="section">
			<h2>六、费用信息</h2>
			<table>
				<tr><td><strong>参与费用</strong></td><td>{participationFee} {currency}</td></tr>
				<tr><td><strong>保证金</strong></td><td>{guaranteeDeposit} {currency}</td></tr>
			</table>
		</div>

		<div class="section">
			<h2>七、其他条款</h2>
			<p><strong>评审标准：</strong>{evaluationCriteria}</p>
			<p><strong>特殊条款：</strong>{specialTerms}</p>
			<p><strong>法律声明：</strong>{legalNotices}</p>
		</div>
	`;
}

function downloadDocument(doc: GeneratedDocument) {
	const link = document.createElement('a');
	link.href = doc.downloadUrl;
	link.download = doc.name;
	link.click();
}

// 辅助函数
function getTemplateName(templateId: string): string {
	const template = wordState.templates.find(t => t.id === templateId);
	return template?.name || '未知模板';
}

function getFieldLabel(fieldKey: string): string {
	const variable = wordState.currentTemplate?.variables.find(v => v.key === fieldKey);
	return variable?.label || fieldKey;
}

function formatDate(dateString: string): string {
	return new Date(dateString).toLocaleString('zh-CN');
}

function loadTemplates() {
	// 这里应该从服务器加载模板列表
	// 暂时使用默认模板
	if (wordState.templates.length === 0) {
		const defaultTemplate: WordTemplate = {
			id: 'default',
			name: '默认采购公告模板',
			description: '标准的采购公告模板，包含所有必要字段',
			fileName: '采购公告模板.docx',
			fileUrl: '', // 空文件URL，将使用HTML转Word生成
			variables: WordProcessor.getDefaultProcurementVariables(),
			createdAt: new Date().toISOString(),
			updatedAt: new Date().toISOString(),
		};

		wordState.templates.push(defaultTemplate);
		
		// 自动选择默认模板
		handleTemplateSelected(defaultTemplate);
		
		// 设置默认的编辑器内容
		wordState.editorContent = generateDefaultHTMLTemplate();
		
		ElMessage.success('已加载默认模板，您可以开始编辑');
	}
}

// 预览功能函数
function generateDocumentPreview() {
	if (!wordState.currentTemplate) {
		ElMessage.error('请先选择模板');
		return;
	}

	try {
		wordState.isGenerating = true;

		// 生成HTML预览内容
		let htmlContent = wordState.editorContent || generateDefaultHTMLTemplate();
		
		// 替换模板变量 - 支持循环变量
		wordState.currentTemplate.variables.forEach(variable => {
			const value = wordState.templateData[variable.key];
			if (value !== undefined && value !== null) {
				if (variable.type === 'array' && Array.isArray(value)) {
					// 处理循环变量
					htmlContent = processArrayVariable(htmlContent, variable.key, value);
				} else {
					// 处理普通变量
					const regex = new RegExp(`\\{${variable.key}\\}`, 'g');
					htmlContent = htmlContent.replace(regex, String(value));
				}
			}
		});

		// 设置预览内容
		wordState.previewContent = htmlContent;
		
		ElMessage.success('预览生成成功，您可以在下方编辑器中查看和修改内容');
		
		// 显示打印指导
		setTimeout(() => {
			ElMessage({
				type: 'info',
				duration: 10000,
				message: '💡 提示：下载文档时如需去除页头页脚，请在浏览器打印选项中关闭"页眉和页脚"设置',
				showClose: true
			});
		}, 1000);
	} catch (error) {
		console.error('生成预览失败:', error);
		ElMessage.error('生成预览失败，请检查模板和数据');
	} finally {
		wordState.isGenerating = false;
	}
}

function downloadFromPreview() {
	if (!wordState.previewContent) {
		ElMessage.error('请先生成预览文档');
		return;
	}

	try {
		wordState.isDownloading = true;

		// 使用预览内容生成Word文档
		const fullHTML = `
			<!DOCTYPE html>
			<html xmlns:o="urn:schemas-microsoft-com:office:office" xmlns:w="urn:schemas-microsoft-com:office:word" xmlns="http://www.w3.org/TR/REC-html40">
				<head>
					<meta charset="utf-8">
					<meta name="ProgId" content="Word.Document">
					<meta name="Generator" content="Microsoft Word">
					<meta name="Originator" content="Microsoft Word">
					<title>${wordState.templateData.announcementTitle || '采购公告'}</title>
					<!--[if gte mso 9]>
					<xml>
						<w:WordDocument>
							<w:View>Print</w:View>
							<w:Zoom>90</w:Zoom>
							<w:DoNotPromptForConvert/>
							<w:DoNotShowInsertionsAndDeletions/>
						</w:WordDocument>
					</xml>
					<![endif]-->
					<style>
						@page {
							size: A4;
							margin: 2.54cm 3.17cm 2.54cm 3.17cm;
							mso-page-orientation: portrait;
							/* 强制去除浏览器默认页头页脚 */
							@top-left { content: none; }
							@top-center { content: none; }
							@top-right { content: none; }
							@bottom-left { content: none; }
							@bottom-center { content: none; }
							@bottom-right { content: none; }
						}
						
						@media print {
							@page {
								margin: 0 !important;
								size: A4;
								/* 完全禁用页头页脚 */
								@top-left { content: none !important; }
								@top-center { content: none !important; }
								@top-right { content: none !important; }
								@bottom-left { content: none !important; }
								@bottom-center { content: none !important; }
								@bottom-right { content: none !important; }
							}
							
							html, body {
								margin: 0 !important;
								padding: 0 !important;
								-webkit-print-color-adjust: exact;
								color-adjust: exact;
							}
						}
						
						body {
							font-family: "微软雅黑", "Microsoft YaHei", "宋体", SimSun, sans-serif;
							font-size: 12pt;
							line-height: 1.5;
							margin: 0;
							padding: 0;
							color: #000;
							background: white;
						}
						
						h1, h2, h3, h4, h5, h6 {
							color: #000;
							margin: 18pt 0 12pt 0;
							font-weight: bold;
						}
						
						h1 { font-size: 18pt; text-align: center; }
						h2 { font-size: 16pt; }
						h3 { font-size: 14pt; }
						h4 { font-size: 12pt; }
						
						p {
							margin: 6pt 0;
							text-align: justify;
							text-justify: inter-ideograph;
						}
						
						div {
							margin: 3pt 0;
							text-align: justify;
							text-justify: inter-ideograph;
							line-height: 1.4;
						}
						
						/* 循环变量生成的div元素紧凑样式 */
						div[style*="margin: 4px 0"] {
							margin: 2pt 0 !important;
							line-height: 1.3 !important;
						}
						
						table {
							border-collapse: collapse;
							width: 100%;
							margin: 12pt 0;
							font-size: 10.5pt;
						}
						
						th, td {
							border: 1pt solid #000;
							padding: 6pt;
							text-align: left;
							vertical-align: top;
						}
						
						th {
							background-color: #f0f0f0;
							font-weight: bold;
							text-align: center;
						}
						
						ul, ol {
							margin: 6pt 0 6pt 24pt;
							padding: 0;
						}
						
						li {
							margin: 3pt 0;
						}
						
						.header {
							text-align: center;
							margin: 24pt 0;
						}
						
						.title {
							font-size: 22pt;
							font-weight: bold;
							margin: 0 0 12pt 0;
						}
						
						.subtitle {
							font-size: 14pt;
							margin: 6pt 0;
						}
						
						.section {
							margin: 18pt 0;
						}
						
						.section-title {
							font-size: 14pt;
							font-weight: bold;
							margin: 12pt 0 6pt 0;
							border-bottom: 1pt solid #000;
							padding-bottom: 3pt;
						}
						
						.indent {
							text-indent: 24pt;
						}
						
						.signature {
							margin-top: 36pt;
							text-align: right;
						}
						
						.footer {
							margin-top: 24pt;
							font-size: 10pt;
							color: #666;
						}
					</style>
				</head>
				<body>
					${wordState.previewContent}
				</body>
			</html>
		`;

		// 创建Word兼容的HTML文档
		const blob = new Blob([fullHTML], { 
			type: 'application/msword' 
		});
		
		const fileName = `${wordState.templateData.announcementTitle || '采购公告'}.doc`;
		
		// 下载Word文件
		const link = document.createElement('a');
		link.href = URL.createObjectURL(blob);
		link.download = fileName;
		link.click();

		// 记录生成历史
		const generatedDoc: GeneratedDocument = {
			id: Date.now().toString(),
			name: fileName,
			templateId: wordState.currentTemplate!.id,
			generatedAt: new Date().toISOString(),
			downloadUrl: URL.createObjectURL(blob),
			data: { ...wordState.templateData },
		};

		wordState.generatedDocuments.unshift(generatedDoc);

		ElMessage.success('Word文档下载成功（.doc格式，可用Word打开）');
	} catch (error) {
		console.error('下载文档失败:', error);
		ElMessage.error('下载文档失败，请重试');
	} finally {
		wordState.isDownloading = false;
	}
}

function clearPreview() {
	wordState.previewContent = '';
	ElMessage.info('预览内容已清除');
}

function handlePreviewSave(content: string) {
	wordState.previewContent = content;
}

onMounted(() => {
	console.log('采购公告组件已加载');
	loadTemplates();
});
</script>

<style lang="scss" scoped>
.procurement-announcement-container {
	padding: 24px;
	background: #ffffff;
	border-radius: 8px;
	box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

	.announcement-header {
		margin-bottom: 24px;
		padding-bottom: 16px;
		border-bottom: 1px solid #f0f0f0;

		.announcement-title {
			margin: 0 0 8px 0;
			font-size: 18px;
			font-weight: 600;
			color: #1d2129;
		}

		.announcement-description {
			margin: 0;
			font-size: 14px;
			color: #86909c;
		}

		.usage-tip {
			margin-top: 16px;
		}
	}

	.announcement-tabs {
		:deep(.el-tabs__content) {
			padding: 20px 0;
		}
	}

	.section-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 20px;
		padding-bottom: 12px;
		border-bottom: 1px solid #e8e8e8;

		h4 {
			margin: 0;
			font-size: 16px;
			font-weight: 600;
			color: #1d2129;
		}

		.template-actions,
		.data-actions,
		.generate-actions {
			display: flex;
			gap: 8px;
		}
	}

	.editor-section {
		padding: 0;
	}

	.data-config-section {
		.template-data-form {
			margin-top: 20px;

			.array-input {
				.complex-array-input {
					.complex-array-item {
						margin-bottom: 16px;

						.array-item-card {
							:deep(.el-card__body) {
								padding: 16px;
							}

							.array-item-header {
								display: flex;
								justify-content: space-between;
								align-items: center;
								margin-bottom: 16px;
								padding-bottom: 8px;
								border-bottom: 1px solid #e8e8e8;

								span {
									font-weight: 600;
									color: #1d2129;
								}
							}
						}
					}
				}

				.simple-array-input {
					.array-item {
						display: flex;
						align-items: center;
						gap: 8px;
						margin-bottom: 8px;

						.el-input {
							flex: 1;
						}
					}
				}

				.array-item {
					display: flex;
					align-items: center;
					gap: 8px;
					margin-bottom: 8px;

					.el-input {
						flex: 1;
					}
				}
			}
		}
	}

	.generate-section {
		.generation-history {
			margin-top: 32px;

			h5 {
				margin: 0 0 16px 0;
				font-size: 14px;
				font-weight: 600;
				color: #1d2129;
			}
		}

		.print-guidance {
			margin-bottom: 20px;
			
			:deep(.el-alert__content) {
				p {
					font-size: 14px;
					line-height: 1.5;
				}
				
				ul {
					font-size: 13px;
					line-height: 1.4;
					
					li {
						margin-bottom: 4px;
						color: #666;
					}
				}
			}
		}
	}

	.validation-result {
		.success-message {
			display: flex;
			align-items: center;
			gap: 8px;
			padding: 16px;
			background: #f6ffed;
			border: 1px solid #b7eb8f;
			border-radius: 6px;
			color: #52c41a;

			.success-icon {
				font-size: 20px;
			}
		}

		.error-message {
			.error-icon {
				font-size: 20px;
				color: #ff4d4f;
			}

			.missing-fields,
			.validation-errors {
				margin-top: 16px;

				h6 {
					margin: 0 0 8px 0;
					font-size: 14px;
					font-weight: 600;
					color: #1d2129;
				}

				ul {
					margin: 0;
					padding-left: 20px;

					li {
						margin-bottom: 4px;
						color: #86909c;
					}
				}
			}
		}
	}

	.preview-editor-section {
		margin-top: 20px;
		
		.preview-header {
			margin-bottom: 16px;
			
			h5 {
				margin: 0 0 12px 0;
				font-size: 16px;
				font-weight: 600;
				color: #1890ff;
			}
		}

		.preview-editor {
			border-radius: 8px;
			overflow: hidden;
			box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
		}
	}
}

/* 打印和预览样式 - 去除页头页脚 */
@media print {
	@page {
		margin: 0;
		size: A4;
	}
	
	.preview-editor-section {
		.preview-header {
			display: none !important;
		}
	}
	
	:deep(.w-e-toolbar) {
		display: none !important;
	}
	
	:deep(.editor-toolbar) {
		display: none !important;
	}
}

/* 预览编辑器内容样式优化 */
:deep(.preview-editor) {
	.w-e-text-container {
		@media print {
			box-shadow: none !important;
			border: none !important;
		}
	}
}

:deep(.el-form-item__label) {
	font-weight: 500;
}

:deep(.el-input-number) {
	width: 100%;
}
</style> 