// Word模板相关类型定义

export interface TemplateVariable {
  key: string;
  label: string;
  type: 'text' | 'number' | 'date' | 'array' | 'image' | 'string' | 'link';
  required: boolean;
  defaultValue?: any;
  description?: string;
}

export interface WordTemplate {
  id: string;
  name: string;
  description?: string;
  fileName: string;
  fileUrl: string;
  variables: TemplateVariable[];
  createdAt: string;
  updatedAt: string;
}

export interface TemplateData {
  [key: string]: any;
}

export interface DocumentGenerationOptions {
  template: WordTemplate;
  data: TemplateData;
  outputFileName?: string;
}

export interface WordDocumentState {
  templates: WordTemplate[];
  currentTemplate: WordTemplate | null;
  templateData: TemplateData;
  generatedDocuments: GeneratedDocument[];
  isGenerating: boolean;
  editorContent: string;
  loading: boolean;
}

export interface GeneratedDocument {
  id: string;
  name: string;
  templateId: string;
  generatedAt: string;
  downloadUrl: string;
  data: TemplateData;
}

// 富文本编辑器配置
export interface EditorConfig {
  placeholder: string;
  readOnly: boolean;
  height: number;
  toolbar: string[];
}

// 模板变量映射配置
export interface VariableMapping {
  templateVariable: string;
  sourceField: string;
  transformer?: (value: any) => any;
}

// 采购公告特定的模板变量
export interface ProcurementTemplateVariables {
  // 基本信息
  announcementTitle: string;
  announcementNumber: string;
  publishDate: string;
  deadline: string;

  // 采购方信息
  purchaserName: string;
  purchaserAddress: string;
  contactPerson: string;
  contactPhone: string;
  contactEmail: string;

  // 项目信息
  projectName: string;
  projectDescription: string;
  projectAddress: string;
  budgetAmount: number;
  currency: string;

  // 资格要求
  qualificationRequirements: Array<{
    requirement: string;
    description: string;
  }>;

  // 报价要求
  quotationRequirements: Array<{
    item: string;
    specification: string;
    quantity: number;
    unit: string;
  }>;

  // 时间安排
  registrationStart: string;
  registrationEnd: string;
  submissionDeadline: string;
  openingTime: string;

  // 费用设置
  participationFee: number;
  guaranteeDeposit: number;

  // 其他条款
  evaluationCriteria: string;
  specialTerms: string;
  legalNotices: string;
} 