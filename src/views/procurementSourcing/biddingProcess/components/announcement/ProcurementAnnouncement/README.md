# Word模板编辑与文档生成功能

## 功能概述

在采购公告组件中集成了完整的Word模板编辑和文档生成功能，支持：

1. **Word模板管理**：上传、编辑、删除Word模板
2. **富文本编辑器**：编辑模板内容和文档
3. **模板变量系统**：定义和管理动态数据字段
4. **数据配置**：填写模板变量数据
5. **文档生成**：基于模板和数据生成Word文档
6. **历史记录**：查看和下载生成的文档
7. **循环变量**：支持数组数据的循环渲染

## 主要功能

### 1. 模板管理

**上传模板**：
- 支持.docx和.doc格式
- 自动提取模板变量
- 配置模板基本信息

**编辑模板**：
- 修改模板名称和描述
- 管理模板变量（类型、标签、是否必填等）
- 配置默认值

**模板变量**：
- 文本类型：普通文本输入
- 数字类型：数值输入
- 日期类型：日期选择器
- 数组类型：动态列表（支持复杂对象）
- 图片类型：图片上传

### 2. 富文本编辑器

**功能特性**：
- 完整的富文本编辑工具栏
- 插入模板变量（支持简单格式和带标签格式）
- 模板预览功能
- 导出预览功能

**变量插入**：
```
简单格式：{变量名}
带标签格式：标签名：{变量名}
```

### 3. 循环变量功能 🆕

**支持的循环语法**：

1. **简单循环**：
```html
{#variableKey}
<p>{item}</p>
{/variableKey}
```

2. **带索引的循环**：
```html
{#variableKey:i}
<p>{i}. {item}</p>
{/variableKey}
```

3. **表格行循环**：
```html
<table>
  <tr>
    <th>序号</th>
    <th>内容</th>
  </tr>
  {#variableKey}
  <tr>
    <td>{index}</td>
    <td>{item}</td>
  </tr>
  {/variableKey}
</table>
```

4. **对象属性访问**：
```html
{#qualificationRequirements}
<tr>
  <td>{index}</td>
  <td>{item.requirement}</td>
  <td>{item.description}</td>
</tr>
{/qualificationRequirements}
```

5. **列表循环**：
```html
<ul>
  {#variableKey}
  <li>{item}</li>
  {/variableKey}
</ul>

<ol>
  {#variableKey}
  <li>{item}</li>
  {/variableKey}
</ol>
```

**循环变量语法说明**：
- `{#variableKey}` - 开始循环
- `{/variableKey}` - 结束循环
- `{item}` - 当前循环项的值
- `{index}` - 当前循环项的序号（从1开始）
- `{index0}` - 当前循环项的索引（从0开始）
- `{item.property}` - 访问对象的属性
- `{item.property|default}` - 访问对象属性，如果为空则使用默认值

**预置的复杂对象数组**：

1. **资格要求（qualificationRequirements）**：
```javascript
{
  requirement: "要求项目",
  description: "具体描述"
}
```

2. **报价要求（quotationRequirements）**：
```javascript
{
  item: "报价项目",
  specification: "规格要求", 
  quantity: 数量,
  unit: "单位"
}
```

### 4. 数据配置

**数据类型支持**：
- **文本**：普通文本输入框
- **数字**：数字输入器
- **日期**：日期选择器
- **数组**：动态添加/删除项目
  - 简单数组：字符串列表
  - 复杂对象数组：结构化数据（如资格要求、报价要求）
- **图片**：图片文件上传

**复杂对象数组编辑**：
- 资格要求：可视化编辑要求项目和描述
- 报价要求：可视化编辑项目、规格、数量、单位
- 自动表单验证和数据结构管理

**数据管理**：
- 重置数据：恢复到默认值
- 加载示例数据：填充预设的示例数据（包含循环变量示例）
- 数据验证：检查必填字段和数据格式

### 5. 文档生成

**生成流程**：
1. 数据验证：检查必填字段和数据格式
2. 循环处理：处理数组变量的循环模板
3. 模板渲染：将数据填充到模板变量
4. 文档生成：生成Word文档
5. 自动下载：直接下载生成的文档

**生成历史**：
- 记录所有生成的文档
- 支持重新下载历史文档
- 显示生成时间和使用的模板

## 使用步骤

### 1. 准备模板

**模板编辑示例**：
```html
<div class="header">
  <h1>{announcementTitle}</h1>
  <p>公告编号：{announcementNumber}</p>
</div>

<div class="section">
  <h2>资格要求</h2>
  <table>
    <tr>
      <th>序号</th>
      <th>要求项目</th>
      <th>具体描述</th>
    </tr>
    {#qualificationRequirements}
    <tr>
      <td>{index}</td>
      <td>{item.requirement}</td>
      <td>{item.description}</td>
    </tr>
    {/qualificationRequirements}
  </table>
</div>
```

### 2. 配置数据

1. 在"数据配置"页面填写基本信息
2. 对于数组类型变量：
   - 简单数组：直接添加字符串项目
   - 资格要求：填写要求项目和描述
   - 报价要求：填写项目、规格、数量、单位
3. 使用"加载示例数据"快速填充测试数据

### 3. 生成文档

1. 点击"验证并生成"检查数据完整性
2. 确认数据无误后生成Word文档
3. 文档将自动下载，包含循环渲染的数据

## 循环变量使用实例

### 实例1：简单列表
```html
<h3>采购项目列表</h3>
<ul>
  {#projectList}
  <li>{item}</li>
  {/projectList}
</ul>
```

### 实例2：表格数据
```html
<table>
  <thead>
    <tr>
      <th>序号</th>
      <th>要求</th>
      <th>说明</th>
    </tr>
  </thead>
  <tbody>
    {#qualificationRequirements}
    <tr>
      <td>{index}</td>
      <td>{item.requirement}</td>
      <td>{item.description}</td>
    </tr>
    {/qualificationRequirements}
  </tbody>
</table>
```

### 实例3：复杂布局
```html
{#quotationRequirements}
<div class="quotation-item">
  <h4>{index}. {item.item}</h4>
  <p><strong>规格：</strong>{item.specification}</p>
  <p><strong>数量：</strong>{item.quantity} {item.unit}</p>
</div>
{/quotationRequirements}
```

## 技术特性

- **类型安全**：TypeScript类型定义确保数据结构正确
- **响应式界面**：基于Vue 3 Composition API的响应式数据管理
- **组件化设计**：模块化的组件结构便于维护和扩展
- **错误处理**：完善的数据验证和错误提示机制
- **兼容性**：支持多种Word文档格式和浏览器环境

## 注意事项

1. **循环变量命名**：确保循环变量名与数据配置中的字段名一致
2. **数据结构**：复杂对象数组需要按照预定义的数据结构填写
3. **模板语法**：循环标签必须正确配对，避免嵌套错误
4. **性能考虑**：大量数据循环时可能影响文档生成性能
5. **浏览器兼容性**：建议使用现代浏览器以获得最佳体验

## 扩展功能

可以根据需要扩展以下功能：

1. **模板分类**：按用途对模板进行分类管理
2. **权限控制**：不同用户权限控制模板的访问和编辑
3. **版本管理**：模板版本控制和历史记录
4. **批量生成**：批量数据导入和批量文档生成
5. **模板市场**：共享和下载公共模板
6. **API集成**：与后端API集成，实现数据自动填充
7. **电子签名**：集成电子签名功能
8. **PDF导出**：支持导出为PDF格式 