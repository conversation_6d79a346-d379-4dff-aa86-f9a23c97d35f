import { ref, computed, nextTick, onMounted, onUnmounted } from 'vue';

export function useAnchorNavigation(isInviteMode: any) {
  const activeAnchor = ref('announcement');
  const sliderStyle = ref({ height: '16px', transform: 'translateY(0px)' });
  const isSliderReady = ref(false);

  // 锚点列表
  const anchorList = computed(() => {
    return [
      { id: 'announcement', label: `${!isInviteMode.value ? '公告' : '邀请'}信息`, show: true },
      { id: 'qualification', label: '资质要求', show: !isInviteMode.value },
      { id: 'quotation', label: '报价要求', show: true },
      { id: 'conditions', label: '报价响应条件', show: !isInviteMode.value },
      { id: 'time', label: '采购时间要求', show: true },
      { id: 'evaluation', label: '评审规则', show: true },
      { id: 'fee', label: '保证金设置', show: true },
      { id: 'notice', label: '供应商报价须知', show: true },
      { id: 'contact', label: '联系方式', show: true },
      { id: 'media', label: '发布媒体', show: !isInviteMode.value },
      { id: 'attachments', label: '采购公告附件', show: !isInviteMode.value },
      { id: 'invite', label: '邀请供应商', show: isInviteMode.value },
      { id: 'attachments', label: '邀请函附件', show: isInviteMode.value },
    ];
  });

  // 锚点导航
  function scrollToSection(sectionId: string) {
    const targetElement = document.getElementById(`section-${sectionId}`);
    const biddingContainer = document.querySelector('.bidding-process-container');

    if (targetElement && biddingContainer) {
      // 立即更新 activeAnchor 和滑块位置
      activeAnchor.value = sectionId;
      updateSliderPosition();

      // 计算目标元素相对于滚动容器的位置
      const containerRect = biddingContainer.getBoundingClientRect();
      const targetRect = targetElement.getBoundingClientRect();
      const scrollTop = biddingContainer.scrollTop;

      // 计算需要滚动到的位置
      const offsetTop = targetRect.top - containerRect.top + scrollTop - 20; // 减去20px间距

      // 滚动容器而不是页面
      biddingContainer.scrollTo({
        top: offsetTop,
        behavior: 'smooth',
      });
    }
  }

  function updateSliderPosition() {
    nextTick(() => {
      const activeItem = document.querySelector('.nav-item.active') as HTMLElement;

      if (activeItem) {
        const relativeTop = activeItem.offsetTop;
        const itemHeight = activeItem.offsetHeight;

        sliderStyle.value = {
          transform: `translateY(${relativeTop}px)`,
          height: `16px`,
        };

        // 首次计算完成后显示滑块
        if (!isSliderReady.value) {
          isSliderReady.value = true;
        }
      }
    });
  }

  // 处理滚动事件，更新活跃锚点
  function handleScroll() {
    const biddingContainer = document.querySelector('.bidding-process-container') as HTMLElement;
    if (!biddingContainer) return;

    const scrollTop = biddingContainer.scrollTop;
    const containerRect = biddingContainer.getBoundingClientRect();

    const sections = anchorList.value
      .map((anchor) => {
        const element = document.getElementById(`section-${anchor.id}`);
        if (!element) return null;

        const elementRect = element.getBoundingClientRect();
        // 计算相对于滚动容器的位置
        const relativeTop = elementRect.top - containerRect.top + scrollTop;

        return {
          id: anchor.id,
          element,
          offsetTop: relativeTop,
        };
      })
      .filter((item) => item !== null);

    // 找到当前滚动位置对应的区域
    for (let i = sections.length - 1; i >= 0; i--) {
      const section = sections[i];
      if (section && section.offsetTop <= scrollTop + 100) {
        if (activeAnchor.value !== section.id) {
          activeAnchor.value = section.id;
          updateSliderPosition();
        }
        break;
      }
    }
  }

  // 初始化
  const initAnchorNavigation = () => {
    nextTick(() => {
      isSliderReady.value = true;
      updateSliderPosition();

      // 监听容器滚动事件
      const biddingContainer = document.querySelector('.bidding-process-container');
      if (biddingContainer) {
        biddingContainer.addEventListener('scroll', handleScroll);
      }

      // 延迟初始化滑块位置，确保DOM完全渲染
      setTimeout(() => {
        updateSliderPosition();
      }, 100);
    });
  };

  // 清理
  const cleanupAnchorNavigation = () => {
    const biddingContainer = document.querySelector('.bidding-process-container');
    if (biddingContainer) {
      biddingContainer.removeEventListener('scroll', handleScroll);
    }
  };

  return {
    activeAnchor,
    sliderStyle,
    isSliderReady,
    anchorList,
    scrollToSection,
    updateSliderPosition,
    handleScroll,
    initAnchorNavigation,
    cleanupAnchorNavigation,
  };
}
