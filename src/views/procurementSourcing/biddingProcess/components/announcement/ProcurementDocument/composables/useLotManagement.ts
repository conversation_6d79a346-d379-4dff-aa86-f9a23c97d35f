import { ref, computed } from 'vue';
import type { BidsSegment, LotInfo } from '../types';
import { 
  getBidsSegmentsBySection, 
  createBidsSegment, 
  addBidsSegment, 
  removeBidsSegment 
} from '../dataTransformer';

export function useLotManagement(bidsSegments: any, lotList: any) {
  // 各个模块独立的标段选中状态
  const activeQualificationLotId = ref('');
  const activeConditionLotId = ref('');
  const activeFeeLotId = ref('');
  const activeInviteLotId = ref('');

  // 初始化标段选中状态
  const initializeLotSelection = (lots: LotInfo[]) => {
    const firstLotId = lots?.[0]?.id || '';
    activeQualificationLotId.value = firstLotId;
    activeConditionLotId.value = firstLotId;
    activeFeeLotId.value = firstLotId;
    activeInviteLotId.value = firstLotId;
  };

  // 各个模块的标段点击处理
  const handleQualificationLotClick = (id: string) => {
    activeQualificationLotId.value = id;
  };

  const handleConditionLotClick = (id: string) => {
    activeConditionLotId.value = id;
  };

  const handleFeeLotClick = (id: string) => {
    activeFeeLotId.value = id;
  };

  const handleInviteLotClick = (id: string) => {
    activeInviteLotId.value = id;
  };

  // 获取指定标段和类型的数据
  function getSectionData(sectionId: string, type: 'QUALIFICATION' | 'CONDITION' | 'FEE' | 'INVITE_SUPPLIERS') {
    return getBidsSegmentsBySection(bidsSegments.value, sectionId, type);
  }

  // 获取当前选中标段的数据
  const getCurrentQualificationData = computed(() => {
    return getSectionData(activeQualificationLotId.value, 'QUALIFICATION');
  });

  const getCurrentConditionData = computed(() => {
    return getSectionData(activeConditionLotId.value, 'CONDITION');
  });

  const getCurrentFeeData = computed(() => {
    const feeSegments = getSectionData(activeFeeLotId.value, 'FEE');
    return feeSegments.length > 0 ? feeSegments[0] : null;
  });

  const getCurrentInviteSuppliers = computed(() => {
    const inviteSegments = getSectionData(activeInviteLotId.value, 'INVITE_SUPPLIERS');
    const result = inviteSegments.length > 0 && inviteSegments[0].inviteSuppliers ? inviteSegments[0].inviteSuppliers : [];
    return result;
  });

  // 资格要求操作
  function addQualificationItem(index: number) {
    const newSegment = createBidsSegment('QUALIFICATION', activeQualificationLotId.value, {
      requirementName: '',
      requirementContent: '',
    });
    bidsSegments.value = addBidsSegment(bidsSegments.value, newSegment, getQualificationSegmentIndex(index));
  }

  function removeQualificationItem(index: number) {
    const qualificationSegments = getCurrentQualificationData.value;
    if (qualificationSegments.length > 1) {
      const segmentIndex = getQualificationSegmentIndex(index);
      bidsSegments.value = removeBidsSegment(bidsSegments.value, segmentIndex);
    }
  }

  function getQualificationSegmentIndex(localIndex: number): number {
    const qualificationSegments = getCurrentQualificationData.value;
    const targetSegment = qualificationSegments[localIndex];
    return bidsSegments.value.findIndex((segment: any) => segment === targetSegment);
  }

  // 报价响应条件操作
  function addConditionItem(index: number) {
    const newSegment = createBidsSegment('CONDITION', activeConditionLotId.value, {
      requirementName: '',
      requirementContent: '',
    });
    bidsSegments.value = addBidsSegment(bidsSegments.value, newSegment, getConditionSegmentIndex(index));
  }

  function removeConditionItem(index: number) {
    const conditionSegments = getCurrentConditionData.value;
    if (conditionSegments.length > 1) {
      const segmentIndex = getConditionSegmentIndex(index);
      bidsSegments.value = removeBidsSegment(bidsSegments.value, segmentIndex);
    }
  }

  function getConditionSegmentIndex(localIndex: number): number {
    const conditionSegments = getCurrentConditionData.value;
    const targetSegment = conditionSegments[localIndex];
    return bidsSegments.value.findIndex((segment: any) => segment === targetSegment);
  }

  // 邀请供应商操作
  function updateInviteSuppliers(suppliers: any[]) {
    const inviteSegments = getSectionData(activeInviteLotId.value, 'INVITE_SUPPLIERS');
    if (inviteSegments.length > 0) {
      inviteSegments[0].inviteSuppliers = suppliers;
    } else {
      // 如果没有邀请供应商段，创建一个
      const newSegment = createBidsSegment('INVITE_SUPPLIERS', activeInviteLotId.value, {
        inviteSuppliers: suppliers,
      });
      bidsSegments.value.push(newSegment);
    }
  }

  function removeInviteSupplier(index: number) {
    const inviteSegments = getSectionData(activeInviteLotId.value, 'INVITE_SUPPLIERS');
    if (inviteSegments.length > 0 && inviteSegments[0].inviteSuppliers) {
      inviteSegments[0].inviteSuppliers.splice(index, 1);
    }
  }

  return {
    activeQualificationLotId,
    activeConditionLotId,
    activeFeeLotId,
    activeInviteLotId,
    initializeLotSelection,
    handleQualificationLotClick,
    handleConditionLotClick,
    handleFeeLotClick,
    handleInviteLotClick,
    getCurrentQualificationData,
    getCurrentConditionData,
    getCurrentFeeData,
    getCurrentInviteSuppliers,
    addQualificationItem,
    removeQualificationItem,
    addConditionItem,
    removeConditionItem,
    updateInviteSuppliers,
    removeInviteSupplier,
  };
}
