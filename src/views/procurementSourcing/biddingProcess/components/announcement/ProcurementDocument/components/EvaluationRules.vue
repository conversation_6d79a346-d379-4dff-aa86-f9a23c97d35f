<template>
  <div
    :id="'section-' + 'evaluation'"
    class="content-section"
    :class="{ 'is-hide-node': isZJWT }"
  >
    <el-collapse
      v-model="activeCollapse"
      class="section-collapse"
      expand-icon-position="left"
    >
      <el-collapse-item name="evaluation">
        <template #title>
          <span class="collapse-title">
            <el-icon class="collapse-icon"><CaretRight /></el-icon>
            评审规则
          </span>
        </template>
        <div class="form-content">
          <div class="section-form">
            <el-row :gutter="24">
              <el-col :span="24">
                <el-form-item
                  label="评审规则"
                  prop="evaluationMethod"
                >
                  <el-radio-group
                    :model-value="evaluationMethod"
                    @update:model-value="$emit('update:evaluation-method', $event)"
                  >
                    <el-radio
                      v-for="option in evaluationRuleOptions"
                      :key="option.value"
                      :label="option.value"
                    >
                      {{ option.label }}
                    </el-radio>
                  </el-radio-group>
                </el-form-item>
              </el-col>
            </el-row>
          </div>
        </div>
      </el-collapse-item>
    </el-collapse>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { CaretRight } from '@element-plus/icons-vue';
import { useBiddingStore } from '@/views/procurementSourcing/biddingProcess/stores';

const biddingStore = useBiddingStore();
const isZJWT = computed(() => biddingStore?.isZJWT);

interface Props {
  evaluationMethod: string;
  evaluationRuleOptions: Array<{ label: string; value: string }>;
}

defineProps<Props>();

defineEmits<{
  'update:evaluation-method': [value: string];
}>();

const activeCollapse = ref(['evaluation']);
</script>

<style lang="scss" scoped>
@import '../../../../styles/collapse-panel.scss';

.content-section {
  margin-bottom: 20px;
  &.is-hide-node {
    margin: 0;
    padding: 0;
    overflow: hidden;
    height: 0;
  }
}

.section-form {
  .el-row {
    @media (max-width: 768px) {
      .el-col {
        &:not(:last-child) {
          margin-bottom: 16px;
        }
      }
    }
  }

  .el-radio-group {
    @media (max-width: 576px) {
      display: flex;
      flex-direction: column;
      gap: 8px;
    }
  }
}
</style>
