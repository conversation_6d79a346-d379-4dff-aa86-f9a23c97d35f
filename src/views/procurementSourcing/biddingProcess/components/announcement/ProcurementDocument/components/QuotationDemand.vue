<template>
  <div
    :id="'section-' + 'quotation'"
    class="content-section"
  >
    <el-collapse
      v-model="activeCollapse"
      class="section-collapse"
      expand-icon-position="left"
    >
      <el-collapse-item name="quotation">
        <template #title>
          <span class="collapse-title">
            <el-icon class="collapse-icon"><CaretRight /></el-icon>
            报价要求
          </span>
        </template>
        <div class="form-content">
          <div class="section-form">
            <template v-if="isCompetitiveBidding">
              <el-row :gutter="24">
                <el-col :span="12">
                  <el-form-item
                    label="开标方式"
                    prop="quotationDemand.tenderWay"
                  >
                    <el-radio-group :model-value="formData.tenderWay" @update:model-value="$emit('update:tender-way', $event)">
                      <el-radio label="ONLINE">线上开标</el-radio>
                      <el-radio label="OFFLINE">线下开标</el-radio>
                    </el-radio-group>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item
                    label="开标地点"
                    prop="purchaseDateDemand.bidOpeningAddress"
                  >
                    <el-input
                      :model-value="formData.bidOpeningAddress"
                      placeholder="请输入开标地点"
                      @update:model-value="$emit('update:bid-opening-address', $event)"
                    />
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row :gutter="24">
                <el-col :span="24">
                  <el-form-item
                    label="文件递交地址"
                    prop="quotationDemand.fileSubmissionAddress"
                  >
                    <el-input
                      :model-value="formData.fileSubmissionAddress"
                      placeholder="请输入文件递交地址"
                      @update:model-value="$emit('update:file-submission-address', $event)"
                    />
                  </el-form-item>
                </el-col>
              </el-row>
            </template>
            <el-row :gutter="24">
              <el-col :span="12">
                <el-form-item label="项目所在地区">
                  <el-cascader
                    :model-value="selectedAreaPath"
                    style="width: 100%"
                    clearable
                    showAllLevels
                    :options="addressOptions"
                    :props="{
                      multiple: false,
                      value: 'value',
                      label: 'label',
                      children: 'children',
                      checkStrictly: false,
                      emitPath: true,
                      expandTrigger: 'hover',
                    }"
                    @update:model-value="$emit('area-change', $event)"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item
                  label="项目详细地址"
                  prop="quotationDemand.address"
                >
                  <el-input
                    :model-value="formData.address"
                    maxlength="100"
                    show-word-limit
                    placeholder="请输入详细地址"
                    @update:model-value="$emit('update:address', $event)"
                  />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="24">
              <el-col :span="12">
                <el-form-item
                  label="报价含税"
                  prop="quotationDemand.includeTax"
                >
                  <el-radio-group :model-value="formData.includeTax" @update:model-value="$emit('update:include-tax', $event)">
                    <el-radio :label="1">是</el-radio>
                    <el-radio :label="0">否</el-radio>
                  </el-radio-group>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item
                  label="发票要求"
                  prop="quotationDemand.certificateType"
                >
                  <el-radio-group :model-value="formData.certificateType" @update:model-value="$emit('update:certificate-type', $event)">
                    <el-radio
                      v-for="option in certificateTypeOptions"
                      :key="option.value"
                      :label="option.value"
                    >
                      {{ option.label }}
                    </el-radio>
                  </el-radio-group>
                </el-form-item>
              </el-col>
            </el-row>
          </div>
        </div>
      </el-collapse-item>
    </el-collapse>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { CaretRight } from '@element-plus/icons-vue';
import { useBiddingStore } from '@/views/procurementSourcing/biddingProcess/stores';
const biddingStore = useBiddingStore();
const isCompetitiveBidding = computed(() => biddingStore?.isCompetitiveBidding);

interface Props {
  formData: {
    tenderWay: string;
    bidOpeningAddress: string;
    fileSubmissionAddress: string;
    address: string;
    includeTax: number;
    certificateType: string;
  };
  sourcingType: string;
  selectedAreaPath: string[];
  addressOptions: any[];
  certificateTypeOptions: Array<{ label: string; value: string }>;
}

defineProps<Props>();

defineEmits<{
  'update:tender-way': [value: string];
  'update:bid-opening-address': [value: string];
  'update:file-submission-address': [value: string];
  'update:address': [value: string];
  'update:include-tax': [value: number];
  'update:certificate-type': [value: string];
  'area-change': [value: any];
}>();

const activeCollapse = ref(['quotation']);
</script>

<style lang="scss" scoped>
@import '../../../../styles/collapse-panel.scss';

.content-section {
  margin-bottom: 20px;
}

.section-form {
  .el-row {
    @media (max-width: 768px) {
      .el-col {
        &:not(:last-child) {
          margin-bottom: 16px;
        }
      }
    }

    @media (max-width: 576px) {
      .el-col {
        &[span='12'],
        &[span='8'] {
          span: 24 !important;
        }
      }
    }
  }

  .el-input,
  .el-select,
  .el-cascader {
    @media (max-width: 576px) {
      width: 100% !important;
    }
  }
}
</style>
