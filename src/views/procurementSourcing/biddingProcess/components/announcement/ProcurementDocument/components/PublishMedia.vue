<template>
  <div
    v-if="!isInviteMode"
    :id="'section-' + 'media'"
    class="content-section"
  >
    <el-collapse
      v-model="activeCollapse"
      class="section-collapse team-section"
      expand-icon-position="left"
    >
      <el-collapse-item name="media">
        <template #title>
          <span class="collapse-title">
            <el-icon class="collapse-icon"><CaretRight /></el-icon>
            发布媒体
          </span>
        </template>
        <div class="form-content">
          <el-checkbox-group
            :model-value="selectedMedia"
            size="small"
            @update:model-value="$emit('update:selected-media', $event)"
          >
            <el-checkbox
              v-for="item in publishMedia"
              :key="item.mediaName"
              :label="item.mediaName"
              size="large"
              border
            >
              {{ item.mediaName }}
            </el-checkbox>
          </el-checkbox-group>
        </div>
      </el-collapse-item>
    </el-collapse>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { CaretRight } from '@element-plus/icons-vue';
import type { PublishMedia as PublishMediaType } from '../types';

interface Props {
  isInviteMode: boolean;
  publishMedia: PublishMediaType[];
  selectedMedia: string[];
}

defineProps<Props>();

defineEmits<{
  'update:selected-media': [value: string[]];
}>();

const activeCollapse = ref(['media']);
</script>

<style lang="scss" scoped>
@import '../../../../styles/collapse-panel.scss';

.content-section {
  margin-bottom: 20px;
}

.form-content {
  .el-checkbox-group {
    display: flex;
    flex-wrap: wrap;
    gap: 12px;

    @media (max-width: 576px) {
      flex-direction: column;
      gap: 8px;
    }
  }

  .el-checkbox {
    margin-right: 0;

    @media (max-width: 576px) {
      width: 100%;
    }
  }
}
</style>
