<template>
  <div
    :id="'section-' + 'time'"
    class="content-section"
  >
    <el-collapse
      v-model="activeCollapse"
      class="section-collapse"
      expand-icon-position="left"
    >
      <el-collapse-item name="time">
        <template #title>
          <span class="collapse-title">
            <el-icon class="collapse-icon"><CaretRight /></el-icon>
            采购时间要求
          </span>
        </template>
        <div class="form-content">
          <div class="section-form">
            <el-row :gutter="24">
              <el-col
                v-if="!isInviteMode && projectDetail?.preQualification === 1"
                :span="12"
              >
                <el-form-item
                  label="报名起止时间"
                  prop="purchaseDateDemand.registerTimeRange"
                >
                  <el-date-picker
                    :model-value="formData.registerTimeRange"
                    type="datetimerange"
                    range-separator="至"
                    :defaultTime="[new Date(2000, 1, 1, 0, 0, 0), new Date(2000, 2, 1, 23, 59, 59)]"
                    start-placeholder="报名开始时间"
                    end-placeholder="报名结束时间"
                    style="width: 100%"
                    format="YYYY-MM-DD HH:mm:ss"
                    value-format="YYYY-MM-DD HH:mm:ss"
                    @update:model-value="$emit('update:register-time-range', $event)"
                  />
                </el-form-item>
              </el-col>
              <el-col
                v-if="!isInviteMode && projectDetail?.preQualification === 1"
                :span="12"
              >
                <el-form-item
                  label="资质预审起止时间"
                  prop="purchaseDateDemand.auditTimeRange"
                >
                  <el-date-picker
                    :model-value="formData.auditTimeRange"
                    type="datetimerange"
                    range-separator="至"
                    :defaultTime="[new Date(2000, 1, 1, 0, 0, 0), new Date(2000, 2, 1, 23, 59, 59)]"
                    start-placeholder="资质预审开始时间"
                    end-placeholder="资质预审结束时间"
                    style="width: 100%"
                    format="YYYY-MM-DD HH:mm:ss"
                    value-format="YYYY-MM-DD HH:mm:ss"
                    @update:model-value="$emit('update:audit-time-range', $event)"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item
                  label="报价起止时间"
                  prop="purchaseDateDemand.quoteTimeRange"
                >
                  <el-date-picker
                    :model-value="formData.quoteTimeRange"
                    type="datetimerange"
                    range-separator="至"
                    :defaultTime="[new Date(2000, 1, 1, 0, 0, 0), new Date(2000, 2, 1, 23, 59, 59)]"
                    start-placeholder="报价开始时间"
                    end-placeholder="报价结束时间"
                    style="width: 100%"
                    format="YYYY-MM-DD HH:mm:ss"
                    value-format="YYYY-MM-DD HH:mm:ss"
                    @update:model-value="$emit('update:quote-time-range', $event)"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item
                  label="开标时间"
                  prop="purchaseDateDemand.bidOpenTime"
                >
                  <el-date-picker
                    :model-value="formData.bidOpenTime"
                    type="datetime"
                    placeholder="选择开标时间"
                    style="width: 100%"
                    format="YYYY-MM-DD HH:mm:ss"
                    value-format="YYYY-MM-DD HH:mm:ss"
                    @update:model-value="$emit('update:bid-open-time', $event)"
                  />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="24" v-if="isCompetitiveBidding">
              <el-col :span="12">
                <el-form-item
                  label="文件获取起止时间"
                  prop="purchaseDateDemand.documentObtainTimeRange"
                >
                  <el-date-picker
                    :model-value="formData.documentObtainTimeRange"
                    type="datetimerange"
                    range-separator="至"
                    :defaultTime="[new Date(2000, 1, 1, 0, 0, 0), new Date(2000, 2, 1, 23, 59, 59)]"
                    start-placeholder="文件获取开始时间"
                    end-placeholder="文件获取结束时间"
                    style="width: 100%"
                    format="YYYY-MM-DD HH:mm:ss"
                    value-format="YYYY-MM-DD HH:mm:ss"
                    @update:model-value="$emit('update:document-obtain-time-range', $event)"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item
                  label="标书费缴纳起止时间"
                  prop="purchaseDateDemand.bidFeePaymentTimeRange"
                >
                  <el-date-picker
                    :model-value="formData.bidFeePaymentTimeRange"
                    type="datetimerange"
                    range-separator="至"
                    :defaultTime="[new Date(2000, 1, 1, 0, 0, 0), new Date(2000, 2, 1, 23, 59, 59)]"
                    start-placeholder="标书费缴纳开始时间"
                    end-placeholder="标书费缴纳结束时间"
                    style="width: 100%"
                    format="YYYY-MM-DD HH:mm:ss"
                    value-format="YYYY-MM-DD HH:mm:ss"
                    @update:model-value="$emit('update:bid-fee-payment-time-range', $event)"
                  />
                </el-form-item>
              </el-col>
            </el-row>
          </div>
        </div>
      </el-collapse-item>
    </el-collapse>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { CaretRight } from '@element-plus/icons-vue';
import { useBiddingStore } from '@/views/procurementSourcing/biddingProcess/stores';
const biddingStore = useBiddingStore();
const isCompetitiveBidding = computed(() => biddingStore?.isCompetitiveBidding);

interface Props {
  formData: {
    registerTimeRange: string[];
    auditTimeRange: string[];
    quoteTimeRange: string[];
    bidOpenTime: string;
    documentObtainTimeRange: string[];
    bidFeePaymentTimeRange: string[];
  };
  isInviteMode: boolean;
  sourcingType: string;
  projectDetail: any;
}

defineProps<Props>();

defineEmits<{
  'update:register-time-range': [value: string[]];
  'update:audit-time-range': [value: string[]];
  'update:quote-time-range': [value: string[]];
  'update:bid-open-time': [value: string];
  'update:document-obtain-time-range': [value: string[]];
  'update:bid-fee-payment-time-range': [value: string[]];
}>();

const activeCollapse = ref(['time']);
</script>

<style lang="scss" scoped>
@import '../../../../styles/collapse-panel.scss';

.content-section {
  margin-bottom: 20px;
}

.section-form {
  .el-row {
    @media (max-width: 768px) {
      .el-col {
        &:not(:last-child) {
          margin-bottom: 16px;
        }
      }
    }

    @media (max-width: 576px) {
      .el-col {
        &[span='12'],
        &[span='8'] {
          span: 24 !important;
        }
      }
    }
  }

  .el-date-picker {
    @media (max-width: 576px) {
      width: 100% !important;
    }
  }
}
</style>
