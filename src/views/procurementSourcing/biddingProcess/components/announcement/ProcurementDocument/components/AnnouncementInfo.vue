<template>
  <div
    :id="'section-' + 'announcement'"
    class="content-section"
  >
    <el-collapse
      v-model="activeCollapse"
      class="section-collapse"
      expand-icon-position="left"
    >
      <el-collapse-item name="announcement">
        <template #title>
          <span class="collapse-title">
            <el-icon class="collapse-icon"><CaretRight /></el-icon>
            {{ isInviteMode ? '邀请函信息' : '公告信息' }}
          </span>
        </template>
        <div class="form-content">
          <div class="section-form">
            <el-row :gutter="24">
              <el-col :span="12">
                <el-form-item
                  :label="isInviteMode ? '邀请函标题' : '公告标题'"
                  prop="announcementInfo.title"
                >
                  <el-input
                    :model-value="formData.title"
                    maxlength="50"
                    show-word-limit
                    placeholder="请输入"
                    @update:model-value="$emit('update:title', $event)"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item
                  label="引用模版"
                  prop="announcementInfo.template"
                >
                  <el-select
                    :model-value="formData.template"
                    placeholder="请选择公告模版"
                    style="width: 100%"
                    @update:model-value="$emit('update:template', $event)"
                  >
                    <el-option
                      v-for="template in templateOptions"
                      :key="template.id"
                      :label="template.templateName"
                      :value="template.id"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
          </div>
        </div>
      </el-collapse-item>
    </el-collapse>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { CaretRight } from '@element-plus/icons-vue';
import type { TemplateOption } from '../types';

interface Props {
  formData: {
    title: string;
    template: string;
  };
  templateOptions: TemplateOption[];
  isInviteMode: boolean;
}

defineProps<Props>();

defineEmits<{
  'update:title': [value: string];
  'update:template': [value: string];
}>();

const activeCollapse = ref(['announcement']);
</script>

<style lang="scss" scoped>
@import '../../../../styles/collapse-panel.scss';

.content-section {
  margin-bottom: 20px;
}

.section-form {
  .el-row {
    @media (max-width: 768px) {
      .el-col {
        &:not(:last-child) {
          margin-bottom: 16px;
        }
      }
    }

    @media (max-width: 576px) {
      .el-col {
        &[span='12'],
        &[span='8'] {
          span: 24 !important;
        }
      }
    }
  }

  .el-input,
  .el-select {
    @media (max-width: 576px) {
      width: 100% !important;
    }
  }
}
</style>
