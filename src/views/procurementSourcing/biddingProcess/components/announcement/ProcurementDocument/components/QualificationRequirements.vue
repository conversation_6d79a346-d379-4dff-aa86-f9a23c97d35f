<template>
  <div
    v-if="!isInviteMode"
    :id="'section-' + 'qualification'"
    class="content-section"
  >
    <el-collapse
      v-model="activeCollapse"
      class="section-collapse team-section"
      expand-icon-position="left"
    >
      <el-collapse-item name="qualification">
        <template #title>
          <span class="collapse-title">
            <el-icon class="collapse-icon"><CaretRight /></el-icon>
            资格要求
          </span>
        </template>
        <div class="form-content">
          <div
            class="flex"
            v-if="lotList.length > 1"
          >
            <div
              class="px-5"
              v-for="(item, index) in lotList"
              :key="index"
            >
              <div
                class="py-9 lot-item mb-4"
                :class="{ active: activeLotId === item.id }"
                @click="$emit('lot-click', item.id)"
              >
                {{ item.sectionName }}
              </div>
            </div>
          </div>
          <el-table
            :data="qualificationData"
            style="width: 100%"
            class="editable-table"
          >
            <el-table-column
              label="序号"
              width="80"
              align="center"
            >
              <template #default="{ $index }">
                {{ $index + 1 }}
              </template>
            </el-table-column>
            <el-table-column label="资质文件名称">
              <template #default="{ row }">
                <el-select
                  v-model="row.requirementName"
                  placeholder="请选择资质文件名称"
                  filterable
                  remote
                  :remote-method="handleCertificateSearch"
                  :loading="certificateLoading"
                  style="width: 100%"
                  @change="(value) => handleCertificateChange(value, row)"
                >
                  <el-option
                    v-for="cert in certificateOptions"
                    :key="cert.id"
                    :label="cert.certName"
                    :value="cert.certName"
                  />
                </el-select>
              </template>
            </el-table-column>
            <el-table-column label="补充说明">
              <template #default="{ row }">
                <el-input
                  v-model="row.requirementContent"
                  placeholder="请输入补充说明"
                />
              </template>
            </el-table-column>
            <el-table-column
              label="操作"
              width="150"
              align="center"
            >
              <template #default="{ $index }">
                <div class="table-actions">
                  <el-link
                    type="primary"
                    size="small"
                    @click="$emit('add-item', $index)"
                  >
                    增加
                  </el-link>
                  <el-link
                    type="danger"
                    size="small"
                    v-if="qualificationData.length > 1"
                    @click="$emit('remove-item', $index)"
                  >
                    删除
                  </el-link>
                </div>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </el-collapse-item>
    </el-collapse>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { CaretRight } from '@element-plus/icons-vue';
import type { BidsSegment, LotInfo } from '../types';

interface Props {
  isInviteMode: boolean;
  lotList: LotInfo[];
  activeLotId: string;
  qualificationData: BidsSegment[];
  certificateOptions: any[];
  certificateLoading: boolean;
}

defineProps<Props>();

defineEmits<{
  'lot-click': [lotId: string];
  'add-item': [index: number];
  'remove-item': [index: number];
  'certificate-search': [query: string];
  'certificate-change': [value: string, row: any];
}>();

const activeCollapse = ref(['qualification']);

const handleCertificateSearch = (query: string) => {
  // 转发事件到父组件
  // 这里需要通过父组件处理，因为证书搜索逻辑在父组件中
};

const handleCertificateChange = (value: string, row: any) => {
  // 转发事件到父组件
};
</script>

<style lang="scss" scoped>
@import '../../../../styles/collapse-panel.scss';

.content-section {
  margin-bottom: 20px;
}

.lot-item {
  cursor: pointer;
  &.active {
    border-bottom: 2px solid var(--Color-Primary-color-primary, #0069ff);
  }
}

.table-actions {
  display: flex;
  gap: 8px;
  justify-content: center;

  @media (max-width: 576px) {
    flex-direction: column;
    gap: 4px;

    .el-button {
      font-size: 12px;
      padding: 4px 8px;
    }
  }
}

.editable-table {
  @media (max-width: 768px) {
    font-size: 12px;

    :deep(.el-table__body-wrapper) {
      overflow-x: auto;
    }
  }
}

.px-5 {
  padding-left: 20px;
  padding-right: 20px;
}

.py-9 {
  padding-top: 9px;
  padding-bottom: 9px;
}

.mb-4 {
  margin-bottom: 16px;
}
</style>
