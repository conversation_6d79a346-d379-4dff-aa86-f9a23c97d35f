<template>
  <div
    :id="'section-' + 'fee'"
    class="content-section"
  >
    <el-collapse
      v-model="activeCollapse"
      class="section-collapse"
      expand-icon-position="left"
    >
      <el-collapse-item name="fee">
        <template #title>
          <span class="collapse-title">
            <el-icon class="collapse-icon"><CaretRight /></el-icon>
            {{ label }}
          </span>
        </template>
        <div class="form-content">
          <div
            class="flex"
            v-if="lotList.length > 1"
          >
            <div
              class="px-5"
              v-for="(item, index) in lotList"
              :key="index"
            >
              <div
                class="py-9 lot-item mb-4"
                :class="{ active: activeLotId === item.id }"
                @click="$emit('lot-click', item.id)"
              >
                {{ item.sectionName }}
              </div>
            </div>
          </div>
          <template v-if="feeData">
            <div class="section-form">
              <el-row :gutter="24">
                <el-col :span="12">
                  <el-form-item label="保证金收取">
                    <el-radio-group
                      v-model="feeData.amountSet"
                      @change="$emit('update:amount-set', $event)"
                    >
                      <el-radio :label="true">线下收取保证金</el-radio>
                      <el-radio :label="false">不收保证金</el-radio>
                    </el-radio-group>
                  </el-form-item>
                </el-col>
              </el-row>
              <template v-if="feeData.amountSet && feeData.feeInfo">
                <el-row :gutter="24">
                  <el-col :span="12">
                    <el-form-item label="保证金金额" required>
                      <el-input
                        v-model="feeData.feeInfo.guaranteeAmount"
                        placeholder="请输入保证金金额"
                        @input="$emit('update:guarantee-amount', $event)"
                      >
                        <template #append>元</template>
                      </el-input>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="汇款银行" required>
                      <el-input
                        v-model="feeData.feeInfo.payBank"
                        placeholder="请输入汇款银行"
                        @input="$emit('update:pay-bank', $event)"
                      />
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row :gutter="24">
                  <el-col :span="12">
                    <el-form-item label="汇款银行账户" required>
                      <el-input
                        v-model="feeData.feeInfo.payAccount"
                        placeholder="请输入银行账户"
                        @input="$emit('update:pay-account', $event)"
                      />
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="汇款开户行">
                      <el-input
                        v-model="feeData.feeInfo.openAccountBank"
                        placeholder="请输入开户行"
                        @input="$emit('update:open-account-bank', $event)"
                      />
                    </el-form-item>
                  </el-col>
                </el-row>
              </template>
            </div>
          </template>
        </div>
      </el-collapse-item>
    </el-collapse>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import { CaretRight } from '@element-plus/icons-vue';
import type { BidsSegment, LotInfo } from '../types';
import { useBiddingStore } from '@/views/procurementSourcing/biddingProcess/stores';

const biddingStore = useBiddingStore();
const isZJWT = computed(() => biddingStore?.isZJWT);
const label = computed(() => isZJWT.value ? '保证金设置' : '费用设置');

interface Props {
  lotList: LotInfo[];
  activeLotId: string;
  feeData: BidsSegment | null;
}

defineProps<Props>();

defineEmits<{
  'lot-click': [lotId: string];
  'update:amount-set': [value: boolean];
  'update:guarantee-amount': [value: string];
  'update:pay-bank': [value: string];
  'update:pay-account': [value: string];
  'update:open-account-bank': [value: string];
}>();

const activeCollapse = ref(['fee']);
</script>

<style lang="scss" scoped>
@import '../../../../styles/collapse-panel.scss';

.content-section {
  margin-bottom: 20px;
}

.lot-item {
  cursor: pointer;
  &.active {
    border-bottom: 2px solid var(--Color-Primary-color-primary, #0069ff);
  }
}

.section-form {
  .el-row {
    @media (max-width: 768px) {
      .el-col {
        &:not(:last-child) {
          margin-bottom: 16px;
        }
      }
    }

    @media (max-width: 576px) {
      .el-col {
        &[span='12'],
        &[span='8'] {
          span: 24 !important;
        }
      }
    }
  }

  .el-input,
  .el-radio-group {
    @media (max-width: 576px) {
      width: 100% !important;
    }
  }
}
</style>
