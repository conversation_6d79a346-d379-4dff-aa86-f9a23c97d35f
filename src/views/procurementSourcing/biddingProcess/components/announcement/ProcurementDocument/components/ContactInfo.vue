<template>
  <div
    :id="'section-' + 'contact'"
    class="content-section"
  >
    <el-collapse
      v-model="activeCollapse"
      class="section-collapse"
      expand-icon-position="left"
    >
      <el-collapse-item name="contact">
        <template #title>
          <span class="collapse-title">
            <el-icon class="collapse-icon"><CaretRight /></el-icon>
            联系方式
          </span>
        </template>
        <div class="form-content">
          <div class="section-form">
            <el-row :gutter="24">
              <el-col :span="12">
                <el-form-item
                  label="采购联系人"
                  prop="contactInfo.contactPerson"
                >
                  <el-input
                    :model-value="contactInfo.contactPerson"
                    placeholder="请输入"
                    @update:model-value="$emit('update:contact-person', $event)"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item
                  label="联系电话"
                  prop="contactInfo.contactPhone"
                >
                  <el-input
                    :model-value="contactInfo.contactPhone"
                    placeholder="请输入"
                    @update:model-value="$emit('update:contact-phone', $event)"
                  />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="24">
              <el-col :span="12">
                <el-form-item
                  label="固定电话"
                  prop="contactInfo.contactFixedPhone"
                >
                  <el-input
                    :model-value="contactInfo.contactFixedPhone"
                    placeholder="请输入"
                    @update:model-value="$emit('update:contact-fixed-phone', $event)"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item
                  label="电子邮件"
                  prop="contactInfo.contactEmail"
                >
                  <el-input
                    :model-value="contactInfo.contactEmail"
                    placeholder="请输入"
                    @update:model-value="$emit('update:contact-email', $event)"
                  />
                </el-form-item>
              </el-col>
            </el-row>
          </div>
        </div>
      </el-collapse-item>
    </el-collapse>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { CaretRight } from '@element-plus/icons-vue';
import type { ContactInfo as ContactInfoType } from '../types';

interface Props {
  contactInfo: ContactInfoType;
}

defineProps<Props>();

defineEmits<{
  'update:contact-person': [value: string];
  'update:contact-phone': [value: string];
  'update:contact-fixed-phone': [value: string];
  'update:contact-email': [value: string];
}>();

const activeCollapse = ref(['contact']);
</script>

<style lang="scss" scoped>
@import '../../../../styles/collapse-panel.scss';

.content-section {
  margin-bottom: 20px;
}

.section-form {
  .el-row {
    @media (max-width: 768px) {
      .el-col {
        &:not(:last-child) {
          margin-bottom: 16px;
        }
      }
    }

    @media (max-width: 576px) {
      .el-col {
        &[span='12'],
        &[span='8'] {
          span: 24 !important;
        }
      }
    }
  }

  .el-input {
    @media (max-width: 576px) {
      width: 100% !important;
    }
  }
}
</style>
