<template>
  <div
    :id="'section-' + 'attachments'"
    class="content-section"
  >
    <el-collapse
      v-model="activeCollapse"
      class="section-collapse"
      expand-icon-position="left"
    >
      <el-collapse-item name="attachments">
        <template #title>
          <span class="collapse-title">
            <el-icon class="collapse-icon"><CaretRight /></el-icon>
            {{ isInviteMode ? '邀请函附件' : '采购公告附件' }}
          </span>
        </template>
        <div class="form-content">
          <div
            class="upload-section"
            style="margin-bottom: 16px"
          >
            <span>{{ isInviteMode ? '邀请函附件：' : '采购公告附件：' }}</span>
            <YunUpload
              :model-value="attachmentInfos"
              @update:model-value="$emit('update:attachment-infos', $event)"
              @change="$emit('upload-change', $event)"
            ></YunUpload>
          </div>
        </div>
      </el-collapse-item>
    </el-collapse>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { CaretRight } from '@element-plus/icons-vue';
import YunUpload from '@/components/YunUpload/index.vue';

interface Props {
  isInviteMode: boolean;
  attachmentInfos: any[];
}

defineProps<Props>();

defineEmits<{
  'update:attachment-infos': [value: any[]];
  'upload-change': [file: any];
}>();

const activeCollapse = ref(['attachments']);
</script>

<style lang="scss" scoped>
@import '../../../../styles/collapse-panel.scss';

.content-section {
  margin-bottom: 20px;
}

.upload-section {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-top: 16px;

  span {
    font-size: 14px;
    color: #606266;
  }

  @media (max-width: 576px) {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
}
</style>
