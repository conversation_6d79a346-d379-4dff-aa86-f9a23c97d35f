<template>
  <div
    v-if="isInviteMode"
    :id="'section-' + 'invite'"
    class="content-section"
  >
    <el-collapse
      v-model="activeCollapse"
      class="section-collapse team-section"
      expand-icon-position="left"
    >
      <el-collapse-item name="invite">
        <template #title>
          <span class="collapse-title">
            <el-icon class="collapse-icon"><CaretRight /></el-icon>
            邀请供应商
          </span>
        </template>
        <div class="form-content">
          <div class="flex justify-between mb-4">
            <div
              class="flex"
              v-if="lotList.length > 1"
            >
              <div
                class="px-5"
                v-for="(item, index) in lotList"
                :key="index"
              >
                <div
                  class="py-9 lot-item mb-4"
                  :class="{ active: activeLotId === item.id }"
                  @click="$emit('lot-click', item.id)"
                >
                  {{ item.sectionName }}
                </div>
              </div>
            </div>
            <el-button
              type="primary"
              @click="$emit('open-supplier-modal')"
            >
              选择供应商
            </el-button>
          </div>
          <el-alert class="mb-1" v-if="isZJWT" title="只能选择一家供应商" type="warning" show-icon :closable="false" />
          <el-table
            :data="inviteSuppliers"
            style="width: 100%"
            class="editable-table"
          >
            <el-table-column
              label="序号"
              width="80"
              align="center"
            >
              <template #default="{ $index }">
                {{ $index + 1 }}
              </template>
            </el-table-column>
            <el-table-column
              prop="supplierName"
              label="供应商名称"
            />
            <el-table-column
              prop="contactName"
              label="联系人"
              width="120"
            />
            <el-table-column
              prop="contactPhone"
              label="联系方式"
              width="150"
            />
            <el-table-column
              label="操作"
              width="100"
              align="center"
            >
              <template #default="{ $index }">
                <el-link
                  type="danger"
                  size="small"
                  @click="$emit('remove-supplier', $index)"
                >
                  删除
                </el-link>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </el-collapse-item>
    </el-collapse>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { CaretRight } from '@element-plus/icons-vue';
import type { InviteSupplier, LotInfo } from '../types';
import { useBiddingStore } from '@/views/procurementSourcing/biddingProcess/stores';

const biddingStore = useBiddingStore();
const isZJWT = computed(() => biddingStore.isZJWT)

interface Props {
  isInviteMode: boolean;
  lotList: LotInfo[];
  activeLotId: string;
  inviteSuppliers: InviteSupplier[];
}

defineProps<Props>();

defineEmits<{
  'lot-click': [lotId: string];
  'open-supplier-modal': [];
  'remove-supplier': [index: number];
}>();

const activeCollapse = ref(['invite']);
</script>

<style lang="scss" scoped>
@import '../../../../styles/collapse-panel.scss';

.content-section {
  margin-bottom: 20px;
}

.lot-item {
  cursor: pointer;
  &.active {
    border-bottom: 2px solid var(--Color-Primary-color-primary, #0069ff);
  }
}

.editable-table {
  @media (max-width: 768px) {
    font-size: 12px;

    :deep(.el-table__body-wrapper) {
      overflow-x: auto;
    }
  }
}

.flex {
  display: flex;

  &.justify-between {
    justify-content: space-between;
  }
}

.mb-4 {
  margin-bottom: 16px;
}

.px-5 {
  padding-left: 20px;
  padding-right: 20px;
}

.py-9 {
  padding-top: 9px;
  padding-bottom: 9px;
}
</style>
