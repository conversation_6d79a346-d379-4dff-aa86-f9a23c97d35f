<template>
  <div
    :id="'section-' + 'notice'"
    class="content-section"
  >
    <el-collapse
      v-model="activeCollapse"
      class="section-collapse"
      expand-icon-position="left"
    >
      <el-collapse-item name="notice">
        <template #title>
          <span class="collapse-title">
            <el-icon class="collapse-icon"><CaretRight /></el-icon>
            供应商报价须知
          </span>
        </template>
        <div class="form-content">
          <div class="section-form">
            <el-row :gutter="24">
              <el-col :span="24">
                <el-form-item
                  label="报价须知"
                  prop="quotationNotice"
                >
                  <el-input
                    :model-value="quotationNotice"
                    type="textarea"
                    :rows="3"
                    placeholder="请输入供应商报价须知"
                    @update:model-value="$emit('update:quotation-notice', $event)"
                  />
                </el-form-item>
              </el-col>
            </el-row>
          </div>
        </div>
      </el-collapse-item>
    </el-collapse>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { CaretRight } from '@element-plus/icons-vue';

interface Props {
  quotationNotice: string;
}

defineProps<Props>();

defineEmits<{
  'update:quotation-notice': [value: string];
}>();

const activeCollapse = ref(['notice']);
</script>

<style lang="scss" scoped>
@import '../../../../styles/collapse-panel.scss';

.content-section {
  margin-bottom: 20px;
}

.section-form {
  .el-row {
    @media (max-width: 768px) {
      .el-col {
        &:not(:last-child) {
          margin-bottom: 16px;
        }
      }
    }
  }

  .el-textarea {
    @media (max-width: 576px) {
      width: 100% !important;
    }
  }
}
</style>
