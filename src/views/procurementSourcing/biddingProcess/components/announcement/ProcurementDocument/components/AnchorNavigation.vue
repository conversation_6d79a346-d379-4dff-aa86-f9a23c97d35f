<template>
  <div class="anchor-navigation">
    <ul class="nav-list">
      <!-- 高亮滑块 -->
      <div
        v-show="isSliderReady"
        class="nav-slider"
        :style="{
          height: sliderStyle.height,
          transform: sliderStyle.transform,
          top: '4px',
        }"
      ></div>
      <template v-for="anchor in anchorList">
        <li
          v-if="anchor?.show"
          :key="anchor.id"
          class="nav-item"
          :class="{ active: activeAnchor === anchor.id }"
          @click="$emit('scroll-to-section', anchor.id)"
        >
          {{ anchor.label }}
        </li>
      </template>
    </ul>
  </div>
</template>

<script setup lang="ts">
interface AnchorItem {
  id: string;
  label: string;
  show: boolean;
}

interface SliderStyle {
  height: string;
  transform: string;
}

defineProps<{
  anchorList: AnchorItem[];
  activeAnchor: string;
  sliderStyle: SliderStyle;
  isSliderReady: boolean;
}>();

defineEmits<{
  'scroll-to-section': [sectionId: string];
}>();
</script>

<style lang="scss" scoped>
.anchor-navigation {
  width: 120px;
  flex-shrink: 0;
  position: sticky;
  border-left: 1px solid #e6eaf0;
  top: 20px;
  height: fit-content;

  @media (max-width: 768px) {
    width: 100%;
    position: relative;
    border-left: none;
    border-bottom: 1px solid #e6eaf0;
    padding-bottom: 12px;
    margin-bottom: 12px;

    .nav-list {
      display: flex;
      flex-wrap: wrap;
      gap: 8px;

      .nav-slider {
        display: none;
      }

      .nav-item {
        padding: 4px 8px;
        border: 1px solid #e6eaf0;
        border-radius: 4px;
        font-size: 11px;

        &.active {
          border-color: var(--Color-Primary-color-primary, #0069ff);
          background-color: rgba(0, 105, 255, 0.1);
        }
      }
    }
  }

  .nav-list {
    list-style: none;
    padding: 0;
    margin: 0;
    position: relative;

    .nav-slider {
      position: absolute;
      left: -1px;
      width: 1px;
      height: 16px;
      background-color: #0069ff;
      transition: transform 0.2s ease-out;
      will-change: transform;
      z-index: 1;

      &[v-show] {
        transition: transform 0.2s ease-out, opacity 0.3s ease;
      }
    }

    .nav-item {
      padding: 2px 12px;
      cursor: pointer;
      border-radius: 4px;
      transition: all 0.3s;
      color: var(--Light-Light-el-text-color-primary, #1c2026);
      font-family: 'PingFang SC';
      font-size: 12px;
      font-style: normal;
      font-weight: 400;
      line-height: 20px;
      position: relative;

      &.active {
        color: var(--Color-Primary-color-primary, #0069ff);
        font-family: 'PingFang SC';
        font-size: 12px;
        font-style: normal;
        font-weight: 600;
        line-height: 20px;
      }
    }
  }
}
</style>
