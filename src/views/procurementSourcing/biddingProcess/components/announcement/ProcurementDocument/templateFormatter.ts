/**
 * 标段信息接口
 */
interface SectionInfo {
  sectionId: string;
  sectionName: string;
  requirements: Array<{
    requirementName: string;
    requirementContent: string;
  }>;
}

/**
 * 费用设置标段信息接口
 */
interface FeeSectionInfo {
  sectionId: string;
  sectionName: string;
  amountSet: boolean;
  feeInfo?: {
    guaranteeAmount: number;
    payAccount: string;
    payBank: string;
    openAccountBank: string;
  };
}

/**
 * 将 API 数据转换为适合模板解析的嵌套循环格式
 * @param apiData 原始 API 数据
 * @param lotList 标段列表信息
 * @returns 转换后的数据对象
 */
export function transformApiDataForTemplate(apiData: any, lotList: Array<{id: string, sectionName: string}> = []): any {
  // 创建标段映射，用于获取标段名称
  const lotMap = new Map(lotList.map(lot => [lot.id, lot.sectionName]));

  // 初始化功能模块的数据结构
  const qualificationRequirements: SectionInfo[] = [];
  const quotationConditions: SectionInfo[] = [];
  const feeSettings: FeeSectionInfo[] = [];
  const inviteSuppliers: any[] = [];

  // 为每个标段初始化数据结构
  lotList.forEach(lot => {
    // 资格要求
    qualificationRequirements.push({
      sectionId: lot.id,
      sectionName: lot.sectionName,
      requirements: []
    });

    // 报价响应条件
    quotationConditions.push({
      sectionId: lot.id,
      sectionName: lot.sectionName,
      requirements: []
    });

    // 费用设置（注意：费用设置下的标段信息不是数组）
    feeSettings.push({
      sectionId: lot.id,
      sectionName: lot.sectionName,
      amountSet: false,
      feeInfo: {
        guaranteeAmount: 0,
        payAccount: '',
        payBank: '',
        openAccountBank: ''
      }
    });

    // 邀请供应商
    inviteSuppliers.push({
      sectionId: lot.id,
      sectionName: lot.sectionName,
      suppliers: []
    });
  });

  // 将 bidsSegments 数据分组到对应的功能模块
  if (apiData.bidsSegments && Array.isArray(apiData.bidsSegments)) {
    apiData.bidsSegments.forEach((segment: any) => {
      switch (segment.requirementType) {
        case 'QUALIFICATION':
          const qualSection = qualificationRequirements.find(s => s.sectionId === segment.sectionId);
          if (qualSection) {
            qualSection.requirements.push({
              requirementName: segment.requirementName || '',
              requirementContent: segment.requirementContent || ''
            });
          }
          break;

        case 'CONDITION':
          const condSection = quotationConditions.find(s => s.sectionId === segment.sectionId);
          if (condSection) {
            condSection.requirements.push({
              requirementName: segment.requirementName || '',
              requirementContent: segment.requirementContent || ''
            });
          }
          break;

        case 'FEE':
          const feeSection = feeSettings.find(s => s.sectionId === segment.sectionId);
          if (feeSection) {
            feeSection.amountSet = segment.amountSet || false;
            feeSection.feeInfo = segment.feeInfo || {
              guaranteeAmount: 0,
              payAccount: '',
              payBank: '',
              openAccountBank: ''
            };
          }
          break;

        case 'INVITE_SUPPLIERS':
          const inviteSection = inviteSuppliers.find(s => s.sectionId === segment.sectionId);
          if (inviteSection) {
            inviteSection.suppliers = segment.inviteSuppliers || [];
          }
          break;
      }
    });
  }

  // 返回包含原始数据和转换后数据的对象
  return {
    ...apiData, // 保留原始数据
    // 按功能模块分组的数据结构
    qualificationRequirements,  // 资格要求数组
    quotationConditions,        // 报价响应条件数组
    feeSettings,                // 费用设置数组
    inviteSuppliers             // 邀请供应商数组
  };
}

/**
 * 模板解析器，支持变量替换和循环语法
 * @param template 模板字符串
 * @param data 数据对象
 * @param options 配置选项
 * @returns 解析后的字符串
 */
export const templateContentFormatter = (
  template: string,
  data: any,
  options: {
    keepUndefinedVariables?: boolean;  // 是否保留未定义的变量（默认true）
    defaultValue?: string;             // 未定义变量的默认值
    removeEmptyLoops?: boolean;        // 是否移除空循环块（默认true）
    removeEmptyTags?: boolean;         // 是否去除多余的空标签（默认false）
    debug?: boolean;                   // 是否开启调试模式（默认false）
    indexStartsFromOne?: boolean;      // 循环索引是否从1开始（默认true）
  } = {}
): string => {
  if (options.debug) {
    console.log('🚀 模板解析开始');
    console.log('📝 模板内容:', template);
    console.log('📊 数据对象:', data);
    console.log('⚙️ 配置选项:', options);
  }

  let result = parseTemplate(template, data, {}, options);

  // 如果启用了清理空标签选项，则清理结果中的空标签
  if (options.removeEmptyTags) {
    if (options.debug) {
      console.log('🧹 开始清理空标签');
      console.log('🧹 清理前的内容:', result);
    }
    result = removeEmptyHtmlTags(result);
    if (options.debug) {
      console.log('🧹 清理后的内容:', result);
    }
  }

  if (options.debug) {
    console.log('✅ 模板解析完成');
    console.log('🎯 最终结果:', result);
  }

  return result;
}

/**
 * 解析模板字符串
 * @param template 模板字符串
 * @param data 数据对象
 * @param context 上下文对象（用于循环中的变量）
 * @param options 配置选项
 * @returns 解析后的字符串
 */
function parseTemplate(
  template: string,
  data: any,
  context: any = {},
  options: any = {}
): string {
  // 合并数据和上下文
  const mergedData = { ...data, ...context };

  // 处理 foreach 循环（递归处理嵌套，同时处理变量替换）
  return processForeachLoops(template, mergedData, options);
}



/**
 * 处理 foreach 循环和 table 循环
 * @param template 模板字符串
 * @param data 数据对象
 * @param options 配置选项
 * @returns 处理后的字符串
 */
function processForeachLoops(template: string, data: any, options: any = {}): string {
  // 先处理 #table 语法
  let processedTemplate = processTableLoops(template, data, options);

  // 再处理 #foreach 语法
  return processStandardForeachLoops(processedTemplate, data, options);
}

/**
 * 处理 #table 循环语法
 * @param template 模板字符串
 * @param data 数据对象
 * @param options 配置选项
 * @returns 处理后的字符串
 */
function processTableLoops(template: string, data: any, options: any = {}): string {
  // 匹配 #table((item, index) of arrayName) ... #end 的模式
  const tableRegex = /#table\s*\(\s*\(\s*(\w+)\s*,\s*(\w+)\s*\)\s+of\s+(\w+(?:\.\w+)*)\s*\)([\s\S]*?)#end/g;

  return template.replace(tableRegex, (match, itemVar, indexVar, arrayPath, content) => {
    if (options.debug) {
      console.log(`🔄 处理 #table 循环: ${itemVar}, ${indexVar} of ${arrayPath}`);
    }

    // 获取数组数据
    const arrayData = getNestedValue(data, arrayPath);

    if (!Array.isArray(arrayData)) {
      if (options.debug) {
        console.log(`⚠️ #table 循环数据不是数组: ${arrayPath}`, arrayData);
      }
      // 如果不是数组，根据配置决定是否移除循环块
      return options.removeEmptyLoops !== false ? '' : content;
    }

    if (options.debug) {
      console.log(`📊 #table 循环数据:`, arrayData);
    }

    // 查找表格中的第一个数据行（包含 <td> 的 <tr>）
    const rowRegex = /(<tr[^>]*>(?:(?!<\/tr>)[\s\S])*?<td[\s\S]*?<\/tr>)/i;
    const rowMatch = content.match(rowRegex);

    if (!rowMatch) {
      if (options.debug) {
        console.log(`⚠️ #table 循环中未找到数据行模板`);
      }
      return content;
    }

    const rowTemplate = rowMatch[0];

    // 为每个数据项生成行
    const newRows = arrayData.map((item, index) => {
      // 创建循环上下文，根据配置决定索引是否从1开始
      const actualIndex = options.indexStartsFromOne !== false ? index + 1 : index;
      const loopContext = {
        ...data,
        [itemVar]: item,
        [indexVar]: actualIndex
      };

      if (options.debug) {
        console.log(`📝 #table 循环项 ${index}: item=`, item, `${indexVar}=${actualIndex}`);
      }

      // 处理行模板中的变量
      return processVariables(rowTemplate, loopContext, {
        keepUndefined: options.keepUndefinedVariables !== false,
        defaultValue: options.defaultValue,
        debug: options.debug
      });
    }).join('');

    // 替换原始行模板为生成的行
    return content.replace(rowTemplate, newRows);
  });
}

/**
 * 处理标准的 #foreach 循环语法
 * @param template 模板字符串
 * @param data 数据对象
 * @param options 配置选项
 * @returns 处理后的字符串
 */
function processStandardForeachLoops(template: string, data: any, options: any = {}): string {
  /**
   * 找到匹配的 #end 位置，考虑嵌套
   */
  function findMatchingEnd(str: string, startPos: number): number {
    let depth = 1;
    let pos = startPos;

    while (pos < str.length && depth > 0) {
      const foreachMatch = str.substring(pos).match(/#foreach/);
      const endMatch = str.substring(pos).match(/#end/);

      if (!endMatch) break;

      const endPos = pos + endMatch.index;
      const foreachPos = foreachMatch ? pos + foreachMatch.index : Infinity;

      if (foreachPos < endPos) {
        depth++;
        pos = foreachPos + 8; // length of "#foreach"
      } else {
        depth--;
        if (depth === 0) {
          return endPos;
        }
        pos = endPos + 4; // length of "#end"
      }
    }

    return -1;
  }

  /**
   * 递归处理嵌套的 foreach 循环
   */
  function processNestedForeach(str: string, currentData: any): string {
    const foreachRegex = /#foreach\s*\(\s*\(\s*(\w+)\s*,\s*(\w+)\s*\)\s+of\s+(\w+(?:\.\w+)*)\s*\)/;
    const match = foreachRegex.exec(str);

    if (!match) {
      // 没有找到 foreach 循环，处理变量替换后返回
      return processVariables(str, currentData, {
        keepUndefined: options.keepUndefinedVariables !== false,
        defaultValue: options.defaultValue,
        debug: options.debug
      });
    }

    const [matchText, itemVar, indexVar, arrayPath] = match;
    const startPos = match.index + matchText.length;
    const endPos = findMatchingEnd(str, startPos);

    if (endPos === -1) {
      // 没有找到匹配的 #end，处理变量替换后返回
      return processVariables(str, currentData, {
        keepUndefined: options.keepUndefinedVariables !== false,
        defaultValue: options.defaultValue,
        debug: options.debug
      });
    }

    const content = str.substring(startPos, endPos);
    const fullMatch = str.substring(match.index, endPos + 4); // +4 for "#end"

    // 获取数组数据
    const arrayData = getNestedValue(currentData, arrayPath);

    if (!Array.isArray(arrayData)) {
      // 如果不是数组，根据配置决定是否移除循环块
      if (options.removeEmptyLoops !== false) {
        // 移除空循环块
        const remaining = str.replace(fullMatch, '');
        return processNestedForeach(remaining, currentData);
      } else {
        // 保留空循环块，但移除 foreach 语法，只保留内容
        const remaining = str.replace(fullMatch, content);
        return processNestedForeach(remaining, currentData);
      }
    }

    let loopResult = '';

    arrayData.forEach((item, index) => {
      // 创建循环上下文，根据配置决定索引是否从1开始
      const actualIndex = options.indexStartsFromOne !== false ? index + 1 : index;
      const loopContext = {
        ...currentData,
        [itemVar]: item,
        [indexVar]: actualIndex
      };

      if (options.debug) {
        console.log(`📝 #foreach 循环项 ${index}: item=`, item, `${indexVar}=${actualIndex}`);
      }

      // 递归处理循环内容（可能包含嵌套的 foreach）
      const processedContent = processNestedForeach(content, loopContext);
      loopResult += processedContent;
    });

    // 替换当前循环并继续处理剩余部分
    const remaining = str.replace(fullMatch, loopResult);
    return processNestedForeach(remaining, currentData);
  }

  return processNestedForeach(template, data);
}

/**
 * 处理变量替换
 * @param template 模板字符串
 * @param data 数据对象
 * @param options 选项配置
 * @returns 处理后的字符串
 */
function processVariables(template: string, data: any, options: {
  keepUndefined?: boolean,
  defaultValue?: string,
  debug?: boolean
} = {}): string {
  // 先清理模板中被HTML标签分割的变量名
  let cleanedTemplate = cleanVariableNames(template);

  if (options.debug && cleanedTemplate !== template) {
    console.log('🧹 清理前的模板:', template);
    console.log('🧹 清理后的模板:', cleanedTemplate);
  }

  // 匹配 {variableName} 或 {object.property} 的模式
  const variableRegex = /\{([^}]+)\}/g;

  return cleanedTemplate.replace(variableRegex, (match, variablePath) => {
    const trimmedPath = variablePath.trim();

    // 如果开启调试模式，打印详细信息
    if (options.debug) {
      debugNestedValue(data, trimmedPath);
    }

    const value = getNestedValue(data, trimmedPath);

    if (value !== undefined && value !== null) {
      if (options.debug) {
        console.log(`✅ 变量替换成功: ${match} -> ${value}`);
      }
      return String(value);
    }

    // 如果配置了默认值，使用默认值
    if (options.defaultValue !== undefined) {
      if (options.debug) {
        console.log(`🔄 使用默认值: ${match} -> ${options.defaultValue}`);
      }
      return options.defaultValue;
    }

    // 如果配置了保留未定义变量，返回原始格式
    if (options.keepUndefined !== false) {
      if (options.debug) {
        console.log(`⚠️ 保留未定义变量: ${match}`);
      }
      return match;
    }

    // 否则返回空字符串
    if (options.debug) {
      console.log(`❌ 移除未定义变量: ${match}`);
    }
    return '';
  });
}

/**
 * 清理模板中被HTML标签分割的变量名
 * @param template 模板字符串
 * @returns 清理后的模板字符串
 */
function cleanVariableNames(template: string): string {
  // 匹配被HTML标签分割的变量名模式
  // 例如: {<span>contactInfo.</span><span>contactPerson</span>}
  const brokenVariableRegex = /\{([^}]*?<[^>]*>[^}]*?)+\}/g;

  return template.replace(brokenVariableRegex, (match) => {
    // 移除所有HTML标签，只保留变量名内容
    const cleanedContent = match.replace(/<[^>]*>/g, '');
    return cleanedContent;
  });
}

/**
 * 获取嵌套对象的值
 * @param obj 对象
 * @param path 路径字符串，如 'a.b.c'
 * @returns 值或 undefined
 */
function getNestedValue(obj: any, path: string): any {
  if (!obj || !path) return undefined;

  const keys = path.split('.');
  let current = obj;

  for (const key of keys) {
    if (current === null || current === undefined) {
      return undefined;
    }
    current = current[key];
  }

  return current;
}

/**
 * 调试函数：打印数据结构和路径解析过程
 * @param obj 数据对象
 * @param path 路径字符串
 */
function debugNestedValue(obj: any, path: string): void {
  console.log(`🔍 调试路径解析: ${path}`);

  if (!obj || !path) {
    console.log('❌ 对象或路径为空');
    return;
  }

  const keys = path.split('.');
  let current = obj;

  console.log(`🔗 路径分解: [${keys.join(', ')}]`);

  for (let i = 0; i < keys.length; i++) {
    const key = keys[i];
    const currentPath = keys.slice(0, i + 1).join('.');

    if (current === null || current === undefined) {
      console.log(`❌ 在路径 "${currentPath}" 处值为 null/undefined`);
      return;
    }

    console.log(`✅ ${currentPath}: ${typeof current[key]} =`, current[key]);
    current = current[key];
  }

  console.log(`🎯 最终结果:`, current);
}

/**
 * 去除多余的空HTML标签
 * @param html HTML字符串
 * @returns 清理后的HTML字符串
 */
function removeEmptyHtmlTags(html: string): string {
  if (!html || typeof html !== 'string') {
    return html;
  }

  // 自闭合标签列表，这些标签即使没有内容也应该保留
  const selfClosingTags = [
    'area', 'base', 'br', 'col', 'embed', 'hr', 'img', 'input',
    'link', 'meta', 'param', 'source', 'track', 'wbr'
  ];

  let result = html;
  let previousResult = '';

  // 循环清理，直到没有更多的空标签可以清理
  while (result !== previousResult) {
    previousResult = result;

    // 匹配空标签的正则表达式
    // 匹配形如 <tag>空白字符</tag> 或 <tag></tag> 的标签
    const emptyTagRegex = /<(\w+)([^>]*)>\s*<\/\1>/gi;

    result = result.replace(emptyTagRegex, (match, tagName, attributes) => {
      // 检查是否是自闭合标签
      if (selfClosingTags.includes(tagName.toLowerCase())) {
        return match; // 保留自闭合标签
      }

      // 检查标签是否有重要属性（如id、class、style等）
      const hasImportantAttributes = /\s+(id|class|style|data-[\w-]+)\s*=/i.test(attributes);

      if (hasImportantAttributes) {
        return match; // 保留有重要属性的标签
      }

      // 移除空标签
      return '';
    });

    // 清理只包含空白字符和换行符的标签
    const whitespaceOnlyRegex = /<(\w+)([^>]*)>[\s\n\r]*<\/\1>/gi;

    result = result.replace(whitespaceOnlyRegex, (match, tagName, attributes) => {
      // 检查是否是自闭合标签
      if (selfClosingTags.includes(tagName.toLowerCase())) {
        return match; // 保留自闭合标签
      }

      // 检查标签是否有重要属性
      const hasImportantAttributes = /\s+(id|class|style|data-[\w-]+)\s*=/i.test(attributes);

      if (hasImportantAttributes) {
        return match; // 保留有重要属性的标签
      }

      // 移除只包含空白字符的标签
      return '';
    });

    // 清理只包含 &nbsp; 实体的标签
    const nbspOnlyRegex = /<(\w+)([^>]*)>(\s|&nbsp;)*<\/\1>/gi;

    result = result.replace(nbspOnlyRegex, (match, tagName, attributes, content) => {
      // 检查是否是自闭合标签
      if (selfClosingTags.includes(tagName.toLowerCase())) {
        return match; // 保留自闭合标签
      }

      // 检查标签是否有重要属性
      const hasImportantAttributes = /\s+(id|class|style|data-[\w-]+)\s*=/i.test(attributes);

      if (hasImportantAttributes) {
        return match; // 保留有重要属性的标签
      }

      // 检查内容是否只包含空白字符和 &nbsp; 实体
      const cleanContent = content.replace(/\s/g, '').replace(/&nbsp;/gi, '');

      if (cleanContent === '') {
        // 移除只包含 &nbsp; 和空白字符的标签
        return '';
      }

      return match; // 保留有其他内容的标签
    });
  }

  // 清理多余的连续空白字符和换行符
  result = result.replace(/\n\s*\n/g, '\n').trim();

  return result;
}
