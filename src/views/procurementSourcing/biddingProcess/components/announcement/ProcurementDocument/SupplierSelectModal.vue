<template>
  <el-dialog
    v-model="visible"
    title="选择供应商"
    width="1000px"
    :before-close="handleClose"
  >
    <div class="supplier-select-modal">
      <!-- 搜索区域 -->
      <el-form :model="searchForm" inline>
        <el-form-item label="供应商名称">
          <el-input
            v-model="searchForm.supplierName"
            placeholder="请输入供应商名称"
            clearable
            style="width: 200px"
          />
        </el-form-item>
        <el-form-item label="供应商类型">
          <el-select
            v-model="searchForm.supplierType"
            placeholder="请选择供应商类型"
            multiple
            clearable
            style="width: 200px"
          >
            <el-option
              v-for="item in SUPPLIER_TYPE_OPTIONS"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="企业性质">
          <el-select
            v-model="searchForm.businessNature"
            placeholder="请选择企业性质"
            multiple
            clearable
            style="width: 200px"
          >
            <el-option
              v-for="item in BUSINESS_NATURE_OPTIONS"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">搜索</el-button>
          <el-button @click="handleReset">重置</el-button>
        </el-form-item>
      </el-form>

      <!-- 供应商列表 -->
      <div class="table-section">
        <el-table
          ref="tableRef"
          :data="supplierList"
          :loading="loading"
          style="width: 100%"
          max-height="400"
          @selection-change="handleSelectionChange"
          @select="handleSelectChange"
          :class="{ 'is-zjwt': isZJWT }"
        >
          <el-table-column type="selection" width="55" />
          <el-table-column prop="supplierName" label="供应商名称" min-width="200" />
          <el-table-column prop="supplierType" label="供应商类型" width="150">
            <template #default="{ row }">
              {{ getSupplierTypeLabel(row.supplierType) }}
            </template>
          </el-table-column>
          <el-table-column prop="enterpriseNature" label="企业性质" width="120">
            <template #default="{ row }">
              {{ getBusinessNatureLabel(row.enterpriseNature) }}
            </template>
          </el-table-column>
          <el-table-column prop="contactName" label="联系人" width="100" />
          <el-table-column prop="contactPhone" label="联系电话" width="120" />
        </el-table>

        <!-- 分页 -->
        <div class="pagination-section">
          <el-pagination
            v-model:current-page="pagination.current"
            v-model:page-size="pagination.size"
            :total="pagination.total"
            :page-sizes="[10, 20, 50, 100]"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleConfirm">
          确定 ({{ selectedSuppliers.length }})
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch, nextTick } from 'vue'
import { ElMessage } from 'yun-design'
import {
  fetchSupplierPage,
  SupplierInfo,
  SupplierPageParams,
  SUPPLIER_TYPE_OPTIONS,
  BUSINESS_NATURE_OPTIONS,
  SUPPLIER_TYPES,
  BUSINESS_NATURES
} from '@/api/supplier'
import {
  InviteSupplier
} from '@/views/procurementSourcing/biddingProcess/components/announcement/ProcurementDocument/types';
import { useBiddingStore } from '@/views/procurementSourcing/biddingProcess/stores';

interface Supplier extends SupplierInfo {}

interface Props {
  modelValue: boolean
  selectedSuppliers?: Supplier[],
}

interface Emits {
  (e: 'update:modelValue', value: boolean): void
  (e: 'confirm', suppliers: Supplier[]): void
}

const props = withDefaults(defineProps<Props>(), {
  selectedSuppliers: () => [],
})

const biddingStore = useBiddingStore();
const isZJWT = computed(() => biddingStore.isZJWT)

const emit = defineEmits<Emits>()

const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const tableRef = ref()
const loading = ref(false)

// 搜索表单
const searchForm = reactive<SupplierPageParams>({
  supplierName: '',
  supplierType: [],
  businessNature: []
})

// 分页数据
const pagination = reactive({
  current: 1,
  size: 20,
  total: 0
})

// 供应商列表
const supplierList = ref<Supplier[]>([])

// 选中的供应商
const selectedSuppliers = ref<Supplier[]>([])

// 获取供应商类型标签
const getSupplierTypeLabel = (type: string) => {
  const option = SUPPLIER_TYPE_OPTIONS.find(item => item.value === type)
  return option ? option.label : type
}

// 获取企业性质标签
const getBusinessNatureLabel = (nature: string) => {
  const option = BUSINESS_NATURE_OPTIONS.find(item => item.value === nature)
  return option ? option.label : nature
}

// 获取供应商列表
const getSupplierList = async () => {
  loading.value = true
  try {
    const params = {
      current: pagination.current,
      size: pagination.size
    }

    const queryData: SupplierPageParams = {
      supplierName: searchForm.supplierName || undefined,
      supplierType: searchForm.supplierType?.length ? searchForm.supplierType : undefined,
      businessNature: searchForm.businessNature?.length ? searchForm.businessNature : undefined
    }

    const response = await fetchSupplierPage(queryData, params)

    if (response.code === 0) {
      supplierList.value = response.data.records || []
      pagination.total = response.data.total || 0
    } else {
      ElMessage.error(response.msg || '获取供应商列表失败')
    }

  } catch (error) {
    ElMessage.error('获取供应商列表失败')
  } finally {
    loading.value = false
  }
}

// 搜索
const handleSearch = () => {
  pagination.current = 1
  getSupplierList()
}

// 重置
const handleReset = () => {
  searchForm.supplierName = ''
  searchForm.supplierType = []
  searchForm.businessNature = []
  pagination.current = 1
  getSupplierList()
}

// 分页大小改变
const handleSizeChange = (size: number) => {
  pagination.size = size
  getSupplierList()
}

// 当前页改变
const handleCurrentChange = (current: number) => {
  pagination.current = current
  getSupplierList()
}


const handleSelectChange = (selection: Supplier[], row: Supplier) => {
  if (isZJWT.value) {
    tableRef.value.clearSelection();
    // tableRef.value.setCurrentRow(row);
    tableRef.value.toggleRowSelection(row, true)
    selectedSuppliers.value = [ row ]
  }
}

// 选择改变
const handleSelectionChange = (selection: Supplier[]) => {
  // 直接委托只能选择一个供应商
  if (isZJWT.value) {
    return
  }

  // 合并当前页选择和之前页面的选择
  const currentPageIds = supplierList.value.map(item => item.id)
  const otherPageSelected = selectedSuppliers.value.filter(item =>
    !currentPageIds.includes(item.id)
  )

  selectedSuppliers.value = [...otherPageSelected, ...selection]
}


// 更新表格选择状态
const updateTableSelection = () => {

  supplierList.value.forEach(row => {
    const isSelected = props.selectedSuppliers.some(item => {
      return item.tenantSupplierId == row.id
    })
    if (isSelected) {
      tableRef.value.toggleRowSelection(row, true)
    }
  })

}

// 确认选择
const handleConfirm = () => {
  const list = selectedSuppliers.value.map(item => {
    return {
      id: item.id, // 保持原有的 id 字段
      tenantSupplierId: String(item.id), // 确保是字符串类型
      supplierName: item.supplierName,
      contactName: item.contactName,
      contactPhone: item.contactPhone
    }
  })
  emit('confirm', list)
  handleClose()
}

// 关闭对话框
const handleClose = () => {
  visible.value = false
}


onMounted(async () => {
  await getSupplierList()
  updateTableSelection()
})
</script>

<style scoped lang="scss">
.supplier-select-modal {
  .search-section {
    margin-bottom: 16px;
    padding: 16px;
    background: #f8f9fa;
    border-radius: 4px;
  }

  .table-section {
    margin-bottom: 16px;
  }

  .pagination-section {
    display: flex;
    justify-content: end;
    margin-top: 16px;
  }

  .selected-section {
    border-top: 1px solid #ebeef5;
    padding-top: 16px;

    .selected-title {
      font-weight: 600;
      margin-bottom: 12px;
      color: #303133;
    }

    .selected-list {
      min-height: 32px;
    }
  }
}

.dialog-footer {
  display: flex;
  justify-content: center;
  gap: 16px;
}

:deep(.is-zjwt .el-table__header .el-table-column--selection .cell) {
  display: none !important;
}
</style>
