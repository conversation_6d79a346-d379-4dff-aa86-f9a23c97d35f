<template>
  <div class="min-h-full flex flex-col">
    <div
      class="component-content"
      :class="{ 'bg-[#fff]': !isShowView }"
    >
      <ProcurementDocument
        v-show="!isShowView"
        @success="handleSave"
      ></ProcurementDocument>
      <ProcurementAnnouncement
        v-show="isShowView"
        @back="isShowView = false"
        :data="formData"
      ></ProcurementAnnouncement>
    </div>
  </div>
</template>

<script setup lang="ts">
import ProcurementAnnouncement from '@/views/procurementSourcing/biddingProcess/components/announcement/ProcurementAnnouncement/index.vue';
import ProcurementDocument from '@/views/procurementSourcing/biddingProcess/components/announcement/ProcurementDocument/index.vue';
import { ref } from 'vue';

const isShowView = ref(false);
const formData = ref({});

const handleSave = (data: any) => {
  isShowView.value = true;
  formData.value = data;
};
</script>

<style lang="scss" scoped>
.component-content {
  position: relative;
  flex: 1;
  border-radius: 6px;
}
</style>
