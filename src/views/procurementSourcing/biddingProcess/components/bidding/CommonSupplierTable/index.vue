<template>
  <div
    class="document-review-container"
    :class="{ 'is-hide-stage-tabs': !isShowSection }"
    v-loading="pageLoading"
  >
    <template v-if="![SCENE_ENUMS.XJCG_TB, SCENE_ENUMS.JZTP_ZB_KB, SCENE_ENUMS.JZTP_TB, SCENE_ENUMS.ZB_TB]?.includes(scene)">
      <StageTabs
        v-model="activeTabIndex"
        @update:sectionId="handleSectionChange"
      />
    </template>

    <!-- 询价采购-投标-导出按钮-位置自定义 -->
    <!-- <template v-if="[SCENE_ENUMS.XJCG_TB, SCENE_ENUMS.JZTP_TB, SCENE_ENUMS.ZB_TB]?.includes(scene)"> -->
      <el-button
        class="right-button xjcg-export"
        @click="handleDownloadSupplierTemplate"
      >
        数据导出
      </el-button>
    <!-- </template> -->

    <div
      v-if="!pageLoading"
      class="flex-1 need-hide-table-card editable-table table-container"
    >
      <yun-pro-table
        ref="proTableRef"
        v-model:pagination="pagination"
        v-model:filter-data="filterTableData"
        v-model:searchData="searchData"
        :table-columns="columns"
        :search-fields="searchFields"
        :auto-height="autoHeight"
        :remote-method="remoteMethod"
        :table-props="tablePropsObj"
        :search-props="searchPropsObj"
        layout="whole"
        :default-fetch="false"
        :extra="{
          position: 'breaster',
        }"
      >
        <!-- 竞争谈判+投标 -->
        <template #extra v-if="[SCENE_ENUMS.JZTP_TB, SCENE_ENUMS.ZB_TB]?.includes(scene)">
          <!-- 询价采购采购-投标可以查看此节点 -->
          <div
            class="alert-section"
            v-if="supplierLessThanThreeCount"
          >
            <img
              src="https://oss-public.yunlizhi.cn/frontend/fe-procurement-platform/alert_icon.svg"
              alt=""
            />
            <div class="flex items-center">
              <span>提醒：报价供应商不足 3 家！</span>
            </div>
          </div>
        </template>
        <!-- <template
          #t_tableHeaderLeft
          v-if="![SCENE_ENUMS.XJCG_TB]?.includes(scene)"
        ></template>
        <template
          #t_tableHeaderRight
          v-if="![SCENE_ENUMS.XJCG_TB]?.includes(scene)"
        >
          <el-button @click="handleDownloadSupplierTemplate"> 数据导出 </el-button>
          询价采购采购-投标可以查看此节点
          <template v-if="[SCENE_ENUMS.XJCG_TB]?.includes(scene)">
            <el-button @click="handleDownloadSupplierTemplate"> 数据导出 </el-button>
          </template>
          <template v-else>
            <el-button @click="handleDownloadSupplierTemplate"> 数据导出 </el-button>
            <div class="right-section">
              已发起 <span class="highlight">{{ quoteRoundCount }}</span> 轮报价
            </div>
          </template>
        </template> -->
        <template #t_action="{ row }">
          <el-button
            type="text"
            @click="handleViewDetail(row)"
            >报价明细</el-button
          >
        </template>
        <template #t_lookUp="{ row }">
          <el-button
            type="text"
            @click="handleViewRegister(row)"
            >查看</el-button
          >
        </template>
      </yun-pro-table>
    </div>

    <!-- 报价详情抽屉 -->
    <!-- <QuotationDetailDrawer
      :row="currentRow"
      v-model:visible="detailDrawerVisible"
    /> -->

    <QuotationDetail ref="quotationDetailRef" />

    <!-- 报名资料 -->
    <AuditDrawer
      v-model:visible="auditDrawerVisible"
      :row="currentRow"
      :btnType="BTN_O_TYPE.DETAIL"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onBeforeMount } from 'vue';
// @ts-ignore
import StageTabs from '@/views/procurementSourcing/biddingProcess/components/bidding/StageTabs.vue';
// @ts-ignore
import AuditDrawer from '@/views/procurementSourcing/biddingProcess/components/bidding/DocumentReview/AuditDrawer.vue';
// @ts-ignore
// import QuotationDetailDrawer from '@/views/procurementSourcing/biddingProcess/components/bidding/QuotationManagement/QuotationDetailDrawer.vue';
import QuotationDetail from '@/views/procurementSourcing/biddingProcess/components/BidEvaluation/online/components/QuotationDetail/index.vue';

import { useProTable } from '@ylz-use/core';
import { useColumns } from './hooks/useColumns';
import { useBiddingStore } from '@/views/procurementSourcing/biddingProcess/stores';
import { queryQuoteListBySupplier } from '@/api/purchasing/bid';
import { ElMessage } from 'yun-design';
import moment from 'moment';
// @ts-ignore
import { useExcel } from '@/views/procurementSourcing/biddingProcess/hooks/useExcel.jsx';
import { BTN_O_TYPE, SCENE_ENUMS } from '@/views/procurementSourcing/biddingProcess/constants';

const quotationDetailRef = ref<any>(null);
const props = withDefaults(defineProps<{ scene?: any, autoHeight?: boolean }>(), {
  scene: '',
  autoHeight: false,
});
// eslint-disable-next-line no-console
console.log('props', props.scene);
const pageLoading = ref(true);

const tableSupplierData = ref([]);
// 详情抽屉控制
// const detailDrawerVisible = ref(false);
// 报名资料抽屉控制
const auditDrawerVisible = ref(false);
const currentRow = ref<any>(null);

const biddingStore = useBiddingStore();
const { exportToExcel } = useExcel();

const currentSectionId = ref('');
const activeTabIndex = ref(0);

const noticeInfo = computed(() => biddingStore?.noticeInfo);
const noticeId = computed(() => biddingStore?.noticeId);
const projectId = computed(() => biddingStore?.projectId);
// const isZJWT = computed(() => biddingStore?.isZJWT);
// const isXJCG = computed(() => biddingStore?.isXJCG);
// const isJZTP = computed(() => biddingStore?.isJZTP);
// const isZB = computed(() => biddingStore?.isZB);
// const isCompetitiveBidding = computed(() => biddingStore?.isCompetitiveBidding);
const isShowSection = computed(() => biddingStore?.isShowSection);

// const isHideStage = computed(() => {
//   return isZJWT.value || isShowSection.value;
// });

// const pagination = ref({});
const { columns, searchFields, searchData } = useColumns({ sectionId: currentSectionId });

const { remoteMethod, tableProps, proTableRef, filterTableData, reLoad, pagination } = useProTable({
  apiFn: queryQuoteListBySupplier,
  async responseHandler(result: any) {
    const list: any = result?.data || [];
    const res = list?.map((row: any) => {
      const { quoteMaterialCount, totalMaterialCount } = row;
      return {
        ...row,
        count: `${quoteMaterialCount || 0}/${totalMaterialCount || 0}`,
      };
    });
    tableSupplierData.value = res;
    return res;
  },
  // eslint-disable-next-line no-unused-vars, @typescript-eslint/no-unused-vars
  customTotalHandler(data: any) {
    return 0;
    // return data?.data?.total || data?.total;
  },
  paramsHandler(params: any) {
    const param = {
      sectionId: currentSectionId.value,
      noticeId: noticeId.value,
      projectId: projectId.value,
      ...params,
    };
    return param;
  },
  // eslint-disable-next-line no-unused-vars, @typescript-eslint/no-unused-vars
  querysHandler(querys: any) {
    const querysData = {
      // ...querys,
      current: pagination.value.page,
      size: 1000, // 先查询所有数据
      // size: pagination.value.size || pagination.value.pageSize,
    };
    return querysData;
  },
  plugins: {
    config: {
      columns: columns.value,
      searchFields: searchFields.value,
    },
    list: ['SEARCH_PLUS'],
  },
});

const supplierLessThanThreeCount = computed(() => {
  return tableSupplierData.value?.length && [...new Set(tableSupplierData.value?.map((item: any) => item?.tenantSupplierId))].length < 3
    ? true
    : false;
});

const searchPropsObj = computed(() => ({
  showCollapseBtn: false,
  collapse: false,
  showResetBtn: false,
  showSubmitBtn: false,
  showOperation: false,
  quantity: 3,
}));

const tablePropsObj = computed(() => ({
  ...tableProps.value,
  stripe: true,
  border: true,
  headerCellStyle: {
    backgroundColor: '#f5f7fa',
    color: '#303133',
    fontWeight: 'bold',
    // height: '40px',
    borderColor: '#EBEEF5',
    'vertical-align': 'middle',
  },
  cellStyle: {
    padding: '0',
    // height: '40px',
    'vertical-align': 'middle',
    borderColor: '#EBEEF5',
  },
  rowStyle: {
    // height: '40px',
    borderColor: '#EBEEF5',
  },
  // rowHeight: 40,
}));

const handleDownloadSupplierTemplate = async () => {
  try {
    const blob = await exportToExcel(
      tableSupplierData.value,
      columns?.value
        ?.filter((item: any) => !item.ingoreExport)
        ?.map((item: any) => ({
          label: item.label,
          prop: item.prop,
          fieldName: item.label,
          fieldCode: item.prop,
        }))
    );
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `供应商数据_${moment().format('YYYY-MM-DD HH:mm:ss')}.xlsx`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    window.URL.revokeObjectURL(url);
  } catch (error) {
    ElMessage.error('导出失败');
  }
};

const quoteRoundCount = computed(() => {
  const list = noticeInfo?.value?.quoteRoundVoList || [];
  const findRound = list.find((item: any) => String(item?.sectionId) === String(currentSectionId.value)) || {};
  return findRound?.currentQuoteRound || 1;
});

// 查看详情
function handleViewDetail(row: any) {
  currentRow.value = { ...row, sectionId: currentSectionId.value, quoteRoundCount: quoteRoundCount.value };
  // detailDrawerVisible.value = true;
  quotationDetailRef.value?.show(currentRow.value);
}

// 报名资料
function handleViewRegister(row: any) {
  currentRow.value = { ...row, sectionId: currentSectionId.value };
  auditDrawerVisible.value = true;
}

// 标段变化事件
async function handleSectionChange(sectionId: string) {
  pageLoading.value = true;
  currentSectionId.value = sectionId;
  pageLoading.value = false;
  setTimeout(async () => {
    if (currentSectionId.value) {
      reLoad();
    }
  }, 300);
}
onMounted(async () => {});
onBeforeMount(async () => {});
defineExpose({
  handleSectionChange,
});
</script>

<style scoped lang="scss">
.document-review-container {
  flex: 1;
  padding: 20px;
  box-sizing: border-box;
  height: 100%;
  display: flex;
  flex-direction: column;
  position: relative;
  // &.isZB,
  // &.isJZTP,
  // &.isXJCG {
    :deep(.dm-filter-root) {
      padding-right: 90px !important;
    }
    .right-button {
      position: absolute;
      z-index: 1;
      right: 0;
      top: 22px;
    }
  // }
  .table-container {
    :deep(.quoteIp-header-cell-class-name .filter-box .filter-box-content .text-box) {
      color: var(--Color-Error-color-error, #ff3b30) !important;
    }
    :deep(.quoteIp-cell-class-name .cell) {
      background-color: rgba(255, 59, 48, 0.06) !important;
      color: var(---el-color-danger, #f53f3f) !important;
    }
    .alert-section {
      display: flex;
      padding: 8px 16px;
      align-items: center;
      gap: 8px;
      align-self: stretch;
      border-radius: var(--Radius-border-radius-small, 2px);
      background: var(--Color-Warning-color-warning-light-9, #fff9e8);
      color: var(--Color-Text-text-color-primary, #1d2129);
      font-family: 'PingFang SC';
      font-size: 13px;
      font-style: normal;
      font-weight: 400;
      line-height: 22px;
    }
    .common-ip-alert {
      border-radius: 4px;
      border: 1px solid var(--Color-Border-border-color-light, #e4e7ed);
      background: var(--color-white, #fff);
      /* light/box-shadow-lighter */
      box-shadow: 0 0 6px 0 rgba(0, 0, 0, 0.12);
      display: flex;
      padding: 8px 16px;
      align-items: center;
      position: relative;
      width: max-content;
      .alert-icon {
        width: 16px;
        height: 16px;
      }
      .alert-msg {
        color: var(--Color-Error-color-error, #ff3b30);
        /* regular/small */
        font-family: 'PingFang SC';
        font-size: 13px;
        font-style: normal;
        font-weight: 400;
        line-height: 22px; /* 169.231% */
        padding-left: 8px;
        padding-right: 24px;
      }
      .alert-close {
        width: 12px;
        height: 12px;
        cursor: pointer;
      }
    }
  }
  :deep(.dm-table-header) {
    padding-top: 0;
    padding-bottom: 16px;
  }
  .right-section {
    color: var(--Color-Text-text-color-secondary, #86909c);
    font-family: 'PingFang SC';
    font-size: 12px;
    font-style: normal;
    font-weight: 400;
    line-height: 20px;
    margin-left: 32px;
    .highlight {
      color: var(--Color-Text-text-color-primary, #1d2129);
      font-family: 'PingFang SC';
      font-size: 12px;
      font-style: normal;
      font-weight: 600;
      line-height: 20px;
    }
  }
}
</style>
