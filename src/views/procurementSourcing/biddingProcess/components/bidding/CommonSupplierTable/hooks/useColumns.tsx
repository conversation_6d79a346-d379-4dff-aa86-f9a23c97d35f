import { ref, computed, watch } from 'vue';
import { useBiddingStore } from '@/views/procurementSourcing/biddingProcess/stores';
import { numberToChinese } from '@/views/procurementSourcing/biddingProcess/utils';
// import { SCENE_ENUMS } from '@/views/procurementSourcing/biddingProcess/constants';

export function useColumns({ sectionId }: any) {
  const biddingStore = useBiddingStore();
  const noticeInfo = computed(() => biddingStore?.noticeInfo);
  // const isZJWT = computed(() => biddingStore?.isZJWT);
  const quotationRounds = ref<any>([]);
  const searchData = ref<any>({});
  const columns = ref([
    {
      label: '序号',
      type: 'index',
      width: 60,
      ingoreExport: true,
    },
    {
      label: '供应商名称',
      prop: 'supplierName',
    },
    {
      label: '联系人',
      prop: 'contactPerson',
    },
    {
      label: '报名资料',
      prop: 'lookUp',
      ingoreExport: true,
    },
    {
      label: '报价轮次',
      prop: 'quoteRoundCount',
    },
    {
      label: '报价/物料条数',
      prop: 'count',
    },
    {
      label: '本轮报价总价',
      prop: 'totalQuoteAmount',
    },
    {
      label: '最终报价IP',
      prop: 'quoteIp',
    },
    {
      label: '操作',
      prop: 'action',
      width: 100,
      fixed: 'right',
      ingoreExport: true,
    },
  ]);

  const quoteRoundCount = ref(1);

  watch([() => sectionId.value, noticeInfo?.value?.quoteRoundVoList], (val) => {
    if (val[0] && val[1]?.length) {
      quoteRoundCount.value = (val[1]?.find((item: any) => String(item?.sectionId) === String(sectionId.value)) || {})?.currentQuoteRound || 1;

      initRoundsData();

      const lastRound = quotationRounds.value?.[quotationRounds.value?.length - 1]?.value || '';
      searchData.value.roundNo = lastRound;
    }
  });

  // 初始化轮次数据
  function initRoundsData() {
    quotationRounds.value = Array.from({ length: quoteRoundCount.value }, (_, i) => i + 1).map((item) => {
      return {
        label: `第${numberToChinese(item)}轮报价`,
        value: item,
      };
    });
  }

  const searchFields = computed(() => {
    const commonFields = [
      // ...(![SCENE_ENUMS.ZJWT_KB]?.includes(scene)
      //   ? [
      //       {
      //         label: '选择报价轮次',
      //         prop: 'roundNo',
      //         type: 'select',
      //         componentAttrs: {
      //           // filterable: true,
      //           // multiple: true,
      //           clearable: false,
      //         },
      //         options: quotationRounds.value,
      //       },
      //     ]
      //   : []),
      {
        label: '选择报价轮次',
        prop: 'roundNo',
        type: 'select',
        componentAttrs: {
          // filterable: true,
          // multiple: true,
          clearable: false,
        },
        options: quotationRounds.value,
      },
      {
        label: '供应商名称',
        prop: 'supplierName',
        type: 'input',
        componentAttrs: {
          clearable: true,
        },
      },
    ];
    return commonFields;
  });

  return {
    columns,
    searchFields,
    searchData,
  };
}
