<template>
  <el-drawer
    v-model="showVisible"
    :title="title"
    size="78vw"
    :with-header="true"
    append-to-body
    destroy-on-close
    :close-on-click-modal="false"
  >
    <el-form
      :rules="rules"
      ref="formRef"
      label-width="145px"
      :model="form"
      class="bidding-process-search-form"
      style="margin-top: 0px"
      v-loading="loading"
    >
      <div>
        <div
          class="form-section"
          v-if="type === T_TYPE.PURCHASER"
        >
          <div class="label-header">供应商信息</div>
          <el-form-item
            label="选择供应商"
            prop="tenantSupplierId"
            label-width="100px"
          >
            <el-select
              v-model="form.tenantSupplierId"
              @change="reset"
              placeholder="请选择供应商"
              style="width: 300px !important"
            >
              <el-option
                v-for="item in supplierInfoList"
                :key="item.value"
                :label="item.label"
                :value="item.id"
              />
            </el-select>
            <div style="padding-left: 12px; color: #e6a23c">请先选择供应商数据</div>
          </el-form-item>
        </div>
        <div
          class="form-section"
          style="margin-bottom: 24px"
        >
          <div class="label-header">报价清单</div>
          <div class="form-content">
            <div class="form-item-wrapper">
              <label class="form-label">物料名称</label>
              <el-input
                v-model="queryForm.materialName"
                placeholder="请输入物料名称"
                clearable
                class="search-input"
              />
            </div>
            <div
              class="form-item-wrapper"
              v-if="type === T_TYPE.PURCHASER"
            >
              <label class="form-label">物料编码</label>
              <el-input
                v-model="queryForm.materialCode"
                placeholder="请输入物料编码"
                clearable
                class="search-input"
              />
            </div>
            <div>
              <el-button
                @click="searchHandler"
                type="primary"
              >
                搜索
              </el-button>
              <el-button @click="reset"> 重置 </el-button>
            </div>
            <div>
              <el-button
                @click="handleExport"
                :disabled="form.quotationList?.length === 0"
              >
                <el-icon style="margin-right: 6px">
                  <Download />
                </el-icon>
                导出报价清单
              </el-button>
              <el-button
                style="position: relative"
                :disabled="form.quotationList?.length === 0"
                :loading="uploadLoading"
              >
                <el-icon style="margin-right: 6px">
                  <Upload />
                </el-icon>
                <input
                  type="file"
                  ref="fileInputRef"
                  @change="handleImport"
                  style="position: absolute; top: 0; left: 0; width: 100%; height: 100%; opacity: 0"
                />
                上传报价清单
              </el-button>
            </div>
          </div>
        </div>
        <el-table
          :data="form.quotationList"
          style="width: 100%"
          class="editable-table"
          @selection-change="handleSelectionChange"
          row-key="id"
          size="small"
        >
          <el-table-column
            fixed="left"
            width="80"
            type="selection"
            v-if="!isCompetitiveBidding && !isZJWT"
          />
          <!-- 渲染动态列 -->
          <template
            v-for="column in mergedFormColumns"
            :key="column.prop || column.label"
          >
            <!-- 如果有子列 -->
            <el-table-column
              v-if="column.children?.length > 0"
              :label="column.label"
              :prop="column.prop"
            >
              <DynamicTableColumn
                v-for="childColumn in column.children"
                :key="childColumn.prop"
                :column="childColumn"
                :get-field-error="getFieldError"
                :validate-field="validateField"
                :get-enum-options="getEnumOptions"
                :change-usage-location="changeUsageLocation"
                :get-quality-indicator="getQualityIndicator"
                @validate-field="validateField"
                @change-usage-location="changeUsageLocation"
                @get-quality-indicator="getQualityIndicator"
              />
            </el-table-column>
            <!-- 如果没有子列，直接渲染 -->
            <DynamicTableColumn
              v-else
              :column="column"
              :get-field-error="getFieldError"
              :validate-field="validateField"
              :get-enum-options="getEnumOptions"
              :change-usage-location="changeUsageLocation"
              :get-quality-indicator="getQualityIndicator"
              @validate-field="validateField"
              @change-usage-location="changeUsageLocation"
              @get-quality-indicator="getQualityIndicator"
            />
          </template>
        </el-table>
      </div>

      <!-- 报价附件 -->
      <div class="form-section">
        <el-form-item
          label="报价单"
          prop="quoteAttachment"
          label-width="80px"
        >
          <YunUpload
            :multiple="false"
            :show-file-list="true"
            v-model="form.quoteAttachment"
            buttonText="上传报价单"
            @change="(v: any) => uploadQuotationAttachment(v, 'quoteAttachment')"
          />
        </el-form-item>
        <el-form-item
          label="其他附件"
          prop="otherAttachment"
          label-width="80px"
        >
          <YunUpload
            :multiple="false"
            :show-file-list="true"
            v-model="form.otherAttachment"
            buttonText="上传文件其他附件"
            @change="(v: any) => uploadQuotationAttachment(v, 'otherAttachment')"
          />
        </el-form-item>
      </div>
    </el-form>

    <template #footer>
      <div class="flex justify-end gap-4">
        <el-button @click="showVisible = false">取消</el-button>
        <el-button
          type="primary"
          @click="handleSubmit"
          :loading="isLoading"
          :disabled="form.quotationList?.length === 0"
        >
          提交
        </el-button>
      </div>
    </template>
  </el-drawer>
</template>

<script setup lang="tsx">
import { ref, watch, defineProps, defineEmits, onMounted, computed } from 'vue';
import { Download, Upload } from '@element-plus/icons-vue';
import { queryMaterialList, submitQuote } from '@/api/purchasing/bid';
import YunUpload from '/@/components/YunUpload/index.vue';
import { ElMessage } from 'yun-design';
import moment from 'moment';
import { T_TYPE, LOCKED_COLUMNS, FIXED_COLLECT } from '@/views/procurementSourcing/biddingProcess/constants/bid';
import { useBiddingStore } from '@/views/procurementSourcing/biddingProcess/stores';
// @ts-ignore
import { useDynamicTable } from '@/views/procurementSourcing/biddingProcess/hooks/useDynamicTable.jsx';
// @ts-ignore
import { useExcel } from '@/views/procurementSourcing/biddingProcess/hooks/useExcel.jsx';
import DynamicTableColumn from './DynamicTableColumn.vue';

const biddingStore = useBiddingStore();
const noticeId = computed(() => biddingStore?.noticeId);
const isZJWT = computed(() => biddingStore?.isZJWT);
const projectPaymentList = computed(() => biddingStore?.projectDetail?.projectPaymentList || []);
const supplierInfoList = computed(() => biddingStore?.supplierInfoList);
const isCompetitiveBidding = computed(() => biddingStore?.isCompetitiveBidding);

const { mergedFormColumns, getDetail, getEnumOptions, getQualityIndicator, changeUsageLocation, dynamicTableData, resetStatus } = useDynamicTable();
const { importExcelFile, exportToExcel } = useExcel();
const isLoading = ref(false);
const loading = ref(false);
const uploadLoading = ref(false);
const formRef = ref();
const selectedRows = ref<any[]>([]);
// 表单数据
const initForm = () => ({
  tenantSupplierId: '',
  noticeId: noticeId.value,
  sectionId: '',
  quoteItems: [],
  quoteAttachment: [],
  quoteAttachmentPath: '',
  quoteAttachmentName: '',
  otherAttachment: [],
  otherAttachmentPath: '',
  otherAttachmentName: '',
  quotationList: [],
});
const form = ref<any>({
  ...initForm(),
});
const props = defineProps<{ visible: boolean; row?: any; type?: any }>();
const emit = defineEmits(['refresh', 'update:visible']);
const showVisible = computed({
  get: () => props.visible,
  set: (v: boolean) => emit('update:visible', v),
});

const selectedQuotationList = computed(() => {
  if (isCompetitiveBidding.value || isZJWT.value) {
    return form.value.quotationList || [];
  }
  const ids = selectedRows.value?.map((item: any) => item.id);
  return form.value.quotationList?.filter((item: any) => ids.includes(item.id)) || [];
});

const title = computed(() => {
  return props.type === T_TYPE.PURCHASER ? '插入供应商报价' : '报价';
});

function handleSelectionChange(v: any) {
  selectedRows.value = v;
}

const handleExport = async () => {
  if (!form.value.quotationList || form.value.quotationList.length === 0) {
    ElMessage.warning('没有可导出的数据');
    return;
  }
  try {
    const blob = await exportToExcel(form.value.quotationList, mergedFormColumns.value, {
      lockedColumns: LOCKED_COLUMNS,
      protectWorksheet: true,
    });
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `报价清单_${moment().format('YYYY-MM-DD HH:mm:ss')}.xlsx`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    window.URL.revokeObjectURL(url);
  } catch (error) {
    ElMessage.error('导出失败');
  }
};

// 处理导入
const fileInputRef = ref<any>();
const handleImport = async () => {
  const file = fileInputRef.value?.files?.[0];
  if (!file) {
    ElMessage({ type: 'warning', message: '请先上传文件' });
    return;
  }
  try {
    uploadLoading.value = true;
    const rows = await importExcelFile(file, mergedFormColumns.value);
    if (rows?.length === 0) {
      ElMessage.warning('暂无符合的数据项');
      return;
    }
    rows?.forEach((item: any) => {
      if (item.materialCode) {
        const materialCode = item.materialCode.trim();
        const findIndex = form.value.quotationList.findIndex((item: any) => item.materialCode === materialCode);
        if (findIndex !== -1) {
          // 筛选出除FIXED_COLLECT以外的字段的值,生成对象格式
          const otherObj = Object.keys(item).filter(
            (key: any) => ![...FIXED_COLLECT, 'quoteAmount', 'usageLocationName', 'qualityIndicatorName'].includes(key)
          );
          const otherValues = otherObj.reduce((acc: any, key: any) => {
            acc[key] = item[key];
            return acc;
          }, {});
          form.value.quotationList[findIndex] = {
            ...form.value.quotationList[findIndex],
            ...otherValues,
          };
        }
      }
    });
    ElMessage.success('导入成功');
    // 清空文件input
    clearFileInput();
  } catch (error) {
    ElMessage.error((error as Error)?.message || '导入失败');
  } finally {
    uploadLoading.value = false;
  }
};

// 清空文件input的方法
function clearFileInput() {
  if (fileInputRef.value) {
    fileInputRef.value.value = '';
  }
}

watch(
  () => showVisible.value,
  (v) => {
    if (v) {
      selectedRows.value = [];
      form.value = initForm();
      handleReset();
      resetStatus();
      if (props.type !== T_TYPE.PURCHASER) {
        getMaterialList();
      }
    }
  }
);

const initQueryForm = () => ({
  materialName: '',
  materialCode: '',
});
const queryForm = ref<any>(initQueryForm());

// 表单校验规则
const rules = {
  quoteAttachment: [{ required: true, message: '请上传报价单', trigger: ['change', 'blur'] }],
  tenantSupplierId: [{ required: true, message: '请选择供应商', trigger: 'change' }],
};

// 自定义表格字段验证
const tableFieldErrors = ref<Record<string, string>>({});

// 获取字段错误信息
function getFieldError(rowIndex: number, fieldProp: string) {
  const key = `${rowIndex}_${fieldProp}`;
  return tableFieldErrors.value[key] || '';
}

// 验证单个字段
function validateField(rowIndex: number, fieldProp: string, column: any) {
  const key = `${rowIndex}_${fieldProp}`;
  const value = form.value.quotationList[rowIndex]?.[fieldProp];

  // 清除之前的错误
  delete tableFieldErrors.value[key];

  // 必填验证
  if (column.isRequired) {
    if (!value && value !== 0) {
      tableFieldErrors.value[key] = `${column.label}不能为空`;
      return false;
    }
  }

  // 根据字段类型验证
  if (value) {
    switch (column.fieldType) {
      case 'TEXT':
        if (column.fieldLength && value.length > column.fieldLength) {
          tableFieldErrors.value[key] = `${column.label}长度不能超过${column.fieldLength}个字符`;
          return false;
        }
        break;
      case 'NUM':
        if (!/^\d+(\.\d+)?$/.test(value.toString())) {
          tableFieldErrors.value[key] = '请输入有效的数字';
          return false;
        }
        if (column.fieldLength && value.toString().length > column.fieldLength) {
          tableFieldErrors.value[key] = `${column.label}总长度不能超过${column.fieldLength}个字符`;
          return false;
        }
        break;
    }
  }

  return true;
}

// 验证所有表格字段
function validateAllTableFields() {
  let isValid = true;
  tableFieldErrors.value = {};

  selectedQuotationList.value?.forEach((row: any) => {
    const rowIndex = form.value.quotationList?.findIndex((item: any) => item.id === row.id);
    const allColumns = mergedFormColumns.value.map((item: any) => [item, ...(item.children || [])]).flat(Infinity);
    allColumns.forEach((column: any) => {
      const fieldValid = validateField(rowIndex, column.prop, column);
      if (!fieldValid) {
        isValid = false;
      }
    });
  });

  return isValid;
}

function uploadQuotationAttachment(v: any, item: any) {
  const file = v[v.length - 1] || {};
  form.value[`${item}Path`] = file?.url || '';
  form.value[`${item}Name`] = file?.name || '';
  setTimeout(() => {
    form.value[`${item}`] = file?.url && file?.name ? [file] : [];
    formRef.value.validateField(item);
  }, 150);
}

function intoErrorView() {
  setTimeout(() => {
    const el = document.querySelector('.el-form-item__error');
    if (el) {
      el?.scrollIntoView({ behavior: 'smooth' });
    }
  });
}

// 处理提交
function handleSubmit() {
  if (isLoading.value) return;

  if (selectedQuotationList.value?.length === 0) {
    ElMessage.error('请选择要提交的数据');
    return;
  }

  // 先验证表格字段
  const tableValid = validateAllTableFields();
  if (!tableValid) {
    ElMessage.error('请完善表格中的必填信息');
  }

  formRef.value?.validate(async (valid: boolean) => {
    intoErrorView();
    if (valid && selectedQuotationList.value.length > 0 && tableValid) {
      isLoading.value = true;
      try {
        const params = {
          noticeId: noticeId.value,
          tenantSupplierId: form.value?.tenantSupplierId || '',
          sectionId: props.row.sectionId,
          quoteAttachmentPath: form.value.quoteAttachmentPath,
          quoteAttachmentName: form.value.quoteAttachmentName,
          otherAttachmentPath: form.value.otherAttachmentPath,
          otherAttachmentName: form.value.otherAttachmentName,
          quoteItems: projectPaymentList.value
            ?.map((itemPay: any) => {
              return selectedQuotationList.value.map((item: any) => ({
                projectItemId: item.id,
                projectPaymentId: itemPay.id,
                dynamicFields: mergedFormColumns.value
                  ?.map((itemC: any) => {
                    // 此时是多支付场景
                    if (String(itemC?.fieldCode) === String(itemPay.id)) {
                      const list = itemC?.children?.map((itemC2: any) => {
                        const [code] = itemC2?.fieldCode.split('_') || '';
                        return {
                          code,
                          value: itemC2?.formatter ? itemC2?.formatter(itemC2, item) : item[itemC2.prop],
                        };
                      });
                      return list;
                    } else {
                      return {
                        code: itemC.prop,
                        value: itemC?.formatter ? itemC?.formatter(itemC, item) : item[itemC.prop],
                      };
                    }
                  })
                  .flat(Infinity)
                  ?.filter((itemV: any) => !!itemV?.value),
              }));
            })
            .flat(Infinity),
        };
        await submitQuote(params);
        emit('refresh');
        ElMessage.success('提交成功');
        showVisible.value = false;
      } catch (error) {
        // ElMessage.error('提交失败');
      } finally {
        isLoading.value = false;
      }
    }
  });
}

function handleReset() {
  queryForm.value = initQueryForm();
  selectedRows.value = [];
}

function throwErrorHandler(message: string = '请先选择供应商数据') {
  if (!form.value.tenantSupplierId && props.type === T_TYPE.PURCHASER) {
    ElMessage.warning(message);
    throw new Error(message);
  }
  return;
}

function searchHandler() {
  throwErrorHandler();
  getMaterialList();
}

function reset() {
  handleReset();
  throwErrorHandler();
  getMaterialList();
}

async function getMaterialList() {
  loading.value = true;
  try {
    const res = await queryMaterialList({
      noticeId: noticeId.value,
      sectionId: props.row.sectionId,
      tenantSupplierId: form.value?.tenantSupplierId || null,
      ...queryForm.value,
    });
    await getDetail(res.data || []);
    form.value.quotationList = dynamicTableData.value;
  } catch (error) {
    form.value.quotationList = [];
  }

  setTimeout(() => {
    loading.value = false;
  }, 150);
}

onMounted(() => {});
</script>

<style scoped lang="scss">
@import '../../../styles/collapse-panel.scss';

.editable-table {
  margin-bottom: 20px;
  :deep(.el-table__cell) {
    position: relative;
  }
}
</style>
