# DynamicTableColumn 组件使用说明

## 概述

`DynamicTableColumn` 是一个支持嵌套列结构的动态表格列组件，可以根据配置动态渲染不同类型的表单控件。

## 功能特性

- ✅ 支持多种表单控件类型（文本输入、数字输入、下拉选择、附件上传等）
- ✅ 支持嵌套列结构（children）
- ✅ 支持字段验证和错误显示
- ✅ 支持只读字段和特殊字段处理
- ✅ TypeScript 类型安全

## 基本用法

```vue
<template>
  <el-table :data="tableData">
    <el-table-column type="selection" width="55" />
    <DynamicTableColumn
      :merged-form-columns="columns"
      :get-field-error="getFieldError"
      :validate-field="validateField"
      :get-enum-options="getEnumOptions"
      :change-usage-location="changeUsageLocation"
      :get-quality-indicator="getQualityIndicator"
      @validate-field="handleValidateField"
      @change-usage-location="handleChangeUsageLocation"
      @get-quality-indicator="handleGetQualityIndicator"
    />
  </el-table>
</template>
```

## 列配置示例

### 普通列配置

```typescript
const columns = [
  {
    prop: 'materialName',
    label: '物料名称',
    fieldType: 'TEXT',
    width: 200,
    isRequired: true,
    attrs: {
      placeholder: '请输入物料名称',
      disabled: false
    }
  },
  {
    prop: 'price',
    label: '单价',
    fieldType: 'NUM',
    width: 150,
    isRequired: true
  },
  {
    prop: 'category',
    label: '分类',
    fieldType: 'ENUM',
    width: 120
  }
]
```

### 嵌套列配置（children）

```typescript
const nestedColumns = [
  {
    label: '基本信息',
    children: [
      {
        prop: 'materialName',
        label: '物料名称',
        fieldType: 'TEXT',
        width: 200,
        isRequired: true
      },
      {
        prop: 'materialCode',
        label: '物料编码',
        fieldType: 'TEXT',
        width: 150
      }
    ]
  },
  {
    label: '价格信息',
    children: [
      {
        prop: 'unitPrice',
        label: '单价',
        fieldType: 'NUM',
        width: 120,
        isRequired: true
      },
      {
        prop: 'totalPrice',
        label: '总价',
        fieldType: 'NUM_CALC',
        width: 120,
        formatter: (column, row) => {
          return (row.unitPrice * row.quantity).toFixed(2)
        }
      }
    ]
  },
  {
    label: '其他',
    children: [
      {
        prop: 'remark',
        label: '备注',
        fieldType: 'TEXT',
        width: 200
      }
    ]
  }
]
```

## 支持的字段类型

| 字段类型 | 说明 | 渲染组件 |
|---------|------|----------|
| `TEXT` | 文本输入 | `el-input` |
| `NUM` | 数字输入 | `el-input-number` |
| `NUM_CALC` | 计算字段（只读） | `span` |
| `ENUM` | 枚举选择 | `el-select` |
| `LINK_FORM` | 关联表单选择 | `el-select` |
| `ATTACH` | 附件上传 | `FileList` |

## 特殊字段处理

### 需求牧场
```typescript
{
  prop: 'usageLocation',
  label: '需求牧场',
  fieldType: 'ENUM',
  // 会触发 changeUsageLocation 方法
}
```

### 质量标准
```typescript
{
  prop: 'qualityStandard',
  label: '质量标准',
  fieldType: 'ENUM',
  // 点击时会触发 getQualityIndicator 方法
}
```

## Props 接口

```typescript
interface Props {
  mergedFormColumns: Column[];
  getFieldError: (rowIndex: number, fieldProp: string) => string;
  validateField: (rowIndex: number, fieldProp: string, column: Column) => boolean;
  getEnumOptions: (column: Column) => ColumnOption[];
  changeUsageLocation: (value: any, row: any, column: Column) => void;
  getQualityIndicator: (row: any) => void;
}
```

## Events

- `@validate-field`: 字段验证时触发
- `@change-usage-location`: 需求牧场变更时触发
- `@get-quality-indicator`: 获取质量指标时触发

## 嵌套结构优势

1. **分组展示**: 可以将相关字段分组显示，提高表格的可读性
2. **递归渲染**: 自动处理任意层级的嵌套结构
3. **灵活配置**: 支持混合使用普通列和嵌套列
4. **功能完整**: 嵌套列中的所有表单控件功能完全保留

## 注意事项

1. 当列配置包含 `children` 且 `children.length > 0` 时，会递归渲染子列
2. 只有叶子节点（没有children的列）才会渲染具体的表单控件
3. 嵌套列的事件会正确传递到父组件
4. 支持任意层级的嵌套，但建议不超过3层以保持界面清晰