<template>
  <div class="document-review-container">
    <div class="flex-1 need-hide-table-card editable-table">
      <yun-pro-table
        ref="proTableRef"
        v-model:pagination="pagination"
        v-model:filter-data="filterTableData"
        v-model:searchData="searchData"
        :table-columns="columns"
        :search-fields="searchFields"
        :auto-height="true"
        :remote-method="remoteMethod"
        :table-props="tablePropsObj"
        :search-props="searchPropsObj"
        layout="whole"
        :default-fetch="false"
      >
        <!-- <template #t_tableHeaderLeft></template>
        <template #t_tableHeaderRight> </template> -->
        <template #t_action="{ row }">
          <el-button
            type="text"
            @click="handleQuote(row)"
            >报价</el-button
          >
          <el-button
            v-if="row.quoteStatus === 'COMPLETED'"
            type="text"
            @click="handleQuoteDetail(row)"
          >
            报价明细
          </el-button>
        </template>
      </yun-pro-table>
    </div>
    <!-- 供应商报价抽屉 -->
    <QuotationDrawer
      v-model:visible="quotationDrawerVisible"
      @refresh="reLoad"
      :row="currentRow"
    />

    <!-- 报价详情抽屉 -->
    <QuotationDetail
      v-model:visible="detailDrawerVisible"
      :row="currentRow"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import QuotationDrawer from './QuotationDrawer.vue';
import QuotationDetail from '@/views/procurementSourcing/biddingProcess/components/BidEvaluation/online/components/QuotationDetail/index.vue';
// import QuotationDetailDrawer from '../QuotationManagement/QuotationDetailDrawer.vue';
import { useProTable } from '@ylz-use/core';
import { useColumns } from './hooks/useColumns';
import { getSelectionRegisterInfo } from '@/api/purchasing/bid';
import { jsonStringToObject } from '@/views/procurementSourcing/biddingProcess/utils/bid';
import { useBiddingStore } from '@/views/procurementSourcing/biddingProcess/stores';

const biddingStore = useBiddingStore();
const noticeId = computed(() => biddingStore?.noticeId);

const currentRow = ref<any>(null);

const pagination = ref({});
const { columns, searchFields, searchData } = useColumns();

const { remoteMethod, tableProps, proTableRef, filterTableData, reLoad } = useProTable({
  apiFn: getSelectionRegisterInfo,
  responseHandler(result: any) {
    const res = result.data || [];
    res?.forEach((item: any) => {
      const depositRespContent = item?.depositRespContent || '{}';
      const depositContent = item?.depositContent || '{}';
      item.depositRespContent = jsonStringToObject(depositRespContent);
      item.depositContent = jsonStringToObject(depositContent);
    });
    return res;
  },
  customTotalHandler(data: any) {
    return data?.data?.total;
  },
  paramsHandler(params: any) {
    const param = {
      noticeId: noticeId.value,
      ...params,
    };
    return param;
  },
  // querysHandler(querys) {
  //   const querysData = {
  //     ...querys,
  //     current: pagination.value.page,
  //     size: pagination.value.size || pagination.value.pageSize,
  //   };
  //   return querysData;
  // },
  plugins: {
    config: {
      columns: columns.value,
      searchFields: searchFields.value,
    },
    list: ['SEARCH_PLUS'],
  },
});

const searchPropsObj = computed(() => ({
  showCollapseBtn: false,
  collapse: false,
  showResetBtn: false,
  showSubmitBtn: false,
  showOperation: false,
  quantity: 3,
}));

const tablePropsObj = computed(() => ({
  ...tableProps.value,
  stripe: false,
  border: false,
  headerCellStyle: {
    backgroundColor: '#f5f7fa',
    color: '#303133',
    fontWeight: 'bold',
    // height: '40px',
    borderColor: '#EBEEF5',
    'vertical-align': 'middle',
  },
  cellStyle: {
    padding: '0',
    // height: '40px',
    'vertical-align': 'middle',
    borderColor: '#EBEEF5',
  },
  rowStyle: {
    // height: '40px',
    borderColor: '#EBEEF5',
  },
  // rowHeight: 40,
}));

const quotationDrawerVisible = ref(false);
// 详情抽屉控制
const detailDrawerVisible = ref(false);

function handleQuote(row: any) {
  currentRow.value = row;
  quotationDrawerVisible.value = true;
}

function handleQuoteDetail(row: any) {
  currentRow.value = row;
  detailDrawerVisible.value = true;
}
onMounted(async () => {
  reLoad();
});
</script>

<style scoped lang="scss">
.document-review-container {
  flex: 1;
  box-sizing: border-box;
  height: 100%;
  display: flex;
  flex-direction: column;
  position: relative;
  :deep(.el-card__body .dm-filter-root) {
    margin: 0 !important;
  }
}
</style>
