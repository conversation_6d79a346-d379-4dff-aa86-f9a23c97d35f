import { ref, computed } from 'vue';
import { useBiddingStore } from '@/views/procurementSourcing/biddingProcess/stores';
// import { isEmptyValue } from '@/views/procurementSourcing/biddingProcess/utils/bid';
export function useColumns() {
  const biddingStore = useBiddingStore();
  // const isZJWT = computed(() => biddingStore?.isZJWT);
  const isShowSection = computed(() => biddingStore?.isShowSection);

  const searchFields = computed(() => {
    const commonFields = [
      {
        label: '标段名称',
        prop: 'selectionName',
        type: 'input',
      },
    ];
    return isShowSection.value ? commonFields : [];
  });
  const searchData = ref({});
  const columns = computed(() => {
    const commonColumns = [
      {
        label: '序号',
        type: 'index',
        width: '80px',
      },
      {
        label: '项目名称',
        prop: 'projectName',
      },
      ...(isShowSection.value
        ? [
            {
              label: '标段名称',
              prop: 'selectionName',
            },
          ]
        : []),
      {
        label: '报价轮次',
        prop: 'currentQuoteRound',
      },
      {
        label: '报价状态',
        prop: 'quoteStatus',
        render: ({ row }: any) => {
          const quoteStatus = row?.quoteStatus;
          // @ts-ignore
          const tagName = quoteStatus === 'COMPLETED' ? '已报价' : '待报价';
          // @ts-ignore
          const tagType = quoteStatus === 'COMPLETED' ? 'success' : 'warning';
          // @ts-ignore
          return <el-tag type={tagType}>{tagName}</el-tag>;
        },
      },
      {
        label: '报价时间',
        prop: 'quoteTime',
        width: '170px',
      },
      {
        label: '报价IP',
        prop: 'quoteIp',
      },
      {
        label: '操作',
        prop: 'action',
        width: '140px',
        fixed: 'right',
      },
    ];
    return commonColumns;
  });

  return {
    columns,
    searchFields,
    searchData,
  };
}
