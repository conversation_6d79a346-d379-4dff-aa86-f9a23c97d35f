<template>
  <!-- 只有当column.prop存在时才渲染列 -->
  <el-table-column
    :key="column.prop"
    :label="column.label"
    :prop="column.prop"
    :min-width="column.width"
    :render-header="column?.renderHeader || null"
    :show-overflow-tooltip="column.showOverflowTooltip"
  >
    <!-- 如果没有子列，渲染具体内容 -->
    <template #default="{ row, $index }">
      <!-- 只读字段 -->
      <span v-if="DYNAMIC_RENDER_COLLECT.includes(column.prop)">
        {{ row[column.prop || ''] || '--' }}
        <!-- <span v-if="column.prop === 'usageLocationId'">
          {{ row['usageLocationName'] || '--' }}
        </span>
        <span v-else-if="column.prop === 'qualityIndicatorId'">
          {{ row['qualityIndicatorName'] || '--' }}
        </span>
        <span v-else>
          {{ row[column.prop || ''] || '--' }}
        </span> -->
      </span>
      <!-- 可编辑字段 -->
      <template v-else>
        <!-- 文本输入框 -->
        <el-input
          v-if="column.fieldType === 'TEXT'"
          v-model="row[column.prop || '']"
          :placeholder="column?.attrs?.placeholder || '请输入'"
          :disabled="column?.attrs?.disabled || false"
          :readonly="!column?.isAllowEdit"
          clearable
          :class="{ 'is-error': getFieldError($index, column.prop || '') }"
          @blur="handleValidateField($index, column.prop || '', column)"
          style="width: 100%"
        />
        <span v-else-if="column.fieldType === 'NUM_CALC'">
          {{ column.formatter ? column.formatter(column, row) : row[column.prop || ''] || '--' }}
        </span>
        <!-- 数字输入框 -->
        <el-input-number
          v-else-if="column.fieldType === 'NUM'"
          v-model="row[column.prop || '']"
          :min="0"
          :precision="2"
          controls-position="right"
          :placeholder="column?.attrs?.placeholder || '请输入'"
          :disabled="column?.attrs?.disabled || false"
          :readonly="!column?.isAllowEdit"
          :class="{ 'is-error': getFieldError($index, column.prop || '') }"
          @blur="handleValidateField($index, column.prop || '', column)"
          style="width: 100%"
        />
        <!-- 下拉选择 -->
        <el-select
          v-else-if="['LINK_FORM', 'ENUM'].includes(column.fieldType || '') && !['需求牧场', '质量标准'].includes(column.label || '')"
          v-model="row[column.prop || '']"
          clearable
          :placeholder="column?.attrs?.placeholder || '请选择'"
          :disabled="column?.attrs?.disabled || false"
          :readonly="!column?.isAllowEdit"
          :class="{ 'is-error': getFieldError($index, column.prop || '') }"
          @change="handleValidateField($index, column.prop || '', column)"
          style="width: 100%"
        >
          <el-option
            v-for="option in getEnumOptions(column)"
            :key="option.value"
            :label="option.label"
            :value="option.value"
          />
        </el-select>
        <!-- 需求牧场 下拉选择 -->
        <el-select
          v-else-if="column.label === '需求牧场'"
          v-model="row[column.prop || '']"
          clearable
          :placeholder="column?.attrs?.placeholder || '请选择'"
          :disabled="column?.attrs?.disabled || false"
          :readonly="!column?.isAllowEdit"
          :class="{ 'is-error': getFieldError($index, column.prop || '') }"
          @change="
              (value: any) => {
                handleChangeUsageLocation(value, row, column);
                handleValidateField($index, column.prop || '', column);
              }
            "
          style="width: 100%"
        >
          <el-option
            v-for="option in getEnumOptions(column)"
            :key="option.value"
            :label="option.label"
            :value="option.value"
          />
        </el-select>
        <!-- 质量标准 下拉选择 -->
        <el-select
          v-model="row[column.prop || '']"
          clearable
          v-else-if="column.label === '质量标准'"
          :placeholder="column?.attrs?.placeholder || '请选择'"
          :disabled="column?.attrs?.disabled || false"
          :readonly="!column?.isAllowEdit"
          :class="{ 'is-error': getFieldError($index, column.prop || '') }"
          @change="handleValidateField($index, column.prop || '', column)"
          style="width: 100%"
          @click.stop="handleGetQualityIndicator(row)"
        >
          <el-option
            v-for="option in row?.indicators || []"
            :key="option.value"
            :label="option.label"
            :value="option.value"
          />
        </el-select>
        <!-- 附件 -->
        <component
          :is="FileList"
          v-else-if="column.fieldType === 'ATTACH'"
          :placeholder="column?.attrs?.placeholder || '请选择'"
          :disabled="column?.attrs?.disabled || false"
          v-model="row[column.prop || '']"
          :class="{ 'is-error': getFieldError($index, column.prop || '') }"
          @update:modelValue="
              (fileList: any) => {
                row[column.prop || ''] = fileList;
                handleValidateField($index, column.prop || '', column);
              }
            "
          style="width: 100%"
        />
        <!-- 其他类型默认为输入框 -->
        <el-input
          v-else
          v-model="row[column.prop || '']"
          :placeholder="column?.attrs?.placeholder || '请选择'"
          :disabled="column?.attrs?.disabled || false"
          :readonly="!column?.isAllowEdit"
          clearable
          :class="{ 'is-error': getFieldError($index, column.prop || '') }"
          @blur="handleValidateField($index, column.prop || '', column)"
          style="width: 100%"
        />
        <!-- 错误信息显示 -->
        <div
          v-if="getFieldError($index, column.prop || '')"
          style="font-size: 12px; color: #f56c6c; line-height: 1; padding-top: 2px"
        >
          {{ getFieldError($index, column.prop || '') }}
        </div>
      </template>
    </template>
  </el-table-column>
</template>

<script setup lang="ts">
import { defineAsyncComponent } from 'vue';
import { DYNAMIC_RENDER_COLLECT } from '@/views/procurementSourcing/biddingProcess/constants/bid';

// 定义接口
interface ColumnOption {
  value: any;
  label: string;
}

interface Column {
  prop?: string;
  label?: string;
  width?: number;
  fieldType?: string;
  isRequired?: boolean;
  isAllowEdit?: boolean;
  fieldLength?: number;
  renderHeader?: any;
  showOverflowTooltip?: boolean;
  formatter?: (column: Column, row: any) => any;
  children?: Column[];
  attrs?: {
    placeholder?: string;
    disabled?: boolean;
  };
}

// 定义Props - 修改为接收单个column对象
interface Props {
  column: Column;
  getFieldError: (rowIndex: number, fieldProp: string) => string;
  validateField: (rowIndex: number, fieldProp: string, column: Column) => boolean;
  getEnumOptions: (column: Column) => ColumnOption[];
  changeUsageLocation: (value: any, row: any, column: Column) => void;
  getQualityIndicator: (row: any) => void;
}

const props = defineProps<Props>();

// 定义事件
const emit = defineEmits<{
  validateField: [rowIndex: number, fieldProp: string, column: Column];
  changeUsageLocation: [value: any, row: any, column: Column];
  getQualityIndicator: [row: any];
}>();

// 动态导入FileList组件
// @ts-ignore
const FileList = defineAsyncComponent(() => import('@/views/purchasing/plan/detail/components/useFileList.jsx'));

// 处理验证字段
function handleValidateField(rowIndex: number, fieldProp: string, column: Column) {
  const isValid = props.validateField(rowIndex, fieldProp, column);
  emit('validateField', rowIndex, fieldProp, column);
  return isValid;
}

// 处理使用位置变更
function handleChangeUsageLocation(value: any, row: any, column: Column) {
  // props.changeUsageLocation(value, row, column);
  emit('changeUsageLocation', value, row, column);
}

// 处理获取质量指标
function handleGetQualityIndicator(row: any) {
  // props.getQualityIndicator(row);
  emit('getQualityIndicator', row);
}
</script>

<style scoped lang="scss">
:deep(.el-table__cell) {
  position: relative;
}
</style>
