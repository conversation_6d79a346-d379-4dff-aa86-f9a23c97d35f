import { ref, computed } from 'vue';
import {
  // REGISTER_REVIEWED_STATUS_OPTIONS,
  DEPOSIT_REVIEWED_STATUS_AUDIT_OPTIONS,
  // DEPOSIT_REVIEWED_STATUS,
  DEPOSIT_REVIEWED_STATUS_OPTIONS,
  DEPOSIT_REVIEWED_STATUS_TAGS,
  DEPOSIT_REVIEWED_STATUS_LABELS,
  DEPOSIT_REVIEWED_STATUS_DONE_OPTIONS,
} from '@/views/procurementSourcing/biddingProcess/constants/bid';
import FileRenderer from '/@/components/Detail/renderers/FileRenderer.vue';
import { useBiddingStore } from '@/views/procurementSourcing/biddingProcess/stores';
// import { isEmptyValue } from '@/views/procurementSourcing/biddingProcess/utils/bid';
export function useColumns() {
  const biddingStore = useBiddingStore();
  const isZJWT = computed(() => biddingStore?.isZJWT);

  const searchFields = computed(() => {
    const commonFields = [
      ...(!isZJWT.value
        ? [
            {
              label: '供应商名称',
              prop: 'supplierName',
              type: 'input',
              componentAttrs: {
                // filterable: true,
                // multiple: true,
                clearable: true,
              },
            },
          ]
        : []),
      {
        label: '供应商保证金状态',
        prop: 'depositStatus',
        type: 'select',
        componentAttrs: {
          // filterable: true,
          // multiple: true,
          clearable: true,
        },
        options: DEPOSIT_REVIEWED_STATUS_OPTIONS,
      },
      {
        label: '审核状态',
        prop: 'depositStatusAudit',
        type: 'select',
        componentAttrs: {
          // filterable: true,
          // multiple: true,
          clearable: true,
        },
        options: DEPOSIT_REVIEWED_STATUS_AUDIT_OPTIONS,
      },
    ];
    return commonFields;
  });
  const searchData = ref({});
  const columns = computed(() => {
    const commonColumns = [
      // {
      //   label: '序号',
      //   type: 'index',
      //   width: '60px',
      // },
      {
        label: '供应商名称',
        prop: 'supplierName',
      },
      {
        label: '联系人',
        prop: 'contactPerson',
      },
      {
        label: '联系方式',
        prop: 'contactPhone',
      },
      {
        label: '供应商缴纳状态',
        prop: 'depositStatus1',
        render: ({ row }: any) => {
          const depositStatus = row?.depositStatus;
          // @ts-ignore
          const tagName = DEPOSIT_REVIEWED_STATUS_DONE_OPTIONS.includes(depositStatus) ? '已上传' : '未上传';
          // @ts-ignore
          const tagType = DEPOSIT_REVIEWED_STATUS_DONE_OPTIONS.includes(depositStatus) ? 'success' : 'danger';
          // @ts-ignore
          return <el-tag type={tagType}>{tagName}</el-tag>;
        },
      },
      {
        label: '附件上传时间',
        prop: 'depositTime',
        width: '170px',
      },
      {
        label: '附件',
        prop: 'depositRespContent',
        width: '170px',
        render: ({ row }: any) => {
          const depositRespContent = row?.depositRespContent || [];
          if (Array.isArray(depositRespContent) && depositRespContent.length > 0) {
            return <FileRenderer value={depositRespContent} />;
          }
          return '-';
        },
      },
      {
        label: '保证金审核状态',
        prop: 'depositStatus',
        render: ({ row }: any) => {
          const depositStatus = row?.depositStatus;
          // @ts-ignore
          const tagName = DEPOSIT_REVIEWED_STATUS_LABELS[depositStatus];
          // @ts-ignore
          const tagType = DEPOSIT_REVIEWED_STATUS_TAGS[depositStatus];
          // @ts-ignore
          return tagName ? <el-tag type={tagType}>{tagName}</el-tag> : '-';
        },
      },
      {
        label: '保证金审核',
        prop: 'quoteAudit',
        fixed: 'right',
        width: '180px',
      },
    ];
    return commonColumns;
  });

  return {
    columns,
    searchFields,
    searchData,
  };
}
