<template>
  <div class="document-review-container">
    <StageTabs
      v-model="activeTabIndex"
      @update:sectionId="handleSectionChange"
      :source="EXPORT_MODULE_TYPE.DEPOSIT"
    />
    <div class="flex-1 need-hide-table-card editable-table">
      <yun-pro-table
        ref="proTableRef"
        v-model:pagination="pagination"
        v-model:filter-data="filterTableData"
        v-model:searchData="searchData"
        :table-columns="columns"
        :search-fields="searchFields"
        :auto-height="true"
        :remote-method="remoteMethod"
        :table-props="tablePropsObj"
        :search-props="searchPropsObj"
        layout="whole"
        :default-fetch="false"
      >
        <!-- <template #t_tableHeaderLeft></template>
        <template #t_tableHeaderRight> </template> -->
        <template #t_quoteAudit="{ row }">
          <el-space v-if="hasAuth">
            <el-link
              type="primary"
              v-if="row.depositStatus === DEPOSIT_REVIEWED_STATUS.PAID"
              @click="handleAudit({ sectionId: row.sectionId, tenantSupplierId: row?.tenantSupplierId || null, pass: true })"
            >
              审核通过
            </el-link>
            <el-link
              type="danger"
              v-if="row.depositStatus === DEPOSIT_REVIEWED_STATUS.PAID"
              @click="handleAudit({ sectionId: row.sectionId, tenantSupplierId: row?.tenantSupplierId || null, pass: false })"
            >
              审核不通过
            </el-link>
          </el-space>
        </template>
      </yun-pro-table>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onBeforeMount } from 'vue';
import StageTabs from '../StageTabs.vue';
import {
  DEPOSIT_REVIEWED_STATUS,
  EXPORT_MODULE_TYPE,
  DEPOSIT_REVIEWED_STATUS_DONE_OPTIONS,
  DEPOSIT_REVIEWED_STATUS_TODO_OPTIONS,
} from '@/views/procurementSourcing/biddingProcess/constants/bid';
import { review } from '@/api/purchasing/bid';
import { ElMessage, ElMessageBox } from 'yun-design';
import { useProTable } from '@ylz-use/core';
import { useColumns } from './hooks/useColumns';
import { useBiddingStore } from '@/views/procurementSourcing/biddingProcess/stores';
import { isProjectMember } from '@/views/procurementSourcing/biddingProcess/utils/bid';
import { getRegisteredSupplierList } from '@/api/purchasing/bid';
import { jsonStringToObject } from '@/views/procurementSourcing/biddingProcess/utils/bid';

const pagination = ref({});
const { columns, searchFields, searchData } = useColumns();

const { remoteMethod, tableProps, proTableRef, filterTableData, reLoad } = useProTable({
  apiFn: getRegisteredSupplierList,
  responseHandler(result: any) {
    const res = result.data || [];
    res?.forEach((item: any) => {
      const depositRespContent = item?.depositRespContent || '{}';
      item.depositRespContent = jsonStringToObject(depositRespContent);
    });
    return res;
  },
  customTotalHandler(data: any) {
    return data?.data?.total;
  },
  paramsHandler(params: any) {
    const { depositStatus, depositStatusAudit, ...paramRest } = params;
    const param = {
      sectionId: currentSectionId.value,
      noticeId: noticeId.value,
      ...paramRest,
    };

    if (depositStatus === 'DONE') {
      param.depositStatusList = DEPOSIT_REVIEWED_STATUS_DONE_OPTIONS;
    }
    if (depositStatus === 'TODO') {
      param.depositStatusList = DEPOSIT_REVIEWED_STATUS_TODO_OPTIONS;
    }

    if (depositStatusAudit) {
      if (param?.depositStatusList && depositStatusAudit?.length) {
        param.depositStatusList = [...param.depositStatusList, depositStatusAudit];
      } else {
        param.depositStatusList = [depositStatusAudit];
      }
    }

    return param;
  },
  // querysHandler(querys) {
  //   const querysData = {
  //     ...querys,
  //     current: pagination.value.page,
  //     size: pagination.value.size || pagination.value.pageSize,
  //   };
  //   return querysData;
  // },
  plugins: {
    config: {
      columns: columns.value,
      searchFields: searchFields.value,
    },
    list: ['SEARCH_PLUS'],
  },
});

const searchPropsObj = computed(() => ({
  showCollapseBtn: false,
  collapse: false,
  showResetBtn: false,
  showSubmitBtn: false,
  showOperation: false,
  quantity: 3,
}));

const tablePropsObj = computed(() => ({
  ...tableProps.value,
  stripe: false,
  border: false,
  headerCellStyle: {
    backgroundColor: '#f5f7fa',
    color: '#303133',
    fontWeight: 'bold',
    // height: '40px',
    borderColor: '#EBEEF5',
    'vertical-align': 'middle',
  },
  cellStyle: {
    padding: '0',
    // height: '40px',
    'vertical-align': 'middle',
    borderColor: '#EBEEF5',
  },
  rowStyle: {
    // height: '40px',
    borderColor: '#EBEEF5',
  },
  // rowHeight: 40,
}));

const biddingStore = useBiddingStore();
const noticeId = computed(() => biddingStore?.noticeId);
const isZJWT = computed(() => biddingStore?.isZJWT);

const currentSectionId = ref('');
const isLoading = ref(false);

const activeTabIndex = ref(0);

const hasAuth = computed(() => {
  return isProjectMember(biddingStore.projectDetail?.projectMemberList) === 'PROJECT_LEADER';
});
function handleAudit(row = {}) {
  ElMessageBox.confirm('确定执行该操作？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  })
    .then(async () => {
      isLoading.value = true;
      try {
        await review({
          noticeId: noticeId.value,
          type: 2,
          ...row,
        });
        ElMessage.success('操作成功');
        reLoad();
      } catch (error) {
        ElMessage.error('操作失败');
      } finally {
        isLoading.value = false;
      }
    })
    .catch(() => {});
}

// 标段变化事件
async function handleSectionChange(sectionId: string) {
  currentSectionId.value = sectionId;
  setTimeout(async () => {
    if (currentSectionId.value) {
      reLoad();
    }
  }, 300);
}
onMounted(async () => {});
onBeforeMount(async () => {});
</script>

<style scoped lang="scss">
.document-review-container {
  flex: 1;
  padding: 20px;
  box-sizing: border-box;
  height: 100%;
  display: flex;
  flex-direction: column;
  position: relative;
  .invite-supplier-btn {
    position: absolute;
    top: 16px;
    right: 20px;
  }
}
</style>
