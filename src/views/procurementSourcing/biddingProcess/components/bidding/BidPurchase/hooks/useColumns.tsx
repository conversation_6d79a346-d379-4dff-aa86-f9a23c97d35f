import { ref, computed } from 'vue';
import FileRenderer from '/@/components/Detail/renderers/FileRenderer.vue';
import { BID_FEE_STATUS_OPTIONS, BID_FILE_DOWNLOAD_STATUS, BID_FEE_STATUS_LABELS, BID_FEE_STATUS_TAGS } from '../const';
import { useBiddingStore } from '@/views/procurementSourcing/biddingProcess/stores';

export function useColumns() {
  const biddingStore = useBiddingStore();
  const isShowSection = computed(() => biddingStore?.isShowSection);

  const searchFields = ref([]);
  const searchData = ref({});
  const columns = computed(() => {
    return [
      {
        label: '序号',
        type: 'index',
        width: '60px',
      },
      {
        prop: 'projectName',
        label: '项目名称',
      },
      ...(isShowSection.value
        ? [
            {
              prop: 'selectionName',
              label: '标段名称',
            },
          ]
        : []),
      {
        prop: 'requirementContent.bidFee',
        label: '标书费金额(元)',
      },
      {
        prop: 'requirementContent.bank',
        label: '汇款银行',
      },
      {
        prop: 'requirementContent.bankAccount',
        label: '汇款银行帐户',
      },
      {
        label: '标书费附件',
        prop: 'tenderFeeRespContent?.url',
        render: ({ row }: any) => {
          return row?.tenderFeeRespContent?.url ? <FileRenderer value={[row?.tenderFeeRespContent]} /> : '--';
        },
      },
      {
        label: '附件上传人',
        prop: 'tenderFeeRespContent.uploadUserName',
      },
      {
        label: '附件上传时间',
        prop: 'tenderFeeRespContent.uploadTime',
        width: '180px',
      },
      {
        prop: 'bidDocPayEndTime',
        label: '标书费缴纳截止时间',
        width: '180px',
      },
      {
        prop: 'tenderFeeStatus',
        label: '标书费状态',
        enums: BID_FEE_STATUS_OPTIONS,
        render: ({ row }: any) => {
          const tenderFeeStatus = row?.tenderFeeStatus;
          // @ts-ignore
          const tagName = BID_FEE_STATUS_LABELS[tenderFeeStatus];
          // @ts-ignore
          const tagType = BID_FEE_STATUS_TAGS[tenderFeeStatus];
          // @ts-ignore
          return tagName ? <el-tag type={tagType}>{tagName}</el-tag> : '-';
        },
      },
      {
        prop: 'fileObtainEndTime',
        label: '文件获取截止时间',
        width: '180px',
      },
      {
        prop: 'bidFileDownloadStatus',
        label: '标书文件下载状态',
        // enums: BID_FILE_DOWNLOAD_OPTIONS,
        render: ({ row }: any) => {
          const bidFileDownloadStatus = row?.bidFileDownloadStatus;
          // @ts-ignore
          const tagName = bidFileDownloadStatus === BID_FILE_DOWNLOAD_STATUS.DOWNLOADED ? '已下载' : '未下载';
          // @ts-ignore
          const tagType = bidFileDownloadStatus === BID_FILE_DOWNLOAD_STATUS.DOWNLOADED ? 'success' : 'warning';
          // @ts-ignore
          return <el-tag type={tagType}>{tagName}</el-tag>;
        },
      },
      {
        label: '操作',
        prop: 'action',
        width: '240px',
        fixed: 'right',
      },
    ];
  });

  return {
    columns,
    searchFields,
    searchData,
  };
}
