// 对象转数组
function objToArr(obj: any) {
  return Object.entries(obj).map(([value, label]) => ({
    label,
    value,
  }));
}

// 导出标书费状态枚举
export const BID_FEE_STATUS = {
  WITHDRAWN: 'WITHDRAWN',
  UNPAID: 'UNPAID',
  PAID: 'PAID',
  VERIFIED: 'VERIFIED',
  REJECTED: 'REJECTED',
} as const;

// 导出标书费状态
// export const BID_FEE_STATUS_LABELS = {
//   [BID_FEE_STATUS.VERIFIED]: '审批通过',
//   [BID_FEE_STATUS.REJECTED]: '审批不通过',
//   [BID_FEE_STATUS.PAID]: '审核中',
//   [BID_FEE_STATUS.WITHDRAWN]: '已撤销',
//   [BID_FEE_STATUS.UNPAID]: '未缴纳',
// } as const;


// 导出标书费审核状态
export const BID_FEE_STATUS_LABELS = {
  [BID_FEE_STATUS.VERIFIED]: '审批通过',
  [BID_FEE_STATUS.REJECTED]: '审批不通过',
  [BID_FEE_STATUS.PAID]: '待审批',
} as const;
// 导出标书费审核状态标签样式
export const BID_FEE_STATUS_TAGS = {
  [BID_FEE_STATUS.VERIFIED]: 'success',
  [BID_FEE_STATUS.REJECTED]: 'danger',
  [BID_FEE_STATUS.PAID]: 'warning',
} as const;

// 导出标书费状态枚举
export const BID_FEE_STATUS_OPTIONS = objToArr(BID_FEE_STATUS_LABELS);


// 标书文件下载状态
export const BID_FILE_DOWNLOAD_STATUS = {
  DOWNLOADED: 'DOWNLOADED',
  UN_DOWNLOAD: 'UN_DOWNLOAD',
} as const;

// 标书文件下载状态
export const BID_FILE_DOWNLOAD_LABELS = {
  [BID_FILE_DOWNLOAD_STATUS.DOWNLOADED]: '已下载',
  [BID_FILE_DOWNLOAD_STATUS.UN_DOWNLOAD]: '未下载',
} as const;

// 标书文件下载状态枚举
export const BID_FILE_DOWNLOAD_OPTIONS = objToArr(BID_FILE_DOWNLOAD_LABELS);