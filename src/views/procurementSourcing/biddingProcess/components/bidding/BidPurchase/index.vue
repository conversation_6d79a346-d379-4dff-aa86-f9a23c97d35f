<template>
  <div class="document-review-container need-hide-table-card editable-table">
    <div class="batch-upload-container">
      <AYNUpload
        :limit="1"
        :disabled="!allUploadDisabled"
        @change="(fileList: any) => uploadDepositAttachment('ALL', fileList, null)"
        displayType="outer"
        buttonText="一键上传标书费附件"
      >
      </AYNUpload>
    </div>
    <yun-pro-table
      ref="proTableRef"
      v-model:pagination="pagination"
      v-model:searchData="searchData"
      :table-columns="columns"
      :search-fields="searchFields"
      :auto-height="true"
      :remote-method="remoteMethod"
      :table-props="tablePropsObj"
      :search-props="searchPropsObj"
      layout="whole"
      :default-fetch="false"
    >
      <!-- <template #t_tableHeaderLeft> </template>
      <template #t_tableHeaderRight>
        <YunUpload
          :limit="1"
          :disabled="!allUploadDisabled"
          :show-file-list="false"
          @change="(v: any) => uploadDepositAttachment('ALL', v, null)"
          v-model="depositAttachment"
          buttonText="一键上传标书费附件"
        />
      </template> -->
      <template #t_action="{ row }">
        <div class="flex gap-2">
          <AYNUpload
            :limit="1"
            @change="(fileList: any) => uploadDepositAttachment('SINGLE', fileList, row)"
            displayType="inner"
            buttonText="上传标书费附件"
            v-if="commonFilterFn(row) && isOverBidDocPayEndTime"
          >
          </AYNUpload>
          <el-link
            type="primary"
            @click="handleWithdrawCall(row)"
            v-if="BID_FEE_STATUS.PAID === row?.tenderFeeStatus && isOverBidDocPayEndTime"
            >撤回审核</el-link
          >
          <el-link
            type="primary"
            v-if="showViewFile(row)"
            @click="viewBidFile(row)"
            >查看文件</el-link
          >
        </div>
      </template>
    </yun-pro-table>

    <yun-drawer
      v-model="showVisible"
      title="查看文件"
      size="X-large"
      :with-header="true"
      :append-to-body="false"
      destroy-on-close
      :show-footer="false"
      :show-confirm-button="false"
      :show-cancel-button="false"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      cancel-button-text="关闭"
      custom-class="bid-custom-drawer"
    >
      <CNDocument :isDisabled="true" :hideAnchor="true" :downloadSectionId="downloadSectionId" />
    </yun-drawer>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onBeforeMount } from 'vue';
import { getSelectionRegisterInfo } from '@/api/purchasing/bid';
import { ElMessage, ElMessageBox } from 'yun-design';
import { useProTable } from '@ylz-use/core';
import { useColumns } from './hooks/useColumns';
import { BID_FEE_STATUS, BID_FILE_DOWNLOAD_STATUS } from './const';
import { jsonStringToObject } from '@/views/procurementSourcing/biddingProcess/utils/bid';
import { payDeposit, withdrawn } from '@/api/purchasing/bid';
import { useBiddingStore } from '@/views/procurementSourcing/biddingProcess/stores';
import moment from 'moment';
import { useUserInfo } from '/@/stores/userInfo';
import AYNUpload from '/@/components/AYNUpload/index.vue';
// import { downloadedBidFileDownloadStatus } from '@/api/purchasing/evaluation';
import CNDocument from '@/views/procurementSourcing/biddingProcess/components/announcement/CNDocument/index.vue';

const showVisible = ref(false);
const downloadSectionId = ref('');

const stores = useUserInfo();
const name = computed(() => stores?.userInfos?.user?.name);

const tableData = ref([]);
// 当前时间已经超过标书费缴纳截止时间 bidDocPayEndTime
const isOverBidDocPayEndTime = computed(() => {
  const now = moment().format('YYYY-MM-DD HH:mm:ss');
  return biddingStore?.noticeInfo?.bidDocPayEndTime ? moment(biddingStore?.noticeInfo?.bidDocPayEndTime).isAfter(now) : false;
});
// const needBondSectionList = computed(() => biddingStore?.needBondSectionList);
// const filterTableData = computed(() => tableData.value.filter((item: any) => needBondSectionList.value?.includes(item?.sectionId)));
const commonFilterFn = (item: any) =>
  [BID_FEE_STATUS.WITHDRAWN, BID_FEE_STATUS.REJECTED, BID_FEE_STATUS.UNPAID].includes(item?.tenderFeeStatus) || !item?.tenderFeeStatus;
const allUploadDisabled = computed(() => tableData.value.some(commonFilterFn) && isOverBidDocPayEndTime.value);

const biddingStore = useBiddingStore();
const noticeId = computed(() => biddingStore?.noticeId);
const tenderFeeList = computed(() => {
  const res = biddingStore?.noticeInfo?.requirementList || [];
  const temp = res?.filter((item: any) => item?.requirementType === 'BID_DOCUMENT' && item?.noticeId === noticeId.value);
  // eslint-disable-next-line no-console
  console.log('tenderFeeList', temp);
  return temp?.map((item: any) => {
    const requirementContent = item?.requirementContent || '{}';
    return {
      ...item,
      requirementContent: jsonStringToObject(requirementContent),
    };
  });
});

const loading = ref(false);

const pagination = ref({});
const { columns, searchFields, searchData } = useColumns();
const { remoteMethod, tableProps, proTableRef, reLoad } = useProTable({
  apiFn: getSelectionRegisterInfo,
  responseHandler(result: any) {
    const res = result.data || [];
    res?.forEach((item: any) => {
      const content = tenderFeeList.value?.find((itemC: any) => itemC?.sectionId === item?.sectionId);
      item.requirement = content || {};
      item.requirementContent = content?.requirementContent || {};
      item.tenderFeeRespContent = jsonStringToObject(item?.tenderFeeRespContent || '[]')?.[0] || {};
      // 标书费一些时间
      item.bidDocPayEndTime = biddingStore?.noticeInfo?.bidDocPayEndTime || '';
      item.bidDocPayStartTime = biddingStore?.noticeInfo?.bidDocPayStartTime || '';
      item.fileObtainEndTime = biddingStore?.noticeInfo?.fileObtainEndTime || '';
      item.fileObtainStartTime = biddingStore?.noticeInfo?.fileObtainStartTime || '';
    });
    tableData.value = res;
    return res;
  },
  customTotalHandler(data: any) {
    return data?.data?.total;
  },
  paramsHandler(params: any) {
    return {
      // sectionId: currentSectionId.value,
      noticeId: noticeId.value,
      ...params,
    };
  },
  // querysHandler() {
  //   const querysData = {
  //     current: pagination.value.page,
  //     size: pagination.value.size || pagination.value.pageSize,
  //   };
  //   return querysData;
  // },
  plugins: {
    config: {
      columns: columns.value,
      searchFields: searchFields.value,
    },
    list: ['SEARCH_PLUS'],
  },
});

// 标书费审核通过并且达到文件获取开始时间，可以查看文件并下载标书
// 截止时间达到后若一次未获取则不能再查看并提示：已超过文件获取时间，在文件获取时间内已查看的可重复查看
function showViewFile(row: any) {
  const {
    tenderFeeStatus,
    // bidDocPayEndTime,
    // bidDocPayStartTime,
    fileObtainEndTime,
    fileObtainStartTime,
    bidFileDownloadStatus,
  } = row;
  const bool = BID_FEE_STATUS.VERIFIED === tenderFeeStatus;
  const now = moment().format('YYYY-MM-DD HH:mm:ss');
  if (bool) {
    const isInTime = moment(fileObtainStartTime).isBefore(now) && moment(fileObtainEndTime).isAfter(now);
    if (isInTime) {
      return true;
    }
    const isInFileObtainTime = moment(fileObtainEndTime).isBefore(now);
    if (isInFileObtainTime) {
      if (bidFileDownloadStatus === BID_FILE_DOWNLOAD_STATUS.DOWNLOADED) {
        return true;
      }
    }
    return false;
  }
  return false;
}

function viewBidFile(row: any) {
  downloadSectionId.value = row.sectionId;
  showVisible.value = true;
  // const attachmentList = biddingStore?.noticeInfo?.attachmentList || [];
  // const bidFile = attachmentList?.find((item: any) => item?.businessType === 'BID_DOC_ATTACHMENT' && item?.businessGroup === row.sectionId);
  // const { fileName, filePath } = bidFile || {};
  // if (fileName && filePath) {
  //   window.open(filePath, '_blank');
  //   if (row?.bidFileDownloadStatus === BID_FILE_DOWNLOAD_STATUS.DOWNLOADED) {
  //     downloadedBidFileDownloadStatus(noticeId.value, row.sectionId);
  //   }
  // } else {
  //   ElMessage.error('文件不存在');
  // }
}

const searchPropsObj = computed(() => ({
  showCollapseBtn: false,
  collapse: false,
  showResetBtn: false,
  showSubmitBtn: false,
  showOperation: false,
  quantity: 3,
}));

const tablePropsObj = computed(() => ({
  ...tableProps.value,
  stripe: false,
  border: false,
  headerCellStyle: {
    backgroundColor: '#f5f7fa',
    color: '#303133',
    fontWeight: 'bold',
    height: '40px',
    borderColor: '#EBEEF5',
  },
  cellStyle: {
    padding: '0',
    height: '40px',
    'vertical-align': 'middle',
    borderColor: '#EBEEF5',
  },
  rowStyle: {
    height: '40px',
    borderColor: '#EBEEF5',
  },
  rowHeight: 40,
}));

async function uploadDepositAttachment(type: string, v: any, row: any) {
  // eslint-disable-next-line no-console
  if (v?.length && Array.isArray(v)) {
    const list = type === 'ALL' ? tableData.value.filter(commonFilterFn) : [row];
    const params = {
      noticeId: noticeId.value,
      depositResponseList: list.map((item: any) => ({
        sectionId: item.sectionId,
        bidderResponse: {
          requirementId: item?.tenderFeeRequirementId,
          // requirementId: item?.requirement?.id,
          responseContent: JSON.stringify(
            v?.map((itemFile: any) => ({
              name: itemFile.name,
              url: itemFile.url,
              uploadTime: moment().format('YYYY-MM-DD HH:mm:ss'),
              uploadUserName: name.value,
            }))
          ),
        },
      })),
    };
    try {
      loading.value = true;
      await payDeposit(params);
      ElMessage.success('上传成功');
      await biddingStore?.getEffectNoticeData();
      await reLoad();
    } catch (error) {
      // ElMessage.error('上传失败');
    } finally {
      loading.value = false;
    }
  }
}

function handleWithdrawCall(row: any) {
  ElMessageBox.confirm('确定执行该操作？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  })
    .then(async () => {
      await withdrawn({
        noticeId: noticeId.value,
        sectionId: row.sectionId,
        type: 4,
      });
      ElMessage.success('操作成功');
      reLoad();
    })
    .catch(() => {});
}

onMounted(async () => {
  reLoad();
});
onBeforeMount(async () => {});
</script>

<style scoped lang="scss">
.document-review-container {
  flex: 1;
  // overflow: hidden;
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
  // gap: 16px;
  height: 100%;
  position: relative;

  :deep(.pro_table-search) {
    display: none;
  }

  .batch-upload-container {
    position: absolute;
    top: -46px;
    right: 2px;
    text-align: right;
    // width: 100%;
    z-index: 10;
  }
}
</style>
