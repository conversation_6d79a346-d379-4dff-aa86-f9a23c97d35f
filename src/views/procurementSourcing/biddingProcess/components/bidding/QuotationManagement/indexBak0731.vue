<template>
  <div
    class="quotation-management"
    v-if="!isZJWT"
  >
    <StageTabs
      v-model="activeTabIndex"
      @update:sectionId="handleSectionChange"
    />
    <!-- 第一行：视图切换和报价信息 -->
    <div class="view-control">
      <div class="left-section">
        <el-radio-group
          v-model="viewMode"
          class="view-tabs"
          @change="handleViewModeChange"
          v-if="!isJZTP"
        >
          <el-radio-button
            v-for="option in VIEW_TYPE_OPTIONS"
            :key="option.value"
            :label="option.value"
          >
            {{ option.label }}
          </el-radio-button>
        </el-radio-group>
        <el-button
          @click="handleInsertQuotation"
          v-if="viewMode === VIEW_TYPE.MATERIAL && hasAuth"
          type="text"
          :icon="Plus"
        >
          插入供应商报价
        </el-button>
      </div>
      <div class="right-section">
        已发起 <span class="highlight">{{ hasQuotation }}</span> 轮报价
      </div>
    </div>

    <!-- 第二行：查询条件和导出按钮 -->
    <div class="document-review-content">
      <el-form
        :model="queryForm"
        inline
        class="bidding-process-search-form"
      >
        <div class="form-content">
          <div class="form-item-wrapper">
            <label class="form-label">选择报价轮次</label>
            <el-select
              v-model="queryForm.roundNo"
              placeholder="请选择"
              class="search-select"
              @change="loadData"
            >
              <el-option
                v-for="item in quotationRounds"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </div>
          <div
            class="form-item-wrapper"
            v-if="viewMode === VIEW_TYPE.MATERIAL"
          >
            <label class="form-label">物料名称</label>
            <el-input
              v-model="queryForm.materialName"
              placeholder="请输入物料名称"
              clearable
              class="search-input"
              @change="loadData"
            />
          </div>
          <div
            class="form-item-wrapper"
            v-else-if="viewMode === VIEW_TYPE.SUPPLIER"
          >
            <label class="form-label">供应商名称</label>
            <el-input
              v-model="queryForm.supplierName"
              placeholder="请输入供应商名称"
              clearable
              class="search-input"
              @change="loadData"
            />
          </div>
          <div>
            <el-button
              @click="loadData"
              type="primary"
            >
              搜索
            </el-button>
            <el-button @click="handleReset"> 重置 </el-button>
            <el-button @click="handleDownloadTemplate"> 数据导出 </el-button>
          </div>
        </div>
      </el-form>
    </div>
    <!-- 第三行：提示信息 -->
    <div
      class="alert-section"
      v-if="viewMode === VIEW_TYPE.MATERIAL && quoteLessThanThreeMaterialCount"
    >
      <img
        src="https://oss-public.yunlizhi.cn/frontend/fe-procurement-platform/alert_icon.svg"
        alt=""
      />
      <div class="flex items-center">
        <span>提醒：{{ quoteLessThanThreeMaterialCount }}条物料报价供应商不足3家！</span>
        <span
          class="cursor-pointer"
          @click="getQuoteLessThanThreeMaterialCode"
          >请查看</span
        >
      </div>
    </div>
    <div
      v-if="viewMode === VIEW_TYPE.MATERIAL && tableData?.length && showIpAlert && handleShowIpAlert?.length"
      class="flex justify-end"
    >
      <div class="common-ip-alert">
        <img
          class="alert-icon"
          src="https://prod-ylzapp-public.oss-cn-zhangjiakou.aliyuncs.com/frontend/fe-procurement-platform/WarnTriangleFilled.svg"
          alt=""
        />
        <span class="alert-msg">系统监测到 {{ handleShowSupplierAlert?.length }} 家供应商IP异常</span>
        <img
          class="close-icon"
          src="https://prod-ylzapp-public.oss-cn-zhangjiakou.aliyuncs.com/frontend/fe-procurement-platform/close.svg"
          alt=""
          @click="handleCloseIpAlert"
        />
      </div>
    </div>
    <div
      class="alert-section"
      v-if="viewMode === VIEW_TYPE.SUPPLIER && supplierLessThanThreeCount"
    >
      <img
        src="https://oss-public.yunlizhi.cn/frontend/fe-procurement-platform/alert_icon.svg"
        alt=""
      />
      <div class="flex items-center">
        <span>提醒：报价供应商不足 3 家！</span>
      </div>
    </div>
    <div
      v-if="viewMode === VIEW_TYPE.MATERIAL"
      class="need-hide-table-card editable-table table-container"
    >
      <yun-pro-table
        ref="proTableRef"
        :table-columns="dynamicColumn"
        v-model:tableData="tableData"
        :auto-height="false"
        layout="whole"
        :default-fetch="false"
        :search-props="searchPropsObj"
        :table-props="{
          ...tablePropsObj,
          'span-method': objectSpanMethod,
          cellStyle: calcCellStyle,
          headerCellStyle: calcHeaderCellStyle,
          cellClassName: calcCellClassName,
          headerCellClassName: calcHeaderCellClassName,
        }"
      >
      </yun-pro-table>
    </div>
    <div
      v-else-if="viewMode === VIEW_TYPE.SUPPLIER"
      class="need-hide-table-card editable-table table-container"
    >
      <yun-pro-table
        ref="proTableRef"
        :table-columns="columnsSupplier"
        v-model:tableData="tableSupplierData"
        :auto-height="false"
        layout="whole"
        :default-fetch="false"
        :search-props="searchPropsObj"
        :table-props="{
          ...tablePropsObj,
        }"
      >
        <template #t_action="{ row }">
          <el-button
            type="text"
            @click="handleViewDetail(row)"
            >报价明细</el-button
          >
        </template>
        <template #t_lookUp="{ row }">
          <el-button
            type="text"
            @click="handleViewRegister(row)"
            >查看</el-button
          >
        </template>
      </yun-pro-table>
    </div>

    <!-- 供应商报价抽屉 -->
    <!-- <QuotationDrawer
      v-model:visible="quotationDrawerVisible"
      @submit="handleQuotationSubmit"
    /> -->
    <QuotationDrawer
      v-model:visible="quotationDrawerVisible"
      @refresh="loadData"
      :row="currentRow"
      :type="T_TYPE.PURCHASER"
    />

    <!-- 报价详情抽屉 -->
    <QuotationDetailDrawer
      :row="currentRow"
      v-model:visible="detailDrawerVisible"
    />

    <!-- 报名资料 -->
    <AuditDrawer
      v-model:visible="auditDrawerVisible"
      :row="currentRow"
      :btnType="BTN_O_TYPE.DETAIL"
    />
    <!-- 不足3条数据 -->
    <!-- <WarnDrawer v-model:visible="warnDrawerVisible" :sectionId="currentSectionId" :quoteRound="hasQuotation" /> -->
  </div>
  <CommonMaterialTable v-if="isZJWT" />
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onBeforeMount } from 'vue';
import StageTabs from '../StageTabs.vue';
// import QuotationDrawer from './QuotationDrawer.vue';
import QuotationDrawer from '@/views/procurementSourcing/biddingProcess/components/bidding/Quotation/QuotationDrawer.vue';
import AuditDrawer from '@/views/procurementSourcing/biddingProcess/components/bidding/DocumentReview/AuditDrawer.vue';
import QuotationDetailDrawer from './QuotationDetailDrawer.vue';
import { Plus } from '@element-plus/icons-vue';
import { queryQuoteListByMaterial, queryQuoteListBySupplier, getQuoteLessThanThreeMaterialCodeList } from '@/api/purchasing/bid';
import { VIEW_TYPE_OPTIONS, VIEW_TYPE, T_TYPE, SUPPLIER_COLUMN, BTN_O_TYPE } from '@/views/procurementSourcing/biddingProcess/constants';
import { numberToChinese } from '@/views/procurementSourcing/biddingProcess/utils';
import { useBiddingStore } from '@/views/procurementSourcing/biddingProcess/stores';
import { ElMessage } from 'yun-design';
import { useColumns } from './hooks/useColumns';
// @ts-ignore
import { useDynamicTable } from '@/views/procurementSourcing/biddingProcess/hooks/useDynamicTable.jsx';
import moment from 'moment';
// @ts-ignore
import { useExcel } from '@/views/procurementSourcing/biddingProcess/hooks/useExcel.jsx';
import { isProjectMember } from '@/views/procurementSourcing/biddingProcess/utils/bid';
import CommonMaterialTable from '@/views/procurementSourcing/biddingProcess/components/bidding/CommonMaterialTable/index.vue';

const { dynamicColumn: dynamicColumnOrigin, tableData, setDynamicColumn, handleTableData, objectSpanMethod } = useDynamicTable();
const dynamicColumn = computed(() => {
  return [
    ...dynamicColumnOrigin.value,
    {
      prop: 'quoteIp',
      label: '投标IP',
      fieldName: '投标IP',
      fieldCode: 'quoteIp',
      width: 140,
    },
  ];
});

const { exportToExcel } = useExcel();

const biddingStore = useBiddingStore();
const noticeInfo = computed(() => biddingStore?.noticeInfo);
const projectId = computed(() => biddingStore?.projectId);
// const isPublic = computed(() => biddingStore?.projectDetail?.inviteMethod === 'PUBLICITY');
const noticeId = computed(() => biddingStore?.noticeId);

const hasAuth = computed(() => {
  return isProjectMember(biddingStore.projectDetail?.projectMemberList) === 'PROJECT_LEADER';
});

const isJZTP = computed(() => biddingStore?.projectDetail?.sourcingType === 'JZTP');
const isZJWT = computed(() => biddingStore?.isZJWT);

// 报名资料抽屉控制
const auditDrawerVisible = ref(false);
const currentRow = ref<any>(null);
// 表格数据
const currentSectionId = ref('');
const tableSupplierData = ref<any[]>([]);
// 查看模式
const viewMode = ref(isJZTP?.value ? VIEW_TYPE.SUPPLIER : VIEW_TYPE.MATERIAL);
// Tab 切换相关
const activeTabIndex = ref(0);
// 报价轮次选项
const quotationRounds = ref<any[]>([]);
// 已经发起多少轮
const hasQuotation = ref(0);

const supplierLessThanThreeCount = computed(() => {
  return tableSupplierData.value?.length && [...new Set(tableSupplierData.value?.map((item: any) => item?.tenantSupplierId))].length < 3 ? true : false;
});

const { columnsSupplier } = useColumns({
  sectionId: currentSectionId.value,
});

const showIpAlert = ref(true);
const handleCloseIpAlert = () => {
  showIpAlert.value = false;
};
// tableData 数组中存在 quoteIp  tenantSupplierId
// 将 quoteIp 相同的 单元格样式标红 表头样式标红
const handleShowIpAlert = computed(() => {
  const quoteIpList = [...new Set(tableData.value?.map((item: any) => item?.quoteIp))];
  return quoteIpList?.filter((item: any) => {
    return tableData.value?.filter((i: any) => i?.quoteIp === item).length > 1;
  });
});

const handleShowSupplierAlert = computed(() => {
  return [
    ...new Set(tableData.value?.filter((item: any) => handleShowIpAlert.value?.includes(item?.quoteIp))?.map((item: any) => item?.tenantSupplierId)),
  ];
});

const calcCellClassName = ({ row, column }: any) => {
  if (column?.property === 'quoteIp' && handleShowIpAlert?.value?.includes(row?.quoteIp)) {
    return 'quoteIp-cell-class-name';
  }
};
const calcCellStyle = ({ row, column }: any) => {
  if (column?.property === 'quoteIp' && handleShowIpAlert?.value?.includes(row?.quoteIp)) {
    return {
      // borderRight: '1px solid var(--Color-Border-border-color-lighter, #EBEEF5)',
      // backgroundColor: 'rgba(255, 59, 48, 0.06)',
      // color: 'var(---el-color-danger, #F53F3F)',
    };
  }
};
const calcHeaderCellClassName = ({ column }: any) => {
  if (column?.property === 'quoteIp' && handleShowIpAlert?.value?.length) {
    return 'quoteIp-header-cell-class-name';
  }
};
const calcHeaderCellStyle = ({ column }: any) => {
  if (column?.property === 'quoteIp' && handleShowIpAlert?.value?.length) {
    return {
      // color: 'var(--Color-Error-color-error, #FF3B30)',
    };
  }
};

// 查询表单
const initFormData = (ext = {}) => {
  return {
    roundNo: '',
    materialName: '',
    supplierName: '',
    ...ext,
  };
};
const queryForm = ref<any>(initFormData());

const handleDownloadMaterialTemplate = async () => {
  try {
    const blob = await exportToExcel(tableData.value, dynamicColumn.value);
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `物料数据_${moment().format('YYYY-MM-DD HH:mm:ss')}.xlsx`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    window.URL.revokeObjectURL(url);
  } catch (error) {
    ElMessage.error('导出失败');
  }
};

const handleDownloadSupplierTemplate = async () => {
  try {
    const blob = await exportToExcel(
      tableSupplierData.value,
      SUPPLIER_COLUMN?.filter((item: any) => !item.ingoreExport)?.map((item: any) => ({
        label: item.label,
        prop: item.prop,
        fieldName: item.label,
        fieldCode: item.prop,
      }))
    );
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `供应商数据_${moment().format('YYYY-MM-DD HH:mm:ss')}.xlsx`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    window.URL.revokeObjectURL(url);
  } catch (error) {
    ElMessage.error('导出失败');
  }
};

// 处理下载模板
async function handleDownloadTemplate() {
  if (viewMode.value === VIEW_TYPE.MATERIAL) {
    handleDownloadMaterialTemplate();
  } else if (viewMode.value === VIEW_TYPE.SUPPLIER) {
    handleDownloadSupplierTemplate();
  }
}
function handleReset() {
  queryForm.value = initFormData({
    roundNo: quotationRounds.value?.[0]?.value || '',
  });
  loadData();
}

const isNewQuotation = computed(() => {
  return queryForm.value?.roundNo === quotationRounds.value?.length;
});

// 条物料报价供应商不足3家
const quoteLessThanThreeMaterialCount = computed(() => {
  const projectItemIds = [...new Set(tableData.value?.map((item: any) => item?.projectItemId) || [])];
  let count = 0;
  projectItemIds?.forEach((item: any) => {
    const quoteList = tableData.value?.filter((i: any) => i?.projectItemId === item);
    if (quoteList?.length < 3) {
      count++;
    }
  });
  return count;
});

// 按物料查询报价列表
async function queryQuoteListByMaterialData(extParams = {}) {
  try {
    const res = await queryQuoteListByMaterial(
      {
        sectionId: currentSectionId.value,
        noticeId: noticeId.value,
        projectId: projectId.value,
        ...queryForm.value,
        ...extParams,
      },
      {
        current: 1,
        size: 200,
      }
    );
    // eslint-disable-next-line no-console

    const list = res?.data?.records || [];

    setDynamicColumn(list);
    handleTableData(list, isNewQuotation.value);
  } catch (error) {
    // eslint-disable-next-line no-console
    console.log('error:', error);
  }
}
// 按供应商查询报价列表
async function queryQuoteListBySupplierData() {
  try {
    const res = await queryQuoteListBySupplier({
      sectionId: currentSectionId.value,
      noticeId: noticeId.value,
      projectId: projectId.value,
      ...queryForm.value,
    });
    // eslint-disable-next-line no-console
    const list = res?.data || [];
    tableSupplierData.value = list?.map((row: any) => {
      const { quoteMaterialCount, totalMaterialCount } = row;
      return {
        ...row,
        count: totalMaterialCount ? `${quoteMaterialCount || 0}/${totalMaterialCount}` : quoteMaterialCount || 0,
      };
    });
  } catch (error) {
    // eslint-disable-next-line no-console
    console.log('error:', error);
    tableSupplierData.value = [];
  }
}
// 切换查看模式
function handleViewModeChange(mode: string) {
  initRoundsData();
  if (mode === VIEW_TYPE.MATERIAL) {
    queryQuoteListByMaterialData();
  } else if (mode === VIEW_TYPE.SUPPLIER) {
    queryQuoteListBySupplierData();
  }
}

async function getQuoteLessThanThreeMaterialCode() {
  try {
    const res = await getQuoteLessThanThreeMaterialCodeList({
      noticeId: noticeId.value,
      sectionId: currentSectionId.value,
      quoteRound: hasQuotation.value,
    });
    if (res?.data?.length) {
      await queryQuoteListByMaterialData({
        materialCodeList: res?.data,
      });
      ElMessage.success('查询成功');
    } else {
      ElMessage.error('暂无数据');
    }
    // eslint-disable-next-line no-empty
  } catch (error) {}
}

const quoteRoundCount = computed(() => {
  const list = noticeInfo?.value?.quoteRoundVoList || [];
  const findRound = list.find((item: any) => String(item?.sectionId) === String(currentSectionId.value)) || {};
  return findRound?.currentQuoteRound || 1;
});

// 初始化轮次数据
function initRoundsData() {
  const count = quoteRoundCount.value;
  hasQuotation.value = count;
  quotationRounds.value = Array.from({ length: count }, (_, i) => i + 1).map((item) => {
    return {
      label: `第${numberToChinese(item)}轮报价`,
      value: item,
    };
  });
  const lastRound = quotationRounds.value?.[quotationRounds.value?.length - 1]?.value || '';
  queryForm.value = initFormData({
    roundNo: lastRound,
  });
}

// 加载数据
async function loadData() {
  if (viewMode.value === VIEW_TYPE.MATERIAL) {
    queryQuoteListByMaterialData();
  } else if (viewMode.value === VIEW_TYPE.SUPPLIER) {
    queryQuoteListBySupplierData();
  }
}

// 抽屉控制
const quotationDrawerVisible = ref(false);

// 详情抽屉控制
const detailDrawerVisible = ref(false);

// // 处理导出
// function handleExport() {
//   console.log('导出');
// }

// 处理插入供应商报价
function handleInsertQuotation() {
  currentRow.value = { sectionId: currentSectionId.value };
  quotationDrawerVisible.value = true;
}

// 查看详情
function handleViewDetail(row: any) {
  currentRow.value = { ...row, sectionId: currentSectionId.value, quoteRoundCount: quoteRoundCount.value };
  detailDrawerVisible.value = true;
}

// 报名资料
function handleViewRegister(row: any) {
  currentRow.value = { ...row, sectionId: currentSectionId.value };
  auditDrawerVisible.value = true;
}

// 标段变化事件
async function handleSectionChange(sectionId: string) {
  currentSectionId.value = sectionId;
  setTimeout(async () => {
    if (currentSectionId.value) {
      initRoundsData();
      loadData();
    }
  }, 300);
}

onMounted(async () => {});
onBeforeMount(async () => {});

const searchPropsObj = computed(() => ({
  showCollapseBtn: false,
  collapse: false,
  showResetBtn: false,
  showSubmitBtn: false,
  showOperation: false,
  quantity: 3,
}));

const tablePropsObj = computed(() => ({
  // ...tableProps.value,
  stripe: false,
  border: false,
  headerCellStyle: {
    // backgroundColor: '#f5f7fa',
    // height: '40px',
    color: '#303133',
    fontWeight: 'bold',
    borderColor: '#EBEEF5',
    'vertical-align': 'middle',
  },
  cellStyle: {
    // height: '40px',
    padding: '0',
    'vertical-align': 'middle',
    borderColor: '#EBEEF5',
  },
  rowStyle: {
    // height: '40px',
    borderColor: '#EBEEF5',
  },
  // rowHeight: 40,
}));
</script>

<style scoped lang="scss">
.quotation-management {
  height: 100%;
  box-sizing: border-box;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  // flex: 1;
  padding: 20px;

  .table-container {
    flex: 1;
  }
  .view-control {
    display: flex;
    justify-content: space-between;
    align-items: center;
    .left-section {
      display: flex;
      align-items: center;
      gap: 40px;

      .view-tabs {
        :deep(.el-radio-button__inner) {
          padding: 8px 16px;
        }
      }
    }

    .right-section {
      color: var(--Color-Text-text-color-secondary, #86909c);
      font-family: 'PingFang SC';
      font-size: 12px;
      font-style: normal;
      font-weight: 400;
      line-height: 20px;
      .highlight {
        color: var(--Color-Text-text-color-primary, #1d2129);
        font-family: 'PingFang SC';
        font-size: 12px;
        font-style: normal;
        font-weight: 600;
        line-height: 20px;
      }
    }
  }

  .search-section {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 16px;

    :deep(.el-form .el-form-item) {
      margin-bottom: 0 !important;
    }

    .left-section {
      flex: 1;
    }
  }

  .alert-section {
    display: flex;
    padding: 8px 16px;
    align-items: center;
    gap: 8px;
    align-self: stretch;
    border-radius: var(--Radius-border-radius-small, 2px);
    background: var(--Color-Warning-color-warning-light-9, #fff9e8);
    color: var(--Color-Text-text-color-primary, #1d2129);
    font-family: 'PingFang SC';
    font-size: 13px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px;
  }

  .common-ip-alert {
    border-radius: 4px;
    border: 1px solid var(--Color-Border-border-color-light, #e4e7ed);
    background: var(--color-white, #fff);
    /* light/box-shadow-lighter */
    box-shadow: 0 0 6px 0 rgba(0, 0, 0, 0.12);
    display: flex;
    padding: 8px 16px;
    align-items: center;
    position: relative;
    width: max-content;
    .alert-icon {
      width: 16px;
      height: 16px;
    }
    .alert-msg {
      color: var(--Color-Error-color-error, #ff3b30);
      /* regular/small */
      font-family: 'PingFang SC';
      font-size: 13px;
      font-style: normal;
      font-weight: 400;
      line-height: 22px; /* 169.231% */
      padding-left: 8px;
      padding-right: 24px;
    }
    .alert-close {
      width: 12px;
      height: 12px;
    }
  }
}

:deep(.quoteIp-header-cell-class-name .filter-box .filter-box-content .text-box) {
  color: var(--Color-Error-color-error, #ff3b30) !important;
}
:deep(.quoteIp-cell-class-name .cell) {
  background-color: rgba(255, 59, 48, 0.06) !important;
  color: var(---el-color-danger, #f53f3f) !important;
}
</style>
