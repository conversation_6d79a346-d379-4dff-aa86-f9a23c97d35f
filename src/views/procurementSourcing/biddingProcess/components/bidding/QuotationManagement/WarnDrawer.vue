<template>
  <el-drawer
    v-model="showVisible"
    title="明细"
    size="60vw"
    :with-header="true"
    custom-class="custom-drawer"
  >
    <div class="quotation-detail" v-loading="loading">
      <el-table
        :data="tableData"
        class="detail-table"
      >
        <el-table-column
          label="序号"
          type="index"
          width="60"
        />
        <el-table-column
          label="物料编码"
          prop="materialCode"
        />
        <el-table-column
          label="物料名称"
          prop="materialName"
        />
        <el-table-column
          label="规格型号"
          prop="specNo"
        />
        <el-table-column
          label="单位"
          prop="unit"
        />
        <el-table-column
          label="需求数量"
          prop="requiredQuantity"
        />
        <el-table-column
          label="可供数量"
          prop="availableQuantity"
        />
        <el-table-column
          label="出厂价"
          prop="factoryPrice"
        >
          <template #default="{ row }"> ¥{{ row.factoryPrice }} </template>
        </el-table-column>
        <el-table-column
          label="运费"
          prop="freight"
        >
          <template #default="{ row }"> ¥{{ row.freight }} </template>
        </el-table-column>
        <el-table-column
          label="到厂价"
          prop="deliveredPrice"
        >
          <template #default="{ row }"> ¥{{ row.deliveredPrice }} </template>
        </el-table-column>
        <el-table-column
          label="报价单价"
          prop="quotationPrice"
        >
          <template #default="{ row }"> ¥{{ row.quotationPrice }} </template>
        </el-table-column>
      </el-table>
    </div>
  </el-drawer>
</template>

<script setup lang="ts">
import { ref, watch, defineProps, defineEmits } from 'vue';
import { useBiddingStore } from '@/views/procurementSourcing/biddingProcess/stores';
import { getQuoteLessThanThreeMaterialCodeList } from '@/api/purchasing/bid';

const biddingStore = useBiddingStore();
const noticeId = computed(() => biddingStore?.noticeId);
const props = defineProps<{ visible: boolean; sectionId: any; quoteRound: any }>();
const emit = defineEmits(['update:visible']);
const loading = ref(false)
const tableData = ref([])
const showVisible = computed({
  get: () => props.visible,
  set: (v: boolean) => emit('update:visible', v),
});
watch(() => showVisible.value, initData);
async function initData() {
	if (showVisible.value) {
		loading.value = true
		try {
			const res = await getQuoteLessThanThreeMaterialCodeList({
				noticeId: noticeId.value,
				sectionId: props?.sectionId,
				quoteRound: props?.quoteRound,
			})
		// eslint-disable-next-line no-empty
		} catch (error) {}
		loading.value = false
	}
}

</script>

<style scoped lang="scss">
.quotation-detail {

  .detail-table {
		margin-top: 24px;
    :deep(.el-table__header) {
      th {
        background-color: var(--Color-Fill-fill-color-light, #f5f7fa);
        color: var(--Color-Text-text-color-primary, #1d2129);
        font-weight: 600;
      }
    }
  }
}
</style>
