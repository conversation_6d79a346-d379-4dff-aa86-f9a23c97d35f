import { ref, computed } from 'vue';
import { useBiddingStore } from '@/views/procurementSourcing/biddingProcess/stores';
import { numberToChinese } from '@/views/procurementSourcing/biddingProcess/utils';

export function useColumns({ sectionId }: any) {
  const searchData = ref<any>({});

  const biddingStore = useBiddingStore();
  const noticeInfo = computed(() => biddingStore?.noticeInfo);
  const quotationRounds = ref<any[]>([]);
  const quoteRoundCount = computed(() => {
    const list = noticeInfo?.value?.quoteRoundVoList || [];
    const findRound = list.find((item: any) => String(item?.sectionId) === String(sectionId.value)) || {};
    return findRound?.currentQuoteRound || 1;
  });
  // 初始化轮次数据
  function initRoundsData() {
    quotationRounds.value = Array.from({ length: quoteRoundCount.value }, (_, i) => i + 1).map((item) => {
      return {
        label: `第${numberToChinese(item)}轮报价`,
        value: item,
      };
    });
  }
  // 初始化轮次表单初始值
  function initFormData() {
    const lastRound = quotationRounds.value?.[quotationRounds.value?.length - 1]?.value || '';
    searchData.value.roundNo = lastRound;
  }

  const searchFieldsMaterial = computed(() => {
    const commonFields = [
      {
        label: '选择报价轮次',
        prop: 'roundNo',
        type: 'select',
        componentAttrs: {
          // filterable: true,
          // multiple: true,
          // clearable: true,
        },
        options: quotationRounds.value,
      },
      {
        label: '物料名称',
        prop: 'materialName',
        type: 'input',
      },
    ];
    return commonFields;
  });

  const searchFieldsSupplier = computed(() => {
    const commonFields = [
      {
        label: '选择报价轮次',
        prop: 'roundNo',
        type: 'select',
        componentAttrs: {
          // filterable: true,
          // multiple: true,
          // clearable: true,
        },
        options: quotationRounds.value,
      },
      {
        label: '供应商名称',
        prop: 'supplierName',
        type: 'input',
      },
    ];
    return commonFields;
  });

  // const searchFields = computed(() => {
  //   if (viewMode.value === VIEW_TYPE.MATERIAL) {
  //     return searchFieldsMaterial.value;
  //   }
  //   return searchFieldsSupplier.value;
  // });

  const columnsSupplier = computed(() => {
    const commonColumns = [
      {
        label: '序号',
        type: 'index',
        width: 60,
        ingoreExport: true,
      },
      {
        label: '供应商名称',
        prop: 'supplierName',
      },
      {
        label: '联系人',
        prop: 'contactPerson',
      },
      {
        label: '报名资料',
        prop: 'lookUp',
        ingoreExport: true,
      },
      {
        label: '报价轮次',
        prop: 'quoteRoundCount',
      },
      {
        label: '报价/物料条数',
        prop: 'count',
      },
      {
        label: '本轮报价总价',
        prop: 'totalQuoteAmount',
      },
      {
        label: '最终报价IP',
        prop: 'quoteIp',
      },
      {
        label: '操作',
        prop: 'action',
        width: 100,
        fixed: 'right',
        ingoreExport: true,
      },
    ];
    return commonColumns;
  });

  return {
    columnsSupplier,
    searchFieldsMaterial,
    searchFieldsSupplier,
    searchData,

    initRoundsData,
    initFormData,
  };
}
