<template>
  <el-drawer
    v-model="showVisible"
    :title="'报价明细'"
    size="78vw"
    :with-header="true"
    append-to-body
    destroy-on-close
    custom-class="bid-custom-drawer"
  >
    <div
      class="quotation-detail"
      v-loading="loading"
    >
      <!-- 报价轮次和基本信息 -->
      <div class="tab-section">
        <el-tabs
          v-model="activeTab"
          @tab-click="handleTabClick"
        >
          <el-tab-pane
            v-for="item in quotationRounds"
            :key="item.value"
            :label="item.label"
            :name="item.value"
          />
        </el-tabs>
      </div>
      <!-- 报价基本信息 -->
      <!-- <div class="info-section">
        <div class="left-section">
          <div class="title">第一次报价</div>
          <div class="info-item">
            <span class="info-label">报价时间：</span>
            <span class="info-value">2025/01/01 12:22</span>
          </div>
          <div class="info-item">
            <span class="info-label">报价IP：</span>
            <span class="info-value">************</span>
          </div>
        </div>
        <div class="right-section">
          <div class="info-item">
            <span class="info-label">报价单：</span>
            <span class="info-value">
              报价单文件.docx
              <el-link
                type="primary"
                class="download-link"
                >下载</el-link
              >
            </span>
          </div>

          <div class="info-item">
            <span class="info-label">其他附件：</span>
            <span class="info-value">
              报价单文件.docx
              <el-link
                type="primary"
                class="download-link"
                >下载</el-link
              >
            </span>
          </div>
        </div>
      </div> -->

      <!-- 搜索区域 -->
      <el-form
        :model="queryForm"
        inline
        class="bidding-process-search-form"
      >
        <div class="form-content">
          <div class="form-item-wrapper">
            <label class="form-label">物料名称</label>
            <el-input
              v-model="queryForm.materialName"
              placeholder="请输入物料名称"
              clearable
              class="search-input"
            />
          </div>
          <div class="form-item-wrapper">
            <label class="form-label">物料编码</label>
            <el-input
              v-model="queryForm.materialCode"
              placeholder="请输入物料编码"
              clearable
              class="search-input"
            />
          </div>
          <div>
            <el-button
              @click="queryQuoteListByMaterialData"
              type="primary"
            >
              搜索
            </el-button>
            <el-button @click="handleReset"> 重置 </el-button>
          </div>
        </div>
      </el-form>

      <!-- 报价明细表格 -->
      <!-- <el-table
        :data="tableData"
        class="editable-table"
        :span-method="objectSpanMethod"
        size="small"
      >
        <template
          v-for="item in dynamicColumn"
          :key="item.prop || item.label"
        >
          <template v-if="item.children?.length">
            <el-table-column
              :key="item.prop"
              :label="item.label"
              :prop="item.prop"
              :min-width="item.width"
              :show-overflow-tooltip="item.showOverflowTooltip"
            >
              <el-table-column
                v-for="child in item.children"
                :key="child.prop"
                :label="child.label"
                :prop="child.prop"
                :min-width="child.width"
                :show-overflow-tooltip="child.showOverflowTooltip"
              />
            </el-table-column>
          </template>
          <el-table-column
            v-else
            :key="item.prop || item.label"
            :label="item.label"
            :prop="item.prop"
            :min-width="item.width"
            :show-overflow-tooltip="item.showOverflowTooltip"
          >
          </el-table-column>
        </template>
      </el-table> -->
      <div class="need-hide-table-card editable-table table-container">
        <yun-pro-table
          ref="proTableRef"
          :table-columns="dynamicColumn"
          v-model:tableData="tableData"
          :auto-height="true"
          layout="whole"
          :default-fetch="false"
          :search-props="searchPropsObj"
          :table-props="{
            ...tablePropsObj,
            'span-method': objectSpanMethod,
          }"
        >
        </yun-pro-table>
      </div>
      <Pagination
        :current="pagination.current"
        :size="pagination.size"
        :total="pagination.total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        v-if="pagination.total > 0"
        style="margin: 16px 0"
      />
    </div>
  </el-drawer>
</template>

<script setup lang="ts">
import { ref, watch, defineProps, defineEmits, computed } from 'vue';
import { queryQuoteListByMaterial } from '@/api/purchasing/bid';
import { useBiddingStore } from '@/views/procurementSourcing/biddingProcess/stores';
import { numberToChinese } from '@/views/procurementSourcing/biddingProcess/utils';
// @ts-ignore
import { useDynamicTable } from '@/views/procurementSourcing/biddingProcess/hooks/useDynamicTable.jsx';
import { useUserRole } from '@/views/procurementSourcing/biddingProcess/utils';

import Pagination from '/@/components/Pagination/index.vue';

const { dynamicColumn: dynamicColumnOrigin, tableData, setDynamicColumn, handleTableData, objectSpanMethod } = useDynamicTable();

const dynamicColumn = computed(() => {
  return [
    ...dynamicColumnOrigin.value,
    {
      prop: 'quoteIp',
      label: '投标IP',
      fieldName: '投标IP',
      fieldCode: 'quoteIp',
      width: 140,
    },
  ];
});

const pagination = ref({
  current: 1,
  size: 10,
  total: 0,
});

const { isSupplier } = useUserRole();
const biddingStore = useBiddingStore();
const ownSupplier = computed(() => biddingStore?.ownSupplier || {});
const noticeId = computed(() => biddingStore?.noticeId);
const projectId = computed(() => biddingStore?.projectId);
const props = defineProps<{ visible: boolean; row: any }>();
const loading = ref(false);
const tableMaterialData = ref([]);
const showVisible = computed({
  get: () => props.visible,
  set: (v: boolean) => emit('update:visible', v),
});
watch(
  () => showVisible.value,
  (val) => {
    if (val) {
      pagination.value.current = 1;
      pagination.value.total = 0;
      pagination.value.size = 10;

      queryForm.value = initFormData();
      initData();
    }
  }
);

function handleTabClick() {
  pagination.value.current = 1;
  pagination.value.size = 10;
  pagination.value.total = 0;

  queryQuoteListByMaterialData();
}

function handleSizeChange(val: number) {
  pagination.value.size = val;
  queryQuoteListByMaterialData();
}

function handleCurrentChange(val: number) {
  pagination.value.current = val;
  queryQuoteListByMaterialData();
}

function handleReset() {
  queryForm.value = initFormData();

  pagination.value.current = 1;
  pagination.value.size = 10;

  queryQuoteListByMaterialData();
}

// 查询表单
const initFormData = () => {
  return {
    materialName: '',
    materialCode: '',
  };
};
const queryForm = ref<any>(initFormData());

const activeTab = ref(0);
// 报价轮次
const quotationRounds = ref<any>([]);

async function initData() {
  const { currentQuoteRound, quoteRoundCount } = props?.row || {};
  const round = quoteRoundCount || currentQuoteRound || null;

  quotationRounds.value = Array.from({ length: round }, (_, i) => i + 1)
    .map((item) => {
      return {
        label: `第${numberToChinese(item)}次报价`,
        value: item,
      };
    })
    .reverse();
  activeTab.value = quotationRounds.value[0]?.value || null;
  queryQuoteListByMaterialData();
}

const isNewQuotation = computed(() => {
  return activeTab.value === quotationRounds.value?.length;
});

const tenantSupplierId = computed(() => {
  if (isSupplier.value) {
    return ownSupplier?.value?.id || null;
  }
  return props?.row?.tenantSupplierId || null;
});

// 按物料查询报价列表
async function queryQuoteListByMaterialData() {
  loading.value = true;
  try {
    const res = await queryQuoteListByMaterial(
      {
        noticeId: noticeId.value,
        projectId: projectId.value,
        sectionId: props?.row?.sectionId,
        roundNo: activeTab.value,
        tenantSupplierId: tenantSupplierId.value,
        ...queryForm.value,
      },
      {
        current: pagination.value.current,
        size: pagination.value.size,
      }
    );
    // eslint-disable-next-line no-console
    const list = res?.data?.records || [];
    tableMaterialData.value = list;
    setDynamicColumn(list);
    handleTableData(list, isNewQuotation.value);
    pagination.value.total = res?.data?.total || 0;
  } catch (error) {
    // eslint-disable-next-line no-console
    console.log('error:', error);
    tableMaterialData.value = [];
  }
  loading.value = false;
}

const emit = defineEmits(['update:visible']);

const searchPropsObj = computed(() => ({
  showCollapseBtn: false,
  collapse: false,
  showResetBtn: false,
  showSubmitBtn: false,
  showOperation: false,
  quantity: 3,
}));

const tablePropsObj = computed(() => ({
  // ...tableProps.value,
  stripe: true,
  border: true,
  headerCellStyle: {
    backgroundColor: '#f5f7fa',
    color: '#303133',
    fontWeight: 'bold',
    // height: '40px',
    borderColor: '#EBEEF5',
    'vertical-align': 'middle',
  },
  cellStyle: {
    padding: '0',
    // height: '40px',
    'vertical-align': 'middle',
    borderColor: '#EBEEF5',
  },
  rowStyle: {
    // height: '40px',
    borderColor: '#EBEEF5',
  },
  // rowHeight: 40,
}));
</script>

<style scoped lang="scss">
.quotation-detail {
  // padding: 0 20px;
  height: 100%;
  display: flex;
  flex-direction: column;
  box-sizing: border-box;

  .table-container {
    flex: 1;
    // 不展拖动的伪元素
    :deep(.el-table th.el-table__cell:not(:last-child):after) {
      display: none !important;
    }
    :deep(.el-table th:nth-last-child(2):before) {
      display: none !important;
    }
    :deep(.el-table__inner-wrapper .el-table__body-wrapper) {
      flex: unset !important;
    }
  }

  .info-section {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;

    .left-section,
    .right-section {
      display: flex;
      align-items: center;
      gap: 8px;
      .title {
        color: var(--Color-Text-text-color-primary, #1d2129);
        font-family: 'PingFang SC';
        font-size: 16px;
        font-style: normal;
        font-weight: 600;
        line-height: 24px;
        position: relative;
        padding-left: 10px;

        &::before {
          content: '';
          display: block;
          width: 2px;
          height: 14px;
          background-color: #0069ff;
          position: absolute;
          left: 0;
          top: 50%;
          transform: translateY(-50%);
        }
      }
    }

    .info-item {
      display: flex;
      align-items: center;
      text-align: center;
      font-family: 'PingFang SC';
      font-size: 12px;
      font-style: normal;
      font-weight: 400;
      border-radius: var(--Radius-border-radius-small, 2px);
      border: 1px solid var(--Color-Border-border-color-light, #e4e7ed);
      padding: 0 4px;
      .info-label {
        color: var(--Color-Text-text-color-secondary, #86909c);
      }

      .value {
        color: var(--Color-Text-text-color-primary, #1d2129);
        display: flex;
        align-items: center;
        gap: 2px;
      }
    }
  }
  .download-link {
    color: var(--Color-Primary-color-primary, #0069ff);
    font-family: 'PingFang SC';
    font-size: 12px;
    font-style: normal;
    font-weight: 400;
  }

  .search-form {
    margin-bottom: 16px;
    width: 100%;

    .form-content {
      display: flex;
      justify-content: space-between;
      width: 75%;
      gap: 40px;

      .form-item-wrapper {
        flex: 1;
        display: flex;
        align-items: center;
        gap: 8px;

        .form-label {
          width: 70px;
          color: var(--Color-Text-text-color-regular, #4e5969);
          font-family: 'PingFang SC';
          font-size: 14px;
          font-style: normal;
          font-weight: 400;
          line-height: 22px;
          text-align: right;
        }

        .search-input {
          width: 100%;

          :deep(.el-input__wrapper) {
            background-color: var(--Color-Fill-fill-color-light, #f5f7fa);
            box-shadow: none;
            border: 1px solid transparent;

            &:hover,
            &:focus {
              border-color: var(--Color-Primary-color-primary, #0069ff);
              background-color: #fff;
            }
          }

          :deep(.el-input__inner) {
            border-radius: var(--Radius-border-radius-small, 2px);
            background: var(--Color-Fill-fill-color-light, #f5f7fa);
            border: none;
            color: #1d2129;
            font-size: 14px;

            &::placeholder {
              color: var(--Color-Text-text-color-regular, #4e5969);
              font-family: 'PingFang SC';
            }
          }
        }
      }
    }
  }

  .detail-table {
    :deep(.el-table__header) {
      th {
        background-color: var(--Color-Fill-fill-color-light, #f5f7fa);
        color: var(--Color-Text-text-color-primary, #1d2129);
        font-weight: 600;
      }
    }
  }
}
</style>
