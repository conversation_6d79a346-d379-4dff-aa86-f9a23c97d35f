<template>
  <div class="quotation-management">
    <StageTabs
      v-model="activeTabIndex"
      @update:sectionId="handleSectionChange"
      :style="cStyle"
    />

    <div
      class="view-control"
      v-if="isXJCG || isCompetitiveBidding"
    >
      <div class="left-section">
        <el-radio-group
          v-model="viewMode"
          class="view-tabs"
          @change="loadData"
          v-if="!isCompetitiveBidding && isXJCG"
        >
          >
          <el-radio-button
            v-for="option in VIEW_TYPE_OPTIONS"
            :key="option.value"
            :label="option.value"
          >
            {{ option.label }}
          </el-radio-button>
        </el-radio-group>
        <el-button
          @click="handleInsertQuotation"
          v-if="viewMode === VIEW_TYPE.MATERIAL && hasAuth && isXJCG"
          type="text"
          :icon="Plus"
        >
          插入供应商报价
        </el-button>
      </div>
      <div class="right-section">
        已发起 <span class="highlight">{{ quoteRoundCount }}</span> 轮报价
      </div>
    </div>
    <!-- 物料表格 -->
    <CommonMaterialTable
      v-if="viewMode === VIEW_TYPE.MATERIAL && scene"
      style="padding: 0"
      :scene="scene"
      ref="materialTableRef"
    />
    <!-- 供应商表格 -->
    <CommonSupplierTable
      v-if="viewMode === VIEW_TYPE.SUPPLIER && scene"
      style="padding: 0"
      :scene="scene"
      ref="supplierTableRef"
    />
    <QuotationDrawer
      v-model:visible="quotationDrawerVisible"
      @refresh="loadData"
      :row="currentRow"
      :type="T_TYPE.PURCHASER"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onBeforeMount } from 'vue';
// @ts-ignore
import StageTabs from '../StageTabs.vue';
// @ts-ignore
import QuotationDrawer from '@/views/procurementSourcing/biddingProcess/components/bidding/Quotation/QuotationDrawer.vue';
import { Plus } from '@element-plus/icons-vue';
import { VIEW_TYPE_OPTIONS, VIEW_TYPE, T_TYPE, SCENE_ENUMS } from '@/views/procurementSourcing/biddingProcess/constants';
import { useBiddingStore } from '@/views/procurementSourcing/biddingProcess/stores';
import { isProjectMember } from '@/views/procurementSourcing/biddingProcess/utils/bid';
// @ts-ignore
import CommonMaterialTable from '@/views/procurementSourcing/biddingProcess/components/bidding/CommonMaterialTable/index.vue';
// @ts-ignore
import CommonSupplierTable from '@/views/procurementSourcing/biddingProcess/components/bidding/CommonSupplierTable/index.vue';

const biddingStore = useBiddingStore();

const hasAuth = computed(() => {
  return isProjectMember(biddingStore.projectDetail?.projectMemberList) === 'PROJECT_LEADER';
});

const noticeInfo = computed(() => biddingStore?.noticeInfo);
const isJZTP = computed(() => biddingStore?.isJZTP);
const isZJWT = computed(() => biddingStore?.isZJWT);
const isXJCG = computed(() => biddingStore?.isXJCG);
const isZB = computed(() => biddingStore?.isZB);

const scene = computed(() => {
  if (isXJCG.value) {
    return SCENE_ENUMS.XJCG_TB;
  }
  if (isZJWT.value) {
    return SCENE_ENUMS.ZJWT_TB;
  }
  if (isJZTP.value) {
    return SCENE_ENUMS.JZTP_TB;
  }
  if (isZB.value) {
    return SCENE_ENUMS.ZB_TB;
  }
  return null;
});

const cStyle = computed(() => {
  if (isJZTP.value || isZB.value) {
    return {
      marginBottom: '0px',
    };
  }
  return {};
});

const isCompetitiveBidding = computed(() => biddingStore?.isCompetitiveBidding);

const currentRow = ref<any>(null);

// 表格数据
const currentSectionId = ref('');
// 查看模式 - 竞争性采购使用供应商视图，其他使用物料视图
const viewMode = ref(isCompetitiveBidding?.value ? VIEW_TYPE.SUPPLIER : VIEW_TYPE.MATERIAL);
// Tab 切换相关
const activeTabIndex = ref(0);
const materialTableRef = ref<any>(null);
const supplierTableRef = ref<any>(null);

function loadData() {
  materialTableRef?.value?.handleSectionChange(currentSectionId.value);
  supplierTableRef?.value?.handleSectionChange(currentSectionId.value);
}

const quoteRoundCount = computed(() => {
  const list = noticeInfo?.value?.quoteRoundVoList || [];
  const findRound = list.find((item: any) => String(item?.sectionId) === String(currentSectionId.value)) || {};
  return findRound?.currentQuoteRound || 1;
});

// 抽屉控制
const quotationDrawerVisible = ref(false);

// 处理插入供应商报价
function handleInsertQuotation() {
  currentRow.value = { sectionId: currentSectionId.value };
  quotationDrawerVisible.value = true;
}

// 标段变化事件
async function handleSectionChange(sectionId: string) {
  currentSectionId.value = sectionId;
  loadData();
}

onMounted(async () => {});
onBeforeMount(async () => {});
</script>

<style scoped lang="scss">
.quotation-management {
  height: 100%;
  box-sizing: border-box;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  // flex: 1;
  padding: 20px;
  .view-control {
    display: flex;
    justify-content: space-between;
    align-items: center;
    .left-section {
      display: flex;
      align-items: center;
      gap: 40px;

      .view-tabs {
        :deep(.el-radio-button__inner) {
          padding: 8px 16px;
        }
      }
    }

    .right-section {
      color: var(--Color-Text-text-color-secondary, #86909c);
      font-family: 'PingFang SC';
      font-size: 12px;
      font-style: normal;
      font-weight: 400;
      line-height: 20px;
      .highlight {
        color: var(--Color-Text-text-color-primary, #1d2129);
        font-family: 'PingFang SC';
        font-size: 12px;
        font-style: normal;
        font-weight: 600;
        line-height: 20px;
      }
    }
  }
}
</style>
