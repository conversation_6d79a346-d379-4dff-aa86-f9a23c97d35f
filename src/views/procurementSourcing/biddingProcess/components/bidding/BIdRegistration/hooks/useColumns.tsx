import { ref, computed } from 'vue';
import { useBiddingStore } from '@/views/procurementSourcing/biddingProcess/stores';
import {
  REGISTER_REVIEWED_STATUS_LABELS,
  REGISTER_REVIEWED_STATUS_TAGS,
  INVITE_STATUS,
  INVITE_STATUS_CALL_STATUS_TAGS,
  INVITE_STATUS_CALL_STATUS_LABELS,
} from '@/views/procurementSourcing/biddingProcess/constants/bid';
import { isEmptyValue } from '@/views/procurementSourcing/biddingProcess/utils/bid';

export function useColumns() {
  const biddingStore = useBiddingStore();
  const isPublic = computed(() => biddingStore?.isPublic);
  // const isZJWT = computed(() => biddingStore?.isZJWT);
  const isShowSection = computed(() => biddingStore?.isShowSection);

  const searchFields = computed(() => {
    const commonFields = [
      {
        label: '标段名称',
        prop: 'selectionName',
        type: 'input',
        componentAttrs: {
          clearable: true,
        },
      },
    ];
    return isShowSection?.value ? commonFields : [];
  });
  const searchData = ref({});
  const columns = computed(() => {
    const commonColumns = [
      {
        label: '序号',
        type: 'index',
        width: '80px',
      },
      {
        label: '项目名称',
        prop: 'projectName',
      },
      ...(isShowSection.value ? [{
        label: '标段名称',
        prop: 'selectionName',
      }] : []),
    ];

    return isPublic.value
      ? [
          ...commonColumns,
          {
            label: '报名情况',
            prop: 'registered',
            render: ({ row }: any) => {
              const registered = row?.registered;
              // @ts-ignore
              const tagName = row.registered ? '已报名' : '待报名';
              // @ts-ignore
              const tagType = row.registered ? 'success' : 'warning';
              // @ts-ignore
              return !isEmptyValue(registered) ? <el-tag type={tagType}>{tagName}</el-tag> : '-';
            },
          },
          {
            label: '资料审核状态',
            prop: 'registerReviewed',
            render: ({ row }: any) => {
              const registerStatus = row?.registerStatus;
              // @ts-ignore
              const tagName = REGISTER_REVIEWED_STATUS_LABELS[registerStatus];
              // @ts-ignore
              const tagType = REGISTER_REVIEWED_STATUS_TAGS[registerStatus];
              // @ts-ignore
              return tagName ? <el-tag type={tagType}>{tagName}</el-tag> : '-';
            },
          },
          {
            label: '报名时间',
            prop: 'registerTime',
            width: '170px',
          },
          {
            label: '操作',
            prop: 'action',
            width: '180px',
            fixed: 'right',
          },
        ]
      : [
          ...commonColumns,
          {
            label: '回执情况',
            prop: 'inviteStatus',
            render: ({ row }: any) => {
              const inviteStatus = row?.inviteStatus;
              // @ts-ignore
              const tagName = [INVITE_STATUS.WITHDRAWN, INVITE_STATUS.PENDING]?.includes(inviteStatus) ? '待回执' : '已回执';
              // @ts-ignore
              const tagType = [INVITE_STATUS.WITHDRAWN, INVITE_STATUS.PENDING]?.includes(inviteStatus) ? 'warning' : 'success';
              // @ts-ignore
              return !isEmptyValue(inviteStatus) ? <el-tag type={tagType}>{tagName}</el-tag> : '-';
            },
          },
          {
            label: '资料审核状态',
            prop: 'registerReviewed',
            render: ({ row }: any) => {
              const inviteStatus = row?.inviteStatus;
              // @ts-ignore
              const tagName = INVITE_STATUS_CALL_STATUS_LABELS[inviteStatus];
              // @ts-ignore
              const tagType = INVITE_STATUS_CALL_STATUS_TAGS[inviteStatus];
              // @ts-ignore
              return tagName ? <el-tag type={tagType}>{tagName}</el-tag> : '-';
            },
          },
          {
            label: '报名时间',
            prop: 'registerTime',
            width: '170px',
          },
          {
            label: '操作',
            prop: 'action',
            width: '180px',
            fixed: 'right',
          },
        ];
  });

  return {
    columns,
    searchFields,
    searchData,
  };
}
