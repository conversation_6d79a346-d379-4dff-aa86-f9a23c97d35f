<template>
  <el-drawer
    v-model="showVisible"
    :title="'报名'"
    size="60vw"
    append-to-body
    destroy-on-close
    :with-header="true"
    custom-class="bid-custom-drawer"
  >
    <el-form
      :model="form"
      :rules="rules"
      ref="formRef"
      label-width="108px"
    >
      <!-- 企业基础资料 -->
      <div class="form-section">
        <div class="label-header">企业基础资料</div>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item
              label="企业名称"
              prop="company.name"
            >
              <el-input
                v-model="form.company.name"
                placeholder="请输入企业名称"
                disabled
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item
              label="统一信用代码"
              prop="company.creditCode"
            >
              <el-input
                v-model="form.company.creditCode"
                placeholder="请输入统一信用代码"
                disabled
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item
              label="成立日期"
              prop="company.registerDate"
            >
              <el-date-picker
                v-model="form.company.registerDate"
                type="date"
                placeholder="请选择成立日期"
                style="width: 100%"
                disabled
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item
              label="注册资本"
              prop="company.registerCapital"
            >
              <el-input
                v-model="form.company.registerCapital"
                placeholder="请输入注册资本"
                suffix="元"
                disabled
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item
              label="法人代表"
              prop="company.legalPerson"
            >
              <el-input
                v-model="form.company.legalPerson"
                placeholder="请输入法人代表"
                disabled
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item
              label="详细地址"
              prop="company.address"
            >
              <el-input
                v-model="form.company.address"
                placeholder="请输入详细地址"
                disabled
              />
            </el-form-item>
          </el-col>
        </el-row>
      </div>
      <!-- 联系人信息 -->
      <div class="form-section">
        <div class="label-header">联系人信息</div>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item
              label="联系人"
              prop="contact.contactPerson"
            >
              <el-input
                v-model="form.contact.contactPerson"
                placeholder="请输入联系人"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item
              label="联系电话"
              prop="contact.contactPhone"
            >
              <el-input
                v-model="form.contact.contactPhone"
                placeholder="请输入联系电话"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item
              label="联系邮箱"
              prop="contact.contactEmail"
            >
              <el-input
                v-model="form.contact.contactEmail"
                placeholder="请输入联系邮箱"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </div>

      <!-- 预审材料 -->
      <div class="form-section">
        <div class="label-header">预审材料</div>
        <el-row :gutter="20">
          <el-col
            :span="24"
            v-for="item in quotationFiles"
            :key="item.id"
          >
            <el-form-item
              :label="item.requirementName"
              :prop="`preAudit.quotation_${item.id}`"
              label-width="108px"
            >
              <YunUpload
                :multiple="false"
                :show-file-list="true"
                v-model="form.preAudit[`quotation_${item.id}`]"
                :buttonText="`请上传${item.requirementName}文件`"
                @change="(v: any) => uploadQuotationAttachment(v, item)"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </div>
      <!-- 报价响应文件 -->
      <div class="form-section">
        <div class="label-header">报名响应文件</div>
        <el-table
          :data="form.quotationResponseFiles"
          style="width: 100%"
        >
          <el-table-column
            label="序号"
            type="index"
            width="60"
          />
          <el-table-column
            label="条件名称"
            prop="requirementName"
          />
          <el-table-column
            label="条件内容"
            prop="requirementContent"
          />
          <el-table-column
            label="操作"
            width="80"
          >
            <template #default="{ $index, row }">
              <el-link
                type="primary"
                @click="onEditQuotation($index)"
                v-if="!row.response"
                >响应</el-link
              >
              <el-link
                type="primary"
                disabled
                v-if="row.response"
                >已响应</el-link
              >
            </template>
          </el-table-column>
        </el-table>
      </div>
    </el-form>
    <template #footer>
      <div class="flex justify-end gap-4 dark:border-gray-700">
        <el-button
          @click="onSubmit"
          type="primary"
          :loading="isLoading"
          >提交</el-button
        >
      </div>
    </template>
  </el-drawer>
</template>

<script setup lang="ts">
import { ref, watch, defineProps, defineEmits, onMounted, computed } from 'vue';
import { tenderRegister, getRegisteredInfo } from '@/api/purchasing/bid';
import { ElMessage } from 'yun-design';
import YunUpload from '/@/components/YunUpload/index.vue';
// import { BID_TYPE } from '@/views/procurementSourcing/biddingProcess/constants/bid';
import { jsonStringToObject, isEmptyValue } from '@/views/procurementSourcing/biddingProcess/utils/bid';
import { useBiddingStore } from '@/views/procurementSourcing/biddingProcess/stores';

const biddingStore = useBiddingStore();
const noticeInfo = computed(() => biddingStore?.noticeInfo);
const ownSupplier = computed(() => biddingStore?.ownSupplier || {});

const noticeId = computed(() => biddingStore?.noticeId);

const props = defineProps<{ visible: boolean; row: any; bidType: any }>();

const quotationFiles = computed(() => {
  return (noticeInfo.value?.requirementList || [])?.filter(
    (item: any) => item.requirementType === 'QUALIFICATION' && item?.sectionId === props.row.sectionId
  );
});
const conditionFiles = computed(() => {
  return (noticeInfo.value?.requirementList || [])?.filter(
    (item: any) => item.requirementType === 'CONDITION' && item?.sectionId === props.row.sectionId
  );
});
const emit = defineEmits(['refresh', 'update:visible']);
const showVisible = computed({
  get: () => props.visible,
  set: (v: boolean) => emit('update:visible', v),
});
watch(() => showVisible.value, initData);

const isLoading = ref(false);
const formRef = ref();
const initForm = () => ({
  // 供应商
  company: {
    name: '',
    creditCode: '',
    registerDate: '',
    registerCapital: '',
    legalPerson: '',
    address: '',
  },
  // 联系人
  contact: {
    contactPerson: '',
    contactPhone: '',
    contactEmail: '',
  },
  // 预审
  preAudit: {},
  // 响应
  quotationResponseFiles: [],
});
const form = ref<any>(initForm());

const quotationRules = ref<any>({});

const rules = computed(() => {
  return {
    'company.name': [{ required: true, message: '请输入企业名称', trigger: 'blur' }],
    'company.registerCapital': [{ required: true, message: '请输入注册资本', trigger: 'blur' }],
    'company.legalPerson': [{ required: true, message: '请输入法人代表', trigger: 'blur' }],
    'company.address': [{ required: true, message: '请输入详细地址', trigger: 'blur' }],
    'contact.contactPerson': [{ required: true, message: '请输入联系人', trigger: 'blur' }],
    'contact.contactPhone': [{ required: true, message: '请输入联系电话', trigger: 'blur' }],
    ...quotationRules.value,
  };
});

// 获取我的报名信息
// eslint-disable-next-line @typescript-eslint/no-unused-vars, no-unused-vars
async function getMyRegisterInfoData() {
  try {
    const res = await getRegisteredInfo({
      noticeId: noticeId.value,
      sectionId: props.row.sectionId,
    });
    const { id, contactPerson, contactPhone, contactEmail, srmTenderSupplierBidderResponseList } = res?.data || {};
    if (id) {
      form.value.contact.contactPerson = contactPerson;
      form.value.contact.contactPhone = contactPhone;
      form.value.contact.contactEmail = contactEmail;
      if (srmTenderSupplierBidderResponseList?.length > 0) {
        const c = srmTenderSupplierBidderResponseList.filter((item: any) => item.requirementType === 'CONDITION');
        form.value.quotationResponseFiles = c.map((item: any) => ({
          requirementName: item.responseName,
          requirementContent: item.responseContent,
          requirementId: item.requirementId,
          response: item.responseContent,
        }));
        const q = srmTenderSupplierBidderResponseList.filter((item: any) => item.requirementType === 'QUALIFICATION');
        q.forEach((item: any) => {
          const key = `quotation_${item.id}`;
          const ruleKey = `preAudit.${key}`;
          form.value.preAudit[key] = jsonStringToObject(item.responseContent);
          quotationRules.value[ruleKey] = [{ required: true, message: `${item.requirementName}文件`, trigger: 'change' }];
        });
        // console.log('form.value', form.value);
      } else {
        initFirstBidData();
      }
    }
  } catch (error) {
    // eslint-disable-next-line no-console
    console.log('error', error);
  }
}

function uploadQuotationAttachment(v: any, item: any) {
  // 取最后一个文件
  if (Array.isArray(v) && v.length >= 2) {
    form.value.preAudit[`quotation_${item.id}`] = [v[v.length - 1]];
  }
}

// 处理参数
function handleParams() {
  const responseList = form.value.quotationResponseFiles.map((item: any) => ({
    requirementId: item.requirementId,
    responseContent: item.response,
  }));
  const preAuditList = Object.keys(form.value.preAudit).map((key: any) => {
    const file = form.value.preAudit[key] || [];
    return {
      requirementId: key.split('_')[1],
      responseContent: JSON.stringify(
        file?.map((itemFile: any) => ({
          name: itemFile.name,
          url: itemFile.url,
        }))
      ),
    };
  });
  const params = {
    noticeId: noticeId.value,
    sectionId: props.row.sectionId,
    ...form.value.contact,
    bidderResponseList: [...responseList, ...preAuditList],
  };
  return params;
}
function onSubmit() {
  formRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      const existResponse = form.value.quotationResponseFiles.some((item: any) => !item.response);
      if (existResponse) {
        const names = form.value.quotationResponseFiles
          .filter((item: any) => !item.response)
          .map((item: any) => item.requirementName)
          .join(',');
        ElMessage.error(`【${names}】未响应`);
        return;
      }
      isLoading.value = true;
      try {
        const params = handleParams();
        await tenderRegister(params);
        ElMessage.success('报名成功');
        setTimeout(() => {
          emit('refresh');
          showVisible.value = false;
        }, 200);
      } catch (error) {
        // ElMessage.error('报名失败');
      } finally {
        isLoading.value = false;
      }
    }
  });
}
function onEditQuotation(index: number) {
  form.value.quotationResponseFiles[index].response = '同意';
  ElMessage.success('响应成功');
}

// 初始化供应商数据
function initSupplierData() {
  form.value.company.name = ownSupplier.value?.supplierName || '';
  form.value.company.creditCode = ownSupplier.value?.socialCreditCode || '';
  form.value.company.registerDate = ownSupplier.value?.establishDate || '';
  form.value.company.registerCapital = ownSupplier.value?.registeredCapital || '';
  form.value.company.legalPerson = ownSupplier.value?.legalPerson || '';
  form.value.company.address = ownSupplier.value?.registeredAddress || '';
}

// 过滤一些无用字符串+判空逻辑
function filterEmptyString(str: string) {
  return !isEmptyValue(str) && str !== '{}' && str !== '[]' ? str : '-';
}

function initFirstBidData() {
  // 预审动态表单
  if (quotationFiles.value.length > 0) {
    quotationFiles.value.forEach((item: any) => {
      const key = `quotation_${item.id}`;
      const ruleKey = `preAudit.${key}`;
      form.value.preAudit[key] = [];
      quotationRules.value[ruleKey] = [{ required: true, message: `${item.requirementName}文件`, trigger: 'change' }];
    });
  }
  // 响应文件
  form.value.quotationResponseFiles = conditionFiles.value.map((item: any) => ({
    requirementName: item.requirementName,
    requirementContent: filterEmptyString(item?.requirementContent),
    requirementId: item.id,
    response: null,
  }));
}

function initData() {
  if (showVisible.value) {
    form.value = initForm();
    initSupplierData();
    initFirstBidData();
    // if (props.bidType === BID_TYPE.FIRST_BID) {
    //   initFirstBidData();
    // } else if (props.bidType === BID_TYPE.AGAIN_BID) {
    //   getMyRegisterInfoData();
    // }
  }
}

onMounted(async () => {});
</script>

<style scoped lang="scss">
@import '../../../styles/collapse-panel.scss';
</style>
