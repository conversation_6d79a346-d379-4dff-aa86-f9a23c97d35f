<template>
  <el-drawer
    v-model="showVisible"
    :title="'邀请回执填报'"
    size="60vw"
    append-to-body
    destroy-on-close
    :with-header="true"
    custom-class="bid-custom-drawer"
  >
    <el-form
      :model="form"
      :rules="rules"
      ref="formRef"
      label-width="108px"
    >
      <!-- 企业基础资料 -->
      <div class="form-section">
        <div class="label-header">企业基础资料</div>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item
              label="企业名称"
              prop="company.supplierName"
            >
              <el-input
                v-model="form.company.supplierName"
                placeholder="请输入企业名称"
                disabled
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item
              label="统一信用代码"
              prop="company.registeredCapital"
            >
              <el-input
                v-model="form.company.registeredCapital"
                placeholder="请输入统一信用代码"
                disabled
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item
              label="成立日期"
              prop="company.legalPerson"
            >
              <el-date-picker
                v-model="form.company.legalPerson"
                type="date"
                placeholder="请选择成立日期"
                style="width: 100%"
                disabled
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item
              label="注册资本"
              prop="company.registeredCapital"
            >
              <el-input
                v-model="form.company.registeredCapital"
                placeholder="请输入注册资本"
                suffix="元"
                disabled
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item
              label="法人代表"
              prop="company.legalPerson"
            >
              <el-input
                v-model="form.company.legalPerson"
                placeholder="请输入法人代表"
                disabled
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item
              label="注册地址"
              prop="company.registeredAddress"
            >
              <el-input
                v-model="form.company.registeredAddress"
                placeholder="请输入注册地址"
                disabled
              />
            </el-form-item>
          </el-col>
        </el-row>
      </div>
      <!-- 联系人信息 -->
      <div class="form-section">
        <div class="label-header">联系人信息</div>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item
              label="联系人"
              prop="contact.concatName"
            >
              <el-input
                v-model="form.contact.concatName"
                placeholder="请输入联系人"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item
              label="联系电话"
              prop="contact.concatPhone"
            >
              <el-input
                v-model="form.contact.concatPhone"
                placeholder="请输入联系电话"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item
              label="联系邮箱"
              prop="contact.concatEmail"
            >
              <el-input
                v-model="form.contact.concatEmail"
                placeholder="请输入联系邮箱"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </div>

      <!-- 预审材料 -->
      <div class="form-section">
        <div class="label-header">邀请回执信息</div>

        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item
              label="回执信息"
              prop="contact.responseContent"
            >
              <el-input
                v-model="form.contact.responseContent"
                placeholder="请输入回执信息"
                type="textarea"
                :rows="4"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item
              label="回执附件"
              prop="contact.attachment"
            >
              <YunUpload
                :multiple="false"
                :show-file-list="true"
                v-model="form.contact.attachment"
                buttonText="上传文件"
                @change="uploadQuotationAttachment"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </div>
    </el-form>
    <template #footer>
      <div class="flex justify-end gap-4 dark:border-gray-700">
        <el-button
          @click="onSubmit"
          type="primary"
          :loading="isLoading"
          >提交</el-button
        >
      </div>
    </template>
  </el-drawer>
</template>

<script setup lang="ts">
import { ref, watch, defineProps, defineEmits, onMounted, computed } from 'vue';
import { inviteResponse } from '@/api/purchasing/bid';
import { ElMessage } from 'yun-design';
import YunUpload from '/@/components/YunUpload/index.vue';
import { useBiddingStore } from '@/views/procurementSourcing/biddingProcess/stores';

const biddingStore = useBiddingStore();
const ownSupplier = computed(() => biddingStore?.ownSupplier || {});
const noticeId = computed(() => biddingStore?.noticeId);
const props = defineProps<{ visible: boolean; row: any }>();
const emit = defineEmits(['refresh', 'update:visible']);
const showVisible = computed({
  get: () => props.visible,
  set: (v: boolean) => emit('update:visible', v),
});
watch(() => showVisible.value, initData);

const isLoading = ref(false);
const formRef = ref();
const initForm = () => ({
  // 供应商
  company: {
    supplierName: '',
    registeredCapital: '',
    socialCreditCode: '',
    establishDate: '',
    legalPerson: '',
    registeredAddress: '',
  },
  // 联系人
  contact: {
    concatName: '',
    concatPhone: '',
    concatEmail: '',
    responseContent: '',
    attachmentName: '',
    attachmentUrl: '',
    attachment: [],
  },
});
const form = ref<any>(initForm());

const rules = computed(() => {
  return {
    'company.supplierName': [{ required: true, message: '请输入企业名称', trigger: 'blur' }],
    'company.registeredCapital': [{ required: true, message: '请输入注册资本', trigger: 'blur' }],
    'company.legalPerson': [{ required: true, message: '请输入法人代表', trigger: 'blur' }],
    'company.registeredAddress': [{ required: true, message: '请输入注册地址', trigger: 'blur' }],
    'contact.concatName': [{ required: true, message: '请输入联系人', trigger: 'blur' }],
    'contact.concatPhone': [{ required: true, message: '请输入联系电话', trigger: 'blur' }],
  };
});

function uploadQuotationAttachment(v: any) {
  const file = v[v.length - 1] || {};
  form.value.contact.attachmentUrl = file?.url;
  form.value.contact.attachmentName = file?.name;
  setTimeout(() => {
    form.value.contact.attachment = file?.url && file?.name ? [file] : [];
  }, 150);
}

function onSubmit() {
  formRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      isLoading.value = true;
      try {
        await inviteResponse({
          noticeId: noticeId.value,
          sectionId: props?.row?.sectionId,
          ...(form.value.contact || {}),
        });
        ElMessage.success('操作成功');
      } catch (error) {
        // ElMessage.error('报名失败');
      } finally {
        setTimeout(() => {
          emit('refresh');
          isLoading.value = false;
          showVisible.value = false;
        }, 200);
      }
    }
  });
}

// 初始化供应商数据
function initSupplierData() {
  const { supplierName, registeredCapital, socialCreditCode, legalPerson, establishDate, registeredAddress } = ownSupplier.value || {};
  form.value.company = {
    supplierName,
    registeredCapital,
    socialCreditCode,
    legalPerson,
    establishDate,
    registeredAddress,
  };
}

function initData() {
  if (showVisible.value) {
    form.value = initForm();
    initSupplierData();
  }
}

onMounted(async () => {});
</script>

<style scoped lang="scss">
@import '../../../styles/collapse-panel.scss';
</style>
