<template>
  <div class="document-review-container">
    <div class="flex-1 need-hide-table-card editable-table">
      <yun-pro-table
        ref="proTableRef"
        v-model:pagination="pagination"
        v-model:filter-data="filterTableData"
        v-model:searchData="searchData"
        :table-columns="columns"
        :search-fields="searchFields"
        :auto-height="true"
        :remote-method="remoteMethod"
        :table-props="tablePropsObj"
        :search-props="searchPropsObj"
        layout="whole"
        :default-fetch="false"
      >
        <!-- <template #t_tableHeaderLeft></template>
        <template #t_tableHeaderRight> </template> -->
        <template #t_action="{ row }">
          <template v-if="isPublic">
            <el-button
              v-if="!row.registered"
              type="text"
              @click="handleRegister(row)"
            >
              报名
            </el-button>
            <el-button
              v-if="row.registered"
              type="text"
              @click="handleReRegister(row)"
            >
              重新报名
            </el-button>
            <el-button
              v-if="row.registerStatus === REGISTER_REVIEWED_STATUS.REGISTERED"
              type="text"
              @click="handleWithdraw(row)"
            >
              撤回审批
            </el-button>
          </template>
          <template v-else>
            <el-button
              v-if="[INVITE_STATUS.WITHDRAWN, INVITE_STATUS.PENDING]?.includes(row.inviteStatus)"
              type="text"
              @click="handleRegisterCall(row)"
            >
              回执
            </el-button>
            <el-button
              v-if="[INVITE_STATUS.REJECTED]?.includes(row.inviteStatus)"
              type="text"
              @click="handleReRegisterCall(row)"
            >
              重新回执
            </el-button>
            <el-button
              v-if="[INVITE_STATUS.ACCEPTED]?.includes(row.inviteStatus)"
              type="text"
              @click="handleWithdrawCall(row)"
            >
              撤回审核
            </el-button>
          </template>
        </template>
      </yun-pro-table>
    </div>

    <!-- 表单 -->
    <SubmitDrawer
      @refresh="reLoad"
      :row="currentRow"
      v-model:visible="submitDrawerVisible"
      :bid-type="bidType"
    />

    <!-- 回执 -->
    <CallbackDrawer
      @refresh="reLoad"
      :row="currentRow"
      v-model:visible="submitDrawerCallVisible"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import SubmitDrawer from './SubmitDrawer.vue';
import CallbackDrawer from './CallbackDrawer.vue';
import { REGISTER_REVIEWED_STATUS, BID_TYPE, INVITE_STATUS } from '@/views/procurementSourcing/biddingProcess/constants/bid';
import { withdrawn } from '@/api/purchasing/bid';
import { ElMessage, ElMessageBox } from 'yun-design';
import { useBiddingStore } from '@/views/procurementSourcing/biddingProcess/stores';
import { useProTable } from '@ylz-use/core';
import { useColumns } from './hooks/useColumns';
import { getSelectionRegisterInfo } from '@/api/purchasing/bid';
import { jsonStringToObject } from '@/views/procurementSourcing/biddingProcess/utils/bid';

const pagination = ref({});
const { columns, searchFields, searchData } = useColumns();

const isPublic = computed(() => biddingStore?.isPublic);
const biddingStore = useBiddingStore();
const noticeId = computed(() => biddingStore?.noticeId);

const submitDrawerVisible = ref(false);
const submitDrawerCallVisible = ref(false);

const currentRow = ref<any>(null);
const bidType = ref<any>(BID_TYPE.FIRST_BID);
const handleRegister = (row: any) => {
  bidType.value = BID_TYPE.FIRST_BID;
  submitDrawerVisible.value = true;
  currentRow.value = row;
};

const { remoteMethod, tableProps, proTableRef, filterTableData, reLoad } = useProTable({
  apiFn: getSelectionRegisterInfo,
  responseHandler(result: any) {
    const res = result.data || [];
    res?.forEach((item: any) => {
      const depositRespContent = item?.depositRespContent || '{}';
      const depositContent = item?.depositContent || '{}';
      item.depositRespContent = jsonStringToObject(depositRespContent);
      item.depositContent = jsonStringToObject(depositContent);
    });
    return res;
  },
  customTotalHandler(data: any) {
    return data?.data?.total;
  },
  paramsHandler(params: any) {
    const param = {
      noticeId: noticeId.value,
      ...params,
    };
    return param;
  },
  // querysHandler(querys) {
  //   const querysData = {
  //     ...querys,
  //     current: pagination.value.page,
  //     size: pagination.value.size || pagination.value.pageSize,
  //   };
  //   return querysData;
  // },
  plugins: {
    config: {
      columns: columns.value,
      searchFields: searchFields.value,
    },
    list: ['SEARCH_PLUS'],
  },
});

const searchPropsObj = computed(() => ({
  showCollapseBtn: false,
  collapse: false,
  showResetBtn: false,
  showSubmitBtn: false,
  showOperation: false,
  quantity: 2,
}));

const tablePropsObj = computed(() => ({
  ...tableProps.value,
  stripe: false,
  border: false,
  headerCellStyle: {
    backgroundColor: '#f5f7fa',
    color: '#303133',
    fontWeight: 'bold',
    // height: '40px',
    borderColor: '#EBEEF5',
    'vertical-align': 'middle',
  },
  cellStyle: {
    padding: '0',
    // height: '40px',
    'vertical-align': 'middle',
    borderColor: '#EBEEF5',
  },
  rowStyle: {
    // height: '40px',
    borderColor: '#EBEEF5',
  },
  // rowHeight: 40,
}));

const handleReRegister = (row: any) => {
  bidType.value = BID_TYPE.AGAIN_BID;
  currentRow.value = row;
  submitDrawerVisible.value = true;
};

const handleRegisterCall = (row: any) => {
  currentRow.value = row;
  submitDrawerCallVisible.value = true;
};

const handleReRegisterCall = (row: any) => {
  currentRow.value = row;
  submitDrawerCallVisible.value = true;
};

function handleWithdrawCall(row: any) {
  ElMessageBox.confirm('确定执行该操作？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  })
    .then(async () => {
      await withdrawn({
        noticeId: noticeId.value,
        sectionId: row.sectionId,
        type: 3,
      });
      ElMessage.success('操作成功');
      reLoad();
    })
    .catch(() => {});
}

function handleWithdraw(row: any) {
  ElMessageBox.confirm('确定执行该操作？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  })
    .then(async () => {
      await withdrawn({
        noticeId: noticeId.value,
        sectionId: row.sectionId,
        type: 1,
      });
      ElMessage.success('操作成功');
      reLoad();
    })
    .catch(() => {});
}

onMounted(async () => {
  reLoad();
});
</script>

<style lang="scss" scoped>
.document-review-container {
  flex: 1;
  box-sizing: border-box;
  height: 100%;
  display: flex;
  flex-direction: column;
  position: relative;
  :deep(.need-hide-table-card .el-card__body .dm-filter-root) {
    margin: 0 !important;
  }
  .invite-supplier-btn {
    position: absolute;
    top: 16px;
    right: 20px;
  }
}
</style>
