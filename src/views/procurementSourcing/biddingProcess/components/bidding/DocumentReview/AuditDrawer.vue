<template>
  <el-drawer
    v-model="showVisible"
    :title="title"
    size="60vw"
    append-to-body
    destroy-on-close
    :with-header="true"
    custom-class="bid-custom-drawer"
  >
    <el-form
      :model="form"
      ref="formRef"
      label-width="108px"
      disabled
      v-loading="loading"
    >
      <!-- 企业基础资料 -->
      <div class="form-section">
        <div class="label-header">企业基础资料</div>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item
              label="企业名称"
              prop="company.name"
            >
              <el-input
                v-model="form.company.name"
                disabled
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item
              label="注册资本"
              prop="company.registerCapital"
            >
              <el-input
                v-model="form.company.registerCapital"
                suffix="元"
                disabled
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item
              label="统一信用代码"
              prop="company.creditCode"
            >
              <el-input
                v-model="form.company.creditCode"
                disabled
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item
              label="法人代表"
              prop="company.legalPerson"
            >
              <el-input
                v-model="form.company.legalPerson"
                disabled
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item
              label="成立日期"
              prop="company.registerDate"
            >
              <el-date-picker
                v-model="form.company.registerDate"
                type="date"
                style="width: 100%"
                disabled
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item
              label="详细地址"
              prop="company.address"
            >
              <el-input
                v-model="form.company.address"
                disabled
              />
            </el-form-item>
          </el-col>
        </el-row>
      </div>
      <!-- 联系人信息 -->
      <div class="form-section">
        <div class="label-header">联系人信息</div>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item
              label="联系人"
              prop="contact.contactPerson"
            >
              <el-input v-model="form.contact.contactPerson" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item
              label="联系电话"
              prop="contact.contactPhone"
            >
              <el-input v-model="form.contact.contactPhone" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item
              label="联系邮箱"
              prop="contact.contactEmail"
            >
              <el-input v-model="form.contact.contactEmail" />
            </el-form-item>
          </el-col>
        </el-row>
      </div>

      <!-- 预审材料 -->
      <div class="form-section">
        <div class="label-header">预审材料</div>
        <el-row :gutter="20">
          <el-col
            :span="24"
            v-for="(q, index) in form?.qualificationList || []"
            :key="index"
          >
            <el-form-item
              :label="q.label"
              label-width="108px"
            >
              <FileRenderer :value="q.value || []" />
            </el-form-item>
          </el-col>
        </el-row>
      </div>
      <!-- 报名响应条件 -->
      <div class="form-section">
        <div class="label-header">报名响应条件</div>
        <el-table
          :data="form.conditionList || []"
          style="width: 100%"
          class="editable-table"
        >
          <el-table-column
            label="序号"
            type="index"
            width="60"
          />
          <el-table-column
            label="条件名称"
            prop="requirementName"
          />
          <el-table-column
            label="条件内容"
            prop="requirementContent"
          />
          <el-table-column
            label="状态"
            prop="status"
          >
            <template #default="{ row }">
              <el-tag :type="row.status ? 'success' : 'danger'">{{ row.status ? '已响应' : '未响应' }}</el-tag>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </el-form>
    <template
      #footer
      v-if="btnType === BTN_O_TYPE.AUDIT"
    >
      <div class="flex justify-end gap-4">
        <el-button
          @click.stop="handleAudit({ pass: false })"
          type="danger"
          :loading="isLoading"
          >审核不通过</el-button
        >
        <el-button
          @click.stop="handleAudit({ pass: true })"
          type="primary"
          :loading="isLoading"
          >审核通过</el-button
        >
      </div>
    </template>
  </el-drawer>
</template>

<script setup lang="ts">
import { ref, defineProps, defineEmits, computed, watch } from 'vue';
import { review, getRegisteredInfo, getBasicInfo } from '@/api/purchasing/bid';
import { ElMessage, ElMessageBox } from 'yun-design';
import { useBiddingStore } from '@/views/procurementSourcing/biddingProcess/stores';
import { BTN_O_TYPE } from '@/views/procurementSourcing/biddingProcess/constants/bid';
import { jsonStringToArray, isEmptyValue } from '@/views/procurementSourcing/biddingProcess/utils/bid';
import FileRenderer from '/@/components/Detail/renderers/FileRenderer.vue';

const biddingStore = useBiddingStore();
const noticeId = computed(() => biddingStore?.noticeId);

const props = defineProps<{ visible: boolean; row: any; btnType: any }>();
const emit = defineEmits(['update:visible', 'refresh']);
const isLoading = ref(false);
const loading = ref(false);
const form = ref<any>({
  // 供应商
  company: {
    name: '',
    creditCode: '',
    registerDate: '',
    registerCapital: '',
    legalPerson: '',
    address: '',
  },
  // 联系人
  contact: {
    contactPerson: '',
    contactPhone: '',
    contactEmail: '',
  },
  qualificationList: [],
  conditionList: [],
});
const showVisible = computed({
  get: () => props.visible,
  set: (v: boolean) => emit('update:visible', v),
});
watch(
  () => showVisible.value,
  (val) => {
    if (val) {
      loadDetails();
    }
  }
);

// 过滤一些无用字符串+判空逻辑
function filterEmptyString(str: string) {
  return !isEmptyValue(str) && str !== '{}' && str !== '[]' ? str : '-';
}

async function loadDetails() {
  loading.value = true;
  try {
    const res = await getRegisteredInfo({
      noticeId: noticeId.value,
      sectionId: props.row.sectionId,
      tenantSupplierId: props.row?.tenantSupplierId || null,
    });
    const { contactEmail, contactPerson, contactPhone, tenantSupplierCode, srmTenderSupplierBidderResponseList } = res?.data || {};
    const sInfo = (await getBasicInfo(tenantSupplierCode)) || {};
    const { supplierName, registeredCapital, socialCreditCode, legalPerson, establishDate, registeredAddress } = sInfo?.data || {};

    const qFile = srmTenderSupplierBidderResponseList?.filter(
      (item: any) => item?.requirementType === 'QUALIFICATION' && item?.sectionId === props.row.sectionId
    );
    const cFile = srmTenderSupplierBidderResponseList?.filter(
      (item: any) => item?.requirementType === 'CONDITION' && item?.sectionId === props.row.sectionId
    );

    form.value.company.name = supplierName || '';
    form.value.company.creditCode = socialCreditCode || '';
    form.value.company.registerDate = establishDate || '';
    form.value.company.registerCapital = registeredCapital || '';
    form.value.company.legalPerson = legalPerson || '';
    form.value.company.address = registeredAddress || '';

    form.value.contact.contactPerson = contactPerson;
    form.value.contact.contactPhone = contactPhone;
    form.value.contact.contactEmail = contactEmail;

    form.value.qualificationList = qFile?.map((item: any) => {
      return {
        label: item?.responseName,
        value: jsonStringToArray(item?.responseContent || '[]'),
      };
    });
    form.value.conditionList = cFile?.map((item: any) => {
      const value = item?.srmTenderRequirement?.requirementContent || '';
      return {
        requirementName: item?.responseName,
        requirementContent: filterEmptyString(value),
        status: !isEmptyValue(value),
      };
    });
    // eslint-disable-next-line no-empty
  } catch (error) {}
  loading.value = false;
}

const title = computed(() => (props?.btnType === BTN_O_TYPE.AUDIT ? '资格审查' : '查看'));
function handleAudit(extParams = {}) {
  ElMessageBox.confirm('确定执行该操作？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  })
    .then(async () => {
      isLoading.value = true;
      try {
        await review({
          noticeId: noticeId.value,
          sectionId: props.row.sectionId,
          tenantSupplierId: props.row?.tenantSupplierId || null,
          type: 1,
          ...extParams,
        });
        ElMessage.success('操作成功');
        emit('refresh');
        showVisible.value = false;
      } catch (error) {
        // ElMessage.error('操作失败');
      } finally {
        isLoading.value = false;
      }
    })
    .catch(() => {});
}
</script>

<style scoped lang="scss">
@import '../../../styles/collapse-panel.scss';
</style>
