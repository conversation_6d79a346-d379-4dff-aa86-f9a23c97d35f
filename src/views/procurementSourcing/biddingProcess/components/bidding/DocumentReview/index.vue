<template>
  <div class="document-review-container" :class="{ 'is-hide-stage-tabs': !isShowSection }">
    <StageTabs
      v-model="activeTabIndex"
      @update:sectionId="handleSectionChange"
    />
    <el-button
      v-if="!isPublic && hasAuth && !isZJWT"
      @click="inviteSupplyHandler"
      type="text"
      :icon="Plus"
      class="invite-supplier-btn"
    >
      邀请供应商
    </el-button>
    <div class="flex-1 need-hide-table-card editable-table">
      <yun-pro-table
        ref="proTableRef"
        v-model:pagination="pagination"
        v-model:filter-data="filterTableData"
        v-model:searchData="searchData"
        :table-columns="columns"
        :search-fields="searchFields"
        :auto-height="true"
        :remote-method="remoteMethod"
        :table-props="tablePropsObj"
        :search-props="searchPropsObj"
        layout="whole"
        :default-fetch="false"
      >
        <!-- <template #t_tableHeaderLeft></template>
        <template #t_tableHeaderRight> </template> -->
        <template #t_action="{ row }">
          <el-button
            type="text"
            @click="onViewDetailCall(row)"
            v-if="[INVITE_STATUS.ACCEPTED, INVITE_STATUS.PASSED, INVITE_STATUS.REJECTED]?.includes(row.inviteStatus)"
            >查看回执</el-button
          >
          <el-button
            type="text"
            v-if="[INVITE_STATUS.ACCEPTED]?.includes(row.inviteStatus) && hasAuth"
            @click="onViewAuditCall(row)"
            >审批回执</el-button
          >
        </template>
        <template #t_regInfo="{ row }">
          <el-button
            v-if="row.registerStatus"
            type="text"
            @click="onViewDetail(row)"
            >查看</el-button
          >
          <el-button
            v-if="row.registerStatus === REGISTER_REVIEWED_STATUS.REGISTERED && hasAuth"
            type="text"
            @click="onViewAudit(row)"
            >资格审查</el-button
          >
        </template>
      </yun-pro-table>
    </div>

    <!-- 审核抽屉 -->
    <AuditDrawer
      v-model:visible="auditDrawerVisible"
      :row="currentRow"
      @refresh="reLoad"
      :btnType="btnType"
    />

    <!-- 邀请供应商 -->
    <ChooseSupplier
      :row="currentRow"
      ref="chooseSupplierRef"
      @refresh="reLoad"
    />

    <!-- 回执信息 -->
    <CallbackDrawer
      @refresh="reLoad"
      :row="currentRow"
      v-model:visible="submitDrawerVisible"
      :btnType="btnType"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onBeforeMount } from 'vue';
import StageTabs from '../StageTabs.vue';
import AuditDrawer from './AuditDrawer.vue';
import CallbackDrawer from './CallbackDrawer.vue';
import { useProTable } from '@ylz-use/core';
import { useColumns } from './hooks/useColumns';
import { getRegisteredSupplierList } from '@/api/purchasing/bid';
import { jsonStringToObject } from '@/views/procurementSourcing/biddingProcess/utils/bid';
import ChooseSupplier from './ChooseSupplier.vue';
import {
  REGISTER_REVIEWED_STATUS,
  BTN_O_TYPE,
  INVITE_STATUS,
  INVITE_STATUS_FEEDBACK_OPTIONS,
} from '@/views/procurementSourcing/biddingProcess/constants/bid';
import { useBiddingStore } from '@/views/procurementSourcing/biddingProcess/stores';
import { isProjectMember } from '@/views/procurementSourcing/biddingProcess/utils/bid';
import { Plus } from '@yun-design/icons-vue';

const pagination = ref({});
const { columns, searchFields, searchData } = useColumns();

const biddingStore = useBiddingStore();
const noticeId = computed(() => biddingStore?.noticeId);
const projectId = computed(() => biddingStore?.projectId);
const isPublic = computed(() => biddingStore?.isPublic);
const isZJWT = computed(() => biddingStore?.isZJWT);
const isShowSection = computed(() => biddingStore?.isShowSection);

const chooseSupplierRef = ref();
const currentSectionId = ref('');
const btnType = ref();

const { remoteMethod, tableProps, proTableRef, filterTableData, reLoad } = useProTable({
  apiFn: getRegisteredSupplierList,
  responseHandler(result: any) {
    const res = result.data || [];
    res?.forEach((item: any) => {
      const depositRespContent = item?.depositRespContent || '{}';
      item.depositRespContent = jsonStringToObject(depositRespContent);
    });
    return res;
  },
  customTotalHandler(data: any) {
    return data?.data?.total;
  },
  paramsHandler(params: any) {
    const { feedbackStatus, callbackStatus, ...rest } = params || {};
    const param = {
      sectionId: currentSectionId.value,
      noticeId: noticeId.value,
      ...rest,
    };
    let inviteStatusList: any[] = [];
    if (feedbackStatus) {
      inviteStatusList = [...inviteStatusList, ...(INVITE_STATUS_FEEDBACK_OPTIONS?.find((item) => item.value === feedbackStatus)?.enums || [])];
    }
    if (callbackStatus) {
      inviteStatusList = [...inviteStatusList, callbackStatus];
    }
    inviteStatusList = inviteStatusList.filter((item) => !!item);
    if (inviteStatusList.length > 0) {
      param.inviteStatusList = [...new Set(inviteStatusList)];
    }
    return param;
  },
  // querysHandler(querys) {
  //   const querysData = {
  //     ...querys,
  //     current: pagination.value.page,
  //     size: pagination.value.size || pagination.value.pageSize,
  //   };
  //   return querysData;
  // },
  plugins: {
    config: {
      columns: columns.value,
      searchFields: searchFields.value,
    },
    list: ['SEARCH_PLUS'],
  },
});

const searchPropsObj = computed(() => ({
  showCollapseBtn: false,
  collapse: false,
  showResetBtn: false,
  showSubmitBtn: false,
  showOperation: false,
  quantity: 3,
}));

const tablePropsObj = computed(() => ({
  ...tableProps.value,
  stripe: false,
  border: false,
  headerCellStyle: {
    backgroundColor: '#f5f7fa',
    color: '#303133',
    fontWeight: 'bold',
    // height: '40px',
    borderColor: '#EBEEF5',
    'vertical-align': 'middle',
  },
  cellStyle: {
    padding: '0',
    // height: '40px',
    'vertical-align': 'middle',
    borderColor: '#EBEEF5',
  },
  rowStyle: {
    // height: '40px',
    borderColor: '#EBEEF5',
  },
  // rowHeight: 40,
}));

const activeTabIndex = ref(0);
const auditDrawerVisible = ref(false);
const submitDrawerVisible = ref(false);
const currentRow = ref<any>(null);

const hasAuth = computed(() => {
  return isProjectMember(biddingStore.projectDetail?.projectMemberList) === 'PROJECT_LEADER';
})

function onViewAudit(row: any) {
  btnType.value = BTN_O_TYPE.AUDIT;
  currentRow.value = row;
  auditDrawerVisible.value = true;
}

function onViewDetail(row: any) {
  btnType.value = BTN_O_TYPE.DETAIL;
  currentRow.value = row;
  auditDrawerVisible.value = true;
}

function onViewAuditCall(row: any) {
  btnType.value = BTN_O_TYPE.AUDIT_CALL;
  currentRow.value = row;
  submitDrawerVisible.value = true;
}

function onViewDetailCall(row: any) {
  btnType.value = BTN_O_TYPE.DETAIL_CALL;
  currentRow.value = row;
  submitDrawerVisible.value = true;
}

// 邀请供应商
async function inviteSupplyHandler() {
  chooseSupplierRef.value?.show({ projectId: projectId.value, noticeId: noticeId.value, sectionId: currentSectionId.value });
}

// 标段变化事件
async function handleSectionChange(sectionId: string) {
  currentSectionId.value = sectionId;
  setTimeout(async () => {
    if (currentSectionId.value) {
      reLoad();
    }
  }, 300);
}
onMounted(async () => {});
onBeforeMount(async () => {});
</script>

<style lang="scss" scoped>
.document-review-container {
  flex: 1;
  padding: 20px;
  box-sizing: border-box;
  height: 100%;
  display: flex;
  flex-direction: column;
  position: relative;
  .invite-supplier-btn {
    position: absolute;
    top: 16px;
    right: 20px;
  }
}
</style>
