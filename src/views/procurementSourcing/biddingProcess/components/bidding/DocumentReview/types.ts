export interface SupplierRow {
  supplierName: string;
  participationType: string;
  participationTime: string;
  contactPerson: string;
  contactInfo: string;
  auditStatus: 'approved' | 'rejected' | 'pending' | 'waiting';
}

export interface PreAuditMaterial {
  idCard: string[]; // 1号证件
  businessLicense: string[]; // 业绩证件
  other: string[]; // 其它证件
}

export interface ContactInfo {
  name: string;
  phone: string;
  email: string;
}

export interface CompanyInfo {
  name: string;
  creditCode: string;
  registerDate: string;
  registerCapital: string;
  legalPerson: string;
  address: string;
}

export interface QuotationFile {
  name: string;
  content: string;
}

export interface SubmitForm {
  company: CompanyInfo;
  contact: ContactInfo;
  preAudit: PreAuditMaterial;
  quotationFiles: QuotationFile[];
}

// 详情和审核组件共用的数据结构
export interface DetailData {
  company: CompanyInfo;
  contact: ContactInfo;
  preAudit: {
    idCard: string; // 文件URL
    businessLicense: string;
    other: string;
  };
  quotationFiles: QuotationFile[];
  auditStatus: 'approved' | 'rejected' | 'pending' | 'waiting';
  auditRemark?: string; // 审核备注
} 