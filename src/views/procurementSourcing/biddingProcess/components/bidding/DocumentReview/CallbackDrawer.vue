<template>
  <el-drawer
    v-model="showVisible"
    :title="title"
    size="60vw"
    append-to-body
    destroy-on-close
    :with-header="true"
    custom-class="bid-custom-drawer"
  >
    <el-form
      :model="form"
      :rules="rules"
      ref="formRef"
      label-width="108px"
      disabled
    >
      <!-- 企业基础资料 -->
      <div class="form-section">
        <div class="label-header">企业基础资料</div>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item
              label="企业名称"
              prop="company.supplierName"
            >
              <el-input
                v-model="form.company.supplierName"
                placeholder="请输入企业名称"
                disabled
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item
              label="统一信用代码"
              prop="company.socialCreditCode"
            >
              <el-input
                v-model="form.company.socialCreditCode"
                placeholder="请输入统一信用代码"
                disabled
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item
              label="成立日期"
              prop="company.establishDate"
            >
              <el-date-picker
                v-model="form.company.establishDate"
                type="date"
                placeholder="请选择成立日期"
                style="width: 100%"
                disabled
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item
              label="注册资本(万)"
              prop="company.registeredCapital"
            >
              <el-input
                v-model="form.company.registeredCapital"
                placeholder="请输入注册资本"
                suffix="元"
                disabled
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item
              label="法人代表"
              prop="company.legalPerson"
            >
              <el-input
                v-model="form.company.legalPerson"
                placeholder="请输入法人代表"
                disabled
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item
              label="注册地址"
              prop="company.registeredAddress"
            >
              <el-input
                v-model="form.company.registeredAddress"
                placeholder="请输入注册地址"
                disabled
              />
            </el-form-item>
          </el-col>
        </el-row>
      </div>
      <!-- 联系人信息 -->
      <div class="form-section">
        <div class="label-header">联系人信息</div>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item
              label="联系人"
              prop="contact.concatName"
            >
              <el-input
                v-model="form.contact.concatName"
                placeholder="请输入联系人"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item
              label="联系电话"
              prop="contact.concatPhone"
            >
              <el-input
                v-model="form.contact.concatPhone"
                placeholder="请输入联系电话"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item
              label="联系邮箱"
              prop="contact.concatEmail"
            >
              <el-input
                v-model="form.contact.concatEmail"
                placeholder="请输入联系邮箱"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </div>

      <!-- 预审材料 -->
      <div class="form-section">
        <div class="label-header">邀请回执信息</div>

        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item
              label="回执信息"
              prop="contact.responseContent"
            >
              <el-input
                v-model="form.contact.responseContent"
                placeholder="请输入回执信息"
                type="textarea"
                :rows="4"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item
              label="回执附件"
              prop="contact.attachment"
            >
              <FileRenderer
                :value="form.contact?.attachment || []"
              />
              <!-- <YunUpload
                :multiple="false"
                :show-file-list="true"
                v-model="form.contact.attachment"
                buttonText="上传文件"
                @change="uploadQuotationAttachment"
              /> -->
            </el-form-item>
          </el-col>
        </el-row>
      </div>
    </el-form>
    <template
      #footer
      v-if="btnType === BTN_O_TYPE.AUDIT_CALL"
    >
      <div class="flex justify-end gap-4 dark:border-gray-700">
        <el-button
          @click.stop="handleAudit({ pass: false })"
          type="danger"
          :loading="isLoading"
          >审批不通过</el-button
        >
        <el-button
          @click.stop="handleAudit({ pass: true })"
          type="primary"
          :loading="isLoading"
          >审批通过</el-button
        >
      </div>
    </template>
  </el-drawer>
</template>

<script setup lang="ts">
import { ref, watch, defineProps, defineEmits, onMounted, computed } from 'vue';
import { inviteDetail, review, getBasicInfo } from '@/api/purchasing/bid';
import { ElMessage, ElMessageBox } from 'yun-design';
// import YunUpload from '/@/components/YunUpload/index.vue';
import { useBiddingStore } from '@/views/procurementSourcing/biddingProcess/stores';
import { BTN_O_TYPE } from '@/views/procurementSourcing/biddingProcess/constants/bid';
import FileRenderer from '/@/components/Detail/renderers/FileRenderer.vue';


const biddingStore = useBiddingStore();
const noticeId = computed(() => biddingStore?.noticeId);
const props = defineProps<{ visible: boolean; row: any; btnType: any }>();
const emit = defineEmits(['refresh', 'update:visible']);
const showVisible = computed({
  get: () => props.visible,
  set: (v: boolean) => emit('update:visible', v),
});
watch(() => showVisible.value, initData);

const isLoading = ref(false);
const formRef = ref();
const initForm = () => ({
  // 供应商
  company: {
    supplierName: '',
    registeredCapital: '',
    socialCreditCode: '',
    establishDate: '',
    legalPerson: '',
    registeredAddress: '',
  },
  // 联系人
  contact: {
    concatName: '',
    concatPhone: '',
    concatEmail: '',
    responseContent: '',
    attachmentName: '',
    attachmentUrl: '',
    attachment: [],
  },
});
const form = ref<any>(initForm());

const rules = computed(() => {
  return {
    'company.supplierName': [{ required: true, message: '请输入企业名称', trigger: 'blur' }],
    'company.registeredCapital': [{ required: true, message: '请输入注册资本', trigger: 'blur' }],
    'company.legalPerson': [{ required: true, message: '请输入法人代表', trigger: 'blur' }],
    'company.registeredAddress': [{ required: true, message: '请输入注册地址', trigger: 'blur' }],
    'contact.concatName': [{ required: true, message: '请输入联系人', trigger: 'blur' }],
    'contact.concatPhone': [{ required: true, message: '请输入联系电话', trigger: 'blur' }],
  };
});

function uploadQuotationAttachment(v: any) {
  const file = v[v.length - 1] || {};
  form.value.contact.attachmentUrl = file?.url;
  form.value.contact.attachmentName = file?.name;
  setTimeout(() => {
    form.value.contact.attachment = file?.url && file?.name ? [file] : [];
  }, 150);
}

const title = computed(() => (props?.btnType === BTN_O_TYPE.AUDIT_CALL ? '审核回执' : '查看回执'));
function handleAudit(extParams = {}) {
  ElMessageBox.confirm('确定执行该操作？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  })
    .then(async () => {
      isLoading.value = true;
      try {
        await review({
          noticeId: noticeId.value,
          sectionId: props.row.sectionId,
          tenantSupplierId: props.row?.tenantSupplierId || null,
          type: 3,
          ...extParams,
        });
        ElMessage.success('操作成功');
        emit('refresh');
        showVisible.value = false;
      } catch (error) {
        // ElMessage.error('操作失败');
      } finally {
        isLoading.value = false;
      }
    })
    .catch(() => {});
}

// 初始化供应商数据
async function initSupplierData() {
  try {
    const tenantSupplierCode = props.row.tenantSupplierCode;
    const sInfo = (await getBasicInfo(tenantSupplierCode)) || {};
    const { supplierName, registeredCapital, socialCreditCode, legalPerson, establishDate, registeredAddress } = sInfo?.data || {};
    form.value.company = {
      supplierName,
      registeredCapital,
      socialCreditCode,
      legalPerson,
      establishDate,
      registeredAddress,
    };
    // eslint-disable-next-line no-empty
  } catch (error) {}
}

// 初始化供应商数据
async function initCallbackData() {
  try {
    const res = await inviteDetail({
      noticeId: noticeId.value,
      sectionId: props.row.sectionId,
      tenantSupplierId: props.row.tenantSupplierId,
    });
    const data = res?.data || {};
    const { concatEmail, concatName, concatPhone, responseContent, attachmentList } = data;
    form.value.contact = {
      concatEmail,
      concatName,
      concatPhone,
      responseContent,
      attachment: (attachmentList || [])?.map((item: any) => ({
        url: item.filePath,
        name: item.fileName,
      }))?.slice(0, 1),
    };
    // eslint-disable-next-line no-empty
  } catch (error) {}
}

function initData() {
  if (showVisible.value) {
    form.value = initForm();
    initSupplierData();
    initCallbackData();
  }
}

onMounted(async () => {});
</script>

<style scoped lang="scss">
@import '../../../styles/collapse-panel.scss';
</style>
