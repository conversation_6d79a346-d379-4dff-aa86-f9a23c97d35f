import { ref, computed } from 'vue';
import { useBiddingStore } from '@/views/procurementSourcing/biddingProcess/stores';
import {
  REGISTER_REVIEWED_STATUS_TAGS,
  REGISTER_TYPE_LABELS,
  REGISTER_REVIEWED_STATUS_LABELS,
  REGISTER_REVIEWED_STATUS_OPTIONS,
  INVITE_STATUS_CALL_STATUS_LABELS,
  INVITE_STATUS_CALL_STATUS_TAGS,
  INVITE_STATUS_FEEDBACK_OPTIONS,
  INVITE_STATUS_CALL_STATUS_OPTIONS,
  INVITE_STATUS,
} from '@/views/procurementSourcing/biddingProcess/constants/bid';
import { objectToOptions, isEmptyValue } from '@/views/procurementSourcing/biddingProcess/utils/bid';
export function useColumns() {
  const biddingStore = useBiddingStore();
  const isPublic = computed(() => biddingStore?.isPublic);
  const isZJWT = computed(() => biddingStore?.isZJWT);

  const searchFields = computed(() => {
    const commonFields = !isZJWT?.value ? [
      {
        label: '供应商名称',
        prop: 'supplierName',
        type: 'input',
        componentAttrs: {
          clearable: true,
        },
      },
    ] : [];
    return isPublic.value
      ? [
          {
            label: '审核状态',
            prop: 'registerStatus',
            type: 'select',
            componentAttrs: {
              // filterable: true,
              // multiple: true,
              clearable: true,
            },
            options: REGISTER_REVIEWED_STATUS_OPTIONS,
          },
        ]
      : [
          ...commonFields,
          {
            label: '邀请反馈',
            prop: 'feedbackStatus',
            type: 'select',
            options: INVITE_STATUS_FEEDBACK_OPTIONS,
            componentAttrs: {
              clearable: true,
            },
          },
          {
            label: '回执审批状态',
            prop: 'callbackStatus',
            width: '140px',
            type: 'select',
            options: INVITE_STATUS_CALL_STATUS_OPTIONS,
            componentAttrs: {
              clearable: true,
            },
          },
        ];
  });
  const searchData = ref({});
  const columns = computed(() => {
    const commonColumns = [
      {
        label: '序号',
        type: 'index',
        width: '60px',
      },
      {
        label: '供应商名称',
        prop: 'supplierName',
      },
      ...(!isZJWT.value
        ? [
            {
              label: '参与方式',
              prop: 'responseType',
              enums: objectToOptions(REGISTER_TYPE_LABELS),
            },
            {
              label: '参与时间',
              prop: 'responseTime',
              width: '170px',
            },
          ]
        : []),
      {
        label: '联系人',
        prop: 'contactPerson',
      },
      {
        label: '联系方式',
        prop: 'contactPhone',
      },
    ];

    return isPublic.value
      ? [
          ...commonColumns,
          {
            label: '资料审核状态',
            prop: 'registerStatus',
            render: ({ row }: any) => {
              const registerStatus = row?.registerStatus;
              // @ts-ignore
              const tagName = REGISTER_REVIEWED_STATUS_LABELS[registerStatus];
              // @ts-ignore
              const tagType = REGISTER_REVIEWED_STATUS_TAGS[registerStatus];
              // @ts-ignore
              return tagName ? <el-tag type={tagType}>{tagName}</el-tag> : '-';
            },
          },
          {
            // TODO
            label: '报名资料',
            prop: 'regInfo',
            fixed: 'right',
            width: '180px',
          },
        ]
      : [
          ...commonColumns,
          {
            label: '邀请时间',
            prop: 'inviteTime',
            width: '170px',
          },
          {
            label: '反馈/回执时间',
            prop: 'responseTime',
            width: '170px',
          },
          ...(!isZJWT.value
            ? []
            : [
                {
                  label: '邀请反馈',
                  prop: 'inviteStatus1',
                  render: ({ row }: any) => {
                    const inviteStatus = row?.inviteStatus;
                    // @ts-ignore
                    const tagName = [INVITE_STATUS.WITHDRAWN, INVITE_STATUS.PENDING]?.includes(inviteStatus) ? '待回执' : '已回执';
                    // @ts-ignore
                    const tagType = [INVITE_STATUS.WITHDRAWN, INVITE_STATUS.PENDING]?.includes(inviteStatus) ? 'warning' : 'success';
                    // @ts-ignore
                    return !isEmptyValue(inviteStatus) ? <el-tag type={tagType}>{tagName}</el-tag> : '-';
                  },
                },
              ]),
          {
            label: '回执审批状态',
            prop: 'inviteStatus',
            render: ({ row }: any) => {
              const inviteStatus = row?.inviteStatus;
              // @ts-ignore
              const tagName = INVITE_STATUS_CALL_STATUS_LABELS[inviteStatus];
              // @ts-ignore
              const tagType = INVITE_STATUS_CALL_STATUS_TAGS[inviteStatus];
              // @ts-ignore
              return tagName ? <el-tag type={tagType}>{tagName}</el-tag> : '-';
            },
          },
          {
            label: '操作',
            prop: 'action',
            width: '180px',
            fixed: 'right',
          },
        ];
  });

  return {
    columns,
    searchFields,
    searchData,
  };
}
