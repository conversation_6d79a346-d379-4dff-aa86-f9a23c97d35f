<template>
  <el-drawer
    v-model="showVisible"
    :title="title"
    size="60vw"
    append-to-body
    destroy-on-close
    :with-header="true"
    custom-class="bid-custom-drawer"
  >
    <div v-loading="loading">
      <!-- 企业基础资料 -->
      <div class="form-section">
        <div class="label-header">企业基础资料</div>
        <el-descriptions :column="2">
          <el-descriptions-item label="企业名称">{{ detail?.supplierName }}</el-descriptions-item>
          <el-descriptions-item label="注册资本(万)">{{ detail?.registeredCapital }}</el-descriptions-item>
          <el-descriptions-item label="统一信用代码">{{ detail?.socialCreditCode }}</el-descriptions-item>
          <el-descriptions-item label="法人代表">{{ detail?.legalPerson }}</el-descriptions-item>
          <el-descriptions-item label="成立日期">{{ detail?.establishDate }}</el-descriptions-item>
          <el-descriptions-item label="注册地址">{{ detail?.registeredAddress }}</el-descriptions-item>
        </el-descriptions>
      </div>
      <!-- 联系人信息 -->
      <div class="form-section">
        <div class="label-header">联系人信息</div>
        <el-descriptions :column="2">
          <el-descriptions-item label="联系人">{{ detail?.contactPerson }}</el-descriptions-item>
          <el-descriptions-item label="联系电话">{{ detail?.contactPhone }}</el-descriptions-item>
          <el-descriptions-item label="联系邮箱">{{ detail?.contactEmail }}</el-descriptions-item>
        </el-descriptions>
      </div>
      <!-- 预审材料 -->
      <div class="form-section">
        <div class="label-header">预审材料</div>
        <el-descriptions :column="1">
          <div
            v-for="(q, index) in detail?.qualificationList || []"
            :key="index"
          >
            <el-descriptions-item :label="q.label">
              <FileRenderer :value="q.value || []" />
            </el-descriptions-item>
          </div>
        </el-descriptions>
      </div>
      <!-- 报价响应文件 -->
      <div class="form-section">
        <div class="label-header">报名响应文件</div>
        <el-table
          :data="detail?.conditionList"
          style="width: 100%"
        >
          <el-table-column
            label="序号"
            type="index"
            width="60"
          />
          <el-table-column
            label="条件名称"
            prop="label"
          />
          <el-table-column
            label="条件内容"
            prop="value"
          />
          <el-table-column
            label="状态"
            prop="status"
          >
            <template #default="{ row }">
              <el-tag :type="row.status ? 'success' : 'danger'">{{ row.status ? '已响应' : '未响应' }}</el-tag>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>
    <template
      #footer
      v-if="btnType === BTN_O_TYPE.AUDIT"
    >
      <div class="flex justify-end gap-4">
        <el-button
          @click.stop="handleAudit({ pass: false })"
          type="danger"
          :loading="isLoading"
          >审核不通过</el-button
        >
        <el-button
          @click.stop="handleAudit({ pass: true })"
          type="primary"
          :loading="isLoading"
          >审核通过</el-button
        >
      </div>
    </template>
  </el-drawer>
</template>

<script setup lang="ts">
import { ref, defineProps, defineEmits, computed, watch } from 'vue';
import { review, getRegisteredInfo, getBasicInfo } from '@/api/purchasing/bid';
import { ElMessage, ElMessageBox } from 'yun-design';
import { useBiddingStore } from '@/views/procurementSourcing/biddingProcess/stores';
import { BTN_O_TYPE } from '@/views/procurementSourcing/biddingProcess/constants/bid';
import { jsonStringToArray } from '@/views/procurementSourcing/biddingProcess/utils/bid';
import FileRenderer from '/@/components/Detail/renderers/FileRenderer.vue';

const biddingStore = useBiddingStore();
const noticeId = computed(() => biddingStore?.noticeId);

const props = defineProps<{ visible: boolean; row: any; btnType: any }>();
const emit = defineEmits(['update:visible', 'refresh']);
const isLoading = ref(false);
const loading = ref(false);
const detail = ref<any>({});
const showVisible = computed({
  get: () => props.visible,
  set: (v: boolean) => emit('update:visible', v),
});
watch(
  () => showVisible.value,
  (val) => {
    if (val) {
      loadDetails();
    }
  }
);

async function loadDetails() {
  loading.value = true;
  try {
    const res = await getRegisteredInfo({
      noticeId: noticeId.value,
      sectionId: props.row.sectionId,
      tenantSupplierId: props.row?.tenantSupplierId || null,
    });
    const { contactEmail, contactPerson, contactPhone, tenantSupplierCode, srmTenderSupplierBidderResponseList } = res?.data || {};
    const sInfo = (await getBasicInfo(tenantSupplierCode)) || {};
    const { supplierName, registeredCapital, socialCreditCode, legalPerson, establishDate, registeredAddress } = sInfo?.data || {};

    const qFile = srmTenderSupplierBidderResponseList?.filter(
      (item) => item?.requirementType === 'QUALIFICATION' && item?.sectionId === props.row.sectionId
    );
    const cFile = srmTenderSupplierBidderResponseList?.filter(
      (item) => item?.requirementType === 'CONDITION' && item?.sectionId === props.row.sectionId
    );

    detail.value = {
      contactEmail,
      contactPerson,
      contactPhone,

      supplierName,
      registeredCapital,
      socialCreditCode,
      legalPerson,
      establishDate,
      registeredAddress,

      qualificationList: qFile?.map((item) => {
        return {
          label: item?.responseName,
          value: jsonStringToArray(item?.responseContent || '[]'),
        };
      }),
      conditionList: cFile?.map((item) => {
        const value = item?.srmTenderRequirement?.requirementContent || '';
        return {
          label: item?.responseName,
          value: value,
          status: !!(value !== '' && value !== null && value !== undefined && value !== 'null' && value !== 'undefined'),
        };
      }),
    };
    // eslint-disable-next-line no-empty
  } catch (error) {}
  loading.value = false;
}

const title = computed(() => (props?.btnType === BTN_O_TYPE.AUDIT ? '审核' : '查看'));
function handleAudit(extParams = {}) {
  ElMessageBox.confirm('确定执行该操作？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  })
    .then(async () => {
      isLoading.value = true;
      try {
        await review({
          noticeId: noticeId.value,
          sectionId: props.row.sectionId,
          tenantSupplierId: props.row?.tenantSupplierId || null,
          type: 1,
          ...extParams,
        });
        ElMessage.success('操作成功');
        emit('refresh');
        showVisible.value = false;
      } catch (error) {
        // ElMessage.error('操作失败');
      } finally {
        isLoading.value = false;
      }
    })
    .catch(() => {});
}
</script>

<style scoped lang="scss">
@import '../../../styles/collapse-panel.scss';
</style>
