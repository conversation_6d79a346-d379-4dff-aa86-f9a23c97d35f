// 导出标书费状态枚举
export const BID_FEE_STATUS = {
  WITHDRAWN: 'WITHDRAWN',
  UNPAID: 'UNPAID',
  PAID: 'PAID',
  VERIFIED: 'VERIFIED',
  REJECTED: 'REJECTED',
} as const;

// 导出标书费审核状态
export const BID_FEE_STATUS_LABELS = {
  [BID_FEE_STATUS.VERIFIED]: '审批通过',
  [BID_FEE_STATUS.REJECTED]: '审批不通过',
  [BID_FEE_STATUS.PAID]: '待审批',
} as const;
// 导出标书费审核状态标签样式
export const BID_FEE_STATUS_TAGS = {
  [BID_FEE_STATUS.VERIFIED]: 'success',
  [BID_FEE_STATUS.REJECTED]: 'danger',
  [BID_FEE_STATUS.PAID]: 'warning',
} as const;

// 供应商缴纳状态
export const BID_FEE_SUPPLIER_STATUS = {
  [BID_FEE_STATUS.UNPAID]: '未缴纳',
  [BID_FEE_STATUS.WITHDRAWN]: '未缴纳',

  [BID_FEE_STATUS.PAID]: '已缴纳',
  [BID_FEE_STATUS.VERIFIED]: '已缴纳',
  [BID_FEE_STATUS.REJECTED]: '已缴纳',
} as const;
// 供应商缴纳状态 标签样式
export const BID_FEE_SUPPLIER_TAGS = {
  [BID_FEE_STATUS.UNPAID]: 'warning',
  [BID_FEE_STATUS.WITHDRAWN]: 'warning',

  [BID_FEE_STATUS.PAID]: 'success',
  [BID_FEE_STATUS.VERIFIED]: 'success',
  [BID_FEE_STATUS.REJECTED]: 'success',
} as const;
// 供应商缴纳状态 下拉枚举
export const BID_FEE_SUPPLIER_OPTIONS = [
  { label: '已缴纳', value: 'DONE', enums: [BID_FEE_STATUS.PAID, BID_FEE_STATUS.VERIFIED, BID_FEE_STATUS.REJECTED] },
  { label: '未缴纳', value: 'TODO', enums: [BID_FEE_STATUS.UNPAID, BID_FEE_STATUS.WITHDRAWN] },
] as const;
// 供应商缴纳状态 审核状态 下拉枚举
export const BID_FEE_SUPPLIER_AUDIT_OPTIONS = [
  { label: BID_FEE_STATUS_LABELS[BID_FEE_STATUS.REJECTED], value: BID_FEE_STATUS.REJECTED },
  { label: BID_FEE_STATUS_LABELS[BID_FEE_STATUS.VERIFIED], value: BID_FEE_STATUS.VERIFIED },
  { label: BID_FEE_STATUS_LABELS[BID_FEE_STATUS.PAID], value: BID_FEE_STATUS.PAID },
] as const;
