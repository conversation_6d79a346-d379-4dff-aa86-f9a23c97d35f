<template>
  <div class="document-review-container">
    <StageTabs
      v-model="activeTabIndex"
      @update:sectionId="handleSectionChange"
    />
    <div class="document-review-content need-hide-table-card editable-table">
      <yun-pro-table
        ref="proTableRef"
        v-model:pagination="pagination"
        v-model:filter-data="filterTableData"
        v-model:searchData="searchData"
        :table-columns="columns"
        :search-fields="searchFields"
        :auto-height="true"
        :remote-method="remoteMethod"
        :table-props="tablePropsObj"
        :search-props="searchPropsObj"
        layout="whole"
        @selectedChange="selectedChange"
        :default-fetch="false"
      >
        <template #t_action="{ row }">
          <div
            class="flex gap-2"
            v-if="BID_FEE_STATUS.PAID === row?.tenderFeeStatus"
          >
            <el-link
              type="primary"
              @click="handleAudit({ pass: true, sectionId: row?.sectionId, tenantSupplierId: row?.tenantSupplierId })"
              >审核通过</el-link
            >
            <el-link
              type="danger"
              @click="handleAudit({ pass: false, sectionId: row?.sectionId, tenantSupplierId: row?.tenantSupplierId })"
              >审核不通过</el-link
            >
          </div>
        </template>
      </yun-pro-table>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onBeforeMount } from 'vue';
import StageTabs from '@/views/procurementSourcing/biddingProcess/components/bidding/StageTabs.vue';
import { getRegisteredSupplierList } from '@/api/purchasing/bid';
import { review } from '@/api/purchasing/bid';
import { ElMessage, ElMessageBox } from 'yun-design';
import { useProTable } from '@ylz-use/core';
import { useColumns } from './hooks/useColumns';
import { BID_FEE_SUPPLIER_OPTIONS, BID_FEE_STATUS } from './const';
import { jsonStringToObject } from '@/views/procurementSourcing/biddingProcess/utils/bid';

import { useBiddingStore } from '@/views/procurementSourcing/biddingProcess/stores';

const biddingStore = useBiddingStore();
const noticeId = computed(() => biddingStore?.noticeId);

const currentSectionId = ref('');
const isLoading = ref(false);

const activeTabIndex = ref(0);

const pagination = ref({});
const { columns, searchFields, searchData } = useColumns();
const { remoteMethod, tableProps, proTableRef, filterTableData, reLoad } = useProTable({
  apiFn: getRegisteredSupplierList,
  responseHandler(result: any) {
    return (result.data || [])?.map((item: any) => {
      return {
        ...item,
        tenderFeeRespContent: jsonStringToObject(item?.tenderFeeRespContent || '[]')?.[0] || {},
      };
    });
  },
  customTotalHandler(data: any) {
    return data?.data?.total;
  },
  paramsHandler(params: any) {
    const { supplierFeeStatus, supplierAuditStatus, ...rest } = params || {};
    const ext: any = {};
    const temp = [];
    if (supplierFeeStatus) {
      const enums = BID_FEE_SUPPLIER_OPTIONS.find((item: any) => item.value === supplierFeeStatus)?.enums || [];
      temp.push(...enums);
    }
    if (supplierAuditStatus) {
      temp.push(supplierAuditStatus);
    }
    if (temp.length > 0) {
      ext.tenderFeeStatusList = [...new Set(temp)];
    }
    return {
      sectionId: currentSectionId.value,
      noticeId: noticeId.value,
      ...rest,
      ...ext,
    };
  },
  // querysHandler() {
  //   const querysData = {
  //     current: pagination.value.page,
  //     size: pagination.value.size || pagination.value.pageSize,
  //   };
  //   return querysData;
  // },
  plugins: {
    config: {
      columns: columns.value,
      searchFields: searchFields.value,
    },
    list: ['SEARCH_PLUS'],
  },
});

function selectedChange() {
  // eslint-disable-next-line no-console
  console.log('selectedChange');
}

const searchPropsObj = computed(() => ({
  showCollapseBtn: false,
  collapse: false,
  showResetBtn: false,
  showSubmitBtn: false,
  showOperation: false,
  quantity: 3,
}));

const tablePropsObj = computed(() => ({
  ...tableProps.value,
  stripe: false,
  border: false,
  headerCellStyle: {
    backgroundColor: '#f5f7fa',
    color: '#303133',
    fontWeight: 'bold',
    height: '40px',
    borderColor: '#EBEEF5',
  },
  cellStyle: {
    padding: '0',
    height: '40px',
    'vertical-align': 'middle',
    borderColor: '#EBEEF5',
  },
  rowStyle: {
    height: '40px',
    borderColor: '#EBEEF5',
  },
  rowHeight: 40,
}));

// eslint-disable-next-line @typescript-eslint/no-unused-vars
// @ts-ignore
function handleAudit(row = {}) {
  ElMessageBox.confirm('确定执行该操作？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  })
    .then(async () => {
      isLoading.value = true;
      try {
        await review({
          noticeId: noticeId.value,
          type: 4,
          ...row,
        });
        ElMessage.success('操作成功');
        reLoad();
      } catch (error) {
        // ElMessage.error('操作失败');
      } finally {
        isLoading.value = false;
      }
    })
    .catch(() => {});
}

// 标段变化事件
async function handleSectionChange(sectionId: string) {
  currentSectionId.value = sectionId;
  setTimeout(async () => {
    if (currentSectionId.value) {
      reLoad();
    }
  }, 300);
}
onMounted(async () => {});
onBeforeMount(async () => {});
</script>

<style scoped lang="scss">
.document-review-container {
  flex: 1;
  padding: 20px;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
  // gap: 16px;
  height: 100%;

  .document-review-content {
    // margin-top: 16px;
    flex: 1;
  }
}

</style>
