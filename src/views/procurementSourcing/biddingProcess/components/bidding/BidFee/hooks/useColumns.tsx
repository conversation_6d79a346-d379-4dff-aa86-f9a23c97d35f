import { ref, computed } from 'vue';
import FileRenderer from '/@/components/Detail/renderers/FileRenderer.vue';
import {
  BID_FEE_SUPPLIER_TAGS,
  BID_FEE_SUPPLIER_OPTIONS,
  BID_FEE_SUPPLIER_AUDIT_OPTIONS,
  BID_FEE_SUPPLIER_STATUS,
  BID_FEE_STATUS_LABELS,
  BID_FEE_STATUS_TAGS,
} from '../const';
export function useColumns() {
  const searchFields = ref([
    {
      label: '供应商名称',
      prop: 'supplierName',
      type: 'input',
      componentAttrs: {
        clearable: true,
      },
    },
    {
      label: '供应商缴纳状态',
      prop: 'supplierFeeStatus',
      type: 'select',
      componentAttrs: {
        // filterable: true,
        // multiple: true,
        clearable: true,
      },
      options: BID_FEE_SUPPLIER_OPTIONS,
    },
    {
      label: '审核状态',
      prop: 'supplierAuditStatus',
      type: 'select',
      componentAttrs: {
        // filterable: true,
        // multiple: true,
        clearable: true,
      },
      options: BID_FEE_SUPPLIER_AUDIT_OPTIONS,
    },
  ]);
  const searchData = ref({});
  const columns = computed(() => {
    return [
      {
        label: '序号',
        type: 'index',
        width: '60px',
      },
      {
        prop: 'supplierName',
        label: '供应商名称',
      },
      {
        prop: 'contactPerson',
        label: '联系人',
      },
      {
        prop: 'contactPhone',
        label: '联系电话',
      },
      {
        label: '供应商缴纳状态',
        prop: 'expertExtractionStatus',
        enums: [],
        // render: ({ row }: any) => {
        //   const tenderFeeStatus = row?.tenderFeeStatus;
        //   return tenderFeeStatus ? BID_FEE_SUPPLIER_STATUS[tenderFeeStatus as keyof typeof BID_FEE_SUPPLIER_STATUS] : '-';
        // },
        render: ({ row }: any) => {
          const tenderFeeStatus = row?.tenderFeeStatus;
          // @ts-ignore
          const tagName = BID_FEE_SUPPLIER_STATUS[tenderFeeStatus];
          // @ts-ignore
          const tagType = BID_FEE_SUPPLIER_TAGS[tenderFeeStatus];
          // @ts-ignore
          return tagName ? <el-tag type={tagType}>{tagName}</el-tag> : '-';
        },
      },
      {
        label: '附件上传时间',
        prop: 'tenderFeeRespContent.uploadTime',
        width: '180px',
      },
      {
        label: '附件',
        prop: 'tenderFeeRespContent?.url',
        render: ({ row }: any) => {
          return row?.tenderFeeRespContent?.url ? <FileRenderer value={[row?.tenderFeeRespContent]} /> : '--';
        },
      },
      {
        label: '标书费审核状态',
        prop: 'tenderFeeStatus',
        enums: [],
        // render: ({ row }: any) => {
        //   const tenderFeeStatus = row?.tenderFeeStatus;
        //   return tenderFeeStatus ? BID_FEE_STATUS_LABELS[tenderFeeStatus as keyof typeof BID_FEE_STATUS_LABELS] : '-';
        // },
        render: ({ row }: any) => {
          const tenderFeeStatus = row?.tenderFeeStatus;
          // @ts-ignore
          const tagName = BID_FEE_STATUS_LABELS[tenderFeeStatus];
          // @ts-ignore
          const tagType = BID_FEE_STATUS_TAGS[tenderFeeStatus];
          // @ts-ignore
          return tagName ? <el-tag type={tagType}>{tagName}</el-tag> : '-';
        },
      },
      {
        label: '操作',
        prop: 'action',
        isSlot: true,
        width: '160px',
        fixed: 'right',
      },
    ];
  });

  return {
    columns,
    searchFields,
    searchData,
  };
}
