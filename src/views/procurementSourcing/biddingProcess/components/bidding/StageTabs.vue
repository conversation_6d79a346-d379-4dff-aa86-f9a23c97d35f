<template>
  <div v-if="sectionsData.length > 1" class="relative stage-tabs-container" :class="{'none-section': !isShowSection}">
    <div
      ref="containerRef"
      class="overflow-x-auto scrollbar-hide"
      @scroll="handleScroll"
    >
      <div
        ref="tabsRef"
        class="flex whitespace-nowrap min-w-max container"
      >
        <div
          v-for="(tab, idx) in tabs"
          :key="tab.key"
          class="tab-item"
          :class="{ active: idx === activeIndex }"
          @click="selectTab(idx)"
        >
          {{ tab.label }}
        </div>
      </div>
    </div>
    <div
      v-if="showRightArrow"
      class="scroll-icon absolute right-0 top-1/2 -translate-y-1/2 bg-gradient-to-l from-white/90 dark:from-gray-800/90 to-transparent p-1 cursor-pointer z-10"
    >
      <div
        class="icon-wrp"
        @click="scrollRight"
      >
        <el-icon><ArrowRight style="color: #fff; font-size: 10px" /></el-icon>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, watchEffect, nextTick, watch, computed } from 'vue';
import { ArrowRight } from '@element-plus/icons-vue';
import { getProjectSections } from '../../api/bidding';
import type { SectionInfo } from '../../types/bidding';
import { useBiddingStore } from '@/views/procurementSourcing/biddingProcess/stores';
import { EXPORT_MODULE_TYPE } from '@/views/procurementSourcing/biddingProcess/constants/bid';

const biddingStore = useBiddingStore();
const projectId = computed(() => biddingStore?.projectId);
const isShowSection = computed(() => biddingStore?.isShowSection);
const needBondSectionList = computed(() => biddingStore?.needBondSectionList);

interface Tab {
  key: string;
  label: string;
  id: number;
}

const props = defineProps<{
	modelValue: number;
	source?: string;
}>();
const emit = defineEmits(['update:modelValue', 'update:sectionId']);

const containerRef = ref<HTMLDivElement | null>(null);
const tabsRef = ref<HTMLDivElement | null>(null);
const showRightArrow = ref(false);
const activeIndex = ref(props.modelValue);
const tabs = ref<Tab[]>([]);
const sectionsData = ref<Tab[]>([]);
const loading = ref(false);

function checkOverflow() {
  const container = containerRef.value;
  const tabsContainer = tabsRef.value;
  if (!container || !tabsContainer) return;
  showRightArrow.value =
    tabsContainer.scrollWidth > container.clientWidth && container.scrollLeft + container.clientWidth < tabsContainer.scrollWidth - 2;
}

function handleScroll() {
  checkOverflow();
}

function scrollRight() {
  const container = containerRef.value;
  if (!container) return;
  container.scrollBy({ left: 120, behavior: 'smooth' });
}

function selectTab(idx: number) {
  emit('update:modelValue', idx);
  activeIndex.value = idx;
}

// 获取标段数据
async function fetchSections() {
  loading.value = true;
  try {
    const response = await getProjectSections(projectId.value);
    if (response.code === 0 && response.data) {
      const sections: SectionInfo[] = response.data;
      sectionsData.value = sections;
      tabs.value = sections.map((section, index) => ({
        key: section.id.toString(),
        label: `标段${String(index + 1).padStart(2, '0')}: ${section.sectionName}`,
        id: section.id,
      }));

			// 保证金特殊逻辑
			if (props.source === EXPORT_MODULE_TYPE.DEPOSIT) {
				tabs.value = tabs.value.filter((tab) => needBondSectionList.value?.includes(tab.id));
			}

			// 如果有数据且当前选中索引有效，emit第一个标段ID
			if (tabs.value.length > 0 && activeIndex.value < tabs.value.length) {
				emit('update:sectionId', tabs.value[activeIndex.value].id);
			}
		}
	} catch (error) {
		console.error('获取标段数据失败:', error);
	} finally {
		loading.value = false;
	}
}

// 监听modelValue变化
watch(
  () => props.modelValue,
  (newVal) => {
    activeIndex.value = newVal;
    if (tabs.value[newVal]) {
      emit('update:sectionId', tabs.value[newVal].id);
    }
  }
);

watch(
  () => projectId.value,
  (newVal) => {
    if (newVal) {
      fetchSections();
    }
  },
  {
    immediate: true,
  }
);

onMounted(() => {
  nextTick(checkOverflow);
  window.addEventListener('resize', checkOverflow);
});

watchEffect(() => {
  nextTick(checkOverflow);
});
</script>

<style scoped lang="scss">
.none-section {
  height: 0 !important;
  overflow: hidden !important;
  padding: 0 !important;
  margin: 0 !important;
}
.scrollbar-hide::-webkit-scrollbar {
  display: none;
}
.scrollbar-hide {
  -ms-overflow-style: none;
  scrollbar-width: none;
}

.container {
  gap: 12px;
  .tab-item {
    display: flex;
    padding: 1px 9px;
    justify-content: center;
    align-items: center;
    gap: 6px;
    border-radius: var(--Radius-border-radius-small, 2px);
    background: var(--Color-Fill-fill-color, #f0f2f5);
    color: var(--Color-Text-text-color-primary, #1d2129);
    text-align: center;
    font-family: 'PingFang SC';
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px;
    border: 1px solid #f0f2f5;
    cursor: pointer;

    &:hover {
      color: var(--Color-Primary-color-primary, #0069ff);
      font-weight: 500;
    }

    &.active {
      color: var(--Color-Primary-color-primary, #0069ff);
      background: #fff;
      border: 1px solid #0069ff;
      font-weight: 500;
    }
  }
}

.scroll-icon {
  width: 40px;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: flex-end;

  .icon-wrp {
    width: 14px;
    height: 14px;
    background: #7e8694;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
  }
}

.relative {
  // margin-bottom: 16px;
}
</style>
