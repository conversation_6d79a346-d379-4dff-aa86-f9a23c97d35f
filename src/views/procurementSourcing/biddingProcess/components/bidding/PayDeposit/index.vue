<template>
  <div class="flex-1 need-hide-table-card editable-table PayDepositWrapper">
    <div class="batch-upload-files">
      <AYNUpload
        :limit="1"
        :disabled="!allUploadDisabled"
        @change="(fileList: any) => uploadDepositAttachment('ALL', fileList, null)"
        displayType="outer"
        buttonText="一键上传保证金附件"
      >
      </AYNUpload>
    </div>
    <yun-pro-table
      ref="proTableRef"
      v-model:pagination="pagination"
      v-model:searchData="searchData"
      :table-columns="columns"
      :search-fields="searchFields"
      :auto-height="true"
      :remote-method="remoteMethod"
      :table-props="tablePropsObj"
      :search-props="searchPropsObj"
      layout="whole"
      :default-fetch="false"
    >
      <!-- <template #t_tableHeaderLeft></template>
        <template #t_tableHeaderRight> </template> -->
      <template #t_action="{ row }">
        <el-space>
          <AYNUpload
            :limit="1"
            @change="(fileList: any) => uploadDepositAttachment('SINGLE', fileList, row)"
            displayType="inner"
            buttonText="上传保证金附件"
            v-if="row.depositStatus !== DEPOSIT_REVIEWED_STATUS.VERIFIED"
          >
          </AYNUpload>
          <el-button
            type="text"
            v-if="row.depositStatus === DEPOSIT_REVIEWED_STATUS.PAID"
            @click="handleWithdraw(row)"
          >
            撤回审核
          </el-button>
        </el-space>
      </template>
    </yun-pro-table>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { DEPOSIT_REVIEWED_STATUS } from '@/views/procurementSourcing/biddingProcess/constants/bid';
import { payDeposit, withdrawn } from '@/api/purchasing/bid';
import { ElMessage, ElMessageBox } from 'yun-design';
import { useBiddingStore } from '@/views/procurementSourcing/biddingProcess/stores';
import { useProTable } from '@ylz-use/core';
import { useColumns } from './hooks/useColumns';
import { jsonStringToObject } from '@/views/procurementSourcing/biddingProcess/utils/bid';
import { getSelectionRegisterInfo } from '@/api/purchasing/bid';
import AYNUpload from '/@/components/AYNUpload/index.vue';

const pagination = ref({});
const { columns, searchFields, searchData } = useColumns();

const filterTableData = ref([]);
const biddingStore = useBiddingStore();
const noticeId = computed(() => biddingStore?.noticeId);
const needBondSectionList = computed(() => biddingStore?.needBondSectionList);
const commonFilterFn = (item: any) => item.depositStatus !== DEPOSIT_REVIEWED_STATUS.VERIFIED;
const loading = ref(false);

const allUploadDisabled = computed(() => filterTableData.value.some(commonFilterFn));

const { remoteMethod, tableProps, proTableRef, reLoad } = useProTable({
  apiFn: getSelectionRegisterInfo,
  responseHandler(result: any) {
    const res = result.data || [];
    res?.forEach((item: any) => {
      const depositRespContent = item?.depositRespContent || '{}';
      const depositContent = item?.depositContent || '{}';
      item.depositRespContent = jsonStringToObject(depositRespContent);
      item.depositContent = jsonStringToObject(depositContent);
    });
    const filterRes = res?.filter((item: any) => needBondSectionList.value?.includes(item?.sectionId));
    filterTableData.value = filterRes;
    return filterRes;
  },
  customTotalHandler(data: any) {
    return data?.data?.total;
  },
  paramsHandler(params: any) {
    const param = {
      noticeId: noticeId.value,
      ...params,
    };
    return param;
  },
  // querysHandler(querys) {
  //   const querysData = {
  //     ...querys,
  //     current: pagination.value.page,
  //     size: pagination.value.size || pagination.value.pageSize,
  //   };
  //   return querysData;
  // },
  plugins: {
    config: {
      columns: columns.value,
      searchFields: searchFields.value,
    },
    list: ['SEARCH_PLUS'],
  },
});

const searchPropsObj = computed(() => ({
  showCollapseBtn: false,
  collapse: false,
  showResetBtn: false,
  showSubmitBtn: false,
  showOperation: false,
  quantity: 2,
}));

const tablePropsObj = computed(() => ({
  ...tableProps.value,
  stripe: false,
  border: false,
  headerCellStyle: {
    backgroundColor: '#f5f7fa',
    color: '#303133',
    fontWeight: 'bold',
    // height: '40px',
    borderColor: '#EBEEF5',
    'vertical-align': 'middle',
  },
  cellStyle: {
    padding: '0',
    // height: '40px',
    'vertical-align': 'middle',
    borderColor: '#EBEEF5',
  },
  rowStyle: {
    // height: '40px',
    borderColor: '#EBEEF5',
  },
  // rowHeight: 40,
}));

async function uploadDepositAttachment(type: string, v: any, row: any) {
  if (v?.length && Array.isArray(v)) {
    const list = type === 'ALL' ? filterTableData.value.filter(commonFilterFn) : [row];
    const params = {
      noticeId: noticeId.value,
      depositResponseList: list.map((item) => ({
        sectionId: item.sectionId,
        bidderResponse: {
          requirementId: item?.depositRequirementId,
          responseContent: JSON.stringify(
            v?.map((itemFile: any) => ({
              name: itemFile.name,
              url: itemFile.url,
            }))
          ),
        },
      })),
    };
    try {
      loading.value = true;
      await payDeposit(params);
      ElMessage.success('上传成功');
      reLoad();
    } catch (error) {
      // ElMessage.error('上传失败');
    } finally {
      loading.value = false;
    }
  }
}

function handleWithdraw(row: any) {
  ElMessageBox.confirm('确定执行该操作？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  })
    .then(async () => {
      await withdrawn({
        noticeId: noticeId.value,
        sectionId: row.sectionId,
        type: 2,
      });
      ElMessage.success('操作成功');
      reLoad();
    })
    .catch(() => {});
}

onMounted(async () => {
  reLoad();
});
</script>

<style scoped lang="scss">
.PayDepositWrapper {
  flex: 1;
  box-sizing: border-box;
  height: 100%;
  display: flex;
  flex-direction: column;
  position: relative;
  :deep(.el-card__body .dm-filter-root) {
    margin: 0 !important;
  }
  .batch-upload-files {
    position: absolute;
    top: -50px;
    right: 0px;
    width: max-content;
    z-index: 10;
  }
}
</style>
