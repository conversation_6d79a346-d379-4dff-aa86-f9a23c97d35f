import { ref, computed } from 'vue';
import { DEPOSIT_REVIEWED_STATUS_LABELS, DEPOSIT_REVIEWED_STATUS_TAGS } from '@/views/procurementSourcing/biddingProcess/constants/bid';
import FileRenderer from '/@/components/Detail/renderers/FileRenderer.vue';
import { useBiddingStore } from '@/views/procurementSourcing/biddingProcess/stores';

export function useColumns() {
  const biddingStore = useBiddingStore();
  // const isZJWT = computed(() => biddingStore?.isZJWT);
  const isShowSection = computed(() => biddingStore?.isShowSection);

  const searchFields = computed(() => {
    const commonFields = isShowSection?.value
      ? [
          {
            label: '标段名称',
            prop: 'selectionName',
            type: 'input',
            componentAttrs: {
              clearable: true,
            },
          },
        ]
      : [];
    return commonFields;
  });
  const searchData = ref({});
  const columns = computed(() => {
    const commonColumns = [
      {
        label: '序号',
        type: 'index',
        width: '80px',
      },
      {
        label: '项目名称',
        prop: 'projectName',
      },
      ...(isShowSection.value
        ? [
            {
              label: '标段名称',
              prop: 'selectionName',
            },
          ]
        : []),
      {
        label: '保证金金额',
        prop: 'depositContent.guaranteeAmount',
      },
      {
        label: '汇款银行',
        prop: 'depositContent.payBank',
      },
      {
        label: '汇款银行帐户',
        prop: 'depositContent.payAccount',
      },
      {
        label: '汇款开户行',
        prop: 'depositContent.openAccountBank',
      },
      {
        label: '保证金附件',
        prop: 'depositRespContent',
        render: ({ row }: any) => {
          const depositRespContent = row?.depositRespContent || [];
          if (Array.isArray(depositRespContent) && depositRespContent.length > 0) {
            return <FileRenderer value={depositRespContent} />;
          }
          return '-';
        },
      },
      {
        label: '附件上传人',
        prop: 'depositRespBy',
      },
      {
        label: '附件上传时间',
        prop: 'depositTime',
        width: '170px',
      },
      {
        label: '保证金状态',
        prop: 'depositStatus',
        render: ({ row }: any) => {
          const depositStatus = row?.depositStatus;
          // @ts-ignore
          const tagName = DEPOSIT_REVIEWED_STATUS_LABELS[depositStatus];
          // @ts-ignore
          const tagType = DEPOSIT_REVIEWED_STATUS_TAGS[depositStatus];
          // @ts-ignore
          return tagName ? <el-tag type={tagType}>{tagName}</el-tag> : '-';
        },
      },
      {
        label: '操作',
        prop: 'action',
        width: '250px',
        fixed: 'right',
      },
    ];
    return commonColumns;
  });

  return {
    columns,
    searchFields,
    searchData,
  };
}
