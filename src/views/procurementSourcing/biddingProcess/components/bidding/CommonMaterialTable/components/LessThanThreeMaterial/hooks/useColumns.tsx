import { ref, computed } from 'vue';
// import { useBiddingStore } from '@/views/procurementSourcing/biddingProcess/stores';
// import { numberToChinese } from '@/views/procurementSourcing/biddingProcess/utils';
// import { SCENE_ENUMS } from '@/views/procurementSourcing/biddingProcess/constants';


// eslint-disable-next-line @typescript-eslint/no-unused-vars, no-unused-vars
export function useColumns({ sectionId, scene }: any) {
  // const biddingStore = useBiddingStore();
  // const noticeInfo = computed(() => biddingStore?.noticeInfo);
  // const isZJWT = computed(() => biddingStore?.isZJWT);
  // const quotationRounds = ref<any>([]);
  const searchData = ref<any>({});
  const columns = ref([]);

  // const quoteRoundCount = ref(1);

  // watch([() => sectionId.value, noticeInfo?.value?.quoteRoundVoList], (val) => {
  //   if (val[0] && val[1]?.length) {
  //     quoteRoundCount.value = (val[1]?.find((item: any) => String(item?.sectionId) === String(sectionId.value)) || {})?.currentQuoteRound || 1;

  //     initRoundsData();

  //     const lastRound = quotationRounds.value?.[quotationRounds.value?.length - 1]?.value || '';
  //     searchData.value.roundNo = lastRound;
  //   }
  // });

  // // 初始化轮次数据
  // function initRoundsData() {
  //   quotationRounds.value = Array.from({ length: quoteRoundCount.value }, (_, i) => i + 1).map((item) => {
  //     return {
  //       label: `第${numberToChinese(item)}轮报价`,
  //       value: item,
  //     };
  //   });
  // }

  const searchFields = computed(() => {
    const commonFields = [
      // ...(![SCENE_ENUMS.ZJWT_KB]?.includes(scene)
      //   ? [
      //       {
      //         label: '选择报价轮次',
      //         prop: 'roundNo',
      //         type: 'select',
      //         componentAttrs: {
      //           // filterable: true,
      //           // multiple: true,
      //           clearable: false,
      //         },
      //         options: quotationRounds.value,
      //       },
      //     ]
      //   : []),
      {
        label: '物料名称',
        prop: 'materialName',
        type: 'input',
        componentAttrs: {
          clearable: true,
        },
      },
      {
        label: '物料编码',
        prop: 'materialCode',
        type: 'input',
        componentAttrs: {
          clearable: true,
        },
      },
    ];
    return commonFields;
  });

  return {
    columns,
    searchFields,
    searchData,
  };
}
