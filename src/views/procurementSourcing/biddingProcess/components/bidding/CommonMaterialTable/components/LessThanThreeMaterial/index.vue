<template>
  <yun-drawer
    v-model="showVisible"
    title="明细数据"
    size="X-large"
    :with-header="true"
    :append-to-body="false"
    destroy-on-close
    :show-footer="false"
    :show-confirm-button="false"
    :show-cancel-button="false"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    custom-class="bid-custom-drawer"
  >
    <div class="need-hide-table-card editable-table table-container">
      <el-button
        class="right-button xjcg-export"
        v-if="searchFields.length"
        @click="handleDownloadMaterialTemplate"
      >
        数据导出
      </el-button>
      <yun-pro-table
        ref="proTableRef"
        v-model:pagination="pagination"
        v-model:filter-data="filterTableData"
        v-model:searchData="searchData"
        :table-columns="dynamicColumn"
        :search-fields="searchFields"
        :auto-height="true"
        :remote-method="remoteMethod"
        :table-props="tablePropsObj"
        :search-props="searchPropsObj"
        layout="whole"
        :default-fetch="false"
        :extra="{
          position: 'breaster',
        }"
      >
        <template #extra>
          <div
            v-if="tableData?.length && showIpAlert && handleShowIpAlert?.length"
            class="flex justify-end"
          >
            <div class="common-ip-alert">
              <img
                class="alert-icon"
                src="https://prod-ylzapp-public.oss-cn-zhangjiakou.aliyuncs.com/frontend/fe-procurement-platform/WarnTriangleFilled.svg"
                alt=""
              />
              <span class="alert-msg">系统监测到 {{ handleShowSupplierAlert?.length }} 家供应商IP异常</span>
              <img
                class="alert-close"
                src="https://prod-ylzapp-public.oss-cn-zhangjiakou.aliyuncs.com/frontend/fe-procurement-platform/close.svg"
                alt=""
                @click="handleCloseIpAlert"
              />
            </div>
          </div>
        </template>
      </yun-pro-table>
    </div>
  </yun-drawer>
</template>

<script setup lang="ts">
import { ref, onMounted, onBeforeMount, computed } from 'vue';
// @ts-ignore
import { useProTable } from '@ylz-use/core';
import { useColumns } from './hooks/useColumns';
// @ts-ignore
import { useBiddingStore } from '@/views/procurementSourcing/biddingProcess/stores';
// @ts-ignore
import { queryQuoteListByMaterial, getQuoteLessThanThreeMaterialCodeList } from '@/api/purchasing/bid';
import { ElMessage } from 'yun-design';
import moment from 'moment';
// @ts-ignore
import { useDynamicTable } from '@/views/procurementSourcing/biddingProcess/hooks/useDynamicTable.jsx';
// @ts-ignore
import { useExcel } from '@/views/procurementSourcing/biddingProcess/hooks/useExcel';
// @ts-ignore
import { SCENE_ENUMS } from '@/views/procurementSourcing/biddingProcess/constants';

const props = withDefaults(defineProps<{ scene?: any }>(), {
  scene: '',
});
// eslint-disable-next-line no-console
console.log('props', props.scene);
const materialCodeList = ref<string[]>([]);
const currentRow = ref<any>({});

const biddingStore = useBiddingStore();
const { exportToExcel } = useExcel();
const { dynamicColumn: dynamicColumnOrigin, tableData, setDynamicColumn, handleTableData, objectSpanMethod } = useDynamicTable();
const dynamicColumn = computed(() => {
  let result = dynamicColumnOrigin.value;
  if ([SCENE_ENUMS.ZJWT_TB, SCENE_ENUMS.ZJWT_KB]?.includes(props.scene)) {
    result = dynamicColumnOrigin.value?.filter((item: any) => item.prop !== 'supplierName');
  }
  return [
    ...result,
    {
      prop: 'quoteIp',
      label: '投标IP',
      fieldName: '投标IP',
      fieldCode: 'quoteIp',
      width: 140,
    },
  ];
});

const currentSectionId = ref('');
const showVisible = ref(false);
const noticeId = computed(() => biddingStore?.noticeId);
const projectId = computed(() => biddingStore?.projectId);
const isZJWT = computed(() => biddingStore?.isZJWT);
// const isXJCG = computed(() => biddingStore?.isXJCG);

// const pagination = ref({});
const { columns, searchFields, searchData } = useColumns({ sectionId: currentSectionId, scene: props.scene });

const { remoteMethod, tableProps, proTableRef, filterTableData, reLoad, pagination } = useProTable({
  apiFn: queryQuoteListByMaterial,
  async responseHandler(result: any) {
    const list: any = result?.data?.records || [];
    await setDynamicColumn();
    await handleTableData(list);
    return tableData.value || [];
  },
  // eslint-disable-next-line no-unused-vars, @typescript-eslint/no-unused-vars
  customTotalHandler(data: any) {
    return 0;
    // return data?.data?.total || data?.total;
  },
  paramsHandler(params: any) {
    const param = {
      noticeId: noticeId.value,
      projectId: projectId.value,
      materialCodeList: materialCodeList.value,
      ...currentRow.value,
      ...params,
    };
    return param;
  },
  // eslint-disable-next-line no-unused-vars, @typescript-eslint/no-unused-vars
  querysHandler(querys: any) {
    const querysData = {
      // ...querys,
      current: pagination.value.page,
      size: 1000, // 先查询所有数据
      // size: pagination.value.size || pagination.value.pageSize,
    };
    return querysData;
  },
  plugins: {
    config: {
      columns: columns.value,
      searchFields: searchFields.value,
    },
    list: ['SEARCH_PLUS'],
  },
});

const showIpAlert = ref(true);
const handleCloseIpAlert = () => {
  showIpAlert.value = false;
};
// tableData 数组中存在 quoteIp  tenantSupplierId
// 将 quoteIp 相同的 单元格样式标红 表头样式标红
const handleShowIpAlert = computed(() => {
  const quoteIpList = [...new Set(tableData.value?.map((item: any) => item?.quoteIp))];
  return quoteIpList?.filter((item: any) => {
    return tableData.value?.filter((i: any) => i?.quoteIp === item).length > 1;
  });
});

const handleShowSupplierAlert = computed(() => {
  return [
    ...new Set(tableData.value?.filter((item: any) => handleShowIpAlert.value?.includes(item?.quoteIp))?.map((item: any) => item?.tenantSupplierId)),
  ];
});

const calcCellClassName = ({ row, column }: any) => {
  if (!isZJWT.value && column?.property === 'quoteIp' && handleShowIpAlert?.value?.includes(row?.quoteIp)) {
    return 'quoteIp-cell-class-name';
  }
};
const calcHeaderCellClassName = ({ column }: any) => {
  if (!isZJWT.value && column?.property === 'quoteIp' && handleShowIpAlert?.value?.length) {
    return 'quoteIp-header-cell-class-name';
  }
};

async function getQuoteLessThanThreeMaterialCode() {
  try {
    const res = await getQuoteLessThanThreeMaterialCodeList({
      noticeId: noticeId.value,
      ...currentRow.value,
    });
    if (res?.data?.length) {
      materialCodeList.value = res?.data;
      await reLoad();
      ElMessage.success('查询成功');
    } else {
      ElMessage.error('暂无数据');
    }
    // eslint-disable-next-line no-empty
  } catch (error) {}
}

function show(row: any = {}) {
  currentRow.value = row;
  showVisible.value = true;
  getQuoteLessThanThreeMaterialCode();
}

const searchPropsObj = computed(() => ({
  showCollapseBtn: false,
  collapse: false,
  showResetBtn: false,
  showSubmitBtn: false,
  showOperation: false,
  quantity: 3,
}));

const tablePropsObj = computed(() => ({
  ...tableProps.value,
  'span-method': objectSpanMethod,
  // cellStyle: calcCellStyle,
  // headerCellStyle: calcHeaderCellStyle,
  cellClassName: calcCellClassName,
  headerCellClassName: calcHeaderCellClassName,
  stripe: true,
  border: true,
  headerCellStyle: {
    backgroundColor: '#f5f7fa',
    color: '#303133',
    fontWeight: 'bold',
    // height: '40px',
    borderColor: '#EBEEF5',
    'vertical-align': 'middle',
  },
  cellStyle: {
    padding: '0',
    // height: '40px',
    'vertical-align': 'middle',
    borderColor: '#EBEEF5',
  },
  rowStyle: {
    // height: '40px',
    borderColor: '#EBEEF5',
  },
  // rowHeight: 40,
}));

const handleDownloadMaterialTemplate = async () => {
  try {
    const blob = await exportToExcel(tableData.value, dynamicColumn.value);
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `物料数据_${moment().format('YYYY-MM-DD HH:mm:ss')}.xlsx`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    window.URL.revokeObjectURL(url);
  } catch (error) {
    ElMessage.error('导出失败');
  }
};

onMounted(async () => {});
onBeforeMount(async () => {});
defineExpose({
  show,
});
</script>

<style scoped lang="scss">
.document-review-container {
  flex: 1;
  padding: 20px;
  box-sizing: border-box;
  height: 100%;
  display: flex;
  flex-direction: column;
  position: relative;
  :deep(.dm-filter-root) {
    padding-right: 90px !important;
  }
  .right-button {
    position: absolute;
    z-index: 1;
    right: 0;
    top: 22px;
  }

  .table-container {
    position: relative;
    height: 98%;
    :deep(.quoteIp-header-cell-class-name .filter-box .filter-box-content .text-box) {
      color: var(--Color-Error-color-error, #ff3b30) !important;
    }
    :deep(.quoteIp-cell-class-name .cell) {
      background-color: rgba(255, 59, 48, 0.06) !important;
      color: var(---el-color-danger, #f53f3f) !important;
    }
    .alert-section {
      display: flex;
      padding: 8px 16px;
      align-items: center;
      gap: 8px;
      align-self: stretch;
      border-radius: var(--Radius-border-radius-small, 2px);
      background: var(--Color-Warning-color-warning-light-9, #fff9e8);
      color: var(--Color-Text-text-color-primary, #1d2129);
      font-family: 'PingFang SC';
      font-size: 13px;
      font-style: normal;
      font-weight: 400;
      line-height: 22px;
    }
    .common-ip-alert {
      border-radius: 4px;
      border: 1px solid var(--Color-Border-border-color-light, #e4e7ed);
      background: var(--color-white, #fff);
      /* light/box-shadow-lighter */
      box-shadow: 0 0 6px 0 rgba(0, 0, 0, 0.12);
      display: flex;
      padding: 8px 16px;
      align-items: center;
      position: relative;
      width: max-content;
      .alert-icon {
        width: 16px;
        height: 16px;
      }
      .alert-msg {
        color: var(--Color-Error-color-error, #ff3b30);
        /* regular/small */
        font-family: 'PingFang SC';
        font-size: 13px;
        font-style: normal;
        font-weight: 400;
        line-height: 22px; /* 169.231% */
        padding-left: 8px;
        padding-right: 24px;
      }
      .alert-close {
        width: 12px;
        height: 12px;
        cursor: pointer;
      }
    }
  }
  :deep(.dm-table-header) {
    padding-top: 0;
    padding-bottom: 16px;
  }
  .right-section {
    color: var(--Color-Text-text-color-secondary, #86909c);
    font-family: 'PingFang SC';
    font-size: 12px;
    font-style: normal;
    font-weight: 400;
    line-height: 20px;
    margin-left: 32px;
    .highlight {
      color: var(--Color-Text-text-color-primary, #1d2129);
      font-family: 'PingFang SC';
      font-size: 12px;
      font-style: normal;
      font-weight: 600;
      line-height: 20px;
    }
  }
  .other-info-wrapper {
    display: flex;
    align-items: flex-start;
    justify-content: space-between;
    margin-top: 20px;
    gap: 40px;

    .info-left {
      display: flex;
      align-items: center;
      gap: 16px;
      flex: 1;
      min-width: 0; /* 允许收缩 */
      overflow: hidden; /* 防止溢出 */
    }

    .info-right {
      display: flex;
      align-items: center;
      gap: 16px;
      flex-shrink: 0; /* 不允许收缩 */
    }

    .item-label {
      color: var(--Color-Text-text-color-primary, #1d2129);
      /* bold/medium */
      font-family: 'PingFang SC';
      font-size: 16px;
      font-style: normal;
      font-weight: 600;
      line-height: 24px; /* 150% */
      position: relative;
      padding-left: 10px;
      flex-shrink: 1; /* 允许收缩 */
      min-width: 0; /* 允许收缩到很小 */
      max-width: 200px; /* 限制最大宽度 */
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      &::before {
        content: ' ';
        position: absolute;
        left: 0;
        top: 50%;
        transform: translateY(-50%);
        width: 2px;
        height: 14px;
        background: var(--Color-Primary-color-primary, #0069ff);
        flex-shrink: 0; /* 前缀不收缩 */
      }
    }

    .item-block {
      display: inline-flex;
      align-items: center;
      border-radius: var(--Radius-border-radius-small, 2px);
      border: 1px solid var(--Color-Border-border-color-light, #e4e7ed);
      background: var(--color-white, #fff);
      height: 24px;
      padding: 2px 8px;
      box-sizing: border-box;
      gap: 8px;
      flex-shrink: 1; /* 允许收缩 */
      min-width: 0; /* 允许收缩 */

      &.file-item {
        max-width: 280px; /* 限制文件项的最大宽度 */
        min-width: 200px; /* 设置最小宽度确保下载按钮可见 */
        flex-shrink: 0; /* 文件项不允许收缩 */
      }

      /* 左侧信息块的特殊样式 */
      .info-left & {
        max-width: 200px; /* 限制左侧item-block的最大宽度 */
        flex-shrink: 1; /* 允许收缩 */
      }

      .label {
        color: var(--Color-Text-text-color-secondary, #86909c);
        /* regular/base */
        font-family: 'PingFang SC';
        font-size: 14px;
        font-style: normal;
        font-weight: 400;
        line-height: 22px; /* 157.143% */
        flex-shrink: 0;
      }

      .value {
        color: var(--Color-Text-text-color-primary, #1d2129);
        /* regular/base */
        font-family: 'PingFang SC';
        font-size: 14px;
        font-style: normal;
        font-weight: 400;
        line-height: 22px;
        flex-shrink: 1; /* 允许收缩 */

        &.file-value {
          max-width: 140px; /* 限制文件名显示宽度 */
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }

        &.ellipsis-value {
          max-width: 160px; /* 限制左侧value显示宽度 */
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          min-width: 0;
        }

        &:not(.file-value):not(.ellipsis-value) {
          flex-shrink: 0;
        }
      }

      .download-btn {
        color: var(--Color-Primary-color-primary, #0069ff);
        /* regular/base */
        font-family: 'PingFang SC';
        font-size: 14px;
        font-style: normal;
        font-weight: 400;
        line-height: 22px;
        flex-shrink: 0; /* 下载按钮不允许收缩 */
        margin-left: 4px;
      }
    }
  }
}
</style>
