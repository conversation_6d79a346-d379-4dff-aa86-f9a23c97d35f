<template>
  <div class="bidding-management-container">
    <!-- 步骤指示器 -->
    <StepsIndicator
      :steps="stepsData"
      @step-click="handleStepClick"
    />

    <div class="supply-bidding-container">
      <div class="deadline-bar" v-if="isPublic || (!isPublic && activeStepIndex !== 0)">
        <div class="deadline-bar-item">
          <div class="deadline-bar-item-title">
            <div class="deadline-bar-item-title-label">报价截止时间</div>
            <div class="deadline-bar-item-title-value">{{ formatDateTime(deadline) }}</div>
          </div>
          <div class="deadline-bar-item-text" v-if="deadlineTime">{{ deadlineTime }}</div>
          <div class="deadline-bar-item-countdown" v-else>
            <div class="deadline-bar-item-countdown-label">剩余时间</div>
            <div class="deadline-bar-item-countdown-value">
              <!-- 天 -->
              <RollingNumber
                :value="countdown.days"
                :old-value="prevCountdown.days"
                :max-value="99"
                :tick="ticks.days"
                direction="down"
              />
              <div>天</div>
              <!-- 时 -->
              <RollingNumber
                :value="countdown.hours"
                :old-value="prevCountdown.hours"
                :max-value="23"
                :tick="ticks.hours"
                direction="down"
              />
              <div>时</div>
              <!-- 分 -->
              <RollingNumber
                :value="countdown.minutes"
                :old-value="prevCountdown.minutes"
                :max-value="59"
                :tick="ticks.minutes"
                direction="down"
              />
              <div>分</div>
              <!-- 秒 -->
              <RollingNumber
                :value="countdown.seconds"
                :old-value="prevCountdown.seconds"
                :max-value="59"
                :tick="ticks.seconds"
                direction="down"
              />
              <div>秒</div>
            </div>
          </div>
        </div>
      </div>

      <!-- 动态组件渲染区域 -->
      <div
        class="component-content"
        v-if="biddingStore.noticeId"
      >
        <component :is="currentComponent" />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, watch } from 'vue';
import { useIntervalFn } from '@vueuse/core';
import StepsIndicator, { type StepItem } from '../../StepsIndicator/index.vue';
import RollingNumber from '../../common/RollingNumber/index.vue';
import { getRemainingTime } from '../../../utils/time-helper';

import BIdRegistration from '../BIdRegistration/index.vue';
import PayDeposit from '../PayDeposit/index.vue';
import Quotation from '../Quotation/index.vue';
import BidPurchase from '../BidPurchase/index.vue';

import { useBiddingStore } from '@/views/procurementSourcing/biddingProcess/stores';

const biddingStore = useBiddingStore();
const isCompetitiveBidding = computed(() => biddingStore?.isCompetitiveBidding);

// 当前激活的步骤索引
const activeStepIndex = ref(0);

const isPublic = computed(() => biddingStore?.isPublic);
// 资格预审标识 1-需要 其他-不需要
const isNeedPreQualification = computed(() => [1, true]?.includes(biddingStore?.projectDetail?.preQualification));
// 邀请回执标识 1-需要 其他-不需要
const isNeedInviteReceipt = computed(() => [1, true]?.includes(biddingStore?.projectDetail?.inviteReceipt));
// 保证金管理标识 1-需要 其他-不需要
const isNeedDeposit = computed(() => [1, true]?.includes(biddingStore?.noticeInfo?.needDeposit));
// 购标标识 1-需要 其他-不需要
const isNeedTenderFee = computed(() => [1, true]?.includes(biddingStore?.noticeInfo?.needTenderFee));

// 截止时间
const deadline = computed(() => biddingStore?.deadline);
const deadlineTime = computed(() => biddingStore?.deadlineTime);

// 倒计时数据
const countdown = ref({ days: 0, hours: 0, minutes: 0, seconds: 0 });
const prevCountdown = ref({ days: 0, hours: 0, minutes: 0, seconds: 0 });
const ticks = ref({ days: 0, hours: 0, minutes: 0, seconds: 0 });

function updateCountdown() {
  if (!deadline.value) {
    return;
  }
  const remaining = getRemainingTime(deadline.value);
  // 先保存旧值
  const old = { ...countdown.value };
  // 天
  if (remaining.days !== old.days) ticks.value.days++;
  // 时
  if (remaining.hours !== old.hours) ticks.value.hours++;
  // 分
  if (remaining.minutes !== old.minutes) ticks.value.minutes++;
  // 秒
  if (remaining.seconds !== old.seconds) ticks.value.seconds++;

  countdown.value = {
    days: remaining.days,
    hours: remaining.hours,
    minutes: remaining.minutes,
    seconds: remaining.seconds,
  };
  prevCountdown.value = old;
}

// 使用 VueUse 的 useIntervalFn 来更新倒计时
const { pause, resume } = useIntervalFn(() => {
  updateCountdown();
}, 1000);

// 组件挂载时启动倒计时
onMounted(() => {
  updateCountdown();
  resume();
});

// 组件卸载时停止倒计时
onUnmounted(() => {
  pause();
});

const steps = computed(() => {
  const res = isPublic.value
    ? [
        {
          id: 0,
          number: 0,
          label: '报名',
          show: isNeedPreQualification.value,
          completed: false,
          current: false,
        },
        {
          id: 1,
          number: 1,
          label: '保证金缴纳',
          show: isNeedDeposit.value,
          completed: false,
          current: false,
        },
        {
          id: 2,
          number: 2,
          label: '购标',
          show: isNeedTenderFee.value,
          completed: false,
          current: false,
        },
        {
          id: 3,
          number: 3,
          label: isCompetitiveBidding.value ? '在线投标' : '在线报价',
          show: true,
          completed: false,
          current: false,
        },
      ]
    : [
        {
          id: 0,
          number: 1,
          label: '邀请回执',
          show: isNeedInviteReceipt.value,
          completed: false,
          current: false,
        },
        {
          id: 1,
          number: 1,
          label: '保证金缴纳',
          show: isNeedDeposit.value,
          completed: false,
          current: false,
        },
        {
          id: 2,
          number: 2,
          label: '购标',
          show: isNeedTenderFee.value,
          completed: false,
          current: false,
        },
        {
          id: 3,
          number: 3,
          label: isCompetitiveBidding.value ? '在线投标' : '在线报价',
          show: true,
          completed: false,
          current: false,
        },
      ];
  return res?.filter((item) => !!item.show);
});

watch(
  () => steps.value,
  () => {
    activeStepIndex.value = (steps?.value?.[0]?.id || 0) as number;
  },
  { immediate: true }
);

const stepsData = computed(() => {
  return steps?.value?.map((item, index) => ({
    ...item,
    current: item.id === activeStepIndex.value,
    number: index + 1, // 节点数字
  }));
});

// 组件映射
const componentMap = {
  0: BIdRegistration,
  1: PayDeposit,
  2: BidPurchase,
  3: Quotation,
};

// 当前显示的组件
const currentComponent = computed(() => {
  return componentMap[activeStepIndex.value as keyof typeof componentMap];
});

// 处理步骤点击事件 - 只切换显示的组件，不修改步骤状态
function handleStepClick(step: StepItem) {
  activeStepIndex.value = step.id as number;
}

// 格式化日期时间
function formatDateTime(date: Date): string {
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    hour12: false,
  });
}

biddingStore?.initData();
</script>

<style lang="scss" scoped>
.bidding-management-container {
  min-height: 100%;
  display: flex;
  flex-direction: column;

  .supply-bidding-container {
    flex: 1;
    display: flex;
    flex-direction: column;
    background: #fff;
    border-radius: 6px;
    margin-top: 12px;
    padding: 8px 20px 20px 20px;

    .deadline-bar {
      padding: 12px 0;
      border-radius: 4px;
      display: flex;
      justify-content: space-between;
      align-items: center;

      .deadline-bar-item {
        display: flex;
        align-items: center;
        &-title {
          display: flex;
          align-items: center;

          &-label {
            padding: 4px 6px;
            border-radius: 2px;
            background: var(--Color-Fill-fill-color, #f0f2f5);
            color: var(--Color-Text-text-color-regular, #4e5969);
            font-family: 'PingFang SC';
            font-size: 14px;
            font-style: normal;
            font-weight: 400;
            line-height: 16px;
          }

          &-value {
            color: var(--Color-Text-text-color-regular, #4e5969);
            font-family: 'PingFang SC';
            font-size: 14px;
            font-style: normal;
            font-weight: 600;
            line-height: 22px;
            margin-left: 8px;
          }
        }

        &-text {
          color: var(--Color-Error-color-error, #ff3b30);
          font-family: 'PingFang SC';
          font-size: 14px;
          font-style: normal;
          font-weight: 400;
          line-height: 22px;
          margin-left: 8px;
        }

        &-countdown {
          margin-left: 32px;
          display: flex;
          align-items: center;

          &-label {
            color: var(--Color-Text-text-color-regular, #4e5969);
            font-family: 'PingFang SC';
            font-size: 14px;
            font-style: normal;
            font-weight: 400;
            line-height: 16px;
            padding: 4px 6px;
          }

          &-value {
            color: var(--Color-Text-text-color-secondary, #86909c);
            font-family: 'PingFang SC';
            font-size: 12px;
            font-style: normal;
            font-weight: 400;
            line-height: 22px;
            display: flex;
            align-items: center;
            gap: 4px;

            .time-bar {
              width: 24px;
              height: 24px;
              border-radius: 2px;
              background: var(--Color-Error-color-error-light-9, #ffede8);
              display: flex;
              align-items: center;
              justify-content: center;
              color: var(--Color-Error-color-error, #ff3b30);
              font-family: 'PingFang SC';
              font-size: 14px;
              font-style: normal;
              font-weight: 600;
              line-height: 22px;
            }
          }
        }
      }
    }

    .component-content {
      flex: 1;
      margin-top: 12px;
      border-radius: 6px;
    }
  }
}
</style>
