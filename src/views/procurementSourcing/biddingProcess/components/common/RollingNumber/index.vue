<template>
  <div class="rolling-number-container">
    <div 
      class="rolling-number-wrapper"
      :class="{ 'is-rolling': isRolling }"
      :style="{ transform: `translateY(${translateY}px)` }"
      :key="props.tick"
    >
      <div 
        v-for="num in numbers" 
        :key="num"
        class="rolling-number"
      >
        {{ num.toString().padStart(2, '0') }}
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, onMounted } from 'vue'

interface Props {
  value: number
  maxValue?: number
  tick?: number
  oldValue?: number
  direction?: 'up' | 'down'
}

const props = withDefaults(defineProps<Props>(), {
  maxValue: 9,
  tick: 0,
  oldValue: 0,
  direction: 'down'
})

const numbers = ref<number[]>([])
const translateY = ref(0)
const isRolling = ref(false)

// Generate array of numbers from 0 to maxValue
function generateNumbers() {
  numbers.value = Array.from({ length: props.maxValue + 1 }, (_, i) => i)
}

function getTranslateY(value: number) {
  return -value * 24
}

// Update position when value changes
watch([
  () => props.value,
  () => props.oldValue,
  () => props.tick
], ([newValue, oldValue]) => {
  if (newValue === oldValue) return
  isRolling.value = true
  // 判断方向
  let nextY = getTranslateY(newValue)
  if (props.direction === 'down') {
    translateY.value = nextY
  } else {
    translateY.value = nextY // 目前只实现down，如需up可扩展
  }
  setTimeout(() => {
    isRolling.value = false
  }, 300)
}, { immediate: true })

onMounted(() => {
  generateNumbers()
  translateY.value = getTranslateY(props.value)
})
</script>

<style lang="scss" scoped>
.rolling-number-container {
  width: 24px;
  height: 24px;
  overflow: hidden;
  position: relative;
  background: var(--Color-Error-color-error-light-9, #ffede8);
  border-radius: 2px;
  display: flex;
  justify-content: center;
}

.rolling-number-wrapper {
  position: absolute;
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  
  &.is-rolling {
    transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }
}

.rolling-number {
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--Color-Error-color-error, #ff3b30);
  font-family: 'PingFang SC';
  font-size: 14px;
  font-weight: 600;
  line-height: 22px;
}
</style> 