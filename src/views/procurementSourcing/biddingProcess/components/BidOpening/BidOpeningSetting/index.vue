<template>
  <div class="bid-opening-setting">
    <!-- 开标基本信息 -->
    <div class="bid-info-section">
      <div class="info-row">
        <div class="info-item">
          <label class="info-label">是否公开报价：</label>
          <div class="info-content">
            <el-radio-group
              v-if="isEdit"
              v-model="publicQuote"
            >
              <el-radio :label="1">是</el-radio>
              <el-radio :label="0">否</el-radio>
            </el-radio-group>
            <span v-else>{{ publicQuote === 1 ? '是' : '否' }}</span>
          </div>
        </div>
        <div class="info-item">
          <label class="info-label">开标人：</label>
          <div class="info-content">
            <el-select
              v-if="isEdit"
              v-model="bidOpener"
              placeholder="请选择开标人"
              filterable
              remote
              clearable
              :remote-method="remoteSearchUsers"
              :loading="searchLoading"
              @change="handleBidOpenerChange"
              style="width: 200px"
            >
              <el-option
                v-for="user in userOptions"
                :key="user.userId"
                :label="`${user.name}`"
                :value="user.userId"
              >
                <span>{{ user.name }}</span>
<!--                <span style="float: right; color: #8492a6; font-size: 13px">{{ user.username }}</span>-->
              </el-option>
            </el-select>
            <span v-else>{{ bidOpenerName || '-' }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 监督人员名单 -->
    <div class="supervisor-section">
      <div class="section-header">
        <div class="header-title">监督人员名单</div>
        <el-button
          v-if="!isEdit && !isBidOpened && isPurchaser"
          type="primary"
          @click="toggleEditMode"
          icon="Edit"
        >
          修改
        </el-button>
      </div>
      <el-table
        :data="supervisionList"
        border
        style="width: 100%"
        :cell-style="{ textAlign: 'center' }"
        :header-cell-style="{ textAlign: 'center', background: 'var(--el-table-row-hover-bg-color)' }"
      >
        <el-table-column
          label="序号"
          type="index"
          width="80"
        />

        <el-table-column
          label="姓名"
          prop="createByName"
          min-width="150"
        >
          <template #default="{ row, $index }">
            <el-select
              v-if="isEdit"
              v-model="row.userId"
              placeholder="请输入名称搜索"
              filterable
              remote
              clearable
              :remote-method="remoteSearchUsers"
              :loading="searchLoading"
              @change="handleNameChange(row, $index)"
              @clear="handleNameClear(row)"
            >
              <el-option
                v-for="user in userOptions"
                :key="user.userId"
                :label="`${user.name}`"
                :value="user.userId"
                :disabled="isUserSelected(user.userId, $index)"
              >
                <span>{{ user.name }}</span>
<!--                <span style="float: right; color: #8492a6; font-size: 13px">{{ user.username }}</span>-->
              </el-option>
            </el-select>
            <span v-else>{{ row.createByName || '-' }}</span>
          </template>
        </el-table-column>
        <el-table-column
          label="联系方式"
          prop="contactPhone"
          min-width="150"
        ></el-table-column>
        <el-table-column
          label="电子邮箱"
          prop="email"
          min-width="150"
        ></el-table-column>
        <el-table-column
          label="操作"
          width="220"
          v-if="isEdit"
        >
          <template #default="{ $index }">
            <el-button
              type="text"
              @click="addRow"
            >
              增加行
            </el-button>
            <el-button
              type="text"
              @click="deleteRow($index)"
              :disabled="supervisionList.length <= 1"
            >
              删除行
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
  </div>
  <!-- 底部操作按钮 -->
  <div
    class="form-actions-wrapper"
    v-if="isEdit"
  >
    <div class="form-actions">
      <el-button @click="handleCancel">取消</el-button>
      <el-button
        type="primary"
        @click="handleSubmit"
        :loading="loading"
        >提交</el-button
      >
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed, defineProps } from 'vue';
import { useRoute } from 'vue-router';
import { ElMessage, ElMessageBox } from 'yun-design';
import { pageList } from '@/api/admin/user';
import { saveBidOpenConfig, getBidOpeningSettingByNotice, getUserByIds } from '../../../api/bidOpeningSetting';
import type { UserOption, SupervisionMember, BidOpenConfigData } from '../../../types/bidOpeningSetting';
import { useBiddingStore } from '@/views/procurementSourcing/biddingProcess/stores';
import { useUserRole } from '@/views/procurementSourcing/biddingProcess/utils';
// 响应式数据
const route = useRoute();
const loading = ref(false);
const isEdit = ref(false);
const supervisionList = ref<SupervisionMember[]>([]);
const userOptions = ref<UserOption[]>([]);
const originalData = ref<SupervisionMember[]>([]);
const publicQuote = ref<number>(0); // 是否公开报价(0-不公开,1-公开)
const bidOpener = ref<string>(''); // 开标人ID
const bidOpenerName = ref<string>(''); // 开标人姓名
const searchLoading = ref(false); // 搜索加载状态

const biddingStore = useBiddingStore();
const noticeId = computed(() => biddingStore?.noticeId);
const { isPurchaser } = useUserRole();

// 判断是否已开标（开标开始后不能修改）
const isBidOpened = computed(() => {
  const progressStatus = biddingStore?.projectDetail?.progressStatus;
  // 当状态为 BID_OPENED（开标中）及之后的状态时，表示已开标，不能修改
  return !['TO_NOTICE', 'NOTICE', 'TENDER_DOC', 'REGISTER', 'QUOTING', 'TO_BID_OPEN'].includes(progressStatus);
});
// 初始化数据
const initData = () => {
  // 默认一行空数据
  supervisionList.value = [
    {
      createByName: '',
      contactPhone: '',
      email: '',
    },
  ];
  // publicQuote.value = 0;
  // bidOpener.value = '';
  originalData.value = JSON.parse(JSON.stringify(supervisionList.value));
};

// 判断是否为首次进入（新建状态）
const isFirstVisit = computed(() => {
  // 如果没有ID参数或者ID为空，则认为是新建状态
  return !route.params.id && !route.query.id;
});

// 切换编辑模式
const toggleEditMode = async () => {
  isEdit.value = true;
  // 备份原始数据
  originalData.value = JSON.parse(JSON.stringify(supervisionList.value));
  // 初始化用户列表，确保编辑时有足够的选项
  await initUserList();
};

// 初始化用户列表（进入页面时默认请求）
const initUserList = async () => {
  try {
    searchLoading.value = true;
    const response = await pageList({
      current: 1,
      size: 50, // 默认获取50条数据
    });

    if (response.data && response.data.records) {
      userOptions.value = response.data.records.map((user: any) => ({
        userId: user.userId,
        name: user.name,
        phone: user.phone,
        email: user.email,
        username: user.username,
      }));
    } else {
      userOptions.value = [];
    }
  } catch (error) {
    console.error('获取用户列表失败:', error);
    userOptions.value = [];
  } finally {
    searchLoading.value = false;
  }
};

// 批量根据用户ID获取用户信息
const loadUserInfoByIds = async (userIds: string[]) => {
  if (!userIds || userIds.length === 0) return new Map();

  try {
    const { data } = await getUserByIds(userIds);
    const userMap = new Map();

    if (data && data.length > 0) {
      // 创建用户信息Map，以userId为key
      data.forEach(user => {
        userMap.set(user.userId, user);

        // 将用户信息添加到用户选项中（去重）
        const existingUserIndex = userOptions.value.findIndex(option => option.userId === user.userId);
        const userOption = {
          userId: user.userId,
          name: user.name,
          phone: user.phone,
          email: user.email,
          username: user.username,
        };

        if (existingUserIndex === -1) {
          userOptions.value.unshift(userOption);
        } else {
          // 更新已存在的用户信息
          userOptions.value[existingUserIndex] = userOption;
        }
      });
    }

    return userMap;
  } catch (error) {
    console.error('批量获取用户信息失败:', error);
    return new Map();
  }
};

// 根据用户ID获取用户信息并设置开标人姓名
const loadBidOpenerInfo = async (userId: string) => {
  if (!userId) return;

  const userMap = await loadUserInfoByIds([userId]);
  const user = userMap.get(userId);
  if (user) {
    bidOpenerName.value = user.name || '';
  }
};

// 远程搜索用户列表
const remoteSearchUsers = async (query: string) => {
  if (!query.trim()) {
    // 如果搜索为空，恢复默认列表
    await initUserList();
    return;
  }

  try {
    searchLoading.value = true;

    // 先尝试用户名搜索
    let response = await pageList({
      current: 1,
      size: 50,
      username: query.trim(),
    });

    console.log('搜索结果 (username):', response.data?.records?.length || 0, '条');

    // 如果用户名搜索无结果，尝试手机号搜索
    if (!response.data?.records?.length) {
      response = await pageList({
        current: 1,
        size: 50,
        phone: query.trim(),
      });
      console.log('搜索结果 (phone):', response.data?.records?.length || 0, '条');
    }

    if (response.data && response.data.records) {
      const searchResults = response.data.records.map((user: any) => ({
        userId: user.userId,
        name: user.name,
        phone: user.phone,
        email: user.email,
        username: user.username,
      }));

      // 获取已选择的用户ID
      const selectedUserIds = new Set();
      if (bidOpener.value) selectedUserIds.add(bidOpener.value);
      supervisionList.value.forEach(item => {
        if (item.userId) selectedUserIds.add(item.userId.toString());
      });

      // 保留已选择的用户
      const selectedUsers = userOptions.value.filter(user =>
        selectedUserIds.has(user.userId)
      );

      // 合并搜索结果，去重
      const allUsers = [...selectedUsers];
      searchResults.forEach(user => {
        if (!selectedUserIds.has(user.userId)) {
          allUsers.push(user);
        }
      });

      console.log('最终用户列表数量:', allUsers.length);
      userOptions.value = allUsers;
    } else {
      // 搜索无结果，保留已选择的用户
      const selectedUserIds = new Set();
      if (bidOpener.value) selectedUserIds.add(bidOpener.value);
      supervisionList.value.forEach(item => {
        if (item.userId) selectedUserIds.add(item.userId.toString());
      });

      userOptions.value = userOptions.value.filter(user =>
        selectedUserIds.has(user.userId)
      );
      console.log('搜索无结果，保留已选择用户数量:', userOptions.value.length);
    }
  } catch (error) {
    console.error('搜索用户失败:', error);
    // 搜索失败时保留已选择的用户
    const selectedUserIds = new Set();
    if (bidOpener.value) selectedUserIds.add(bidOpener.value);
    supervisionList.value.forEach(item => {
      if (item.userId) selectedUserIds.add(item.userId.toString());
    });

    userOptions.value = userOptions.value.filter(user =>
      selectedUserIds.has(user.userId)
    );
  } finally {
    searchLoading.value = false;
  }
};

// 检查用户是否已被选择
const isUserSelected = (userId: string, currentIndex: number) => {
  return supervisionList.value.some((item, index) => index !== currentIndex && item.userId?.toString() === userId);
};

// 开标人选择变化处理
const handleBidOpenerChange = (userId: string) => {
  if (userId) {
    const selectedUser = userOptions.value.find(user => user.userId === userId);
    if (selectedUser) {
      bidOpenerName.value = selectedUser.name;
    }
  } else {
    bidOpenerName.value = '';
  }
};

// 姓名选择变化处理
const handleNameChange = (row: SupervisionMember, index: number) => {
  if (row.userId) {
    const selectedUser = userOptions.value.find((user) => user.userId === row.userId.toString());
    if (selectedUser) {
      // 自动填充联系方式和姓名
      row.createByName = selectedUser.name;
      row.contactPhone = selectedUser.phone || '';
      row.email = selectedUser.email || '';
    }
  }
};

// 姓名清除处理
const handleNameClear = (row: SupervisionMember) => {
  row.createByName = '';
  row.contactPhone = '';
  row.email = '';
  row.userId = undefined;
  // 不清空搜索结果，保持默认列表
};

// 添加行
const addRow = () => {
  supervisionList.value.push({
    createByName: '',
    contactPhone: '',
    email: '',
  });
};

// 删除行
const deleteRow = (index: number) => {
  if (supervisionList.value.length <= 1) {
    ElMessage.warning('至少需要保留一行数据');
    return;
  }
  supervisionList.value.splice(index, 1);
};

// 取消操作
const handleCancel = () => {
  ElMessageBox.confirm('确定要取消吗？未保存的数据将丢失', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  })
    .then(() => {
      // 恢复原始数据
      supervisionList.value = JSON.parse(JSON.stringify(originalData.value));
      isEdit.value = false;
      // 重新加载开标人信息
      if (bidOpener.value) {
        loadBidOpenerInfo(bidOpener.value);
      }
    })
    .catch(() => {
      // 用户取消
    });
};

// 加载开标设置数据
const loadBidOpeningData = async () => {
  try {
    loading.value = true;

    // 根据props或路由参数获取数据
    if (!noticeId.value) return;

    const { data } = await getBidOpeningSettingByNotice(noticeId.value);
    publicQuote.value = data.publicQuote || 0;
    supervisionList.value = data.supervisionList || [];

    // 收集所有需要查询的用户ID
    const userIdsToLoad = [];

    // 添加开标人ID
    const bidOpenUserId = data.bidOpen?.userId || data.projectLeader?.userId;
    if (bidOpenUserId) {
      bidOpener.value = bidOpenUserId;
      userIdsToLoad.push(bidOpenUserId);
    }

    // 添加监督人员ID
    if (supervisionList.value && supervisionList.value.length > 0) {
      supervisionList.value.forEach(item => {
        if (item.userId) {
          userIdsToLoad.push(item.userId.toString());
        }
      });
    }

    // 批量加载用户信息
    if (userIdsToLoad.length > 0) {
      const userMap = await loadUserInfoByIds(userIdsToLoad);

      // 设置开标人姓名
      if (bidOpenUserId) {
        const bidOpenerUser = userMap.get(bidOpenUserId);
        if (bidOpenerUser) {
          bidOpenerName.value = bidOpenerUser.name || '';
        }
      }

      // 设置监督人员姓名和联系信息（使用Map，O(1)时间复杂度）
      supervisionList.value.forEach(item => {
        if (item.userId) {
          const user = userMap.get(item.userId.toString());
          if (user) {
            item.createByName = user.name;
            item.contactPhone = user.phone || item.contactPhone;
            item.email = user.email || item.email;
          }
        }
      });
    }

    if (!supervisionList.value.length && !isBidOpened.value && isPurchaser.value) {
      isEdit.value = true;
      initData();
    }
  } catch (error) {
    console.error('加载开标设置数据失败:', error);
    initData();
  } finally {
    loading.value = false;
  }
};

// 提交数据
const handleSubmit = async () => {
  // 验证公告ID
  if (!noticeId.value) {
    ElMessage.error('缺少公告ID');
    return;
  }

  // 获取有效的监督人员（必须有userId）
  const validSupervisionList = supervisionList.value.filter((item) =>
    item.createByName?.trim() && item.userId
  );

  if (validSupervisionList.length === 0) {
    ElMessage.error('请至少选择一个有效的监督人员');
    return;
  }

  // 提取监督人员的userId列表
  const supervisionUserIdList = validSupervisionList
    .map((item) => item.userId)

  if (supervisionUserIdList.length === 0) {
    ElMessage.error('监督人员ID无效，请重新选择');
    return;
  }

  try {
    loading.value = true;

    const submitData: BidOpenConfigData = {
      noticeId: noticeId.value,
      publicQuote: publicQuote.value,
      supervisionUserIdList: supervisionUserIdList,
    };

    if (bidOpener.value) {
      const bidOpenUserId = bidOpener.value;
      submitData.bidOpenUserId = bidOpenUserId;
    }

    await saveBidOpenConfig(submitData);

    ElMessage.success('保存成功');
    isEdit.value = false;
    originalData.value = JSON.parse(JSON.stringify(supervisionList.value));

    // 保存成功后更新开标人显示名称
    if (bidOpener.value) {
      const selectedUser = userOptions.value.find(user => user.userId === bidOpener.value);
      if (selectedUser) {
        bidOpenerName.value = selectedUser.name;
      }
    }
  } catch (error) {
    console.error('保存失败:', error);
    ElMessage.error('保存失败');
  } finally {
    loading.value = false;
  }
};

// 获取角色文本
const getRoleText = (role?: string) => {
  const roleMap: Record<string, string> = {
    PROJECT_LEADER: '项目负责人',
    PROJECT_MEMBER: '项目成员',
    EVALUATION_LEADER: '评审组长',
    EVALUATION_MEMBER: '评审成员',
    SUPERVISION: '监督人员',
  };
  return roleMap[role || ''] || role || '-';
};

// 组件挂载时初始化
onMounted(async () => {
  // 加载已有数据
  await loadBidOpeningData();
});
</script>

<style scoped lang="scss">
.bid-opening-setting {
  min-height: 100%;
  display: flex;
  flex-flow: column;
  gap: 20px;
  padding: 20px;
  width: 100%;
  .header-actions {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
    padding-bottom: 16px;
    border-bottom: 1px solid #f0f0f0;
  }

  .bid-info-section {
    margin-bottom: 20px;
    width: 100%;

    .info-row {
      display: flex;
      gap: 140px;
      align-items: center;

      .info-item {
        display: flex;
        align-items: center;
        gap: 8px;

        .info-label {
          font-weight: 500;
          color: #374151;
          white-space: nowrap;
        }

        .info-content {
          color: #6b7280;
        }
      }
    }
  }

  .supervisor-section {
    margin-bottom: 30px;
    width: 100%;

    .section-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;

      h4 {
        margin: 0;
        font-size: 16px;
        font-weight: 500;
        color: #374151;
        position: relative;

        &::before {
          content: '';
          position: absolute;
          left: -8px;
          top: 50%;
          transform: translateY(-50%);
          width: 4px;
          height: 16px;
          background: #409eff;
          border-radius: 2px;
        }
      }

      .section-actions {
        display: flex;
        gap: 8px;
      }
    }
  }

  .footer-actions {
    display: flex;
    justify-content: center;
    gap: 16px;
    padding-top: 24px;
    border-top: 1px solid #f0f0f0;
    margin-top: 24px;

    .el-button {
      min-width: 100px;
      height: 40px;
    }
  }
}

.form-actions-wrapper {
  width: 100%;
  box-sizing: border-box;
  background-color: #fff;
  z-index: 1000;
  position: sticky;
  bottom: -20px;
  left: 0;
  right: 0;
  padding: 16px 24px;
  display: flex;
  gap: 12px;
  border-top: 1px solid var(--Color-Border-border-color-light, #e4e7ed);

  .form-actions {
    display: flex;
    justify-content: flex-start;
    gap: 12px;
    width: 100%;
  }
}
</style>
