import ExcelJS from 'exceljs';
import { ElMessage } from 'yun-design';

// 按物料查看的数据结构
interface MaterialBidRecord {
  materialCode: string;
  materialName: string;
  specModel: string;
  requiredQuantity: number;
  supplierName: string;
  quoteQuantity: number;
  arrivalPrice: number;
  freight: number;
  factoryPrice: number;
  quoteAmount: number;
  quoteIp: string;
  ipAbnormal?: boolean;
}

// 按供应商查看的数据结构
interface SupplierViewRow {
  supplierName: string;
  contactName: string;
  contactPhone: string;
  roundNo: number;
  materialNum: number;
  quoteTotal: number;
  quoteIp: string;
  ipAbnormal?: boolean;
}

export function useBidOpeningExport() {

  // 导出按物料查看的数据
  async function exportMaterialData(data: MaterialBidRecord[], queryParams: any, roundNo) {
    try {
      const workbook = new ExcelJS.Workbook();
      const worksheet = workbook.addWorksheet('按物料查看');

      // 添加标题信息
      const titleRow = worksheet.getRow(1);
      titleRow.getCell(1).value = `开标记录 - 按物料查看 (第${roundNo}轮)`;
      titleRow.font = { bold: true, size: 14 };
      worksheet.mergeCells('A1:K1');

      const infoRow = worksheet.getRow(2);
      infoRow.getCell(1).value = `导出时间: ${new Date().toLocaleString()}`;
      infoRow.font = { size: 10, color: { argb: 'FF666666' } };
      worksheet.mergeCells('A2:K2');

      // 设置表头
      const headers = [
        '物料编码',
        '物料名称',
        '规格型号',
        '需求数量',
        '报价供应商',
        '可供数量',
        '出厂价',
        '运费',
        '到厂价',
        '总价',
        '投标IP'
      ];

      // 添加表头
      const headerRow = worksheet.getRow(4);
      headers.forEach((header, index) => {
        headerRow.getCell(index + 1).value = header;
      });

      // 设置表头样式
      headerRow.font = { bold: true };
      headerRow.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: 'FFE0E0E0' }
      };

      // 添加数据行
      data.forEach((item, index) => {
        const row = worksheet.getRow(index + 5);
        row.getCell(1).value = item.materialCode;
        row.getCell(2).value = item.materialName;
        row.getCell(3).value = item.specModel;
        row.getCell(4).value = item.requiredQuantity;
        row.getCell(5).value = item.supplierName;
        row.getCell(6).value = item.quoteQuantity;
        row.getCell(7).value = typeof item.factoryPrice === 'number' ? item.factoryPrice : parseFloat(item.factoryPrice) || 0;
        row.getCell(8).value = typeof item.freight === 'number' ? item.freight : parseFloat(item.freight) || 0;
        row.getCell(9).value = typeof item.arrivalPrice === 'number' ? item.arrivalPrice : parseFloat(item.arrivalPrice) || 0;
        row.getCell(10).value = typeof item.quoteAmount === 'number' ? item.quoteAmount : parseFloat(item.quoteAmount) || 0;
        row.getCell(11).value = item.quoteIp;

        // 设置数字格式
        [7, 8, 9, 10].forEach(colIndex => {
          row.getCell(colIndex).numFmt = '#,##0.00';
        });

        // 如果IP异常，设置红色字体
        if (item.ipAbnormal) {
          row.getCell(11).font = { color: { argb: 'FFFF0000' } };
        }
      });

      // 设置列宽
      worksheet.columns = [
        { width: 15 }, // 物料编码
        { width: 25 }, // 物料名称
        { width: 20 }, // 规格型号
        { width: 12 }, // 需求数量
        { width: 20 }, // 报价供应商
        { width: 12 }, // 可供数量
        { width: 12 }, // 出厂价
        { width: 10 }, // 运费
        { width: 12 }, // 到厂价
        { width: 12 }, // 总价
        { width: 15 }  // 投标IP
      ];

      // 添加边框
      worksheet.eachRow((row, rowNumber) => {
        row.eachCell((cell) => {
          cell.border = {
            top: { style: 'thin' },
            left: { style: 'thin' },
            bottom: { style: 'thin' },
            right: { style: 'thin' }
          };
        });
      });

      // 生成文件名
      const fileName = `开标记录-按物料查看-第${roundNo}轮-${new Date().toLocaleDateString()}.xlsx`;

      // 导出文件
      const buffer = await workbook.xlsx.writeBuffer();
      const blob = new Blob([buffer], {
        type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
      });

      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = fileName;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);

      ElMessage.success('导出成功');
    } catch (error) {
      console.error('导出失败:', error);
      ElMessage.error('导出失败');
    }
  }

  // 导出按供应商查看的数据
  async function exportSupplierData(data: SupplierViewRow[], queryParams: any, roundNo: any) {
    try {
      const workbook = new ExcelJS.Workbook();
      const worksheet = workbook.addWorksheet('按供应商查看');

      // 添加标题信息
      const titleRow = worksheet.getRow(1);
      titleRow.getCell(1).value = `开标记录 - 按供应商查看 (第${roundNo}轮)`;
      titleRow.font = { bold: true, size: 14 };
      worksheet.mergeCells('A1:H1');

      const infoRow = worksheet.getRow(2);
      infoRow.getCell(1).value = `导出时间: ${new Date().toLocaleString()}`;
      infoRow.font = { size: 10, color: { argb: 'FF666666' } };
      worksheet.mergeCells('A2:H2');

      // 设置表头
      const headers = [
        '序号',
        '供应商名称',
        '联系人',
        '联系电话',
        '报价轮次',
        '报价物料条数',
        '本轮报价总计',
        '报价IP'
      ];

      // 添加表头
      const headerRow = worksheet.getRow(4);
      headers.forEach((header, index) => {
        headerRow.getCell(index + 1).value = header;
      });

      // 设置表头样式
      headerRow.font = { bold: true };
      headerRow.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: 'FFE0E0E0' }
      };

      // 添加数据行
      data.forEach((item, index) => {
        const row = worksheet.getRow(index + 5);
        row.getCell(1).value = index + 1;
        row.getCell(2).value = item.supplierName;
        row.getCell(3).value = item.contactName;
        row.getCell(4).value = item.contactPhone;
        row.getCell(5).value = item.roundNo;
        row.getCell(6).value = item.materialNum;
        row.getCell(7).value = typeof item.quoteTotal === 'number' ? item.quoteTotal : parseFloat(item.quoteTotal) || 0;
        row.getCell(8).value = item.quoteIp;

        // 设置数字格式
        row.getCell(7).numFmt = '#,##0.00';

        // 如果IP异常，设置红色字体
        if (item.ipAbnormal) {
          row.getCell(8).font = { color: { argb: 'FFFF0000' } };
        }
      });

      // 设置列宽
      worksheet.columns = [
        { width: 8 },  // 序号
        { width: 25 }, // 供应商名称
        { width: 12 }, // 联系人
        { width: 15 }, // 联系电话
        { width: 12 }, // 报价轮次
        { width: 15 }, // 报价物料条数
        { width: 15 }, // 本轮报价总计
        { width: 15 }  // 报价IP
      ];

      // 添加边框
      worksheet.eachRow((row, rowNumber) => {
        row.eachCell((cell) => {
          cell.border = {
            top: { style: 'thin' },
            left: { style: 'thin' },
            bottom: { style: 'thin' },
            right: { style: 'thin' }
          };
        });
      });

      // 生成文件名
      const fileName = `开标记录-按供应商查看-第${roundNo}轮-${new Date().toLocaleDateString()}.xlsx`;

      // 导出文件
      const buffer = await workbook.xlsx.writeBuffer();
      const blob = new Blob([buffer], {
        type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
      });

      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = fileName;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);

      ElMessage.success('导出成功');
    } catch (error) {
      console.error('导出失败:', error);
      ElMessage.error('导出失败');
    }
  }

  return {
    exportMaterialData,
    exportSupplierData
  };
}
