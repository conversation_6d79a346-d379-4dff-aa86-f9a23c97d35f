<template>
  <div class="export-test">
    <h2>导出功能测试</h2>
    
    <div class="test-buttons">
      <el-button @click="testMaterialExport" type="primary">
        测试按物料导出
      </el-button>
      
      <el-button @click="testSupplierExport" type="success">
        测试按供应商导出
      </el-button>
    </div>
    
    <div class="test-data">
      <h3>测试数据预览</h3>
      
      <h4>物料数据:</h4>
      <pre>{{ JSON.stringify(mockMaterialData, null, 2) }}</pre>
      
      <h4>供应商数据:</h4>
      <pre>{{ JSON.stringify(mockSupplierData, null, 2) }}</pre>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { useBidOpeningExport } from './useBidOpeningExport';

const { exportMaterialData, exportSupplierData } = useBidOpeningExport();

// 模拟物料数据
const mockMaterialData = ref([
  {
    materialCode: 'MAT001',
    materialName: '钢材',
    specModel: 'Q235B',
    requiredQuantity: 100,
    supplierName: '供应商A',
    quoteQuantity: 100,
    arrivalPrice: 5000,
    freight: 200,
    factoryPrice: 4800,
    quoteAmount: 500000,
    quoteIp: '*************',
    ipAbnormal: false
  },
  {
    materialCode: 'MAT001',
    materialName: '钢材',
    specModel: 'Q235B',
    requiredQuantity: 100,
    supplierName: '供应商B',
    quoteQuantity: 90,
    arrivalPrice: 4900,
    freight: 180,
    factoryPrice: 4720,
    quoteAmount: 441000,
    quoteIp: '*************',
    ipAbnormal: true
  },
  {
    materialCode: 'MAT002',
    materialName: '水泥',
    specModel: 'P.O 42.5',
    requiredQuantity: 200,
    supplierName: '供应商C',
    quoteQuantity: 200,
    arrivalPrice: 450,
    freight: 50,
    factoryPrice: 400,
    quoteAmount: 90000,
    quoteIp: '*************',
    ipAbnormal: false
  }
]);

// 模拟供应商数据
const mockSupplierData = ref([
  {
    supplierName: '供应商A',
    contactName: '张三',
    contactPhone: '***********',
    roundNo: 1,
    materialNum: 5,
    quoteTotal: 1000000,
    quoteIp: '*************',
    ipAbnormal: false
  },
  {
    supplierName: '供应商B',
    contactName: '李四',
    contactPhone: '***********',
    roundNo: 1,
    materialNum: 3,
    quoteTotal: 800000,
    quoteIp: '*************',
    ipAbnormal: true
  },
  {
    supplierName: '供应商C',
    contactName: '王五',
    contactPhone: '***********',
    roundNo: 1,
    materialNum: 4,
    quoteTotal: 1200000,
    quoteIp: '*************',
    ipAbnormal: false
  }
]);

// 模拟查询参数
const mockQueryParams = {
  projectId: '20',
  noticeId: '19',
  sectionId: '28',
  roundNo: 1,
  materialName: '',
  supplierName: ''
};

// 测试按物料导出
async function testMaterialExport() {
  try {
    await exportMaterialData(mockMaterialData.value, mockQueryParams);
  } catch (error) {
    console.error('测试按物料导出失败:', error);
  }
}

// 测试按供应商导出
async function testSupplierExport() {
  try {
    await exportSupplierData(mockSupplierData.value, mockQueryParams);
  } catch (error) {
    console.error('测试按供应商导出失败:', error);
  }
}
</script>

<style scoped lang="scss">
.export-test {
  padding: 20px;
  
  .test-buttons {
    margin: 20px 0;
    display: flex;
    gap: 10px;
  }
  
  .test-data {
    margin-top: 30px;
    
    h3, h4 {
      margin: 15px 0 10px 0;
    }
    
    pre {
      background: #f5f5f5;
      padding: 10px;
      border-radius: 4px;
      font-size: 12px;
      max-height: 200px;
      overflow-y: auto;
    }
  }
}
</style>
