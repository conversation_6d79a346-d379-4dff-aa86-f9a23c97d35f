<template>
  <el-drawer
    v-model="visible"
    title="发起再次报价"
    :size="800"
    :before-close="handleClose"
  >
    <div class="re-quote-drawer">
      <el-form
        ref="formRef"
        :model="formData"
        :rules="rules"
        label-width="120px"
        class="quote-form"
      >
        <el-form-item
          label="报价截止时间"
          prop="latestQuoteEndTime"
        >
          <el-date-picker
            v-model="formData.latestQuoteEndTime"
            type="datetime"
            placeholder="请选择"
            format="YYYY-MM-DD HH:mm:ss"
            value-format="YYYY-MM-DD HH:mm:ss"
            style="width: 100%"
          />
        </el-form-item>

        <el-form-item
          label="开标时间"
          prop="bidOpenTime"
        >
          <el-date-picker
            v-model="formData.bidOpenTime"
            type="datetime"
            placeholder="请选择"
            format="YYYY-MM-DD HH:mm:ss"
            value-format="YYYY-MM-DD HH:mm:ss"
            style="width: 100%"
            :disabled="bidOpenTimeDisabled"
          />
        </el-form-item>

        <el-form-item
          label="选择标段"
          prop="sectionId"
          v-show="!isZJWT"
        >
          <el-select
            v-model="formData.sectionId"
            placeholder="请选择"
            style="width: 100%"
            @change="changeSupplier"
          >
            <el-option
              v-for="section in sectionList"
              :key="section.id"
              :label="section.sectionName"
              :value="section.id"
            />
          </el-select>
        </el-form-item>
      </el-form>
      <div class="supplier-section">
        <div class="flex justify-between">
          <div>
            <div class="header-title">选择重新报价的供应商</div>
          </div>
          <div class="quote-info" v-if="quoteCount">
            您将发起第 <span class="quote-count">{{ quoteCount + 1 }}</span> 轮报价，发起后供应商可再次报价，请确认是否发起？
          </div>
          <!--          <el-button type="primary" @click="openSupplierModal">选择供应商</el-button>-->
        </div>
        <el-table
          :data="supplierDataList"
          style="width: 100%; margin-top: 16px"
          @selection-change="handleSelectionChange"
          max-height="300"
        >
          <el-table-column
            type="selection"
            width="55"
          />
          <el-table-column
            type="index"
            prop="sequence"
            label="序号"
            width="80"
            align="center"
          />
          <el-table-column
            prop="value"
            label="供应商名称"
          />
        </el-table>
      </div>

      <div class="drawer-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button
          type="primary"
          @click="handleConfirm"
          :loading="loading"
          >确定</el-button
        >
      </div>
    </div>

    <!-- 供应商选择模态框 -->
    <!--    <SupplierSelectModal-->
    <!--      v-model="supplierModalVisible"-->
    <!--      :selected-suppliers="supplierDataList"-->
    <!--      @confirm="handleSupplierSelect"-->
    <!--    />-->
  </el-drawer>
</template>

<script setup lang="ts">
import { ref, reactive, computed } from 'vue';
import { ElMessage } from 'yun-design';
// import SupplierSelectModal from './SupplierSelectModal.vue'
import { getQuoteAgainSupplierList, getQuoteCount, getQuoteAgainBidOpenInfo } from '@/views/procurementSourcing/biddingProcess/api/bidOpening';
import { useBiddingStore } from '@/views/procurementSourcing/biddingProcess/stores';
import { getProjectSections } from '@/views/procurementSourcing/biddingProcess/api';
import { storeToRefs } from 'pinia';
import { useUserInfo } from '@/stores/userInfo';
const biddingStore = useBiddingStore();
interface Supplier {
  id: string;
  name: string;
  // sequence: number
}

interface Section {
  id: string;
  name: string;
}

interface Props {
  modelValue: boolean;
  // sectionList: Section[]
  confirm: Function;
  queryData: object;
  deadline?: string | Date; // 添加deadline属性
}

interface Emits {
  (e: 'update:modelValue', value: boolean): void;
  // (e: 'confirm', data: any): void
}

const props = withDefaults(defineProps<Props>(), {
  confirm: () => {},
  deadline: '', // 设置默认值
});
const projectId = computed(() => biddingStore?.projectId);
const projectDetail = computed(() => biddingStore?.projectDetail);
const noticeId = computed(() => biddingStore?.noticeId);
const quoteCount = ref(0);
const sectionList = ref([]);
const userInfoStore = useUserInfo();
const { userInfos } = storeToRefs(userInfoStore);

const isZJWT = computed(() => biddingStore?.isZJWT);


const isBidOpener = computed(() => {
  const userList = projectDetail.value.projectMemberList.map(item => item.userId)
  const userId = userInfos.value.user.userId;
  if(userId) {
    return userList.includes(userId)
  }
  return false
})

const emit = defineEmits<Emits>();
const visible = ref(false);

// const visible = computed({
//   get: () => props.modelValue,
//   set: (value) => emit('update:modelValue', value)
// })

const formRef = ref();
const loading = ref(false);
const supplierModalVisible = ref(false);
const bidOpenTimeDisabled = ref(false); // 开标时间是否禁用

// 表单数据
const formData = reactive({
  latestQuoteEndTime: '',
  bidOpenTime: '',
  sectionId: '',
  tenantSupplierIds: [] as string[],
});

const supplierDataList = ref<Supplier[]>([]);
const fetchSections = async () => {
  try {
    const { data } = await getProjectSections(projectId.value);
    sectionList.value = data
    if(sectionList.value.length === 1) {
      formData.sectionId = sectionList.value[0].id
      changeSupplier()
    }
  } catch (e) {
    console.log(e);
  }
};


// 标段列表（模拟数据，实际应该从API获取）
// const sectionList = ref<Section[]>([
//   { id: '1', name: '标段一' },
//   { id: '2', name: '标段二' },
//   { id: '3', name: '标段三' }
// ])

// 表单验证规则
const rules = {
  latestQuoteEndTime: [{ required: true, message: '请选择报价截止时间', trigger: 'change' }],
  bidOpenTime: [{ required: true, message: '请选择开标时间', trigger: 'change' }],
  sectionId: [{ required: true, message: '请选择标段', trigger: 'change' }],
  // tenantSupplierIds: [
  //   {
  //     required: true,
  //     validator: (rule: any, value: any, callback: any) => {
  //       if (supplierDataList.value.length === 0) {
  //         callback(new Error('请选择供应商'))
  //       } else {
  //         callback()
  //       }
  //     },
  //     trigger: 'change'
  //   }
  // ]
};

const handleSelectionChange = (val) => {
  formData.tenantSupplierIds = val.map((item: any) => item.key);
};

const changeSupplier = async () => {
  try {
    const quoteRoundVoList = biddingStore?.noticeInfo?.quoteRoundVoList
    const roundData = quoteRoundVoList.find((item: any) => String(item?.sectionId) === String(formData.sectionId)) || {}
    quoteCount.value = roundData.currentQuoteRound
    formData.tenantSupplierIds = [];
    const { data } = await getQuoteAgainSupplierList({
      sectionId: formData.sectionId,
      noticeId: noticeId.value,
      currentQuoteRound: quoteCount.value,
    });
    supplierDataList.value = data;
    fetchBidOpenInfo()
  } catch (e) {
    console.log(e);
  }
};

// 确认提交
const handleConfirm = async () => {
  try {
    await formRef.value?.validate();

    if(!formData.tenantSupplierIds.length) {
      return ElMessage.warning("请选择供应商")
    }

    loading.value = true;

    const submitData = {
      ...formData,
    };

    await props.confirm(submitData);

    // emit('confirm', submitData)
    // ElMessage.success('发起再次报价成功')
    handleClose();
  } catch (error) {
    console.error('表单验证失败:', error);
  } finally {
    loading.value = false;
  }
};

// 关闭抽屉
const handleClose = () => {
  // 重置表单
  formRef.value?.resetFields();
  supplierDataList.value = [];
  formData.tenantSupplierIds = [];

  // 重置开标时间禁用状态
  bidOpenTimeDisabled.value = false;

  visible.value = false;
};

// 获取开标时间信息
const fetchBidOpenInfo = async () => {
  try {
    const { data } = await getQuoteAgainBidOpenInfo({
      ...props.queryData,
      roundNo: quoteCount.value,
    });

    if (data && data.openTime) {
      // 如果返回的data对象里面有openTime，就使用它作为默认值，并禁用开标时间
      formData.bidOpenTime = data.openTime;
      bidOpenTimeDisabled.value = true;
      console.log('使用接口返回的开标时间:', data.openTime);
    } else {
      // 如果没有值，就取开标大厅的deadline作为默认值，可以修改
      formData.bidOpenTime = props.deadline || '';
      bidOpenTimeDisabled.value = false;
      console.log('使用deadline作为默认开标时间:', props.deadline);
    }
  } catch (error) {
    console.error('获取开标时间信息失败:', error);
    // 出错时使用deadline作为默认值
    formData.bidOpenTime = props.deadline || '';
    bidOpenTimeDisabled.value = false;
  }
};

defineExpose({
  show: async () => {
    visible.value = true;
    // initRoundsData()

    try {
      // const { data } = await getQuoteCount(props.queryData);
      // quoteCount.value = data ? +data + 1 : 0;
      fetchSections();

      // 获取开标时间信息
      await fetchBidOpenInfo();
    } catch (e) {
      console.log(e);
    }
  },
});
</script>

<style scoped lang="scss">
.re-quote-drawer {
  padding: 0 24px 24px;

  .quote-info {
    //background: #f8f9fa;
    //padding: 16px;
    //border-radius: 4px;
    //margin-bottom: 24px;
    color: #666;

    .quote-count {
      color: #409eff;
      font-weight: 600;
    }
  }

  .drawer-footer {
    display: flex;
    justify-content: center;
    gap: 16px;
    padding-top: 24px;
  }
}
</style>
