<template>
	<div class="bid-opening-hall-container">
		<div class="deadline-bar">
			<div class="deadline-bar-item">
				<div class="deadline-bar-item-title">
					<div class="deadline-bar-item-title-label">开标时间</div>
					<div class="deadline-bar-item-title-value">{{ formatDateTime(deadline) }}</div>
				</div>
				<div class="deadline-bar-item-countdown">
					<template v-if="!isOpenTimeReached">
            <div class="deadline-bar-item-countdown-label">剩余时间</div>
            <div class="deadline-bar-item-countdown-value">
              <!-- 天 -->
              <RollingNumber
                :value="countdown.days"
                :old-value="prevCountdown.days"
                :max-value="99"
                :tick="ticks.days"
                direction="down"
              />
              <div>天</div>
              <!-- 时 -->
              <RollingNumber
                :value="countdown.hours"
                :old-value="prevCountdown.hours"
                :max-value="23"
                :tick="ticks.hours"
                direction="down"
              />
              <div>时</div>
              <!-- 分 -->
              <RollingNumber
                :value="countdown.minutes"
                :old-value="prevCountdown.minutes"
                :max-value="59"
                :tick="ticks.minutes"
                direction="down"
              />
              <div>分</div>
              <!-- 秒 -->
              <RollingNumber
                :value="countdown.seconds"
                :old-value="prevCountdown.seconds"
                :max-value="59"
                :tick="ticks.seconds"
                direction="down"
              />
              <div>秒</div>
            </div>
          </template>
          <template v-else>
            <div>已达到开标时间</div>
          </template>
				</div>
			</div>
			<div class="actions">
        <template v-if="isSupplier">
          <el-button @click="handleSign" v-if="detailData.openStatus === '已开标'"> 签到 </el-button>
        </template>
        <template v-else>
          <el-button @click="handleReQuote" v-if="detailData.openStatus === '已开标' && !isZB"> 发起再次报价 </el-button>
          <el-button
            @click="handleStartBidOpening"
            v-if="detailData.openStatus === '待开标' && isBidOpener"
            :disabled="!isOpenTimeReached"
            :title="!isOpenTimeReached ? '未到达开标时间，无法开标' : ''"
          >
            手动开标
          </el-button>
          <el-button @click="handleEndBidOpening" v-if="detailData.openStatus === '已开标' && isBidOpener"> 结束开标 </el-button>
        </template>
			</div>
		</div>

		<div class="status-bar">
			<div class="status-bar-item">
				<div class="status-bar-item-icon">
					<img src="https://oss-public.yunlizhi.cn/frontend/fe-procurement-platform/bidOpeningStatus.svg" />
				</div>
				<div class="status-bar-item-content">
					<div class="status-bar-item-content-label">{{detailData.openStatus}}</div>
					<div class="status-bar-item-content-value">开标状态</div>
				</div>
			</div>
			<div class="status-bar-item">
				<div class="status-bar-item-icon">
					<img src="https://oss-public.yunlizhi.cn/frontend/fe-procurement-platform/bigOpeningFile.svg" />
				</div>
				<div class="status-bar-item-content">
					<div class="status-bar-item-content-label">{{detailData?.fileDeliveryCount || 0}}</div>
					<div class="status-bar-item-content-value">文件递交人数</div>
				</div>
			</div>
		</div>

		<div class="news-info">
			<div class="news-info-title">消息记录</div>
			<div class="news-info-content" ref="newsContentRef" @scroll="handleScroll">
				<template v-if="detailData.openStatus !== '未开标'">
					<div class="news-info-item" v-for="(item, index) in openLogInfoList" :key="index">
						<div class="news-info-item-detail">{{item.operationByName}}{{objectKeyValue[item.operationType]}}</div>
						<div class="news-info-item-time">{{item.operationTime}}</div>
					</div>
					<div v-if="loading" class="loading-more">加载中...</div>
					<div v-if="!hasMore && openLogInfoList.length > 0" class="no-more">没有更多数据了</div>
				</template>
				<template v-else>
					<div class="news-info-placeholder">开标后将显示消息记录</div>
				</template>
			</div>
		</div>
	</div>

	<!-- 发起再次报价抽屉 -->
	<ReQuoteDrawer
		ref="reQuoteDrawerRef"
		:queryData="formData"
		:quote-count="3"
		:confirm="handleReQuoteConfirm"
		:deadline="deadline"
	/>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, onUnmounted, computed, nextTick, watch } from 'vue';
import { useIntervalFn } from '@vueuse/core';
import RollingNumber from '../../common/RollingNumber/index.vue';
import ReQuoteDrawer from './components/ReQuoteDrawer.vue';
import { getRemainingTime } from '../../../utils/time-helper';
import {
  addBidOpeningOperationLog, endBidOpening,
  getBidOpeningOperationLog,
  startBidOpening,
  reQuote,
} from '@/views/procurementSourcing/biddingProcess/api/bidOpening';
import { useUserRole } from '@/views/procurementSourcing/biddingProcess/utils';
import { useMessage, useMessageBox } from '@/hooks/message';
import { useBiddingStore } from '@/views/procurementSourcing/biddingProcess/stores';
import { storeToRefs } from 'pinia';
import { useUserInfo } from '@/stores/userInfo';
const { isSupplier, isPurchaser } = useUserRole();

const userInfoStore = useUserInfo();
const { userInfos } = storeToRefs(userInfoStore);
const biddingStore = useBiddingStore()
// const projectDetail = computed(() => biddingStore?.projectDetail);
const noticeInfo = computed(() => biddingStore?.noticeInfo);
const isZB = computed(() => biddingStore?.isZB);

const isBidOpener = computed(() => {
  // console.log(noticeInfo.value);
  // const userList = projectDetail.value.projectMemberList.map(item => item.userId)
  if(isPurchaser.value) {
    const userId = userInfos.value.user.userId;
    return userId === noticeInfo.value.bidOpener
  }
  return false
  // if(userId) {
  //   return userList.includes(userId)
  // }
  // return false
})

const currentUser = computed(() => {
  return userInfoStore.userInfos.user;
});

const objectKeyValue = {
  JOIN: '进入开标大厅',
  LEAVE: '离开开标大厅',
  START: '开始开标',
  END: '结束开标',
  SIGN: '签到',
}


const noticeId = computed(() => biddingStore?.noticeId);
const projectId = computed(() => biddingStore?.projectId);

const formData = computed(() => ({
  noticeId: noticeId.value,
  projectId: projectId.value,
  // sectionId: '28',
  // sectionIdList: []
}))

const reQuoteDrawerRef = ref()

const pageParams = reactive({
  current: 1,
  size: 20,
})

const detailData = ref({})
const openLogInfoList = ref([])
const loading = ref(false)
const hasMore = ref(true)
const newsContentRef = ref(null)
// 截止时间
const deadline = ref(null);

// 倒计时数据
const countdown = ref({ days: 0, hours: 0, minutes: 0, seconds: 0 });
const prevCountdown = ref({ days: 0, hours: 0, minutes: 0, seconds: 0 });
const ticks = ref({ days: 0, hours: 0, minutes: 0, seconds: 0 });

// 判断是否已到达开标时间
const isOpenTimeReached = computed(() => {
  if (!deadline.value) return false;
  const remaining = getRemainingTime(deadline.value);
  return remaining.total <= 0;
});

function updateCountdown() {
  if(!deadline.value) return
	const remaining = getRemainingTime(deadline.value);
	// 先保存旧值
	const old = { ...countdown.value };
	// 天
	if (remaining.days !== old.days) ticks.value.days++;
	// 时
	if (remaining.hours !== old.hours) ticks.value.hours++;
	// 分
	if (remaining.minutes !== old.minutes) ticks.value.minutes++;
	// 秒
	if (remaining.seconds !== old.seconds) ticks.value.seconds++;

	countdown.value = {
		days: remaining.days,
		hours: remaining.hours,
		minutes: remaining.minutes,
		seconds: remaining.seconds,
	};
	prevCountdown.value = old;
}

// 使用 VueUse 的 useIntervalFn 来更新倒计时
const { pause, resume } = useIntervalFn(() => {
	updateCountdown();
}, 1000);

// 组件挂载时启动倒计时
onMounted(() => {
	updateCountdown();
	resume();
});

// 组件卸载时停止倒计时
onUnmounted(() => {
	pause();
});

// 格式化日期时间
function formatDateTime(date: Date): string {
	return date?.toLocaleString('zh-CN', {
		year: 'numeric',
		month: '2-digit',
		day: '2-digit',
		hour: '2-digit',
		minute: '2-digit',
		hour12: false,
	}) || '-';
}

// 检查是否需要自动加载更多数据来填满容器
const checkAndLoadMore = () => {
  if (!newsContentRef.value || !hasMore.value || loading.value) return

  const container = newsContentRef.value
  const { scrollHeight, clientHeight } = container

  // 如果内容高度小于容器高度 + 一些缓冲空间，说明还有空间，继续加载
  // 添加50px的缓冲，确保有足够的内容可以滚动
  if (scrollHeight <= clientHeight + 50) {
    setTimeout(() => {
      loadMore()
    }, 100) // 添加小延迟避免过于频繁的请求
  }
}

// 滚动事件处理
const handleScroll = (event) => {
  const { scrollTop, scrollHeight, clientHeight } = event.target
  // 当滚动到底部附近时加载更多数据
  if (scrollTop + clientHeight >= scrollHeight - 10 && hasMore.value && !loading.value) {
    loadMore()
  }
}

// 加载更多数据
const loadMore = () => {
  pageParams.current += 1
  getLogList(true)
}

// 签到
const handleSign = async () => {
  try {
    await handleAddBigOpening('SIGN')
    useMessage().success('签到成功')
    // 重置分页并重新加载
    pageParams.current = 1
    hasMore.value = true
    getLogList()
  } catch (e) {
    console.log(e);
  }
}

// 发起再次报价
const handleReQuote = () => {
  // reQuoteDrawerVisible.value = true
  reQuoteDrawerRef.value.show()
}

// 处理再次报价确认
const handleReQuoteConfirm = async (data) => {
  try {
    // 调用再次报价API
    await reQuote({
      ...formData.value,
      ...data
    })

    // 添加操作日志
    // await handleAddBigOpening('RE_QUOTE')

    // 重新加载消息列表
    pageParams.current = 1
    hasMore.value = true
    getLogList()
    biddingStore.initProjectDetail()
		// 需要更新公告---报价截止时间
    biddingStore?.getEffectNoticeData()
    useMessage().success('发起再次报价成功')
  } catch (error) {
    console.error('发起再次报价失败:', error)
    return Promise.reject()
  }
}

const handleStartBidOpening = async () => {
  useMessageBox()
    .confirm('是否确认手动开标?')
    .then(async () => {
      try {
        await startBidOpening(formData.value)
        useMessage().success('开标成功');
        handleAddBigOpening('START')
        // 重置分页并重新加载
        pageParams.current = 1
        hasMore.value = true
        getLogList()
        biddingStore.initProjectDetail()
      } catch (e) {
        console.log(e);
      }
    })
    .catch((err) => {
    });
}

const handleEndBidOpening = async () => {
  useMessageBox()
    .confirm('是否结束开标?')
    .then(async () => {
      try {
        await endBidOpening(formData.value)
        useMessage().success('结束开标成功');
        // 结束开标成功后才触发日志
        await handleAddBigOpening('END')
        // 重置分页并重新加载
        pageParams.current = 1
        hasMore.value = true
        getLogList()
        biddingStore.initProjectDetail()
      } catch (e) {
        console.log(e);
      }
    })
    .catch((err) => {
    });
}

const handleAddBigOpening = async (type) => {
  try {
    if(!isOpenTimeReached.value || detailData.value.openStatus !== '已开标') return
    if(!formData.value.noticeId) {
      return
    }
    await addBidOpeningOperationLog({
      ...formData.value,
      operationType: type,
      operationRoleEnum: isSupplier ? 'SUPPLIER' : 'BID_OPENER', // 操作角色(SUPPLIER-供应商、BID_OPENER-开标人)
      operationById: currentUser.value.userId,
      operationByName: currentUser.value.name
    })
    // await getLogList()
  } catch (e) {
    console.log(e);
  }
}

const getLogList = async (isLoadMore = false) => {
  if (loading.value) return

  try {
    loading.value = true
    const {data} = await getBidOpeningOperationLog({
      ...formData.value,
      ...pageParams
    })

    detailData.value = data
    const newRecords = data.openLogInfoList?.records || []

    if (isLoadMore) {
      // 追加数据
      openLogInfoList.value = [...openLogInfoList.value, ...newRecords]
    } else {
      // 初始加载或刷新
      openLogInfoList.value = newRecords
    }

    // 检查是否还有更多数据
    const total = data.openLogInfoList?.total || 0
    const currentTotal = openLogInfoList.value.length
    hasMore.value = currentTotal < total

    deadline.value = data.openTime
    // 在下一个tick检查是否需要继续加载数据来填满容器
    nextTick(() => {
      checkAndLoadMore()
    })
  } catch (e) {
    console.log(e);
  } finally {
    loading.value = false
  }
}

const init = async () => {
  try {
    const {data} = await getBidOpeningOperationLog({
      ...formData.value,
      current: 1,
      size: 1,
    })
    detailData.value = data
    deadline.value = data.openTime
    await handleAddBigOpening('JOIN')
    await getLogList()
  } catch (e) {
    console.log(e);
  }
}



watch(() => [projectId.value, noticeId.value], (newValues, oldValues) => {
  // 只有当两个值都存在时才执行 init
  if (newValues[0] && newValues[1]) {
    init()
  }
}, {
  immediate: true, // 立即执行一次
  // 移除 deep: true，因为监听的是基本类型值
})


const handleBeforeUnload = () => {
  handleAddBigOpening('LEAVE')
};

// 窗口大小改变时重新检查是否需要加载更多数据
const handleResize = () => {
  nextTick(() => {
    checkAndLoadMore()
  })
}

onMounted(async () => {
  window.addEventListener('beforeunload', handleBeforeUnload);
  window.addEventListener('resize', handleResize);
})

onUnmounted(() => {
  handleAddBigOpening('LEAVE')
  window.removeEventListener('beforeunload', handleBeforeUnload);
  window.removeEventListener('resize', handleResize);
});


biddingStore?.initData();
</script>

<style scoped lang="scss">
.bid-opening-hall-container {
	display: flex;
	flex-direction: column;
	height: 100%;
}

.deadline-bar {
	background: #fff;
	padding: 16px 20px 8px 20px;
	display: flex;
	justify-content: space-between;
	align-items: center;
	flex-shrink: 0;

	.deadline-bar-item {
		display: flex;
		&-title {
			display: flex;
			align-items: center;

			&-label {
				padding: 4px 6px;
				border-radius: 2px;
				background: var(--Color-Fill-fill-color, #f0f2f5);
				color: var(--Color-Text-text-color-regular, #4e5969);
				font-family: 'PingFang SC';
				font-size: 14px;
				font-style: normal;
				font-weight: 400;
				line-height: 16px;
			}

			&-value {
				color: var(--Color-Text-text-color-regular, #4e5969);
				font-family: 'PingFang SC';
				font-size: 14px;
				font-style: normal;
				font-weight: 600;
				line-height: 22px;
				margin-left: 8px;
			}
		}

		&-countdown {
			margin-left: 32px;
			display: flex;
			align-items: center;

			&-label {
				color: var(--Color-Text-text-color-regular, #4e5969);
				font-family: 'PingFang SC';
				font-size: 14px;
				font-style: normal;
				font-weight: 400;
				line-height: 16px;
				padding: 4px 6px;
			}

			&-value {
				color: var(--Color-Text-text-color-secondary, #86909c);
				font-family: 'PingFang SC';
				font-size: 12px;
				font-style: normal;
				font-weight: 400;
				line-height: 22px;
				display: flex;
				align-items: center;
				gap: 4px;

				.time-bar {
					width: 24px;
					height: 24px;
					border-radius: 2px;
					background: var(--Color-Error-color-error-light-9, #ffede8);
					display: flex;
					align-items: center;
					justify-content: center;
					color: var(--Color-Error-color-error, #ff3b30);
					font-family: 'PingFang SC';
					font-size: 14px;
					font-style: normal;
					font-weight: 600;
					line-height: 22px;
				}
			}
		}
	}
}

.status-bar {
	display: flex;
	align-items: center;
	gap: 128px;
	padding: 8px 20px 16px 20px;
	border-bottom: 1px solid var(--Color-Border-border-color, #dcdfe6);
	flex-shrink: 0;

	.status-bar-item {
		display: flex;
		align-items: center;
		gap: 16px;

		&-icon {
			width: 40px;
			height: 40px;
		}

		&-content {
			display: flex;
			flex-direction: column;
			gap: 3px;

			&-label {
				color: var(--Color-Text-text-color-primary, #1d2129);
				font-family: 'PingFang SC';
				font-size: 18px;
				font-style: normal;
				font-weight: 600;
				line-height: 30px;
			}

			&-value {
				color: var(--Color-Text-text-color-regular, #4e5969);
				font-family: 'PingFang SC';
				font-size: 14px;
				font-style: normal;
				font-weight: 400;
				line-height: 22px;
			}
		}
	}
}

.news-info {
	padding: 16px 20px;
	display: flex;
	flex-direction: column;
	flex: 1;
	min-height: 0;
	overflow: hidden;
	position: relative;

	&-title {
		color: var(--Color-Text-text-color-primary, #1d2129);
		font-family: 'PingFang SC';
		font-size: 16px;
		font-style: normal;
		font-weight: 600;
		line-height: 24px;
		padding-left: 10px;
		position: relative;
		margin-bottom: 16px;
		flex-shrink: 0;

		&::before {
			content: '';
			display: inline-block;
			width: 2px;
			height: 14px;
			background: var(--Color-Primary-color-primary, #0069ff);
			margin-right: 8px;
			position: absolute;
			left: 0;
			top: 50%;
			transform: translateY(-50%);
		}
	}

	&-content {
		position: absolute;
		top: 56px; // 标题高度(24px) + margin-bottom(16px)
		left: 20px;
		right: 20px;
		bottom: 16px;
		overflow-y: auto;
		display: flex;
		flex-direction: column;
		gap: 12px;

		&::-webkit-scrollbar {
			width: 6px;
		}

		&::-webkit-scrollbar-track {
			background: #f1f1f1;
			border-radius: 3px;
		}

		&::-webkit-scrollbar-thumb {
			background: #c1c1c1;
			border-radius: 3px;

			&:hover {
				background: #a8a8a8;
			}
		}
	}

	&-item {
		display: flex;
		align-items: center;
		justify-content: space-between;
		color: var(--Color-Text-text-color-secondary, #86909c);
		font-family: 'PingFang SC';
		font-size: 14px;
		font-style: normal;
		font-weight: 400;
		line-height: 22px;
		flex-shrink: 0;
	}

	.loading-more,
	.no-more {
		text-align: center;
		padding: 12px 0;
		color: var(--Color-Text-text-color-secondary, #86909c);
		font-size: 12px;
		flex-shrink: 0;
	}

	.loading-more {
		color: var(--Color-Primary-color-primary, #0069ff);
	}

	.news-info-placeholder {
		text-align: center;
		padding: 40px 20px;
		color: var(--Color-Text-text-color-secondary, #86909c);
		font-size: 14px;
		background: var(--Color-Fill-color-fill-light, #f7f8fa);
		border-radius: 4px;
		margin: 12px;
	}
}
</style>
