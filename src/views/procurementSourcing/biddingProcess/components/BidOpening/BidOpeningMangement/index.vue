<template>
  <div class="bid-opening-management-container">
    <!-- 步骤指示器 -->
    <!--    <div class="flex gap-8 items-center">-->
    <StepsIndicator
      :steps="stepsData"
      @step-click="handleStepClick"
    />

    <!--      <div v-if="isOfflineEvaluation" class="h-6 flex items-center text-[12px]">-->
    <!--        当前为线下开标，开标大厅和开标记录不可使用-->
    <!--      </div>-->
    <!--    </div>-->

    <!-- 动态组件渲染区域 -->
    <div class="component-content">
      <component :is="currentComponent" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import StepsIndicator, { type StepItem } from '../../StepsIndicator/index.vue';
import BidOpeningHall from '../BidOpeningHall/index.vue';
import BidOpeningRecord from '../BidOpeningRecord/index.vue';
import BidOpeningSetting from '../BidOpeningSetting/index.vue';
import { useBiddingStore } from '@/views/procurementSourcing/biddingProcess/stores';
import { useUserRole } from '@/views/procurementSourcing/biddingProcess/utils/useUserRole';

const biddingStore = useBiddingStore();
const isCompetitiveBidding = computed(() => biddingStore?.isCompetitiveBidding);
// 用户角色判断
const { isPurchaser } = useUserRole();

// 当前激活的步骤索引
const activeStepIndex = ref(0);

// 步骤数据
const stepsData = ref([]);

const noticeInfo = computed(() => biddingStore?.noticeInfo);
// 判断是否为线下评标
const isOfflineEvaluation = computed(() => {
  return noticeInfo.value?.tenderWay === 'OFFLINE';
});

watch(
  () => [isCompetitiveBidding.value, isOfflineEvaluation.value],
  (val) => {
    let arr = [
      {
        label: '开标大厅',
        completed: false,
        current: false,
        accessible: !isOfflineEvaluation.value,
        errorMsg: '当前为线下开标，开标大厅和开标记录不可使用',
      },
      {
        label: '开标记录',
        completed: false,
        current: false,
        accessible: !isOfflineEvaluation.value,
        errorMsg: '当前为线下开标，开标大厅和开标记录不可使用',
      },
    ];
    // if (isCompetitiveBidding.value && isPurchaser.value) {
    //   arr = [
    //     {
    //       label: '开标设置',
    //       completed: false,
    //       current: false,
    //       accessible: true,
    //     },
    //     ...arr,
    //   ];
    // }

    arr = [
      {
        label: '开标设置',
        completed: false,
        current: false,
        accessible: true,
      },
      ...arr,
    ];
    stepsData.value = arr.map((item, index) => {
      return {
        id: index,
        number: index + 1,
        ...item,
      };
    });
  },
  {
    immediate: true,
    deep: true,
  }
);

// 组件映射
const componentMap = computed(() => {
  return {
    0: BidOpeningSetting,
    1: BidOpeningHall,
    2: BidOpeningRecord,
  };
  if (isCompetitiveBidding.value && isPurchaser.value) {
    return {
      0: BidOpeningSetting,
      1: BidOpeningHall,
      2: BidOpeningRecord,
    };
  }
  return {
    0: BidOpeningHall,
    1: BidOpeningRecord,
  };
});

// 当前显示的组件
const currentComponent = computed(() => {
  return componentMap.value[activeStepIndex.value as keyof typeof componentMap];
});

// 处理步骤点击事件 - 只切换显示的组件，不修改步骤状态
function handleStepClick(step: StepItem, index: number) {
  stepsData.value.forEach((item) => {
    item.current = false;
  });
  activeStepIndex.value = index;
  stepsData.value[index].current = true;
}

handleStepClick(null, 0);

// const { initProjectDetail } = useBiddingStore();
// initProjectDetail();
</script>

<style lang="scss" scoped>
.bid-opening-management-container {
  //min-height: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;

  .component-content {
    flex: 1;
    margin-top: 12px;
    border-radius: 6px;
    background: var(--Color-Fill-fill-color-blank, #fff);
  }
}
</style>
