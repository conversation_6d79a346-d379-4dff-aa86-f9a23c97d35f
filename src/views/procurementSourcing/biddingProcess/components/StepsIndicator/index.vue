<template>
	<div class="steps-indicator">
		<template
			v-for="(step, index) in steps"
			:key="step.id || index"
		>
			<div
				class="step-item"
				:class="{
					completed: step.completed,
					current: step.current,
					default: !step.completed && !step.current,
					disabled: step.accessible === false,
				}"
				@click="handleStepClick(step, index)"
			>
				<div class="step-number">{{ step.number }}</div>
				<div class="step-label flex items-center">{{ step.label }}</div>
			</div>
			<!-- 添加箭头分隔符，除了最后一个步骤 -->
			<div
				v-if="index < steps.length - 1"
				class="step-separator"
			>
				<el-icon class="arrow-icon">
					<ArrowRight style="font-size: 12px" />
				</el-icon>
			</div>
		</template>
	</div>
</template>

<script setup lang="ts">
import { ArrowRight } from '@element-plus/icons-vue';
import { ElMessage } from 'yun-design';

// 导出类型定义
export interface StepItem {
	id?: string | number;
	number: string | number;
	label: string;
	completed: boolean;
	current?: boolean; // 新增当前节点状态
	show?: boolean; // 新增是否显示
	accessible?: boolean; // 新增可访问性状态
	errorMsg?: string; // 新增错误信息
}

export interface StepsIndicatorProps {
	steps: StepItem[];
}

export interface StepsIndicatorEmits {
	(e: 'step-click', step: StepItem, index: number): void;
}

const props = defineProps<StepsIndicatorProps>();
const emit = defineEmits<StepsIndicatorEmits>();

// 处理步骤点击事件 - 只发射事件，不修改状态
function handleStepClick(step: StepItem, index: number) {
	if (step?.errorMsg && step.accessible === false) {
		ElMessage.error(step.errorMsg);
		return;
	}
	// 检查步骤是否可访问
	if (step.accessible === false) {
		return; // 阻止访问不可用的步骤
	}

	emit('step-click', step, index);
}
</script>

<style lang="scss" scoped>
.steps-indicator {
	display: flex;
	align-items: center;

	.step-item {
		display: flex;
		align-items: center;
		gap: 8px;
		color: var(--Color-Text-text-color-primary, #1d2129);
		font-family: 'PingFang SC';
		font-size: 14px;
		font-style: normal;
		font-weight: 400;
		height: 24px;
		cursor: pointer;

		.step-number {
			display: flex;
			width: 18px;
			height: 18px;
			justify-content: center;
			align-items: center;
			border-radius: 3px;
			border: 1px solid var(--Color-Text-text-color-disabled, #c0c4cc);
			color: var(--Color-Text-text-color-regular, #4e5969);
			text-align: center;
			font-family: 'PingFang SC';
			font-size: 12px;
			font-style: normal;
			font-weight: 600;
			line-height: 20px;
		}

		// 已完成状态
		&.completed {
			.step-number {
				background: var(--Color-Success-color-success, #00B42A);
				color: #fff;
				border: none;
			}

			color: var(--Color-Text-text-color-primary, #1d2129);

			// 已完成且为当前节点时：绿色方框 + 蓝色文字
			&.current {
				.step-number {
					background: var(--Color-Success-color-success, #00B42A); // 保持绿色
					color: #fff;
					border: none;
				}

				color: var(--Color-Primary-color-primary, #0069FF); // 蓝色文字
				font-weight: 500;
			}
		}

		// 当前节点状态（未完成）
		&.current:not(.completed) {
			.step-number {
				background: var(--Color-Primary-color-primary, #0069FF);
				color: #fff;
				border: none;
			}

			color: var(--Color-Text-text-color-primary, #1d2129);
			font-weight: 500;
		}

		// 默认状态
		&.default {
			.step-number {
				border: 1px solid var(--Color-Text-text-color-disabled, #c0c4cc);
				color: var(--Color-Text-text-color-regular, #4e5969);
				background: transparent;
			}

			color: var(--Color-Text-text-color-secondary, #86909c);
		}

		// 不可访问状态
		&.disabled {
			cursor: not-allowed;
			color: var(--Color-Text-text-color-disabled, #c0c4cc);
		}
	}

	.step-separator {
		display: flex;
		align-items: center;
		margin: 0 10px;

		.arrow-icon {
			font-size: 16px;
			color: #86909c;
		}
	}
}
</style>
