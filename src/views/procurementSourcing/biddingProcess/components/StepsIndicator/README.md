# StepsIndicator 步骤指示器组件

一个可复用的步骤指示器组件，支持三种节点状态：已完成、当前、默认。

## 功能特性

- 支持三种状态：`completed`（已完成）、`current`（当前）、`default`（默认）
- 状态完全由外部数据控制，组件本身不修改状态
- 可点击切换，通过事件通知父组件
- 箭头分隔符美化界面
- TypeScript 类型安全

## 基础用法

```vue
<template>
  <StepsIndicator
    :steps="stepsData"
    @step-click="handleStepClick"
  />
</template>

<script setup lang="ts">
import { ref } from 'vue';
import StepsIndicator, { type StepItem } from './components/StepsIndicator/index.vue';

const stepsData = ref<StepItem[]>([
  {
    id: 1,
    number: 1,
    label: '采购项目',
    completed: true,   // 已完成状态
    current: false,
  },
  {
    id: 2,
    number: 2,
    label: '采购文件',
    completed: false,
    current: true,     // 当前状态
  },
  {
    id: 3,
    number: 3,
    label: '采购公告',
    completed: false,  // 默认状态
    current: false,
  },
]);

function handleStepClick(step: StepItem, index: number) {
  console.log('点击了步骤:', step, '索引:', index);
  // 这里只处理界面逻辑，状态由外部控制
}
</script>
```

## 状态类型

### 1. completed（已完成）
- 蓝色背景圆圈
- 白色数字
- 黑色粗体文字

### 2. current（当前）
- 橙色背景圆圈
- 白色数字
- 橙色粗体文字

### 3. default（默认）
- 透明背景，灰色边框圆圈
- 灰色数字
- 灰色文字

## 状态控制示例

```typescript
// 设置当前步骤
function setCurrentStep(index: number) {
  // 清除所有current状态
  stepsData.value.forEach((step) => {
    step.current = false;
  });
  // 设置指定步骤为当前
  if (stepsData.value[index]) {
    stepsData.value[index].current = true;
  }
}

// 完成指定步骤
function completeStep(index: number) {
  if (stepsData.value[index]) {
    stepsData.value[index].completed = true;
    stepsData.value[index].current = false;
  }
}

// 重置所有步骤
function resetAllSteps() {
  stepsData.value.forEach((step, index) => {
    step.completed = false;
    step.current = index === 0; // 第一个为当前
  });
}
```

## API

### Props

| 参数 | 说明 | 类型 | 默认值 |
|------|------|------|--------|
| steps | 步骤数据数组 | `StepItem[]` | `[]` |

### Events

| 事件名 | 说明 | 回调参数 |
|--------|------|----------|
| step-click | 点击步骤时触发 | `(step: StepItem, index: number)` |

### StepItem 类型定义

```typescript
interface StepItem {
  id?: string | number;     // 唯一标识
  number: string | number;  // 步骤编号
  label: string;           // 步骤标签
  completed: boolean;      // 是否已完成
  current?: boolean;       // 是否为当前步骤
}
```

## 注意事项

1. **状态控制**：组件本身不修改步骤状态，所有状态变更应该由父组件控制
2. **唯一性**：同一时间只应该有一个步骤处于 `current` 状态
3. **数据响应**：当步骤数据发生变化时，组件会自动更新UI
4. **事件处理**：`step-click` 事件只用于通知父组件，不应依赖此事件修改状态

## 完整示例

参考 `src/views/procurementSourcing/biddingProcess/components/announcement/AnnouncementInfo/index.vue` 文件中的完整使用示例。

## 动态更新示例

```vue
<script setup lang="ts">
import { ref } from 'vue';

const stepsData = ref<StepItem[]>([...]);

// 更新步骤状态
function updateStepStatus(stepId: number, completed: boolean) {
  const step = stepsData.value.find(s => s.id === stepId);
  if (step) {
    step.completed = completed;
  }
}

// 添加新步骤
function addStep(newStep: StepItem) {
  stepsData.value.push(newStep);
}
</script>
```

## 样式说明

组件使用了 CSS 变量，支持主题切换：

- `--Color-Text-text-color-primary`: 主要文本颜色
- `--Color-Text-text-color-disabled`: 禁用文本颜色
- `--Color-Text-text-color-regular`: 常规文本颜色
- `--Color-Primary-color-primary`: 主色调

组件保持了原有的布局和样式，包括：
- 18x18px 的步骤编号圆形
- 箭头分隔符
- 完成状态的蓝色背景
- 响应式字体和间距 