<template>
  <div
    class="bidding-process-container"
    :class="{ 'audit-mode': isAuditMode }"
  >
    <!-- 项目标题栏 -->
    <div
      v-if="!isAuditMode"
      class="project-title-header"
    >
      <div class="title-left">
        <el-button
          type="text"
          size="small"
          icon="ArrowLeft"
          @click="handleBack"
        >
          返回
        </el-button>
        <span class="project-title">{{ projectDetail?.projectName || '-' }}</span>

        <div
          class="bigging-label"
          v-if="isPublic"
        >
          公开询价
        </div>
        <div
          class="bigging-label"
          v-else
        >
          邀请询价
        </div>
      </div>
    </div>

    <div
      v-if="!isAuditMode"
      class="process-navigation"
    >
      <!-- 自定义标签页导航 -->
      <div class="custom-tab-navigation">
        <div class="tab-nodes">
          <div
            v-for="(node, index) in processNodes"
            :key="node.key"
            class="tab-node"
            :class="{
              active: currentNodeIndex === index && !isNodeCompletedFn(index),
              completed: isNodeCompletedFn(index) && currentNodeIndex !== index,
              'completed-active': isNodeCompletedFn(index) && currentNodeIndex === index,
              'current-progress': actualCurrentNodeIndex === index && currentNodeIndex !== index,
              disabled: !isNodeAccessible(index),
            }"
            @click="handleTabChange(index)"
          >
            <span
              v-if="!['contract', 'archive'].includes(node.key)"
              class="node-dot"
            ></span>
            <span
              v-else
              class="node-dot-normal"
            ></span>
            <span class="node-label">{{ node.label }}</span>
          </div>
        </div>
      </div>

      <div class="additional">
        <el-button
          type="text"
          size="small"
          @click="handleOnlineQa"
        >
          在线问答
          <el-icon class="ml-1">
            <ArrowRight />
          </el-icon>
        </el-button>
        <!-- <el-button
					type="text"
					size="small"
				>
					文件澄清
					<el-icon class="ml-1">
						<ArrowRight />
					</el-icon>
				</el-button> -->
      </div>
    </div>

    <!-- 当前节点内容 -->
    <div class="process-content">
      <component
        :is="currentComponent"
        v-if="currentComponent"
      />
    </div>

    <!-- 在线答疑抽屉 -->
    <OnlineQaDrawer v-model="onlineQaDrawerVisible" />
  </div>
</template>

<script setup lang="ts">
import { ref, watch, computed } from 'vue';
import { useUserRole } from './utils/useUserRole';
import { useRoute, useRouter } from 'vue-router';
import { useBiddingProcessFlow } from './hooks/useBiddingProcessFlow';
import { getCurrentActiveNodeIndex } from './utils/process-status-helper';
import { isNodeAccessible as isNodeAccessibleWithControl } from './utils/node-access-control';
import { enableDefaultRules } from './utils/node-access-manager';
import { enableDefaultVisibilityRules } from './utils/node-visibility-manager';
import { ArrowRight } from '@element-plus/icons-vue';
import OnlineQaDrawer from './components/OnlineQa/OnlineQaDrawer.vue';
import { useBiddingStore } from '@/views/procurementSourcing/biddingProcess/stores';

// 获取当前用户角色（mock）
const { userInfo } = useUserRole();
// mock 招标类型，后续可从 props、store、接口等获取
// const tenderType = ref<'public' | 'invite'>('public');

// 在线答疑抽屉控制
const onlineQaDrawerVisible = ref(false);

// 流标抽屉控制
// const tenderFailedDrawerVisible = ref(false);
const router = useRouter();
const route = useRoute();

// 使用整合后的流程 hook
const { processNodes, currentComponent, currentNodeIndex, setCurrentNode, progressStatus, isNodeCompletedFn, autoSetCurrentNode } =
  useBiddingProcessFlow({
    userRole: userInfo.value.role,
    // tenderType: isPublic.value,
    projectId: 'mock-project-id',
    initialNodeIndex: -1,
  });

// 获取实际的当前进行节点索引（基于项目状态和项目类型）
const actualCurrentNodeIndex = computed(() => {
  if (progressStatus.value && processNodes.value.length > 0) {
    const projectType = projectDetail.value?.sourcingType;
    const tenderType = isPublic.value ? 'public' : 'invite';
    return getCurrentActiveNodeIndex(userInfo.value.role, progressStatus.value, processNodes.value, projectType, tenderType);
  }
  return 0;
});

// 判断节点是否可访问（当前选中节点、实际当前节点、已完成节点或特殊访问权限）
function isNodeAccessible(index: number): boolean {
  const node = processNodes.value[index];
  if (!node) return false;

  const projectType = projectDetail.value?.sourcingType || '';
  const tenderType = isPublic.value ? 'public' : 'invite';
  const currentProgressStatus = progressStatus.value || '';

  // 使用新的节点访问控制逻辑
  return isNodeAccessibleWithControl(
    index,
    node.key,
    currentNodeIndex.value,
    actualCurrentNodeIndex.value,
    isNodeCompletedFn.value(index),
    projectType,
    userInfo.value.role,
    tenderType,
    currentProgressStatus
  );
}

// 切换节点事件
function handleTabChange(index: number) {
  // 只有可访问的节点才能切换
  if (isNodeAccessible(index)) {
    setCurrentNode(index);
  }
}

// 审核模式判断
const isAuditMode = computed(() => {
  // 可以根据多种方式判断审核模式：
  // 1. 路由查询参数 audit=true
  // 2. 路由查询参数 mode=audit
  // 3. 或者其他业务逻辑判断
  return route.query.audit === 'true' || route.query.mode === 'audit';
});

// step参数处理
const step = route.query.step;

// 在审核模式下，根据step参数设置当前节点
const handleAuditModeStep = () => {
  if (isAuditMode.value && step) {
    const stepIndex = parseInt(step as string) - 1; // step从1开始，索引从0开始
    if (stepIndex >= 0 && stepIndex < processNodes.value.length) {
      setCurrentNode(stepIndex);
    }
  }
};

// 处理在线答疑点击
function handleOnlineQa() {
  onlineQaDrawerVisible.value = true;
}

const handleBack = () => {
  router.back();
};

const biddingStore = useBiddingStore();

const projectDetail = computed(() => biddingStore?.projectDetail);

const isPublic = computed(() => projectDetail.value?.inviteMethod === 'PUBLICITY');

// 初始化项目详情并自动设置当前节点
const initializeProcess = async () => {
  // 启用默认的节点访问规则
  enableDefaultRules();
  // 启用默认的节点可见性规则
  enableDefaultVisibilityRules();

  await biddingStore.initProjectDetail();
  // 如果是审核模式，优先根据step参数设置节点
  if (isAuditMode.value) {
    handleAuditModeStep();
  } else {
    // 否则根据progressStatus自动设置当前节点
    autoSetCurrentNode();
  }
};

initializeProcess();

// 监听项目详情变化，自动调整流程节点
watch(
  () => progressStatus.value,
  (newStatus) => {
    if (newStatus && !isAuditMode.value) {
      // 非审核模式下才自动设置节点
      // autoSetCurrentNode();
    }
  },
  { immediate: false }
);

// 监听路由查询参数变化，处理审核模式切换
watch(
  () => route.query,
  () => {
    if (isAuditMode.value) {
      handleAuditModeStep();
    }
  },
  { immediate: false }
);
</script>

<style lang="scss" scoped>
.bidding-process-container {
  position: relative;
  padding: 20px;
  background-color: #f9fafb;
  height: calc(100vh - 84px);
  background: #f0f2f5;
  overflow-y: auto;
  display: flex;
  flex-direction: column;

  // 审核模式样式
  &.audit-mode {
    padding: 10px;
    height: 100vh; // 审核模式下占满整个视口
    overflow-y: unset;
    .process-content {
      margin-top: 0; // 移除顶部间距
    }
  }
}

// 项目标题栏样式
.project-title-header {
  display: flex;
  justify-content: space-between;
  align-items: center;

  .title-left {
    display: flex;
    align-items: center;
    gap: 20px;

    .project-title {
      color: var(--Color-Text-text-color-primary, #1d2129);
      font-family: 'PingFang SC';
      font-size: 18px;
      font-style: normal;
      font-weight: 600;
      line-height: 22px;
    }

    .bigging-label {
      display: flex;
      padding: 0px 8px;
      justify-content: center;
      align-items: center;
      gap: 4px;
      border-radius: var(--Radius-border-radius-small, 2px);
      background: var(--Color-Primary-color-primary, #0069ff);
      color: var(--color-white, #fff);
      text-align: center;
      font-family: 'PingFang SC';
      font-size: 12px;
      font-style: normal;
      font-weight: 400;
      line-height: 20px;
    }
  }

  .title-right {
    display: flex;
    align-items: center;
    gap: 20px;

    .dropdown-trigger {
      display: flex;
      align-items: center;
      gap: 4px;
      cursor: pointer;

      .dropdown-text {
        color: var(--Color-Error-color-error, #ff3b30);
        text-align: center;
        /* medium/base */
        font-family: 'PingFang SC';
        font-size: 14px;
        font-style: normal;
        font-weight: 500;
        line-height: 22px;
      }

      .dropdown-icon {
        color: var(--Color-Error-color-error, #ff3b30);
      }
    }

    .dropdown-item-content {
      display: flex;
      align-items: center;
      gap: 8px;

      .status-dot {
        width: 8px;
        height: 8px;
        border-radius: 50%;
        flex-shrink: 0;

        &.normal {
          background-color: #00b42a;
        }

        &.warning {
          background-color: #ffd700;
        }

        &.exception {
          background-color: #ff0000;
        }

        &.urgent {
          background-color: #ff0000;
        }
      }

      span {
        font-size: 14px;
        font-weight: 500;
        color: var(--Color-Text-text-color-primary, #1d2129);
      }
    }
  }
}

.process-navigation {
  margin: 16px 0;
  display: flex;
  justify-content: space-between;
  align-items: center;

  .additional {
    display: flex;
    gap: 16px;

    .el-button {
      display: flex;
      align-items: center;

      .ml-1 {
        margin-left: 4px;
      }
    }
  }
  // 自定义标签页导航
  .custom-tab-navigation {
    display: flex;
    align-items: center;

    .tab-title {
      font-size: 18px;
      font-weight: 600;
      color: var(--Color-Text-text-color-primary, #1d2129);
      margin-right: 40px;
    }

    .tab-nodes {
      display: flex;
      gap: 16px;

      .tab-node {
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        width: 100px;
        height: 40px;
        position: relative;
        transition: all 0.2s ease;
        background: url('./asset/imgs/unstart-step.png') no-repeat center center;
        background-size: contain;
        color: var(--Color-Text-text-color-regular, #4e5969);
        text-align: center;
        font-family: 'PingFang SC';
        font-size: 16px;
        font-style: normal;
        font-weight: 400;
        line-height: 24px;

        &.active {
          background: url('./asset/imgs/processing-step.svg') no-repeat center center;
          background-size: contain;
          color: var(--Color-Text-text-color-primary, #1d2129);
          text-align: center;
          font-family: 'PingFang SC';
          font-size: 16px;
          font-style: normal;
          font-weight: 500;
          line-height: 24px;
          .node-dot {
            background-color: #0069ff;
          }
        }

        &.completed {
          background: url('./asset/imgs/complete-step.png') no-repeat center center;
          background-size: contain;
          color: var(--Color-Text-text-color-regular, #4e5969);
          text-align: center;
          font-family: 'PingFang SC';
          font-size: 16px;
          font-style: normal;
          font-weight: 400;
          line-height: 24px;
          .node-dot {
            background-color: #00b42a;
          }
        }

        &.completed-active {
          background: url('./asset/imgs/processing-step.svg') no-repeat center center;
          background-size: contain;
          color: var(--Color-Text-text-color-primary, #1d2129);
          text-align: center;
          font-family: 'PingFang SC';
          font-size: 16px;
          font-style: normal;
          font-weight: 500;
          line-height: 24px;
          .node-dot {
            background-color: #00b42a;
          }
        }

        &.current-progress {
          //background: url('./asset/imgs/processing-step.svg') no-repeat center center;
          //background-size: contain;
          //color: var(--Color-Text-text-color-primary, #1d2129);
          //text-align: center;
          //font-family: 'PingFang SC';
          //font-size: 16px;
          //font-style: normal;
          //font-weight: 500;
          //line-height: 24px;
          //border: 2px solid #0069ff;

          .node-dot {
            background-color: #0069ff;
          }

          // 添加进行中标识
          //&::before {
          //  content: '●';
          //  position: absolute;
          //  top: -5px;
          //  left: -5px;
          //  width: 16px;
          //  height: 16px;
          //  background: #0069ff;
          //  color: white;
          //  border-radius: 50%;
          //  font-size: 8px;
          //  display: flex;
          //  align-items: center;
          //  justify-content: center;
          //  animation: pulse 2s infinite;
          //}
        }

        &.disabled {
          opacity: 0.5;
          cursor: not-allowed;
          pointer-events: none;

          &:hover {
            transform: none;
          }
        }

        .node-dot {
          display: inline-block;
          width: 5px;
          height: 5px;
          border-radius: 50%;
          background-color: #86909c;
          margin-right: 4px;
        }

        .node-dot-normal {
          display: inline-block;
          width: 5px;
          height: 5px;
          border-radius: 50%;
          background-color: #0069ff;
          margin-right: 4px;
        }

        .node-label {
          font-size: 14px;
          font-weight: 500;
        }
      }
    }
  }
}

.process-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  .content-placeholder {
    text-align: center;
    padding: 2rem;

    .placeholder-title {
      font-size: 1.25rem;
      font-weight: 600;
      color: #1f2937;
      margin-bottom: 1rem;
    }

    .placeholder-description {
      color: #6b7280;
      margin-bottom: 2rem;
      line-height: 1.6;
    }

    .process-controls {
      display: flex;
      justify-content: center;
      gap: 1rem;
      margin-top: 2rem;
      padding-top: 1.5rem;
      border-top: 1px solid #e5e7eb;

      .btn {
        padding: 0.5rem 1rem;
        border-radius: 0.375rem;
        border: none;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.2s ease;

        &.btn-primary {
          background-color: #3b82f6;
          color: white;

          &:hover {
            background-color: #2563eb;
          }
        }

        &.btn-secondary {
          background-color: #6b7280;
          color: white;

          &:hover {
            background-color: #4b5563;
          }
        }
      }
    }
  }
}
</style>

<style lang="scss">
@import './styles/index.scss';
</style>
