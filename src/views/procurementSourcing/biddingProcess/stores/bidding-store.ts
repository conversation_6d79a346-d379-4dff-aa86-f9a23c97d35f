/**
 * 投标状态管理
 */
import { defineStore, storeToRefs } from 'pinia'
import { ref, computed, shallowRef, watch, onMounted } from 'vue'
// import { useRoute } from 'vue-router'
import { getProjectDetail, getEffectNotice, getSupplierInfo, getOwnSupplier } from '@/api/purchasing/bid';
import { useUserRole, getParameterByName } from '@/views/procurementSourcing/biddingProcess/utils';
import moment from 'moment';
import { getCityList } from '@/api/purchasing/plan';
import { fetchList as getBaseUsageLocation } from '@/api/lowcode/base-usage-location/index';
import { getDynamicFields } from '@/api/purchasing/config';
import {
  getNegotiationDocDetail
} from '@/views/procurementSourcing/biddingProcess/components/announcement/CNDocument/api';
import { getMyMemberList, getNoticeEvaluationCompleteStatus } from '@/api/purchasing/evaluation';
import { SOURCING_TYPES, isCompetitiveBiddingType } from '../constants/sourcing-types';

import { useUserInfo } from '@/stores/userInfo';

// 定义城市数据结构的类型
interface District {
  adcode: string;
  name: string;
}

interface City {
  name: string;
  districts?: District[];
}

interface Province {
  name: string;
  districts?: City[];
}

export const useBiddingStore = defineStore('bidding', () => {
  const { isSupplier } = useUserRole();

  // 动态字段
  const baseServiceTypeFieldList = shallowRef<any[]>([]);


  const userInfoStore = useUserInfo();
  const { userInfos } = storeToRefs(userInfoStore);

  // 行政区数据
  const cityList = shallowRef<Province[]>([]);
  // 需求牧场数据
  const usageLocationList = shallowRef<any[]>([]);
  // 获取行政区数据
  async function getCityTree() {
    try {
      const res = await getCityList();
      cityList.value = res?.districts?.[0]?.districts || [];
    } catch (error) {
      cityList.value = [];
    }
  }
  // 获取需求牧场数据
  async function getUsageLocation() {
    try {
      const usageLocationRes = await getBaseUsageLocation({ statusList: 'ENABLED' }, { page: 1, size: 1000 });
      usageLocationList.value = (usageLocationRes?.data?.records || []).map((i: any) => {
        return {
          label: i.locationName,
          value: i.id,
          ...i,
        };
      });
    } catch (error) {
      usageLocationList.value = [];
    }
  }

  // 获取行政区数据
  async function getDynamicFieldsFn(serviceTypeId: string, sourcingType: string) {
    try {
      const res = await getDynamicFields(serviceTypeId, sourcingType);
      baseServiceTypeFieldList.value = res?.data?.baseServiceTypeFieldList || [];
    } catch (error) {
      baseServiceTypeFieldList.value = [];
    }
  }

  // 创建区县代码到地址的映射缓存
  const districtAddressMap = computed(() => {
    const map = new Map<string, string>();
    cityList.value?.forEach((province) => {
      province.districts?.forEach((city) => {
        city.districts?.forEach((district) => {
          if (district.adcode) {
            map.set(district.adcode, `${province.name} / ${city.name} / ${district.name}`);
          }
        });
      });
    });

    return map;
  });

  // 根据district查找完整的省市区信息（优化版本）
  const getAddressByDistrict = (districtCode: string): string => {
    if (!districtCode) return '';
    return districtAddressMap.value.get(districtCode) || '';
  };


  // 项目详情
  const projectDetail = ref<any>(null);
  const projectId = computed(() => projectDetail?.value?.id);
  // 公告详情
  const noticeInfo = ref<any>(null);
  const noticeId = computed(() => noticeInfo?.value?.id);
  const hasNotice = computed(() => projectDetail?.value?.effectNotice);
  // 竞谈文件详情
  const cndDocumentInfo = ref<any>(null);
  // 租户供应商数据
  const supplierInfoList = ref<any>([]);
  // 账户供应商信息
  const ownSupplier = ref<any>({});
  // 查询当前公告下的需要保证金的标段
  const needBondSectionList = computed(() => {
    const res = (noticeInfo?.value?.requirementList || [])?.map((item: any) => {
      try {
        return {
          sectionId: item?.sectionId || '',
          requirementType: item?.requirementType,
          requirementContent: JSON.parse(item?.requirementContent || '{}'),
        }
      } catch (error) {
        return {
          sectionId: item?.sectionId || '',
          requirementType: item?.requirementType,
          requirementContent: {},
        }
      }
    });
    return res.filter((item: any) => item?.requirementType === 'FEE' && item?.requirementContent?.guaranteeAmount > 0)?.map((item: any) => item.sectionId);
  });
  // 是否是公开招标
  const isPublic = computed(() => projectDetail?.value?.inviteMethod === 'PUBLICITY');
  // 截止时间
  const deadline = computed(() => noticeInfo?.value?.quoteEndTime);
  const deadlineTime = computed(() => {
    if (!deadline.value) return null;
    const time = moment(deadline.value).unix();
    const now = moment().unix();
    if (time < now) return '报价时间已截止';
    return null;
  });

  // 是否是招标项目负责人
  const isProjectLeader = computed(() => {
    const userList = projectDetail.value?.projectMemberList?.filter((item: any) => item.role === 'PROJECT_LEADER').map((item: any) => item.userId)
    const userId = userInfos.value.user.userId;
    if (userId) {
      return userList?.includes(userId)
    }
    return false
  })



  // 获取采购立项详情
  async function getProjectDetailData(extParams = {}) {
    try {
      // const route = useRoute()
      const res = await getProjectDetail({ projectCode: getParameterByName('projectCode'), ...extParams });
      projectDetail.value = res?.data || null;
    } catch (error) {
      // eslint-disable-next-line no-console
      console.error(error, "00000")
      projectDetail.value = null;
    }
  }

  // 获取当前项目有效的招标公告 TODO: 20需要修改
  async function getEffectNoticeData() {
    const projeceId = projectDetail.value?.id;
    if (!projeceId) {
      return;
    }
    try {
      const res = await getEffectNotice(projeceId);
      noticeInfo.value = res?.data || null;
    } catch (error) {
      noticeInfo.value = null;
    }
  }

  // 初始化一些必要数据
  async function initData() {
    try {
      // await initProjectDetail()
      // await getProjectDetailData();
      // await getEffectNoticeData();
      getSupplierInfoData()
      // 供应商角色才需要
      if (isSupplier.value) {
        await getOwnSupplierData()
      }

      // 获取行政区数据 避免重复请求
      if (cityList.value?.length === 0) {
        getCityTree();
      }
      // 获取需求牧场数据 避免重复请求
      if (usageLocationList.value?.length === 0) {
        getUsageLocation();
      }
      // eslint-disable-next-line no-empty
    } catch (error) { }
  }
  // 供方获取供自己的报名信息
  async function getSupplierInfoData(extParams: any = {}) {
    try {
      const res = await getSupplierInfo({
        status: 'ENABLED',
        // approvalStatus: 'APPROVED',
        ...extParams,
      });
      const list = res?.data?.records || [];
      supplierInfoList.value = list.map((item: any) => ({
        ...item,
        id: item?.id || '',
        label: item?.supplierName || '',
        value: item?.supplierCode || '',
      }));
    } catch (error) {
      supplierInfoList.value = [];
    }
  }
  // 获取当前登录账号的供应商信息
  async function getOwnSupplierData() {
    try {
      const res = await getOwnSupplier();
      ownSupplier.value = res?.data || null;
    } catch (error) {
      ownSupplier.value = null;
    }
  }

  async function initProjectDetail() {
    projectDetail.value = null;
    noticeInfo.value = null;
    await getProjectDetailData();
    if (projectDetail?.value?.effectNotice) {
      await getEffectNoticeData();
    }
  }

  async function initCndDocument() {
    try {
      cndDocumentInfo.value = null;
      const {data} = await getNegotiationDocDetail(noticeId.value);
      cndDocumentInfo.value = data
    } catch (e) {
      // eslint-disable-next-line no-console
      console.log(e);
    }
  }

  watch(() => projectDetail.value, (newVal) => {
    if (newVal?.serviceTypeId && newVal?.sourcingType) {
      getDynamicFieldsFn(newVal?.serviceTypeId, newVal?.sourcingType);
    }
    loadMyMemberList();
  })

  onMounted(() => { })

  // 获取当前登录人的id
  const stores = useUserInfo();
  const currentUserId = computed(() => stores?.userInfos?.user?.userId);
  // 是开标人
  const isBidOpener = computed(() => currentUserId?.value === noticeInfo?.value?.bidOpener);
  // 获取成员类型
  const memberList = ref<any[]>([]);
  async function loadMyMemberList(pid?: any) {
    const pids = pid || projectId.value;
    if (!pids) {
      return;
    }
    try {
      const res = await getMyMemberList(pids);
      memberList.value = res?.data || [];
    } catch (error) {
      memberList.value = [];
    }
  }

 // 是项目小组成员
  const isProjectMemberFn = (p: any) => p?.memberType === 'PROJECT_MEMBER' && p?.role === 'PROJECT_MEMBER' && p?.userId === currentUserId.value
  const isProjectMember = computed(() => memberList.value?.some(isProjectMemberFn))

   // 是项目小组负责人
   const isProjectMemberLeaderFn = (p: any) => p?.memberType === 'PROJECT_MEMBER' && p?.role === 'PROJECT_LEADER' && p?.userId === currentUserId.value
   const isProjectMemberLeader = computed(() => memberList.value?.some(isProjectMemberLeaderFn))

  // 监督成员
  const isSupervisionFn = (p: any) => p?.memberType === 'SUPERVISION' && p?.role === 'SUPERVISION' && p?.userId === currentUserId.value
  const isSupervision = computed(() => memberList.value?.some(isSupervisionFn))

  // 评标委员会专家
  const isEvaluationMemberFn = (p: any) => p?.memberType === 'EVALUATION_MEMBER' && p?.role === 'EVALUATION_LEADER' && p?.userId === currentUserId.value
  const isEvaluationMember = computed(() => memberList.value?.some(isEvaluationMemberFn))

  // 评标委员会成员
  const isEvaluationCommitteeMemberFn = (p: any) => p?.memberType === 'EVALUATION_MEMBER' && p?.role === 'EVALUATION_MEMBER' && p?.userId === currentUserId.value
  const isEvaluationCommitteeMember = computed(() => memberList.value?.some(isEvaluationCommitteeMemberFn))

  // 项目小组(成员|组长)+监督成员+评标专家
  const isMSEMember = computed(() => isProjectMemberLeader.value || isProjectMember.value || isSupervision.value || isEvaluationMember.value || isEvaluationCommitteeMember.value)

  // 开标结束后
  // 状态见 D:\workspace\fe-procurement-platform\src\views\procurementSourcing\biddingProcess\constants\process-status.ts
  const isBidOpened = computed(
    () => !['TO_NOTICE', 'NOTICE', 'REGISTER', 'QUOTING', 'TO_BID_OPEN', 'BID_OPENED'].includes(projectDetail.value?.progressStatus)
  );
  // 判断评审项是否全部完成
  const isEvaluationAllCompleted = ref(false);
  async function checkEvaluationAllCompleted() {
    if (!noticeId.value) {
      return;
    }
    try {
      const res = await getNoticeEvaluationCompleteStatus(noticeId.value);
      isEvaluationAllCompleted.value = res?.data;
    } catch (error) {
      isEvaluationAllCompleted.value = false;
    }
  }

  const isXJCG = computed(() => projectDetail.value?.sourcingType === SOURCING_TYPES.XJCG)
  // 采购类型相关的计算属性
  const isZJWT = computed(() => projectDetail.value?.sourcingType === SOURCING_TYPES.ZJWT)
  const isJZTP = computed(() => projectDetail.value?.sourcingType === SOURCING_TYPES.JZTP)
  const isZB = computed(() => projectDetail.value?.sourcingType === SOURCING_TYPES.ZB)

  // 竞争性采购类型（JZTP 和 ZB 逻辑相同）
  const isCompetitiveBidding = computed(() =>
    isCompetitiveBiddingType(projectDetail.value?.sourcingType)
  )

  // 是否展示标段搜索框和字段列
  const isShowSection = computed(() => {
    const s = projectDetail.value?.projectSectionList;
    if (!s || !Array.isArray(s) || s?.length < 2) {
      return false;
    }
    return true;
  })

  return {
    isProjectLeader,

    cityList,
    getCityTree,

    usageLocationList,
    getUsageLocation,

    getAddressByDistrict,

    projectId,
    projectDetail,
    getProjectDetailData,

    noticeId,
    noticeInfo,
    hasNotice,
    getEffectNoticeData,

    supplierInfoList,
    getSupplierInfoData,

    ownSupplier,
    getOwnSupplierData,

    initData,
    initProjectDetail,

    initCndDocument,
    cndDocumentInfo,

    needBondSectionList,
    isPublic,

    deadline,
    deadlineTime,

    baseServiceTypeFieldList,

    isBidOpener,
    currentUserId,

    loadMyMemberList,
    memberList,
    isProjectMemberFn,
    isProjectMember,
    isProjectMemberLeaderFn,
    isProjectMemberLeader,
    isSupervisionFn,
    isSupervision,
    isEvaluationMemberFn,
    isEvaluationMember,
    isEvaluationCommitteeMemberFn,
    isEvaluationCommitteeMember,

    isMSEMember,
    isBidOpened,

    isEvaluationAllCompleted,
    checkEvaluationAllCompleted,
    getDynamicFieldsFn,

    isZJWT,
    isJZTP,
    isXJCG,
    isZB,
    isCompetitiveBidding,
    isShowSection,
  }
})
