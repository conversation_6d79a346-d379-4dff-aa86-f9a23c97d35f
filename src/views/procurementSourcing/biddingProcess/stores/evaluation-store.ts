/**
 * 评标状态管理
 */
import { defineStore } from 'pinia'
import { ref } from 'vue'

export const useEvaluationStore = defineStore('evaluation', () => {
  // 评标信息
  const evaluationInfo = ref(null)

  // 专家列表
  const expertList = ref<any[]>([])

  // 评标结果
  const evaluationResults = ref<any[]>([])

  // 加载状态
  const loading = ref(false)


  // 当前评标项
  // 使用数组解放,防止多tab切换时,数据不一致
  // 以 expertCode 作为唯一标识
  const currentEvaluationItem = ref<any>([])

  /**
   * 设置评标信息
   */
  function setEvaluationInfo(info: any) {
    evaluationInfo.value = info
  }

  /**
   * 设置专家列表
   */
  function setExpertList(list: any[]) {
    expertList.value = list
  }

  /**
   * 设置评标结果
   */
  function setEvaluationResults(results: any[]) {
    evaluationResults.value = results
  }

  /**
   * 设置加载状态
   */
  function setLoading(state: boolean) {
    loading.value = state
  }

  /**
   * 设置当前评标项
   */
  function setCurrentEvaluationItem(item: any) {
    currentEvaluationItem.value = [...currentEvaluationItem.value, item]
  }

  return {
    evaluationInfo,
    expertList,
    evaluationResults,
    loading,
    currentEvaluationItem,
    setEvaluationInfo,
    setExpertList,
    setEvaluationResults,
    setLoading,
    setCurrentEvaluationItem,
  }
})