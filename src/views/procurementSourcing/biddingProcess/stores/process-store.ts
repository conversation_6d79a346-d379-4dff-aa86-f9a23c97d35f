/**
 * 流程状态管理
 */
import { defineStore } from 'pinia'
import { ref } from 'vue'

export const useProcessStore = defineStore('biddingProcess', () => {
  // 当前项目ID
  const currentProjectId = ref<string | null>(null)
  
  // 当前流程状态
  const currentStatus = ref<string | null>(null)
  
  // 当前节点
  const currentNode = ref<string | null>(null)

  /**
   * 设置当前项目
   */
  function setCurrentProject(projectId: string) {
    currentProjectId.value = projectId
  }

  /**
   * 设置流程状态
   */
  function setProcessStatus(status: string) {
    currentStatus.value = status
  }

  /**
   * 设置当前节点
   */
  function setCurrentNode(node: string) {
    currentNode.value = node
  }

  /**
   * 重置状态
   */
  function reset() {
    currentProjectId.value = null
    currentStatus.value = null
    currentNode.value = null
  }

  return {
    currentProjectId,
    currentStatus,
    currentNode,
    setCurrentProject,
    setProcessStatus,
    setCurrentNode,
    reset
  }
}) 