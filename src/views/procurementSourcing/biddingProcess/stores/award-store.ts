/**
 * 定标状态管理
 */
import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { getTenderBidById } from '../api/award'
import type { TenderBidPublicityDetail, TenderBidDetailResponse } from '../types/award'
import { useBiddingStore } from '@/views/procurementSourcing/biddingProcess/stores';

const biddingStore = useBiddingStore();
const projectDetail = computed(() => biddingStore?.projectDetail);
const isZJWT = computed(() => biddingStore?.isZJWT);

export const useAwardStore = defineStore('award', () => {


  // 定标详情数据
  const tenderBidDetail = ref<TenderBidPublicityDetail | null>(null)

  // 加载状态
  const loading = ref(false)

  // 数据是否已加载过（用于缓存判断）
  const isDataLoaded = ref(false)

  // 当前缓存的项目信息（用于判断是否需要重新请求）
  const cachedParams = ref<{ noticeId: number; projectId: number } | null>(null)


  const winnerAnnouncementEditMode = computed(() => {

    if (!tenderBidDetail.value?.noticeContent) {
      return false
    }


    return !['APPROVE_REJECT', 'APPROVE_REVOKE'].includes(tenderBidDetail.value.noticeAuditStatus) ? true : false

  })

  const winnerPublicityEditMode = computed(() => {
    if (!tenderBidDetail.value?.publicityContent) {
      return false
    }

    return !['APPROVE_REJECT', 'APPROVE_REVOKE'].includes(tenderBidDetail.value.publicityAuditStatus) ? true : false
  })

  /**
   * 流程控制 - 计算各步骤的完成状态
   */
  const stepCompletionStatus = computed(() => {
    const progressStatus = projectDetail.value?.progressStatus;
    
    // 中标通知书完成状态：最晚完成的节点
    const winnerNotificationCompleted = progressStatus === 'BID_WON_NOTICE' || progressStatus === 'COMPLETED';
    
    // 中标公告完成状态：公告完成或后续节点完成
    const winnerAnnouncementCompleted = progressStatus === 'BID_WON_NOTICE' || progressStatus === 'COMPLETED' || winnerNotificationCompleted;
    
    // 中标公示完成状态：公示完成或后续节点完成
    const winnerPublicityCompleted = progressStatus === 'BID_WON_PUBLICITY' || progressStatus === 'BID_WON_NOTICE' || progressStatus === 'COMPLETED' || winnerAnnouncementCompleted;
    
    // 定标结果完成状态：定标完成或后续节点完成
    const awardResultCompleted = progressStatus === 'AWARDED' || progressStatus === 'BID_WON_PUBLICITY' || progressStatus === 'BID_WON_NOTICE' || progressStatus === 'COMPLETED' || winnerPublicityCompleted;
    
    return {
      // 定标结果完成状态：有定标报告内容
      awardResultCompleted,

      // 中标公示完成状态：有公示内容
      winnerPublicityCompleted,

      // 中标公告完成状态：有公告内容
      winnerAnnouncementCompleted,

      // 中标通知书完成状态：暂时根据业务逻辑判断（可后续扩展）
      winnerNotificationCompleted
    }
  })

  /**
   * 流程访问控制 - 判断各步骤是否可访问
   */
  const stepAccessControl = computed(() => {
    const status = stepCompletionStatus.value
    const inviteMethod = projectDetail.value?.inviteMethod
    const progressStatus = projectDetail.value?.progressStatus

    return {
      // 定标结果：始终可访问
      awardResultAccessible: true,

      // 中标公示：定标结果完成后可访问，或者后续节点已完成
      winnerPublicityAccessible: status.awardResultCompleted || 
        progressStatus === 'BID_WON_PUBLICITY' || 
        progressStatus === 'BID_WON_NOTICE' || 
        progressStatus === 'COMPLETED',

      // 中标公告：中标公示完成后可访问，或者后续节点已完成
      winnerAnnouncementAccessible: status.winnerPublicityCompleted || 
        progressStatus === 'BID_WON_NOTICE' || 
        progressStatus === 'COMPLETED',

      // 中标通知书：根据邀请方式调整访问规则
      winnerNotificationAccessible: inviteMethod === "PUBLICITY" 
        ? (status.winnerAnnouncementCompleted || progressStatus === 'COMPLETED')  // PUBLICITY 模式：中标公告完成后可访问
        : (status.awardResultCompleted || progressStatus === 'COMPLETED')         // 非 PUBLICITY 模式：定标结果完成后可访问
    }
  })

  /**
   * 获取采购方的步骤配置
   */
  function getPurchaserSteps(currentStepId: number = 0) {
    const status = stepCompletionStatus.value
    const access = stepAccessControl.value
    const inviteMethod = projectDetail.value?.inviteMethod
    const isZJWTValue = isZJWT.value

    // 基础步骤配置
    const baseSteps = [
      {
        id: 0,
        number: 1,
        label: '定标结果',
        completed: status.awardResultCompleted,
        current: currentStepId === 0,
        accessible: access.awardResultAccessible,
      },
      {
        id: 1,
        number: 2,
        label: '中标公示',
        completed: status.winnerPublicityCompleted,
        current: currentStepId === 1,
        accessible: access.winnerPublicityAccessible,
      },
      {
        id: 2,
        number: 3,
        label: '中标公告',
        completed: status.winnerAnnouncementCompleted,
        current: currentStepId === 2,
        accessible: access.winnerAnnouncementAccessible,
      },
      {
        id: 3,
        number: 4,
        label: '中标通知书',
        completed: status.winnerNotificationCompleted,
        current: currentStepId === 3,
        accessible: access.winnerNotificationAccessible,
      },
    ]

    // 根据邀请方式或是否为ZJWT过滤步骤
    if (inviteMethod !== "PUBLICITY" || isZJWTValue) {
      // 非 PUBLICITY 模式或ZJWT模式下，过滤掉公示和公告步骤
      const filteredSteps = baseSteps.filter(step => 
        step.id !== 1 && step.id !== 2 // 过滤掉公示和公告步骤
      )
      
      // 重新调整 number 确保升序
      return filteredSteps.map((step, index) => ({
        ...step,
        number: index + 1
      }))
    }

    return baseSteps
  }

  /**
   * 获取供应商的步骤配置
   */
  function getSupplierSteps(currentStepId: number = 10) {
    const status = stepCompletionStatus.value
    const access = stepAccessControl.value
    const inviteMethod = projectDetail.value?.inviteMethod
    const isZJWTValue = isZJWT.value

    // 基础步骤配置
    const baseSteps = [
      {
        id: 10,
        number: 1,
        label: '中标公示',
        completed: status.winnerPublicityCompleted,
        current: currentStepId === 10,
        accessible: access.winnerPublicityAccessible,
      },
      {
        id: 11,
        number: 2,
        label: '中标公告',
        completed: status.winnerAnnouncementCompleted,
        current: currentStepId === 11,
        accessible: access.winnerAnnouncementAccessible,
      },
      {
        id: 12,
        number: 3,
        label: '中标通知书',
        completed: status.winnerNotificationCompleted,
        current: currentStepId === 12,
        accessible: access.winnerNotificationAccessible,
      },
    ]

    // 根据邀请方式或是否为ZJWT过滤步骤
    if (inviteMethod !== "PUBLICITY" || isZJWTValue) {
      // 非 PUBLICITY 模式或ZJWT模式下，过滤掉公示和公告查看步骤
      const filteredSteps = baseSteps.filter(step => 
        step.id !== 10 && step.id !== 11 // 过滤掉公示和公告查看步骤
      )
      
      // 重新调整 number 确保升序
      return filteredSteps.map((step, index) => ({
        ...step,
        number: index + 1
      }))
    }

    return baseSteps
  }

  /**
   * 检查步骤是否可访问
   */
  function isStepAccessible(stepId: number): boolean {
    const access = stepAccessControl.value
    const inviteMethod = projectDetail.value?.inviteMethod
    
    switch (stepId) {
      case 0: return access.awardResultAccessible
      case 1:
      case 10: return access.winnerPublicityAccessible
      case 2:
      case 11: return access.winnerAnnouncementAccessible
      case 3:
      case 12: return access.winnerNotificationAccessible
      default: return false
    }
  }

  /**
   * 检查参数是否变化
   */
  function hasParamsChanged(noticeId: number, projectId: number): boolean {
    if (!cachedParams.value) return true
    return cachedParams.value.noticeId !== noticeId || cachedParams.value.projectId !== projectId
  }

  /**
   * 获取定标详情数据
   * @param noticeId 公告ID
   * @param projectId 项目ID
   * @param forceRefresh 是否强制刷新
   */
  async function getTenderBidDetail(
    noticeId: number,
    projectId: number,
    forceRefresh = false
  ): Promise<TenderBidPublicityDetail | null> {
    try {
      // 如果有缓存数据且参数未变化且不强制刷新，直接返回缓存
      if (
        !forceRefresh &&
        isDataLoaded.value &&
        tenderBidDetail.value &&
        !hasParamsChanged(noticeId, projectId)
      ) {
        return tenderBidDetail.value
      }

      loading.value = true

      const response: TenderBidDetailResponse = await getTenderBidById(noticeId, projectId)

      if (response.code === 0 && response.data) {
        tenderBidDetail.value = response.data
        isDataLoaded.value = true
        cachedParams.value = { noticeId, projectId }
        return response.data
      } else {
        // 接口返回失败，清空数据
        tenderBidDetail.value = null
        isDataLoaded.value = true
        cachedParams.value = { noticeId, projectId }
        return null
      }
    } catch (error) {
      console.error('获取定标详情失败:', error)
      // 请求失败，清空数据
      tenderBidDetail.value = null
      isDataLoaded.value = true
      cachedParams.value = { noticeId, projectId }
      return null
    } finally {
      loading.value = false
    }
  }

  /**
   * 清空定标详情数据
   */
  function clearTenderBidDetail() {
    tenderBidDetail.value = null
    isDataLoaded.value = false
    cachedParams.value = null
  }

  /**
   * 更新定标详情数据（用于提交后的数据更新）
   */
  function updateTenderBidDetail(data: Partial<TenderBidPublicityDetail>) {
    if (tenderBidDetail.value) {
      Object.assign(tenderBidDetail.value, data)
    }
  }

  /**
   * 专门为 AwardResult 组件提供的逻辑处理
   */
  async function loadForAwardResult(noticeId: number, projectId: number, forceRefresh = false) {
    const data = await getTenderBidDetail(noticeId, projectId, forceRefresh)

    return {
      data,
      // 可以在这里添加 AwardResult 特有的数据处理逻辑
      fillFormData: (formData: any) => {
        if (data) {
          // AwardResult 特有的数据回填逻辑
          if (data.awardTemplateId) {
            formData.template = data.awardTemplateId.toString()
          }
          if (data.awardReportContent) {
            formData.content = data.awardReportContent
          }
        }
      }
    }
  }

  /**
   * 专门为 WinnerAnnouncement 组件提供的逻辑处理
   */
  async function loadForWinnerAnnouncement(noticeId: number, projectId: number, forceRefresh = false) {
    const data = await getTenderBidDetail(noticeId, projectId, forceRefresh)

    return {
      data,
      isEditMode: winnerAnnouncementEditMode.value,
      // WinnerAnnouncement 特有的数据处理逻辑
      fillFormData: (formData: any) => {
        if (data) {
          // WinnerAnnouncement 特有的数据回填逻辑
          if (data.noticeTemplateId) {
            formData.templateId = data.noticeTemplateId.toString()
          }
          if (data.noticeContent) {
            formData.content = data.noticeContent
          }
          if (data.noticeTitle) {
            formData.title = data.noticeTitle
          }
          // 更多回填逻辑...
        }
      }
    }
  }

  /**
   * 专门为 WinnerPublicity 组件提供的逻辑处理
   */
  async function loadForWinnerPublicity(noticeId: number, projectId: number, forceRefresh = false) {
    const data = await getTenderBidDetail(noticeId, projectId, forceRefresh)

    return {
      data,
      isEditMode: winnerPublicityEditMode.value,
      // WinnerPublicity 特有的数据处理逻辑
      fillFormData: (formData: any) => {
        if (data) {
          // WinnerPublicity 特有的数据回填逻辑
          if (data.publicityTemplateId) {
            formData.templateId = data.publicityTemplateId.toString()
          }
          if (data.publicityContent) {
            formData.content = data.publicityContent
          }
          if (data.publicityTitle) {
            formData.title = data.publicityTitle
          }
          if (data.publicityStartTime && data.publicityEndTime) {
            formData.publicityTimeRange = [data.publicityStartTime, data.publicityEndTime]
          }
          // 更多回填逻辑...
        }
      }
    }
  }



  return {
    // 状态
    tenderBidDetail,
    loading,
    isDataLoaded,


    winnerAnnouncementEditMode,
    winnerPublicityEditMode,

    // 流程控制
    stepCompletionStatus,
    stepAccessControl,
    getPurchaserSteps,
    getSupplierSteps,
    isStepAccessible,

    // 通用方法
    getTenderBidDetail,
    clearTenderBidDetail,
    updateTenderBidDetail,

    // 组件专用方法
    loadForAwardResult,
    loadForWinnerAnnouncement,
    loadForWinnerPublicity
  }
}) 