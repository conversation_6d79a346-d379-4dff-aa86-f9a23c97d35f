// 项目表单数据类型定义

// 基础信息
export interface BasicInfo {
  projectName: string;
  name: string;
  procurementMethod: 'public' | 'invite';
  suggestion: string;
  budget: string;
  budgetUnit: 'wan' | 'yuan';
  description: string;
}

// 支付信息
export interface PaymentInfo {
  id: number;
  method: string;
  accountPeriod: string;
  accountNotes: string;
}

// 时间限制
export interface TimeLimit {
  registrationStart: Date | null;
  openingTime: Date | null;
  deadline: Date | null;
}

// 团队成员
export interface TeamMember {
  id: number;
  role: string;
  name: string;
  phone: string;
  position: string;
}

// 项目清单项
export interface ProjectListItem {
  index: number;
  category: string;
  name: string;
  model: string;
  unit: string;
  quantity: string;
  unitPrice: string;
  amount: string;
  deliveryDate: string;
  notes: string;
}

// 项目清单
export interface ProjectList {
  activeTab: 'material1' | 'material2' | 'material3';
  materials: {
    material1: ProjectListItem[];
    material2: ProjectListItem[];
    material3: ProjectListItem[];
  };
}

// 附件信息
export interface AttachmentInfo {
  id: string;
  name: string;
  url: string;
  size: number;
  type: string;
  uploadTime: Date;
}

// 部门信息
export interface Department {
  id: number;
  name: string;
}

// 标段信息
export interface BidSection {
  id: number;
  sectionName: string;
  sectionNumber: string;
}

// 标段信息配置
export interface BidSectionInfo {
  sectionType: 'single' | 'multiple';
  sections: BidSection[];
}

// 完整的项目表单数据
export interface ProjectFormData {
  basicInfo: BasicInfo;
  paymentInfo: PaymentInfo[];
  timeLimit: TimeLimit;
  teamMembers: TeamMember[];
  bidSectionInfo: BidSectionInfo;
  projectList: ProjectList;
  attachments: AttachmentInfo[];
}

// 表单验证结果
export interface FormValidationResult {
  isValid: boolean;
  errors: string[];
}

// 可选人员
export interface AvailablePerson {
  id: number;
  name: string;
  phone: string;
} 