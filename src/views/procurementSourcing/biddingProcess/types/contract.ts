/**
 * 合同相关类型定义
 */

// 合同状态枚举
export enum ContractStatus {
  DRAFT = 'draft', // 草稿
  UNDER_REVIEW = 'under_review', // 审核中
  APPROVED = 'approved', // 已批准
  REJECTED = 'rejected', // 已拒绝
  SIGNED = 'signed', // 已签署
  EFFECTIVE = 'effective', // 已生效
  SUSPENDED = 'suspended', // 已暂停
  TERMINATED = 'terminated', // 已终止
  COMPLETED = 'completed' // 已完成
}

// 合同类型枚举
export enum ContractType {
  PURCHASE = 'purchase', // 采购合同
  SERVICE = 'service', // 服务合同
  CONSTRUCTION = 'construction', // 工程合同
  LEASE = 'lease', // 租赁合同
  CONSULTING = 'consulting' // 咨询合同
}

// 合同信息接口
export interface ContractInfo {
  id: string
  projectId: string
  biddingId: string
  contractNo: string
  title: string
  type: ContractType
  status: ContractStatus
  supplier: SupplierInfo
  buyer: BuyerInfo
  amount: number
  currency: string
  signDate?: string
  effectiveDate?: string
  expiryDate?: string
  deliveryDate?: string
  paymentTerms: PaymentTerm[]
  clauses: ContractClause[]
  attachments: string[]
  createdBy: string
  createdAt: string
  updatedAt: string
}

// 供应商信息接口
export interface SupplierInfo {
  id: string
  name: string
  code: string
  legalPerson: string
  address: string
  phone: string
  email: string
  bankAccount: string
  bankName: string
  taxNo: string
}

// 采购方信息接口
export interface BuyerInfo {
  id: string
  name: string
  code: string
  legalPerson: string
  address: string
  phone: string
  email: string
  bankAccount: string
  bankName: string
  taxNo: string
}

// 付款条款接口
export interface PaymentTerm {
  id: string
  sequence: number
  description: string
  percentage: number
  amount: number
  dueDate: string
  condition: string
  status: PaymentStatus
  actualPayDate?: string
  actualAmount?: number
}

// 付款状态枚举
export enum PaymentStatus {
  PENDING = 'pending', // 待付款
  PAID = 'paid', // 已付款
  OVERDUE = 'overdue', // 已逾期
  CANCELLED = 'cancelled' // 已取消
}

// 合同条款接口
export interface ContractClause {
  id: string
  title: string
  content: string
  type: ClauseType
  isRequired: boolean
  order: number
}

// 条款类型枚举
export enum ClauseType {
  GENERAL = 'general', // 一般条款
  PAYMENT = 'payment', // 付款条款
  DELIVERY = 'delivery', // 交付条款
  WARRANTY = 'warranty', // 保修条款
  LIABILITY = 'liability', // 责任条款
  TERMINATION = 'termination', // 终止条款
  DISPUTE = 'dispute', // 争议条款
  CONFIDENTIALITY = 'confidentiality', // 保密条款
  INTELLECTUAL_PROPERTY = 'intellectual_property', // 知识产权条款
  FORCE_MAJEURE = 'force_majeure' // 不可抗力条款
}

// 合同审批记录接口
export interface ContractApproval {
  id: string
  contractId: string
  approver: string
  approverName: string
  approvalLevel: number
  status: ApprovalStatus
  comments?: string
  approvalTime?: string
  deadline: string
}

// 审批状态枚举
export enum ApprovalStatus {
  PENDING = 'pending', // 待审批
  APPROVED = 'approved', // 已批准
  REJECTED = 'rejected', // 已拒绝
  RETURNED = 'returned' // 已退回
}

// 合同签署记录接口
export interface ContractSignature {
  id: string
  contractId: string
  signer: string
  signerName: string
  signerRole: string
  organization: string
  signMethod: SignMethod
  signTime: string
  signLocation?: string
  certificateInfo?: CertificateInfo
  isValid: boolean
}

// 签署方式枚举
export enum SignMethod {
  HANDWRITTEN = 'handwritten', // 手写签名
  ELECTRONIC = 'electronic', // 电子签名
  DIGITAL = 'digital', // 数字签名
  SEAL = 'seal' // 印章
}

// 证书信息接口
export interface CertificateInfo {
  serialNumber: string
  issuer: string
  subject: string
  validFrom: string
  validTo: string
  algorithm: string
  fingerprint: string
}

// 合同变更记录接口
export interface ContractChange {
  id: string
  contractId: string
  changeType: ChangeType
  description: string
  reason: string
  oldValue?: any
  newValue?: any
  requestBy: string
  requestTime: string
  approvedBy?: string
  approvedTime?: string
  status: ChangeStatus
  effectiveDate?: string
}

// 变更类型枚举
export enum ChangeType {
  AMOUNT = 'amount', // 金额变更
  DELIVERY_DATE = 'delivery_date', // 交付日期变更
  PAYMENT_TERMS = 'payment_terms', // 付款条款变更
  SCOPE = 'scope', // 范围变更
  SPECIFICATION = 'specification', // 规格变更
  OTHER = 'other' // 其他变更
}

// 变更状态枚举
export enum ChangeStatus {
  REQUESTED = 'requested', // 已申请
  UNDER_REVIEW = 'under_review', // 审核中
  APPROVED = 'approved', // 已批准
  REJECTED = 'rejected', // 已拒绝
  IMPLEMENTED = 'implemented' // 已实施
}

// 合同履行记录接口
export interface ContractPerformance {
  id: string
  contractId: string
  milestone: string
  description: string
  plannedDate: string
  actualDate?: string
  status: PerformanceStatus
  progress: number // 进度百分比
  deliverables: string[]
  issues?: string[]
  remarks?: string
  recordBy: string
  recordTime: string
}

// 履行状态枚举
export enum PerformanceStatus {
  NOT_STARTED = 'not_started', // 未开始
  IN_PROGRESS = 'in_progress', // 进行中
  COMPLETED = 'completed', // 已完成
  DELAYED = 'delayed', // 已延期
  CANCELLED = 'cancelled' // 已取消
} 