import { type BatchAwardNoticeItem } from '../api/WinnerNotification';


/**
 * 定标模块类型定义
 */

// 查询物料请求参数接口
export interface QueryMaterialParams {
  sectionId?: number; // 标段ID
  roundNo?: number; // 报价轮次
  materialName?: string; // 物料名称
  supplierName?: string; // 供应商名称
  current?: number; // 页码
  size?: number; // 页大小
  noticeId?: number; // 公告ID
  projectId?: number; // 采购立项ID
}

// 供应商报价信息接口
export interface SupplierQuoteInfo {
  quoteItemId: number; // 报价明细ID
  tenantSupplierId: number; // 租户供应商ID
  tenderSupplierId: number; // 供应商明细ID
  procurementProjectPaymentId: number; // 采购立项支付方式ID
  roundNo: number; // 轮次
  requiredQuantity: number; // 需求数量
  quoteQuantity: number; // 报价数量
  quotePrice: number; // 报价单价
  quoteAmount: number; // 报价金额
  deliveryPeriod: string; // 交货期
  quoteTime: string; // 报价时间
  quoteIp: string; // 报价IP
  awarded: number; // 是否中标(0-否, 1-是)
  awardedQuantity: number; // 中标数量
  remark: string; // 备注
  supplierName?: string; // 供应商名称
  contactPerson?: string; // 联系人
  contactPhone?: string; // 联系电话
}

// 物料信息接口
export interface MaterialInfo {
  projectItemId: number; // 项目明细ID
  materialCode: string; // 物料编码
  materialName: string; // 物料名称
  specModel: string; // 规格型号
  unit: string; // 单位
  requiredQuantity: number; // 需求数量
  unitPrice: number; // 计划单价
  supplierQuoteList: SupplierQuoteInfo[]; // 供应商报价列表
}

// API响应数据结构
export interface QueryMaterialResponse {
  code: number;
  msg: string;
  data: {
    records: MaterialInfo[];
    total: number;
    size: number;
    current: number;
  };
}

// 用于表格显示的扁平化数据结构
export interface MaterialTableRow {
  projectItemId: number;
  materialCode: string;
  materialName: string;
  specModel: string;
  unit: string;
  requiredQuantity: number;
  unitPrice: number;
  supplierCount: number;
  supplierName: string;
  roundNo: number;
  quoteQuantity: number;
  quotePrice: number;
  quoteAmount: number;
  deliveryPeriod: string;
  quoteTime: string;
  quoteIp: string;
  awarded: number;
  awardedQuantity: number;
  remark: string;
  // 额外的字段用于兼容现有表格
  quoteItemId?: number;
  tenantSupplierId?: number;
  contactPerson?: string;
  contactPhone?: string;
}

// 模板选项接口
export interface TemplateOption {
  id: string;
  templateName: string;
  [key: string]: any;
}

// 定标基本信息接口
export interface AwardInfo {
  awardPerson: string; // 定标人
  awardTime: string; // 定标时间
  approvalStatus: 'pending' | 'approved' | 'rejected'; // 审批状态
  projectName: string; // 项目名称
  projectCode: string; // 项目编码
  procurementMethod: string; // 采购方式
  budgetAmount: number; // 预算金额
  awardAmount: number; // 定标金额
}

// 公告表单数据接口
export interface AnnouncementForm {
  template: string; // 模板ID
  content: string; // 公告内容
}

// 附件信息接口
export interface AttachmentInfo {
  resultAttachment: {
    name: string;
    size: number;
    uploadTime: string;
    url: string;
  };
}

// 备注信息接口
export interface RemarkInfo {
  remark: string;
}

// 定标提交审核请求数据接口
export interface SubmitAwardReviewData {
  noticeId: number; // 公告ID
  projectId: number; // 采购立项ID
  awardTemplateId: number; // 引用模版ID
  awardReportContent: string; // 定标报告内容
  attachmentInfos?: Array<{
    businessId?: number; // 业务ID
    businessType?: string; // 业务类型(PROJECT-采购立项、NOTICE-招标公告)
    fileName: string; // 文件名称
    filePath: string; // 文件路径
    fileType: string; // 文件类型
    fileSize: number; // 文件大小(字节)
  }>; // 附件信息
  awardRemark?: string; // 定标备注
  projectItemList: Array<{
    id: number; // 主键ID
    projectId: number; // 采购立项ID
    sectionId: number; // 标段ID
    tenantSupplierId: number; // 租户供应商ID
    supplierName: string; // 供应商名称
    materialCode: string; // 物料编码
    materialName: string; // 物料名称
    specModel: string; // 规格型号
    unit: string; // 单位
    requiredQuantity: number; // 需求数量
    remark?: string; // 备注
    bidQuantity: number; // 中标数量
    isBid: boolean; // 是否中标
    bidAmount: number; // 中标价格
    projectPaymentId: number; // 中标账期
  }>; // 采购项目明细信息
  awardNoticeReqList: Array<BatchAwardNoticeItem>
}


// 中标公示详情数据接口
export interface TenderBidPublicityDetail {
  // 基本信息
  id: number; // 主键ID
  tenantId: number; // 租户ID
  deptId: number; // 部门ID
  projectId: number; // 采购立项ID
  noticeId: number; // 公告ID
  sectionId: number; // 标段ID

  // 评标备注信息
  awardRemark: string; // 定标备注

  // 定标相关字段
  awardTemplateId: number; // 定标引用模版ID
  awardAttachments: Array<{
    businessId?: number;
    businessType?: string;
    fileName: string;
    filePath: string;
    fileType: string;
    fileSize: number;
  }>; // 定标附件数组

  // 中标公示相关字段
  awardReportStatus: string; // 定标报告状态(DRAFT-草稿、SUBMITTED-已提交、APPROVED-已批准、REJECTED-拒绝)
  examineRemark: string; // 审批备注
  awardReportContent: string; // 定标报告内容
  awardReportTime: string; // 定标报告时间

  // 公示状态
  publicityStatus: string; // 公示状态(UNPUBLISHED-未发布、PUBLISHED-已公示、EXPIRED-已过期)
  noticeAuditStatus: string; // 公告审批状态(DRAFT-草稿、SUBMITTED-已提交、APPROVED-已批准、REJECTED-拒绝)
  publicityAuditStatus: string; // 公示审批状态(DRAFT-草稿、SUBMITTED-已提交、APPROVED-已批准、REJECTED-拒绝)

  // 公示详细信息
  publicityTitle: string; // 公示标题
  publicityTemplateId: number; // 公示引用模版ID
  publicityContent: string; // 公示内容
  publicityStartTime: string; // 公示开始时间
  publicityEndTime: string; // 公示结束时间

  // 公告状态
  noticeStatus: string; // 公告状态(UNPUBLISHED-未发布、PUBLISHED-已发布、EXPIRED-已过期)

  // 公告详细信息  
  noticeTitle: string; // 公告标题
  noticeTemplateId: number; // 公告引用模版ID
  noticeContent: string; // 公告内容
  noticeTime: string; // 公告时间

  // 结果状态
  resultStatus: string; // 整体状态(DRAFT-草稿、PROCESSING-处理中、COMPLETE-已完成、REJECTED-已驳回)

  // 当前轮次
  currentRound: number; // 当前轮次(来自 tdrm_tender_open数据)

  // 创建和更新信息
  createdyId: number; // 创建人ID
  createdBy: string; // 创建人
  createByName: string; // 创建人名称
  createdTime: string; // 创建时间
  updatedyId: number; // 修改人ID
  updatedBy: string; // 修改人
  updatedByName: string; // 修改人名称
  updatedTime: string; // 修改时间

  // 附件相关
  publicityAttachments: object[]; // 中标公示附件数组
  noticeAttachments: object[]; // 中标公告附件数组

  traceId: string; // 跟踪ID
}

// API响应接口
export interface TenderBidDetailResponse {
  code: number;
  bizCode: string;
  msg: string;
  data: TenderBidPublicityDetail;
  traceId: string;
}

