/**
 * 流程相关类型定义
 */

// 流程状态枚举
export enum ProcessStatus {
  DRAFT = 'draft', // 草稿
  PUBLISHED = 'published', // 已发布
  BIDDING = 'bidding', // 投标中
  OPENING = 'opening', // 开标中
  EVALUATING = 'evaluating', // 评标中
  AWARDED = 'awarded', // 已定标
  CONTRACTED = 'contracted', // 已签约
  ARCHIVED = 'archived', // 已归档
  CANCELLED = 'cancelled' // 已取消
}

// 流程节点类型
export enum ProcessNode {
  PROCUREMENT = 'procurement', // 采购项目
  ANNOUNCEMENT = 'announcement', // 发标
  BIDDING = 'bidding', // 投标
  OPENING = 'opening', // 开标
  EVALUATION = 'evaluation', // 评标
  AWARD = 'award', // 定标
  CONTRACT = 'contract', // 合同签订
  ARCHIVE = 'archive' // 项目归档
}

// 项目信息接口
export interface ProjectInfo {
  id: string
  name: string
  code: string
  description?: string
  procurementMethod: string
  budget: number
  status: string
  createdBy: string
  createdAt: Date
  updatedAt: Date
}

// 流程信息接口
export interface ProcessInfo {
  id: string
  projectId: string
  currentNode: ProcessNode
  status: ProcessStatus
  startTime: string
  endTime?: string
  createdBy: string
  createdAt: string
  updatedAt: string
}

// 流程节点信息接口
export interface ProcessNodeInfo {
  node: ProcessNode
  name: string
  status: ProcessStatus
  startTime?: string
  endTime?: string
  duration?: number
  operator?: string
  remark?: string
}

// 流程操作接口
export interface ProcessOperation {
  id: string
  processId: string
  node: ProcessNode
  operation: string
  operator: string
  operatorName: string
  operateTime: string
  remark?: string
  attachments?: string[]
}

// 流程配置接口
export interface ProcessConfig {
  id: string
  name: string
  nodes: ProcessNodeConfig[]
  rules: ProcessRule[]
  isActive: boolean
  createdAt: string
  updatedAt: string
}

// 流程节点配置接口
export interface ProcessNodeConfig {
  node: ProcessNode
  name: string
  description: string
  required: boolean
  timeLimit?: number // 时间限制（小时）
  autoNext?: boolean // 是否自动进入下一节点
  permissions: string[] // 操作权限
}

// 流程规则接口
export interface ProcessRule {
  id: string
  name: string
  condition: string
  action: string
  isActive: boolean
} 