/**
 * 开标设置相关类型定义
 */

/**
 * 监督人员信息
 */
export interface SupervisorInfo {
  id?: string
  name: string
  phone: string
  email: string
  userId?: string
}

/**
 * 开标配置提交数据
 */
export interface BidOpenConfigData {
  noticeId: string;         // 公告ID(必须)
  publicQuote: number;      // 是否公开报价(必须)(0-不公开,1-公开)
  bidOpenUserId?: number;   // 开标人员ID(非必须)
  supervisionUserIdList: number[]; // 监督人员ID列表(必须)
}

/**
 * 用户选项
 */
export interface UserOption {
  userId: string
  name: string
  phone?: string
  email?: string
  username: string // 用户名，用于搜索
}

/**
 * 开标设置表单数据
 */
export interface BidOpeningSettingForm {
  supervisorList: SupervisorInfo[]
}

/**
 * API响应数据
 */
export interface BidOpeningSettingResponse {
  code: number
  message: string
  data: BidOpeningSettingData
}

/**
 * 分页查询参数
 */
export interface PageParams {
  current: number
  size: number
  keyword?: string
}

/**
 * 用户分页响应
 */
export interface UserPageResponse {
  code: number
  message: string
  data: {
    records: UserOption[]
    total: number
    current: number
    size: number
  }
}

/**
 * 监督人员信息（API返回格式）
 */
export interface SupervisionMember {
  id?: string
  tenantId?: string
  deptId?: string
  businessId?: string
  userId?: string
  memberType?: 'PROJECT_MEMBER' | 'EVALUATION_MEMBER' | 'SUPERVISION'
  role?: 'PROJECT_LEADER' | 'PROJECT_MEMBER' | 'EVALUATION_LEADER' | 'EVALUATION_MEMBER' | 'SUPERVISION'
  contactPhone?: string
  email?: string
  delFlag?: number
  createById?: number
  createBy?: string
  createByName?: string
  createTime?: string
  updateById?: number
  updateBy?: string
  updateByName?: string
  updateTime?: string
}
