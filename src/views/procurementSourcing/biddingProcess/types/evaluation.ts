/**
 * 评标相关类型定义
 */

// 评标状态枚举
export enum EvaluationStatus {
  NOT_STARTED = 'not_started', // 未开始
  IN_PROGRESS = 'in_progress', // 进行中
  COMPLETED = 'completed', // 已完成
  SUSPENDED = 'suspended', // 已暂停
  CANCELLED = 'cancelled' // 已取消
}

// 评标方法枚举
export enum EvaluationMethod {
  LOWEST_PRICE = 'lowest_price', // 最低价法
  COMPREHENSIVE = 'comprehensive', // 综合评分法
  COMPETITIVE_NEGOTIATION = 'competitive_negotiation', // 竞争性谈判
  SINGLE_SOURCE = 'single_source' // 单一来源
}

// 评标信息接口
export interface EvaluationInfo {
  id: string
  projectId: string
  method: EvaluationMethod
  status: EvaluationStatus
  startTime: string
  endTime?: string
  experts: ExpertInfo[]
  criteria: EvaluationCriteria[]
  results: EvaluationResult[]
  report?: EvaluationReport
  createdBy: string
  createdAt: string
  updatedAt: string
}

// 专家信息接口
export interface ExpertInfo {
  id: string
  name: string
  title: string
  organization: string
  specialty: string[]
  level: ExpertLevel
  phone: string
  email: string
  isAvailable: boolean
  conflictOfInterest: boolean
  participationHistory: number
  rating: number
  createdAt: string
}

// 专家级别枚举
export enum ExpertLevel {
  SENIOR = 'senior', // 高级专家
  INTERMEDIATE = 'intermediate', // 中级专家
  JUNIOR = 'junior' // 初级专家
}

// 评标标准接口
export interface EvaluationCriteria {
  id: string
  name: string
  description: string
  weight: number // 权重（百分比）
  maxScore: number
  type: CriteriaType
  subCriteria?: EvaluationCriteria[]
  isRequired: boolean
}

// 标准类型枚举
export enum CriteriaType {
  TECHNICAL = 'technical', // 技术标准
  COMMERCIAL = 'commercial', // 商务标准
  QUALIFICATION = 'qualification', // 资质标准
  EXPERIENCE = 'experience', // 经验标准
  PRICE = 'price' // 价格标准
}

// 评标结果接口
export interface EvaluationResult {
  id: string
  evaluationId: string
  biddingId: string
  expertId: string
  scores: ScoreDetail[]
  totalScore: number
  rank: number
  recommendation: string
  comments: string
  submitTime: string
  isValid: boolean
}

// 评分详情接口
export interface ScoreDetail {
  criteriaId: string
  criteriaName: string
  score: number
  maxScore: number
  comment?: string
}

// 评标报告接口
export interface EvaluationReport {
  id: string
  evaluationId: string
  title: string
  summary: string
  methodology: string
  process: string
  results: ReportResult[]
  recommendations: string[]
  conclusion: string
  attachments: string[]
  generatedBy: string
  generatedAt: string
  approvedBy?: string
  approvedAt?: string
  status: ReportStatus
}

// 报告结果接口
export interface ReportResult {
  biddingId: string
  bidderName: string
  totalScore: number
  rank: number
  technicalScore: number
  commercialScore: number
  priceScore: number
  isRecommended: boolean
  strengths: string[]
  weaknesses: string[]
}

// 报告状态枚举
export enum ReportStatus {
  DRAFT = 'draft', // 草稿
  SUBMITTED = 'submitted', // 已提交
  APPROVED = 'approved', // 已批准
  REJECTED = 'rejected' // 已拒绝
}

// 专家评审记录接口
export interface ExpertReviewRecord {
  id: string
  evaluationId: string
  expertId: string
  biddingId: string
  reviewTime: string
  duration: number // 评审时长（分钟）
  actions: ReviewAction[]
  finalScore: number
  confidence: number // 信心度（1-10）
  difficulty: number // 难度评级（1-10）
}

// 评审动作接口
export interface ReviewAction {
  timestamp: string
  action: string
  target: string
  value?: any
  comment?: string
}

// 评标异议接口
export interface EvaluationObjection {
  id: string
  evaluationId: string
  biddingId: string
  objector: string
  objectorName: string
  type: ObjectionType
  content: string
  evidence: string[]
  submitTime: string
  status: ObjectionStatus
  response?: string
  responseBy?: string
  responseTime?: string
  resolution?: string
}

// 异议类型枚举
export enum ObjectionType {
  SCORING_ERROR = 'scoring_error', // 评分错误
  PROCEDURE_VIOLATION = 'procedure_violation', // 程序违规
  EXPERT_CONFLICT = 'expert_conflict', // 专家利益冲突
  CRITERIA_DISPUTE = 'criteria_dispute', // 标准争议
  OTHER = 'other' // 其他
}

// 异议状态枚举
export enum ObjectionStatus {
  SUBMITTED = 'submitted', // 已提交
  UNDER_REVIEW = 'under_review', // 审查中
  ACCEPTED = 'accepted', // 已接受
  REJECTED = 'rejected', // 已拒绝
  WITHDRAWN = 'withdrawn' // 已撤回
} 