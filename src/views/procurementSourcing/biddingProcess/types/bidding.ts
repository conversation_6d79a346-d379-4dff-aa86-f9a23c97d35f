/**
 * 投标相关类型定义
 */

// 投标状态枚举
export enum BiddingStatus {
  REGISTERED = 'registered', // 已报名
  DOCUMENT_SUBMITTED = 'document_submitted', // 已提交资料
  DOCUMENT_APPROVED = 'document_approved', // 资料已审核
  DOCUMENT_REJECTED = 'document_rejected', // 资料被拒绝
  BOND_PAID = 'bond_paid', // 已缴纳保证金
  QUOTED = 'quoted', // 已报价
  OPENED = 'opened', // 已开标
  WITHDRAWN = 'withdrawn' // 已撤回
}

// 投标人信息接口
export interface BidderInfo {
  id: string
  companyName: string
  companyCode: string
  legalPerson: string
  contactPerson: string
  contactPhone: string
  contactEmail: string
  address: string
  registeredCapital: number
  businessScope: string
  qualifications: QualificationInfo[]
  createdAt: string
  updatedAt: string
}

// 资质信息接口
export interface QualificationInfo {
  id: string
  type: string
  name: string
  level: string
  certificateNo: string
  issueDate: string
  expiryDate: string
  issuer: string
  attachments: string[]
}

// 投标信息接口
export interface BiddingInfo {
  id: string
  projectId: string
  bidderId: string
  bidderInfo: BidderInfo
  status: BiddingStatus
  registrationTime: string
  documentSubmitTime?: string
  documentReviewTime?: string
  bondAmount?: number
  bondPayTime?: string
  quotationTime?: string
  quotationAmount?: number
  isWinner: boolean
  rank?: number
  score?: number
  remark?: string
  createdAt: string
  updatedAt: string
}

// 投标文档接口
export interface BiddingDocument {
  id: string
  biddingId: string
  type: DocumentType
  name: string
  fileName: string
  fileSize: number
  filePath: string
  uploadTime: string
  uploadBy: string
  reviewStatus: ReviewStatus
  reviewTime?: string
  reviewBy?: string
  reviewComment?: string
}

// 文档类型枚举
export enum DocumentType {
  BUSINESS_LICENSE = 'business_license', // 营业执照
  QUALIFICATION = 'qualification', // 资质证书
  FINANCIAL_REPORT = 'financial_report', // 财务报告
  TECHNICAL_PROPOSAL = 'technical_proposal', // 技术方案
  COMMERCIAL_PROPOSAL = 'commercial_proposal', // 商务方案
  LEGAL_STATEMENT = 'legal_statement', // 法律声明
  OTHER = 'other' // 其他
}

// 审核状态枚举
export enum ReviewStatus {
  PENDING = 'pending', // 待审核
  APPROVED = 'approved', // 已通过
  REJECTED = 'rejected', // 已拒绝
  NEED_SUPPLEMENT = 'need_supplement' // 需要补充
}

// 保证金信息接口
export interface BondInfo {
  id: string
  biddingId: string
  amount: number
  paymentMethod: string
  paymentAccount: string
  paymentTime: string
  receiptNo: string
  status: BondStatus
  refundTime?: string
  refundAmount?: number
  remark?: string
}

// 保证金状态枚举
export enum BondStatus {
  UNPAID = 'unpaid', // 未缴纳
  PAID = 'paid', // 已缴纳
  REFUNDED = 'refunded', // 已退还
  FORFEITED = 'forfeited' // 已没收
}

// 报价信息接口
export interface QuotationInfo {
  id: string
  biddingId: string
  totalAmount: number
  currency: string
  validityPeriod: number // 有效期（天）
  deliveryPeriod: number // 交货期（天）
  paymentTerms: string
  technicalScore?: number
  commercialScore?: number
  totalScore?: number
  items: QuotationItem[]
  submitTime: string
  isEncrypted: boolean
  encryptionKey?: string
}

// 报价明细接口
export interface QuotationItem {
  id: string
  quotationId: string
  itemNo: string
  itemName: string
  specification: string
  unit: string
  quantity: number
  unitPrice: number
  totalPrice: number
  remark?: string
}

// 标段基础信息
export interface SectionInfo {
  id: number
  sectionCode: string
  sectionName: string
  projectId: number
  tenantId: number
  deptId: number
  delFlag: number
  createById: number
  createBy: string
  createByName: string
  createTime: string
  updateById: number
  updateBy: string
  updateByName: string
  updateTime: string
  remark?: string
}

// StageTabs组件使用的标段数据结构
export interface TabItem {
  key: string
  label: string
  id: number
}

// API响应数据结构
export interface SectionListResponse {
  code: number
  msg: string
  data: SectionInfo[]
}

// API响应的完整结构（包装在data字段中）
export interface ApiResponse<T = any> {
  data: {
    code: number
    msg: string
    data: T
  }
} 