// 通知状态枚举
export enum NoticeStatus {
  UNSENT = 'UNSENT', // 未发送
  SENT = 'SENT', // 已发送
}

// 签章状态枚举
export enum SignatureStatus {
  UNSIGNED = 'UNSIGNED', // 未签章
  SIGNING = 'SIGNING', // 签章中
  SIGNED = 'SIGNED', // 已签章
}

// 中标供应商信息接口
export interface AwardedSupplierInfo {
  tenantSupplierId: number; // 租户供应商ID
  supplierCode: string; // 供应商编号
  supplierName: string; // 供应商名称
  supplierShortName: string; // 供应商简称
  supplierType: string; // 供应商类型
  enterpriseNature: string; // 企业性质
  supplierCategory: string; // 供应商分类
  supplierStatus: string; // 供应商状态
  legalPerson: string; // 法人代表
  legalPersonPhone: string; // 法人电话
  registeredAddress: string; // 注册地址
  contactName: string; // 联系人姓名
  contactPhone: string; // 联系人电话
  awardedMaterialCount: number; // 中标物料数量
  totalAwardedAmount: number; // 中标总金额
  totalAwardedQuantity: number; // 中标总数量
  serviceStartDate: string; // 合作开始时间
  serviceEndDate: string; // 合作结束时间
  annualRevenue: number; // 年平均营业额
  companyProfile: string; // 公司简介
  businessScope: string; // 经营范围
  remark: string; // 备注
  status: string; // 启用状态
  sectionId: number; // 标段ID
  traceId: string;
  // 中标通知相关字段
  noticeStatus?: NoticeStatus | string; // 通知状态
  signatureStatus?: SignatureStatus | string; // 签章状态
  notificationSentTime?: string; // 通知发送时间
  isSignaturePending?: boolean; // 是否待签章
  notificationContent?: string; // 通知书内容
  sending?: boolean; // 发送状态（用于UI显示）
}

// API 响应接口
export interface AwardedSuppliersResponse {
  code: number;
  bizCode: string;
  msg: string;
  data: AwardedSupplierInfo[];
  traceId: string;
}

// 获取中标供应商列表的请求参数接口
export interface GetAwardedSuppliersParams {
  sectionId?: string; // 标段ID
  [key: string]: any;
}

// 邀请供应商信息接口
export interface InviteSupplierInfo {
  sectionId: number; // 标段ID
  tenantSupplierId: number; // 租户供应商ID
  supplierName?: string; // 供应商名称
  contactPerson?: string; // 采购联系人
  contactPhone?: string; // 联系电话
  dealPrice?: number; // 成交价格
}

// 中标公司信息接口
export interface BidCompanyInfo {
  tenantSupplierId: number; // 租户供应商ID
  supplierName: string; // 供应商名称
  sectionId?: number; // 标段ID
  notificationContent?: string; // 通知书内容
}

// 发送中标通知的请求参数接口
export interface SendNotificationParams {
  noticeId: number; // 公告ID
  projectId: number; // 采购立项ID
  bidCompanyList: BidCompanyInfo[]; // 中标公司信息列表
  sectionId?: number; // 标段ID（可选）
  tenantSupplierId?: number; // 租户供应商ID（可选）
  supplierName?: string; // 供应商名称（可选）
}

// 编辑中标通知书内容的请求参数接口
export interface EditNotificationContentParams {
  noticeId: number; // 公告ID
  projectId: number; // 采购立项ID
  sectionId: number; // 标段ID
  tenantSupplierId: number; // 租户供应商ID
  noticeContent: string; // 通知书内容
} 