// 中标供应商信息接口
export interface AwardedSupplierInfo {
  tenantSupplierId: number; // 租户供应商ID
  supplierCode: string; // 供应商编号
  supplierName: string; // 供应商名称
  supplierShortName: string; // 供应商简称
  supplierType: string; // 供应商类型
  enterpriseNature: string; // 企业性质
  supplierCategory: string; // 供应商分类
  supplierStatus: string; // 供应商状态
  legalPerson: string; // 法人代表
  legalPersonPhone: string; // 法人电话
  registeredAddress: string; // 注册地址
  contactName: string; // 联系人姓名
  contactPhone: string; // 联系人电话
  awardedMaterialCount: number; // 中标物料数量
  totalAwardedAmount: number; // 中标总金额
  totalAwardedQuantity: number; // 中标总数量
  serviceStartDate: string; // 合作开始时间
  serviceEndDate: string; // 合作结束时间
  annualRevenue: number; // 年平均营业额
  companyProfile: string; // 公司简介
  businessScope: string; // 经营范围
  remark: string; // 备注
  status: string; // 启用状态
  sectionId: number; // 标段ID
  traceId: string;
  showDetail: boolean;
  awardReportStatus: string; // 中标报告状态
}

// API 响应接口
export interface AwardedSuppliersResponse {
  code: number;
  bizCode: string;
  msg: string;
  data: AwardedSupplierInfo[];
  traceId: string;
}

// 获取中标供应商列表的请求参数接口
export interface GetAwardedSuppliersParams {
  // 可以根据需要添加筛选参数，比如标段筛选等
  sectionId?: string; // 标段ID
  [key: string]: any;
}

// 邀请供应商信息接口
export interface InviteSupplierInfo {
  sectionId: number; // 标段ID
  tenantSupplierId: number; // 租户供应商ID
  supplierName?: string; // 供应商名称
  contactPerson?: string; // 采购联系人
  contactPhone?: string; // 联系电话
  dealPrice?: number; // 成交价格
}

// 附件信息接口
export interface AttachmentInfo {
  id?: string;
  name?: string;
  url?: string;
  size?: number;
  type?: string;
}

// 保存中标公示的请求参数接口
export interface SaveBidPublicityParams {
  noticeId: number; // 公告ID
  projectId: number; // 采购立项ID
  inviteSupplierList: InviteSupplierInfo[]; // 邀请供应商列表
  isPublicity: boolean; // 变更中标公示--是否发布中标公示
  publicityStartTime: string; // 变更中标公示--公示开始时间
  publicityEndTime: string; // 变更中标公示--公示结束时间
  publicityTitle: string; // 公示标题
  publicityTemplateId: number; // 引用模版ID
  publicityContent: string; // 公示内容
  attachmentInfos?: AttachmentInfo[]; // 附件信息
} 