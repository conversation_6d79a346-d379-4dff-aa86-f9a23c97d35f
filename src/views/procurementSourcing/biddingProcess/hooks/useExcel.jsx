/* eslint-disable no-console */
// import { ElMessage } from 'yun-design';
import ExcelJS from 'exceljs';

export function useExcel() {
  // 导入Excel文件
  async function importExcelFile(file, fields) {
    try {
      const workbook = new ExcelJS.Workbook();
      await workbook.xlsx.load(await file.arrayBuffer());
      const worksheet = workbook.getWorksheet(1);

      if (!worksheet) {
        throw new Error('未找到工作表');
      }

      // 展平嵌套字段结构
      const { flatFields, headerStructure } = flattenFields(fields);

      // 检查是否有多级表头
      const hasParentHeaders = headerStructure.some((item) => item.isParent);
      const headerRowCount = hasParentHeaders ? 2 : 1;

      // 验证表头
      if (hasParentHeaders) {
        // 验证两级表头
        const parentHeaders = [];
        const childHeaders = [];

        headerStructure.forEach((item) => {
          if (item.isParent) {
            // 父级表头可能跨多列
            for (let i = item.startCol; i <= item.endCol; i++) {
              parentHeaders[i] = item.field.fieldName || item.field.label;
            }
          } else if (item.isChild) {
            childHeaders[item.colIndex] = item.field.fieldName || item.field.label;
          } else {
            // 单级表头，需要占两行
            parentHeaders[item.colIndex] = item.field.fieldName || item.field.label;
            childHeaders[item.colIndex] = item.field.fieldName || item.field.label;
          }
        });

        // 验证第一行表头（父级表头）
        const firstRowHeaders = worksheet.getRow(1).values.slice(1);
        const isFirstRowValid = parentHeaders.every((expectedHeader, index) => {
          const actualHeader = firstRowHeaders[index];
          const headerText = typeof actualHeader === 'object' ? actualHeader.richText?.[1]?.text || actualHeader.text : actualHeader;
          return headerText === expectedHeader;
        });

        // 验证第二行表头（子级表头）
        const secondRowHeaders = worksheet.getRow(2).values.slice(1);
        const isSecondRowValid = childHeaders.every((expectedHeader, index) => {
          const actualHeader = secondRowHeaders[index];
          const headerText = typeof actualHeader === 'object' ? actualHeader.richText?.[1]?.text || actualHeader.text : actualHeader;
          return headerText === expectedHeader;
        });

        if (!isFirstRowValid || !isSecondRowValid) {
          throw new Error('导入的Excel文件格式不正确，请使用正确的模板');
        }
      } else {
        // 验证单级表头
        const headerRow = worksheet.getRow(1);
        const headers = headerRow.values.slice(1);
        const fieldNames = flatFields.map((field) => field.fieldName);

        const isValidHeader = headers.every((header, index) => {
          const headerText = typeof header === 'object' ? header.richText?.[1]?.text || header.text : header;
          return headerText === fieldNames[index];
        });

        if (!isValidHeader) {
          throw new Error('导入的Excel文件格式不正确，请使用正确的模板');
        }
      }

      // 读取数据行
      const rows = [];
      worksheet.eachRow((row, rowNumber) => {
        if (rowNumber > headerRowCount) {
          // 跳过表头行
          const rowData = {};
          let hasData = false;

          row.eachCell((cell, colNumber) => {
            const fieldIndex = colNumber - 1;
            const field = flatFields[fieldIndex];

            if (field && cell.value !== null && cell.value !== undefined && cell.value !== '') {
              hasData = true;
              const key = field.fieldCode;

              if (field.fieldType === 'ENUM' && field.enumValues) {
                const enumOptions = field.enumValues || [];
                rowData[key] = enumOptions?.find((i) => i.label === cell.value)?.value || cell.value;
              } else {
                rowData[key] = cell.value;
              }
            }
          });

          // 只有当行中有数据时才添加到结果中
          if (hasData) {
            rows.push(rowData);
          }
        }
      });

      // 过滤出必填项填写完整的行
      const validRows = rows.filter((row) => {
        // 检查所有必填字段是否都已填写
        return flatFields.every((field) => {
          if (field.isRequired) {
            const value = row[field.fieldCode];
            // 检查值是否存在且不为空
            return value !== null && value !== undefined && value !== '' && String(value).trim() !== '';
          }
          return true; // 非必填字段总是通过验证
        });
      });

      console.log('importExcelFile - 总行数:', rows.length);
      console.log('importExcelFile - 必填项完整的行数:', validRows.length);
      console.log('importExcelFile - 必填字段:', flatFields.filter(f => f.isRequired).map(f => f.fieldName));

      return validRows;
    } catch (error) {
      // eslint-disable-next-line no-console
      console.error('导入Excel文件失败:', error);
      throw error;
    }
  }

  /**
   * 展平嵌套的字段结构
   * @param {Array} fields - 字段配置数组
   * @returns {Object} 包含展平后的字段和层级信息
   */
  function flattenFields(fields) {
    const flatFields = [];
    const headerStructure = [];

    fields.forEach((field) => {
      if (field.children && field.children.length > 0) {
        // 有子字段的情况
        const parentHeaderInfo = {
          field,
          startCol: flatFields.length,
          endCol: flatFields.length + field.children.length - 1,
          isParent: true,
        };
        headerStructure.push(parentHeaderInfo);

        field.children.forEach((child) => {
          const currentIndex = flatFields.length;
          flatFields.push({
            ...child,
            parentField: field,
          });
          headerStructure.push({
            field: child,
            parentField: field,
            colIndex: currentIndex,
            isChild: true,
          });
        });
      } else {
        // 没有子字段的情况
        const currentIndex = flatFields.length;
        flatFields.push(field);
        headerStructure.push({
          field,
          colIndex: currentIndex,
          isParent: false,
          isChild: false,
        });
      }
    });

    return {
      flatFields,
      headerStructure,
    };
  }

  // 辅助函数：检查列是否需要锁定
  function isColumnLocked(colIndex, field, lockedColumns) {
    // 公式列合并
    if (field.fieldType === 'NUM_CALC') return true;

    if (lockedColumns.length === 0) return false;

    // 支持按索引锁定
    if (lockedColumns.includes(colIndex)) return true;

    // 支持按字段代码锁定
    if (lockedColumns.includes(field.fieldCode)) return true;

    // 支持按字段名锁定
    if (lockedColumns.includes(field.fieldName)) return true;

    return false;
  }

  /**
   * 创建多级表头
   * @param {Object} worksheet - ExcelJS工作表对象
   * @param {Array} headerStructure - 表头结构信息
   * @param {Object} options - 保护选项
   */
  function createMultiLevelHeaders(worksheet, headerStructure, options = {}) {
    const { protectWorksheet = false, lockedColumns = [] } = options;

    // 检查是否需要创建两级表头
    const hasParentHeaders = headerStructure.some((item) => item.isParent);

    if (hasParentHeaders) {
      // 创建两级表头
      headerStructure.forEach((item) => {
        if (item.isParent) {
          // 父级表头 - 合并单元格
          const startCol = item.startCol + 1;
          const endCol = item.endCol + 1;

          if (startCol === endCol) {
            // 只有一个子列，不需要合并
            const cell = worksheet.getCell(1, startCol);
            cell.value = item.field.fieldName || item.field.label;
          } else {
            // 合并父级表头单元格
            worksheet.mergeCells(1, startCol, 1, endCol);
            const cell = worksheet.getCell(1, startCol);
            cell.value = item.field.fieldName || item.field.label;
          }

          // 设置父级表头样式
          const cell = worksheet.getCell(1, startCol);
          cell.alignment = { horizontal: 'center', vertical: 'middle' };
          cell.fill = {
            type: 'pattern',
            pattern: 'solid',
            fgColor: { argb: 'FFE6E6E6' },
          };

          if (protectWorksheet) {
            cell.protection = { locked: true };
          }
        }
      });

      // 创建子级表头
      headerStructure.forEach((item) => {
        if (item.isChild) {
          const cell = worksheet.getCell(2, item.colIndex + 1);

          if (item.field.isRequired) {
            cell.value = {
              richText: [
                { text: '*', font: { color: { argb: 'FFFF0000' }, bold: true } },
                { text: item.field.fieldName || item.field.label, font: { color: { argb: 'FF000000' } } },
              ],
            };
          } else {
            cell.value = item.field.fieldName || item.field.label;
          }

          // 设置子级表头样式
          cell.alignment = { horizontal: 'center', vertical: 'middle' };

          if (protectWorksheet) {
            const isLocked = isColumnLocked(item.colIndex, item.field, lockedColumns);
            cell.protection = { locked: true };

            if (isLocked) {
              cell.fill = {
                type: 'pattern',
                pattern: 'solid',
                fgColor: { argb: 'FFE6E6E6' },
              };
            }
          }
        } else if (!item.isParent) {
          // 单级表头 - 需要跨两行
          worksheet.mergeCells(1, item.colIndex + 1, 2, item.colIndex + 1);
          const cell = worksheet.getCell(1, item.colIndex + 1);

          if (item.field.isRequired) {
            cell.value = {
              richText: [
                { text: '*', font: { color: { argb: 'FFFF0000' }, bold: true } },
                { text: item.field.fieldName || item.field.label, font: { color: { argb: 'FF000000' } } },
              ],
            };
          } else {
            cell.value = item.field.fieldName || item.field.label;
          }

          // 设置单级表头样式
          cell.alignment = { horizontal: 'center', vertical: 'middle' };

          if (protectWorksheet) {
            const isLocked = isColumnLocked(item.colIndex, item.field, lockedColumns);
            cell.protection = { locked: true };

            if (isLocked) {
              cell.fill = {
                type: 'pattern',
                pattern: 'solid',
                fgColor: { argb: 'FFE6E6E6' },
              };
            }
          }
        }
      });

      return 2; // 返回表头行数
    } else {
      // 只有一级表头
      headerStructure.forEach((item, idx) => {
        const cell = worksheet.getCell(1, idx + 1);

        if (item.field.isRequired) {
          cell.value = {
            richText: [
              { text: '*', font: { color: { argb: 'FFFF0000' }, bold: true } },
              { text: item.field.fieldName || item.field.label, font: { color: { argb: 'FF000000' } } },
            ],
          };
        } else {
          cell.value = item.field.fieldName || item.field.label;
        }

        if (protectWorksheet) {
          const isLocked = isColumnLocked(idx, item.field, lockedColumns);
          cell.protection = { locked: true };

          if (isLocked) {
            cell.fill = {
              type: 'pattern',
              pattern: 'solid',
              fgColor: { argb: 'FFE6E6E6' },
            };
          }
        }
      });

      return 1; // 返回表头行数
    }
  }

  // 导出数据到Excel
  async function exportToExcel(data, fields, options = {}) {
    // 解构选项参数
    const {
      protectWorksheet = false, // 是否保护工作表
      password = 'default123', // 保护密码
      lockedColumns = [], // 需要锁定的列索引数组或字段代码数组
      allowInsertRows = false, // 是否允许插入行
      allowDeleteRows = false, // 是否允许删除行
      allowFormatColumns = false, // 是否允许格式化列
      allowFormatRows = false, // 是否允许格式化行
    } = options;

    console.log('exportToExcel', data);
    console.log('exportToExcel', fields);
    console.log('exportToExcel', options);

    // 辅助函数：将列索引转换为Excel列名
    function getColumnName(index) {
      let result = '';
      while (index >= 0) {
        result = String.fromCharCode(65 + (index % 26)) + result;
        index = Math.floor(index / 26) - 1;
      }
      return result;
    }

    try {
      const workbook = new ExcelJS.Workbook();
      const worksheet = workbook.addWorksheet('数据导出');

      // 展平嵌套字段结构
      const { flatFields, headerStructure } = flattenFields(fields);

      // 创建一个隐藏的工作表用于存放下拉选项数据
      const dropdownSheet = workbook.addWorksheet('下拉选项', { state: 'hidden' });

      // 创建多级表头
      const headerRowCount = createMultiLevelHeaders(worksheet, headerStructure, {
        protectWorksheet,
        lockedColumns,
      });

      // 枚举下拉数据处理
      let dropdownColumnIndex = 0;
      flatFields.forEach((field, colIdx) => {
        if (field.fieldType === 'ENUM') {
          let enumOptions = field.enumValues || [];

          if (field.fieldType === 'ENUM' && field?.fieldName === '质量标准') {
            enumOptions = data?.map((i) => ({ label: i['qualityIndicatorName'], value: i['qualityIndicatorId'] }));
          }

          if (Array.isArray(enumOptions) && enumOptions.length > 0) {
            const labels = enumOptions.map((opt) => opt.label);

            // 将下拉选项写入隐藏工作表
            const dropdownColumnName = getColumnName(dropdownColumnIndex);
            labels.forEach((label, rowIndex) => {
              dropdownSheet.getCell(`${dropdownColumnName}${rowIndex + 1}`).value = label;
            });

            // 定义下拉选项的范围
            const dropdownRange = `下拉选项!${dropdownColumnName}1:${dropdownColumnName}${labels.length}`;

            // 获取当前列的列名
            const currentColumnName = getColumnName(colIdx);

            // 为当前列添加数据验证（限制在合理的行数范围内）
            const maxRows = Math.max(1000, data.length + 100);

            // 只对未锁定的列添加数据验证
            if (!isColumnLocked(colIdx, field, lockedColumns)) {
              worksheet.dataValidations.add(`${currentColumnName}${headerRowCount + 1}:${currentColumnName}${maxRows}`, {
                type: 'list',
                allowBlank: true,
                formulae: [dropdownRange],
                showErrorMessage: true,
                errorTitle: '输入错误',
                error: '请从下拉列表中选择有效选项',
                showInputMessage: true,
                promptTitle: '选择提示',
                prompt: '请从下拉列表中选择选项',
              });
            }

            dropdownColumnIndex++;
          }
        }
      });

      // 添加数据行
      data.forEach((row, rowIndex) => {
        const dataRow = worksheet.getRow(rowIndex + headerRowCount + 1);
        flatFields.forEach((field, colIndex) => {
          const key = field.fieldCode;
          let cellValue = row[key];
          const cell = dataRow.getCell(colIndex + 1);

          // 处理枚举值
          let enumOptions = field.enumValues || [];

          if (field.fieldType === 'ENUM' && field?.fieldName === '质量标准') {
            enumOptions = [{ label: row['qualityIndicatorName'], value: row['qualityIndicatorId'] }];
          }

          if (field.fieldType === 'ENUM' && enumOptions?.length > 0) {
            try {
              cellValue = enumOptions?.find((item) => item.value === cellValue)?.label || cellValue;
              cell.value = cellValue;
            } catch (e) {
              console.error('解析枚举值失败:', e);
            }
          }

          // 处理附件
          if (field.fieldType === 'ATTACH' && cellValue) {
            try {
              // 解析附件JSON字符串
              const attachments = typeof cellValue === 'string' ? JSON.parse(cellValue) : cellValue;

              if (Array.isArray(attachments) && attachments.length > 0) {
                // 设置第一个附件
                const firstAttachment = attachments[0];
                cell.value = {
                  text: firstAttachment.name || '查看附件',
                  hyperlink: import.meta.env.VITE_ADMIN_PROXY_PATH + '/api' + firstAttachment.url,
                };

                // 如果有多个附件，添加注释
                if (attachments.length > 1) {
                  const noteTexts = [
                    { text: '其他附件：\n' },
                    ...attachments.slice(1).map((attachment) => ({
                      text: `${attachment.name || '查看附件'}\n`,
                      hyperlink: import.meta.env.VITE_ADMIN_PROXY_PATH + '/api' + attachment.url,
                    })),
                  ];

                  cell.note = {
                    texts: noteTexts,
                  };
                }

                cell.font = {
                  color: { argb: 'FF0000FF' },
                  underline: true,
                };
              } else {
                cell.value = cellValue;
              }
            } catch (e) {
              console.error('解析附件数据失败:', e);
              cell.value = cellValue;
            }
          } else {
            cell.value = cellValue;
          }

          // 设置单元格保护
          if (protectWorksheet) {
            const isLocked = isColumnLocked(colIndex, field, lockedColumns);
            cell.protection = { locked: isLocked };

            // 为锁定的单元格添加背景色
            if (isLocked) {
              cell.fill = {
                type: 'pattern',
                pattern: 'solid',
                fgColor: { argb: 'FFF0F0F0' }, // 更浅的灰色背景
              };
            }
          }
        });
      });

      // 设置列宽
      worksheet.columns = flatFields.map(() => ({ width: 20 }));

      // 添加边框
      worksheet.eachRow((row) => {
        row.eachCell((cell) => {
          cell.border = {
            top: { style: 'thin' },
            left: { style: 'thin' },
            bottom: { style: 'thin' },
            right: { style: 'thin' },
          };
        });
      });

      // 保护工作表
      if (protectWorksheet) {
        worksheet.protect(password, {
          selectLockedCells: true, // 允许选择锁定的单元格
          selectUnlockedCells: true, // 允许选择未锁定的单元格
          formatCells: false, // 禁止格式化单元格
          formatColumns: allowFormatColumns,
          formatRows: allowFormatRows,
          insertRows: allowInsertRows,
          deleteRows: allowDeleteRows,
          insertColumns: false, // 禁止插入列
          deleteColumns: false, // 禁止删除列
          insertHyperlinks: false, // 禁止插入超链接
          deleteHyperlinks: false, // 禁止删除超链接
          sort: false, // 禁止排序
          autoFilter: false, // 禁止自动筛选
          pivotTables: false, // 禁止数据透视表
        });
      }

      // 导出
      const buffer = await workbook.xlsx.writeBuffer();
      return new Blob([buffer], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
    } catch (error) {
      console.error('导出Excel失败:', error);
      throw error;
    }
  }

  return {
    importExcelFile,
    exportToExcel,
  };
}
