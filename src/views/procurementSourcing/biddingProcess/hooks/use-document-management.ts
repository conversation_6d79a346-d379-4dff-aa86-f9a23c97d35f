/**
 * 文档管理逻辑钩子
 */
import { ref } from 'vue'

export function useDocumentManagement() {
  // 文档列表
  const documentList = ref([])
  
  // 加载状态
  const loading = ref(false)
  
  // 错误信息
  const error = ref<string | null>(null)

  /**
   * 获取文档列表
   */
  async function fetchDocumentList(projectId: string): Promise<void> {
    try {
      loading.value = true
      error.value = null
      
      // TODO: 调用API获取文档列表
      
    } catch (err) {
      error.value = err instanceof Error ? err.message : '获取文档列表失败'
    } finally {
      loading.value = false
    }
  }

  return {
    documentList,
    loading,
    error,
    fetchDocumentList
  }
} 