/* eslint-disable no-console */
import { ref, computed, onMounted } from 'vue';
import { fetchList as getBaseQualityIndicator } from '@/api/lowcode/base-quality-indicator/index';
import { MERGE_COLUMNS, FILED_ARRAY, FILED_ARRAY_OPTION, FIXED_COLLECT } from '@/views/procurementSourcing/biddingProcess/constants/bid';
import { useBiddingStore } from '@/views/procurementSourcing/biddingProcess/stores';
import { extractFieldNames, replaceFieldNames } from '@/utils/stringParser';
import { calculateFieldExpression } from '@/utils/safeCalculator';
import { useUserRole } from '@/views/procurementSourcing/biddingProcess/utils';

export const useDynamicTable = () => {
  const loading = ref(false);
  const biddingStore = useBiddingStore();
  const { isSupplier } = useUserRole();
  const isSupplierRole = computed(() => isSupplier.value);

  const dynamicTableData = ref([]);
  const dynamicColumn = ref([]);
  const tableData = ref([]);
  // 是否已开标
  // 状态见 D:\workspace\fe-procurement-platform\src\views\procurementSourcing\biddingProcess\constants\process-status.ts
  const isBidOpened = computed(
    () => !['TO_NOTICE', 'NOTICE', 'REGISTER', 'QUOTING', 'TO_BID_OPEN'].includes(biddingStore?.projectDetail?.progressStatus)
  );
  const baseServiceTypeFieldList = computed(() => biddingStore?.baseServiceTypeFieldList);
  const usageLocations = computed(() => biddingStore?.usageLocationList);

  function resetStatus() {
    dynamicTableData.value = [];
    dynamicColumn.value = [];
    tableData.value = [];
  }

  const commonColumnProps = {
    width: 120,
    showOverflowTooltip: true,
  };

  function getBaseServiceTypeFieldList(res) {
    const baseEntities = (res || [])?.map((item) => {
      return {
        ...item,
        ...commonColumnProps,
        prop: item?.fieldCode,
        // fieldCode: specialFieldCode[item?.fieldCode] ||  item?.fieldCode
      };
    });
    // 固定字段
    const fixedEntities = baseEntities?.filter((item) => FIXED_COLLECT?.includes(item?.fieldCode));
    // 动态字段
    const dynamicEntities = baseEntities?.filter((item) => !FIXED_COLLECT?.includes(item?.fieldCode));
    // 动态字段-报价字段
    const quoteEntities = dynamicEntities?.filter((item) => item?.payIsManyType === '0');
    // 动态字段-多支付方式字段
    const multiplePaymentEntities = dynamicEntities?.filter((item) => item?.payIsManyType === '1');
    const multiplePaymentEntitiesCode = multiplePaymentEntities?.map((item) => item?.fieldCode) || [];

    const specialField = [
      { fieldCode: 'quotePrice', fieldValue: '', fieldType: 'NUM' },
      { fieldCode: 'quoteAmount', fieldValue: '', fieldType: 'NUM_CALC' },
    ]?.filter((item) => multiplePaymentEntitiesCode?.includes(item?.fieldCode));

    return {
      baseEntities,
      fixedEntities,
      dynamicEntities,
      quoteEntities,
      multiplePaymentEntities,
      multiplePaymentEntitiesCode,
      specialField,
    };
  }

  const entities = computed(() => {
    const baseEntities = baseServiceTypeFieldList.value?.filter((item) => item?.applyToQuote === 'YES');
    return getBaseServiceTypeFieldList(baseEntities);
  });
  // 修改需求牧场填充所在区域和详细地址
  const changeUsageLocation = (val, row) => {
    let current = usageLocations.value.find((i) => i.value === val) || {};
    console.log('changeUsageLocation-usageLocations', usageLocations.value);
    console.log('changeUsageLocation-value', val);
    console.log('changeUsageLocation-current', current);
    console.log('changeUsageLocation-result', biddingStore?.getAddressByDistrict(current.district));

    row['usageLocationRegion'] = biddingStore?.getAddressByDistrict(current.district);
    row['usageLocationAddress'] = current.address;
  };

  // 动态字段配置
  const dynamicFields = ref([]);

  // 获取质量标准数据
  const getQualityIndicator = async (row) => {
    // 获取质量标准-通过物料筛选
    const indicatorsRes = await getBaseQualityIndicator({ page: 1, size: 1000 }, { statusList: 'ENABLED', materialCode: row.materialCode });
    row.indicators = (indicatorsRes.data.records || []).map((i) => {
      return {
        label: i.indicatorName,
        value: i.id,
      };
    });
  };

  /**
   * 根据字段配置生成校验规则
   * @param column 列配置
   * @returns 校验规则对象
   */
  function getFieldRules(column) {
    const rules = [];

    // 必填校验
    if (column.isRequired) {
      rules.push({
        required: true,
        message: `${column.label}不能为空`,
        trigger: ['blur', 'change'],
      });
    }

    // 根据字段类型添加特定校验
    switch (column.fieldType) {
      case 'TEXT':
        if (column.fieldLength) {
          rules.push({
            max: Number(column.fieldLength),
            message: `${column.label}长度不能超过${column.fieldLength}个字符`,
            trigger: 'blur',
          });
        }
        break;

      case 'NUM':
        rules.push({
          pattern: /^\d+(\.\d+)?$/,
          message: '请输入有效的数字',
          trigger: 'blur',
        });

        if (column.fieldLength) {
          rules.push({
            validator: (rule, value, callback) => {
              if (value && value.toString().length > column.fieldLength) {
                callback(new Error(`${column.label}总长度不能超过${column.fieldLength}个字符`));
              } else {
                callback();
              }
            },
            trigger: 'blur',
          });
        }
        break;

      case 'ENUM':
      case 'LINK_FORM':
        if (column.isRequired) {
          rules.push({
            required: true,
            message: `请选择${column.label}`,
            trigger: 'change',
          });
        }
        break;
    }

    // 如果没有任何规则，返回空数组
    return rules.length > 0 ? rules : [];
  }

  /**
   * 获取枚举字段的选项
   * @param column 列配置
   * @returns 选项数组
   */
  function getEnumOptions(column) {
    if (Array.isArray(column.enumValues)) {
      return column.enumValues || [];
    }
    try {
      const options = JSON.parse(column.enumValues);
      return Array.isArray(options) ? options : [];
    } catch (error) {
      return [];
    }
  }

  // 根据动态字段生成表格内表单配置
  const generateDynamicFormConfig = (fields) => {
    const columns = [];

    // 确保fields是一个有效的数组
    if (!Array.isArray(fields)) {
      return columns;
    }

    fields.forEach((field) => {
      // 确保field和field.fieldCode存在且有效
      if (!field || !field.fieldCode || typeof field.fieldCode !== 'string' || field.fieldCode.trim() === '') {
        console.warn('跳过无效的字段:', field);
        return;
      }

      // 清理字段代码，确保只包含有效字符
      const safeFieldCode = field.fieldCode.replace(/[^a-zA-Z0-9_.]/g, '');
      if (!safeFieldCode) {
        console.warn('字段代码包含无效字符，跳过:', field.fieldCode);
        return;
      }

      const formItem = {
        defaultValue: field.defaultValue,
        fieldType: field.fieldType,
        fieldName: field.fieldName,
        fieldCode: field.fieldCode,
        enumValues: field.enumValues || [],
        isRequired: field.isRequired || false,
        isAllowEdit: field.isAllowEdit || true,
        children: field.children || [],

        prop: safeFieldCode,
        label: field.rename || field.fieldName || safeFieldCode,
        rules: field.isRequired ? [{ required: true, message: `${field.rename || field.fieldName || safeFieldCode}不能为空` }] : [],
        colProps: { span: 24 },
        width: ['质量标准', '所在区域'].includes(field.fieldName) ? 200 : 140,
        renderHeader: field.isRequired
          ? ({ column }) => {
              return (
                <span>
                  <i style="color: #FF3B30">*</i> {column.label}
                </span>
              );
            }
          : undefined,
      };
      // 根据字段类型设置不同的表单项类型和校验规则
      switch (field.fieldType) {
        case 'TEXT':
          if (field.fieldLength) {
            formItem.rules.push({
              max: Number(field.fieldLength),
              message: `${field.rename || field.fieldName || safeFieldCode}长度不能超过${field.fieldLength}个字符`,
            });
          }
          formItem.attrs = {
            disabled: FILED_ARRAY.includes(field.fieldCode),
            placeholder: field.fieldDesc || '请输入',
          };
          break;
        case 'NUM':
          formItem.rules.push({
            pattern: /^\d+(\.\d+)?$/,
            message: '请输入数字',
          });
          if (field.fieldLength) {
            formItem.rules.push({
              validator: (rule, value, callback) => {
                if (value && value.toString().length > field.fieldLength) {
                  callback(new Error(`${field.rename || field.fieldName || safeFieldCode}总长度不能超过${field.fieldLength}个字符`));
                } else {
                  callback();
                }
              },
              trigger: 'blur',
            });
          }
          break;
        case 'NUM_CALC':
          formItem.formatter = calculate;
          break;
        case 'ENUM':
          // formItem.enums = [];
          // try {
          //   if (field.enumValues) {
          //     const parsedEnums = JSON.parse(field.enumValues);
          //     formItem.enums = Array.isArray(parsedEnums)
          //       ? parsedEnums.map((item) => ({
          //           label: item.label,
          //           value: item.value,
          //         }))
          //       : [];
          //   }
          // } catch (error) {
          //   console.error('解析枚举值失败:', error);
          // }
          break;
        case 'LINK_FORM':
          // formItem.enums = [];
          break;
        case 'ATTACH':
          break;
        default:
      }
      columns.push(formItem);
    });
    return columns;
  };

  // 合并固定字段和动态字段的表单配置
  const mergedFormColumns = computed(() => {
    const res = getBaseServiceTypeFieldList(dynamicFields.value?.sort((a, b) => a.sort - b.sort));
    const fields = generateDynamicColumn(res);
    const columns = generateDynamicFormConfig(fields) || [];
    const newColumns = columns?.map((i) => {
      return {
        ...i,
        children: i.children?.length ? generateDynamicFormConfig(i.children) : [],
        // ...commonColumnProps,
      };
    });
    return newColumns;
    // 过滤掉无效的列配置
    // return columns.filter((column) => column && column.prop && typeof column.prop === 'string' && column.prop.trim() !== '');
  });

  // 获取详情
  const getDetail = async (res) => {
    try {
      const list = res?.filter((item) => item?.baseServiceTypeFieldEntityList?.length) || [];
      const baseServiceTypeFieldList = list?.[0]?.baseServiceTypeFieldEntityList || [];
      const config =
        Array.isArray(baseServiceTypeFieldList) &&
        baseServiceTypeFieldList.length &&
        baseServiceTypeFieldList.map((i) => {
          if (i.fieldName === '需求牧场') {
            i.enumValues = JSON.stringify(usageLocations.value || []);
          }
          // if (i.fieldName === '质量标准') {
          //   i.enumValues = JSON.stringify(qualityIndicators.value || [])
          // }
          i.isRequired = i.required || i.quoteIsRequired === '1';
          i.isAllowEdit = !!(i.applyToQuoteEdit === 'YES'); // 是否可编辑
          try {
            const options = JSON.parse(i.enumValues);
            const enums = Array.isArray(options) ? options : [];
            i.enumValues = enums.map((item) => ({
              label: item.label,
              value: item.value,
            }));
          } catch (error) {
            i.enumValues = [];
          }
          return i;
        });
      dynamicTableData.value = res?.map((j) => {
        let { baseServiceTypeFieldEntityList, ...rest } = j;
        let dynamicFieldsValues = {};
        baseServiceTypeFieldEntityList?.forEach((j) => {
          if (j.fieldType === 'ATTACH') {
            dynamicFieldsValues[j.fieldCode] = JSON.parse(j.fieldValue || []);
          } else {
            dynamicFieldsValues[j.fieldCode] = j.fieldValue;
          }
        });

        const current = usageLocations.value.find((i) => i.value === j?.usageLocationId) || {};
        rest.indicators = [{ label: j.qualityIndicatorName, value: j.qualityIndicatorId }];
        return {
          ...dynamicFieldsValues,
          ...rest,
          usageLocationRegion: biddingStore?.getAddressByDistrict(current.district),
          usageLocationAddress: current.address,
        };
      });
      dynamicFields.value = config;
    } catch (error) {
      // eslint-disable-next-line no-console
      console.error('获取详情失败:', error);
    }
  };

  const handleTotalResult = (row) => {
    const { baseEntities } = entities.value;
    baseEntities.forEach((item) => {
      if (item.fieldType === 'NUM_CALC') {
        row[item.fieldCode] = calculate(item, row);
      }
    });
  };

  const handlePaymentListResult = (row, paymentIDS) => {
    if (paymentIDS && paymentIDS?.length) {
      const paymentList = projectPaymentList.value?.filter((i) => paymentIDS?.includes(i?.id)) || [];
      row.paymentList = paymentList?.map((i) => {
        return {
          ...i,
          amount: row[`quoteAmount_${i?.id}`] || row[`quoteAmount`] || 0,
          price: row[`quotePrice_${i?.id}`] || row[`quotePrice`] || 0,
        };
      });
    } else {
      row.paymentList = (projectPaymentList.value || [])?.map((i) => {
        return {
          ...i,
          amount: 0,
          price: 0,
        };
      });
    }
  };

  /**
   * 解析计算规则并计算结果
   * @param {object} field - 字段配置对象
   * @param {object} row - 当前行数据
   * @returns {number|string} - 计算结果
   */

  // defaultValue ==> @availableQuantity*@quotePrice
  const calculate = (col, row) => {
    const { multiplePaymentEntitiesCode } = entities.value;
    let { defaultValue, prop } = col;

    // 检查row是否为空对象或无效值
    if (!defaultValue || !row || Object.keys(row).length === 0) {
      row[prop] = '';
      return '';
    }

    try {
      // 兼容多支付方式
      const attrs = prop?.split('_') || [];
      const paymentId = attrs?.[1] || '';
      const e = extractFieldNames(defaultValue);

      if (paymentId && e?.length) {
        const fieldMapping = e?.reduce((acc, i) => {
          if (multiplePaymentEntitiesCode?.includes(i)) {
            acc[i] = `${i}_${paymentId}`;
          } else {
            acc[i] = `${i}`;
          }
          return acc;
        }, {});
        defaultValue = replaceFieldNames(defaultValue, fieldMapping);
      }

      // 使用安全的字段表达式计算器
      const result = calculateFieldExpression(defaultValue, row, {
        decimals: 2,
        defaultValue: 0,
        debug: false, // 可以根据需要开启调试
        returnEmptyOnError: true
      });

      // 更新row对象中的值
      row[prop] = result;
      return result;

    } catch (error) {
      console.error('计算规则解析错误:', error, '表达式:', defaultValue, '字段:', prop);
      row[prop] = '';
      return '';
    }
  };

  // 动态表头的一些固定字段
  const DYNAMIC_TABLE_HEAD_FIELDS = FILED_ARRAY_OPTION?.map((item) => ({
    ...item,
    prop: item.value,
  }));
  // 计算每个物料的行数，用于合并单元格
  const getMaterialRowSpans = () => {
    const spanMap = new Map();
    let currentMaterialCode = '';
    let startIndex = 0;
    let count = 0;

    tableData.value.forEach((row, index) => {
      if (row.materialCode !== currentMaterialCode) {
        // 如果是新的物料，先保存上一个物料的信息
        if (currentMaterialCode && count > 0) {
          spanMap.set(`${currentMaterialCode}_${startIndex}`, { startIndex, count });
        }
        // 开始新物料的计数
        currentMaterialCode = row.materialCode;
        startIndex = index;
        count = 1;
      } else {
        // 相同物料，计数增加
        count++;
      }
    });

    // 保存最后一个物料的信息
    if (currentMaterialCode && count > 0) {
      spanMap.set(`${currentMaterialCode}_${startIndex}`, { startIndex, count });
    }

    return spanMap;
  };

  const objectSpanMethod = ({ row, column, rowIndex }) => {
    // 只对固定字段进行合并
    if (MERGE_COLUMNS.includes(column.property)) {
      const spanMap = getMaterialRowSpans();
      const materialCode = row.materialCode;

      // 查找当前行是否是某个物料组的起始行
      for (const [key, value] of spanMap.entries()) {
        const [code, startIdx] = key.split('_');
        if (code === materialCode && Number(startIdx) === rowIndex) {
          // 如果是起始行，返回合并的行数
          return [value.count, 1];
        }
      }

      // 查找当前行是否在某个物料组的中间行
      for (const [key, value] of spanMap.entries()) {
        const [code, startIdx] = key.split('_');
        const start = Number(startIdx);
        if (code === materialCode && rowIndex > start && rowIndex < start + value.count) {
          // 如果是中间行，隐藏该单元格
          return [0, 0];
        }
      }
    }

    // 非固定字段或不需要合并的情况，返回默认值
    return [1, 1];
  };

  // 项目付款列表 多支付方式 只需要名称
  const projectPaymentList = computed(() => {
    return (biddingStore?.projectDetail?.projectPaymentList || [])?.map((item) => ({
      // deptId: item?.deptId,
      // paymentMethodId: item?.paymentMethodId,
      // paymentPeriodId: item?.paymentPeriodId,
      // paymentRemark: item?.paymentRemark,
      // projectId: item?.projectId,
      // tenantId: item?.tenantId,
      id: item?.id,
      paymentMethodName: item?.paymentMethodName,
      value: item?.id,
      label: item?.paymentMethodName,
    }));
  });

  const arrayToObjectDynamic = ({ array, paymentId, row } = {}) => {
    return array?.reduce((acc, item) => {
      const key = paymentId ? `${item.fieldCode}_${paymentId}` : item.fieldCode;
      // 特殊字段 用于回显值 需要根据row中的值计算
      // if (['quotePrice', 'quoteAmount'].includes(item.fieldCode)) {
      //   acc[key] = item.fieldValue || row?.[item.fieldCode] || '';
      // } else {
      //   acc[key] = item.fieldValue || '';
      // }
      acc[key] = item.fieldValue || row?.[item.fieldCode] || '';
      return acc;
    }, {});
  };

  // 获取拼接动态列
  const getDynamicColumn = (array) => {
    return array?.reduce((acc, item) => {
      acc.push({
        label: item?.fieldName,
        prop: item?.fieldCode,
        fieldType: item?.fieldType,
        ...addSortField(item),
        ...commonColumnProps,
      });
      return acc;
    }, []);
  };

  // 生成动态列
  const generateDynamicColumn = (res = null) => {
    const { quoteEntities, fixedEntities, multiplePaymentEntities } = res || entities.value;
    const paymentList = projectPaymentList.value
      ?.map((item) => {
        return {
          fieldName: item?.paymentMethodName,
          fieldCode: item?.id,
          ...commonColumnProps,
          // fileType: 'MULTIPLE_PAYMENT',
          children: multiplePaymentEntities?.map((child) => ({
            ...child,
            fieldCode: `${child?.fieldCode}_${item?.id}`,
          })),
        };
      })
      ?.filter((item) => !!item?.children?.length);
    return [...fixedEntities, ...quoteEntities, ...paymentList];
  };


   // 添加排序字段
  function addSortField(item) {
    const fieldType = item?.fieldType;
    if (['NUM_CALC', 'NUM'].includes(fieldType)) {
      return {
        // sortable: true,
      };
    }
    return {}
  }

  // 设置动态列
  const setDynamicColumn = () => {
    const { quoteEntities, fixedEntities, multiplePaymentEntities } = entities.value;
    console.log(fixedEntities, 'fixedEntities');
    const fixedColumn = getDynamicColumn(fixedEntities);
    const quoteColumn = getDynamicColumn(quoteEntities);
    const paymentList = projectPaymentList.value
      ?.map((item) => {
        return {
          label: item?.paymentMethodName,
          prop: item?.id,
          children: multiplePaymentEntities
            ?.map((child) => ({
              label: child?.fieldName,
              prop: `${child?.fieldCode}_${item?.id}`,
              fieldName: child?.fieldName,
              fieldCode: `${child?.fieldCode}_${item?.id}`,
              ...commonColumnProps,
              ...addSortField(child),
            }))
            ?.filter((item) => !!item?.label),
        };
      })
      ?.filter((item) => !!item?.label && !!item?.children?.length);
    dynamicColumn.value = [
      ...fixedColumn,
      {
        label: '报价供应商',
        prop: 'supplierName',
        ...commonColumnProps,
      },
      ...quoteColumn,
      ...paymentList,
    ]?.map((item) => {
      return {
        ...item,
        fieldName: item?.label,
        fieldCode: item?.prop,
      };
    });
    console.log(dynamicColumn.value, 'dynamicColumn.value');
  };

  const forceHideValue = (forceHide = false) => {
    // 供应商角色不隐藏值
    if (isSupplierRole.value) {
      return {};
    }

    const { dynamicEntities } = entities.value;
    const paymentIds = projectPaymentList.value?.map((item) => item?.id) || [];
    if (forceHide && !isBidOpened.value) {
      return dynamicEntities?.reduce((acc, item) => {
        if (['NUM_CALC', 'NUM'].includes(item.fieldType) && item.payIsManyType === '0') {
          acc[item.fieldCode] = '*';
        }
        if (['NUM_CALC', 'NUM'].includes(item.fieldType) && item.payIsManyType === '1') {
          paymentIds?.forEach((paymentId) => {
            acc[`${item.fieldCode}_${paymentId}`] = '*';
          });
        }
        return acc;
      }, {});
    }
    return {};
  };

  const handleTableData = (data, isNewQuotation = false) => {
    const { multiplePaymentEntitiesCode, quoteEntities, multiplePaymentEntities, specialField } = entities.value;
    const list = [];
    data?.forEach((item) => {
      // 获取固定字段 物料信息
      const supplierQuoteList = item?.supplierQuoteList || [];
      const materialInfo = { ...item };
      const current = usageLocations.value.find((i) => i?.value === materialInfo?.usageLocationId) || {};
      materialInfo.usageLocationRegion = materialInfo?.usageLocationRegion || biddingStore?.getAddressByDistrict(current?.district);
      materialInfo.usageLocationAddress = materialInfo?.usageLocationAddress || current?.address;
      // 将系统供应商 的多支付方式合并成一个
      if (supplierQuoteList?.length) {
        const uniqueSupplierId = [...new Set(supplierQuoteList?.map((item) => `${item?.tenantSupplierId}`))];
        uniqueSupplierId?.forEach((id) => {
          const paymentIDS = (supplierQuoteList?.filter((i) => `${i?.tenantSupplierId}` === id) || [])?.map((p) => p?.procurementProjectPaymentId);
          const items = projectPaymentList.value?.reduce((acc, paymentId) => {
            const quote =
              supplierQuoteList?.find((i) => `${i?.tenantSupplierId}` === id && `${i?.procurementProjectPaymentId}` === paymentId?.id) || {};
            const s = (quote?.srmTenderBidderQuoteFieldValues || [])?.concat(specialField);
            const d = s?.filter((i) => multiplePaymentEntitiesCode?.includes(i?.fieldCode)) || [];
            const f = s?.filter((i) => !multiplePaymentEntitiesCode?.includes(i?.fieldCode)) || [];
            acc = {
              ...acc,
              // 多支付方式字段
              ...arrayToObjectDynamic({ array: d, paymentId: paymentId?.id, row: quote }),
              // 非多支付方式字段
              ...arrayToObjectDynamic({ array: f }),
              // 报价字段
              ...(quote || {}),
            };
            return acc;
          }, {});
          const temp = {
            ...materialInfo,
            ...items,

            // 特殊处理
            ...forceHideValue(isNewQuotation),
            usageLocationId: materialInfo?.usageLocationName,
            qualityIndicatorId: materialInfo?.qualityIndicatorName,
          };
          handleTotalResult(temp);
          handlePaymentListResult(temp, paymentIDS);
          list.push(temp);
        });
      } else {

        const items = projectPaymentList.value?.reduce((acc, paymentId) => {
          acc = {
            ...acc,
            ...arrayToObjectDynamic({ array: multiplePaymentEntities, paymentId: paymentId?.id, row: materialInfo }),
          };
          return acc;
        }, {});
        const temp = {
          ...materialInfo,
          ...arrayToObjectDynamic({ array: quoteEntities, row: materialInfo }),
          ...items,

          // 中标才有
          ...arrayToObjectDynamic({ array: item?.srmTenderBidderQuoteFieldValueList || [] }),

          // 特殊处理
          ...forceHideValue(isNewQuotation),
          usageLocationId: materialInfo?.usageLocationName,
          qualityIndicatorId: materialInfo?.qualityIndicatorName,
        };
        handleTotalResult(temp);
        handlePaymentListResult(temp, temp?.procurementProjectPaymentId);
        list.push(temp);
      }
    });
    // 按物料编码排序，确保相同物料的行连续
    list.sort((a, b) => {
      if (a.materialCode !== b.materialCode) {
        return a.materialCode.localeCompare(b.materialCode);
      }
      return 0;
    });

    console.log(list, 'list');
    tableData.value = list;
  };

  // 在组件挂载时获取动态字段
  onMounted(() => {});

  return {
    loading,
    dynamicFields,
    getDetail,

    dynamicTableData,
    mergedFormColumns,

    getFieldRules,
    getEnumOptions,
    getQualityIndicator,
    changeUsageLocation,

    DYNAMIC_TABLE_HEAD_FIELDS,
    dynamicColumn,
    setDynamicColumn,

    tableData,
    handleTableData,

    objectSpanMethod,

    resetStatus,
  };
};
