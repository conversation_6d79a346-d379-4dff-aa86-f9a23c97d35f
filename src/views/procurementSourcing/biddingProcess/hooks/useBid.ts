import { getSelectionRegisterInfo, getRegisteredSupplierList } from '@/api/purchasing/bid';
import { ref, onMounted } from 'vue';
import { jsonStringToObject } from '@/views/procurementSourcing/biddingProcess/utils/bid';
import { useBiddingStore } from '@/views/procurementSourcing/biddingProcess/stores';

export function useBid() {
  const tableData = ref<any[]>([]);
  const registeredSupplierList = ref<any[]>([]);

  const biddingStore = useBiddingStore();
  const noticeId = computed(() => biddingStore?.noticeId);

  // 获取当前项目有效的招标公告 TODO: 20需要修改
  async function getSelectionRegisterInfoData(extraParams: any = {}) {
    if (!noticeId.value) return;
    try {
      const res = await getSelectionRegisterInfo({
        noticeId: noticeId.value,
        ...extraParams,
      });
      tableData.value = (res?.data || [])?.map((item: any) => ({
        ...item,
        depositContent: jsonStringToObject(item?.depositContent),
        depositRespContent: jsonStringToObject(item?.depositRespContent),
      }));
    } catch (error) {
      tableData.value = [];
    }
  }

  // 采方获取已报名的供方列表
  async function getRegisteredSupplierListData(extParams: any = {}) {
    try {
      const res = await getRegisteredSupplierList({
        ...extParams,
      });
      registeredSupplierList.value = (res?.data || [])?.map((item: any) => ({
        ...item,
        depositRespContent: jsonStringToObject(item?.depositRespContent),
      }));
    } catch (error) {
      registeredSupplierList.value = [];
    }
  }

  onMounted(async () => {

  });
  return {
    tableData,
    getSelectionRegisterInfoData,

    registeredSupplierList,
    getRegisteredSupplierListData,
  };
}
