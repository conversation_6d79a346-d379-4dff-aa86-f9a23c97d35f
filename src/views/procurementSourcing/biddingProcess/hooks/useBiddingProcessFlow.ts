import { ref, computed, markRaw, type Component } from 'vue';
// import ProjectInfo from '../components/procurement/ProjectInfo/index.vue';
import ProjectInfo from '@/views/purchasing/projectProposal/detail/index.vue';
import ProjectNotice from '../components/procurement/NoticeWrapper/index.vue';
import AnnouncementInfo from '../components/announcement/AnnouncementInfo/index.vue';
import BiddingManagement from '../components/bidding/BiddingManagement/index.vue';
import BidOpeningMangement from '../components/BidOpening/BidOpeningMangement/index.vue';
import AwardManagement from '../components/award/AwardManagement/index.vue';
import SupplyBiddingManagement from '../components/bidding/SupplyBiddingManagement/index.vue';
import ContractSigining from '../components/ContractSigining/index.vue';
import ProjectArchive from '../components/ProjectArchive/index.vue';
import { isNodeCompleted, getCurrentActiveNodeIndex } from '../utils/process-status-helper';
import { useBiddingStore } from '../stores';
import BidEvaluation from '../components/BidEvaluation/index.vue';
import { getProcessNodeKeys } from '../utils/project-type-helper';
import { isNodeVisible } from '../utils/node-access-control';
// 角色类型
export type UserRole = 'purchaser' | 'supplier' | 'bidExpert' | 'bidAgency' | 'guest';
// 招标类型
export type TenderType = 'public' | 'invite';

// 流程节点 key
export enum PROCESS_NODES {
  PROCUREMENT = 'procurement',
  ANNOUNCEMENT = 'announcement',
  BIDDING = 'bidding',
  OPENING = 'opening',
  AWARD = 'award',
  EVALUATION = 'evaluation',
  CONTRACT = 'contract',
  ARCHIVE = 'archive',
}

// 节点配置类型
export interface ProcessNodeConfig {
  key: PROCESS_NODES;
  label: string;
  icon: string;
  component: Component;
}

// 流程状态类型（可扩展）
export interface ProcessFlowState {
  id: string;
  projectId: string;
  currentNode: PROCESS_NODES;
  status: string;
  startTime: string;
  createdBy: string;
  createdAt: string;
  updatedAt: string;
}

// 节点配置映射 - 将节点key映射到具体的配置
const NODE_CONFIG_MAP: Record<string, ProcessNodeConfig> = {
  [PROCESS_NODES.PROCUREMENT]: { key: PROCESS_NODES.PROCUREMENT, label: '采购需求', icon: 'Folder', component: ProjectInfo },
  [PROCESS_NODES.ANNOUNCEMENT]: { key: PROCESS_NODES.ANNOUNCEMENT, label: '发标', icon: 'Bell', component: AnnouncementInfo },
  [PROCESS_NODES.BIDDING]: { key: PROCESS_NODES.BIDDING, label: '投标', icon: 'Document', component: BiddingManagement },
  [PROCESS_NODES.OPENING]: { key: PROCESS_NODES.OPENING, label: '开标', icon: 'Unlock', component: BidOpeningMangement },
  [PROCESS_NODES.EVALUATION]: { key: PROCESS_NODES.EVALUATION, label: '评标', icon: 'Star', component: BidEvaluation },
  [PROCESS_NODES.AWARD]: { key: PROCESS_NODES.AWARD, label: '定标', icon: 'Trophy', component: AwardManagement },
  [PROCESS_NODES.CONTRACT]: { key: PROCESS_NODES.CONTRACT, label: '合同签订', icon: 'File', component: ContractSigining },
  [PROCESS_NODES.ARCHIVE]: { key: PROCESS_NODES.ARCHIVE, label: '项目归档', icon: 'Folder', component: ProjectArchive },
};

// 供应商特殊节点标签映射
const SUPPLIER_NODE_LABELS: Record<string, Record<TenderType, string>> = {
  [PROCESS_NODES.ANNOUNCEMENT]: {
    public: '采购公告',
    invite: '采购邀请函'
  }
};

// 供应商特殊组件映射
const SUPPLIER_NODE_COMPONENTS: Record<string, Component> = {
  [PROCESS_NODES.BIDDING]: SupplyBiddingManagement,
  [PROCESS_NODES.ANNOUNCEMENT]: ProjectNotice,
};

/**
 * 根据项目类型、用户角色和招标方式动态生成节点配置
 */
function generateProcessNodes(
  projectType: string,
  userRole: UserRole,
  tenderType: TenderType
): ProcessNodeConfig[] {
  // 获取该项目类型下的节点key列表
  const nodeKeys = getProcessNodeKeys(projectType, userRole, tenderType);

  return nodeKeys.map(nodeKey => {
    // 获取基础配置
    const baseConfig = NODE_CONFIG_MAP[nodeKey];
    if (!baseConfig) {
      console.warn(`未找到节点配置: ${nodeKey}`);
      return null;
    }

    // 为供应商角色定制标签和组件
    if (userRole === 'supplier') {
      const customLabel = SUPPLIER_NODE_LABELS[nodeKey]?.[tenderType];
      const customComponent = SUPPLIER_NODE_COMPONENTS[nodeKey];

      return {
        ...baseConfig,
        label: customLabel || baseConfig.label,
        component: customComponent || baseConfig.component
      };
    }

    return baseConfig;
  }).filter(Boolean) as ProcessNodeConfig[];
}

interface UseBiddingProcessFlowOptions {
  userRole: UserRole;
  // isPublic: any;
  projectId?: string;
  initialNodeIndex?: number;
}

export function useBiddingProcessFlow(options: UseBiddingProcessFlowOptions) {
  const { userRole, projectId = 'default-project', initialNodeIndex = 0 } = options;

  // 获取项目详情store
  const biddingStore = useBiddingStore();
  const projectDetail = computed(() => biddingStore?.projectDetail);
  const isPublic = computed(() => projectDetail.value?.inviteMethod === 'PUBLICITY');

  // 获取项目类型
  const projectType = computed(() => projectDetail.value?.sourcingType || '');

  // 获取开标方式
  const tenderWay = computed(() => biddingStore?.noticeInfo?.tenderWay);

  // 评标委员会成员特殊逻辑
  const isSkipNodes = computed(() => {
    return userRole === 'bidExpert' && (biddingStore.isEvaluationMember || biddingStore.isEvaluationCommitteeMember)
  })


  // 节点配置 - 使用动态生成的方式，并过滤不可见的节点
  const processNodes = computed(() => {
    const tenderType = isPublic.value ? 'public' : 'invite';

    // 如果没有项目类型，返回空数组
    if (!projectType.value) {
      return [];
    }

    // 生成所有节点
    const allNodes = generateProcessNodes(projectType.value, userRole, tenderType);

    // 过滤掉不可见的节点
    return allNodes.filter(node => {
      return isNodeVisible(
        node.key,
        projectType.value,
        userRole,
        tenderType,
        tenderWay.value
      );
    });
  });

  // 当前节点索引
  const currentNodeIndex = ref(initialNodeIndex);

  // 当前流程状态
  const currentProcess = ref<ProcessFlowState>({
    id: 'default-process',
    projectId,
    currentNode: processNodes.value[initialNodeIndex]?.key,
    status: 'bidding',
    startTime: new Date().toISOString(),
    createdBy: 'system',
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  });

  // 当前节点组件
  const currentComponent = computed(() => {
    const nodes = processNodes.value;
    if (!nodes.length || currentNodeIndex.value === -1) return null;
    return markRaw(nodes[currentNodeIndex.value]?.component || null);
  });

  // 获取项目进度状态
  const progressStatus = computed(() => {
    return biddingStore.projectDetail?.progressStatus || '';
  });

  // 判断节点是否已完成
  const isNodeCompletedFn = computed(() => (nodeIndex: number) => {

    if (progressStatus.value === 'COMPLETED' && !isSkipNodes.value) {
      return true;
    }

    const node = processNodes.value[nodeIndex];
    if (!node || !progressStatus.value) return false;

    const currentProjectType = projectDetail.value?.sourcingType;
    const tenderType = isPublic.value ? 'public' : 'invite';
    return isNodeCompleted(userRole, progressStatus.value, node.key, currentProjectType, tenderType);
  });

  // 根据项目状态自动设置当前活跃节点
  const autoSetCurrentNode = () => {
    if (progressStatus.value && processNodes.value.length > 0) {
      const currentProjectType = projectDetail.value?.sourcingType;
      const tenderType = isPublic.value ? 'public' : 'invite';
      const activeIndex = getCurrentActiveNodeIndex(userRole, progressStatus.value, processNodes.value, currentProjectType, tenderType);
      currentNodeIndex.value = activeIndex;
      currentProcess.value.currentNode = processNodes.value[activeIndex]?.key ?? PROCESS_NODES.PROCUREMENT;
    }
  };

  // 设置当前节点索引
  function setCurrentNode(index: number) {
    if (index >= 0 && index < processNodes.value.length) {
      currentNodeIndex.value = index;
      currentProcess.value.currentNode = processNodes.value[index].key;
      currentProcess.value.updatedAt = new Date().toISOString();
    }
  }

  // 流程推进
  function moveToNextNode() {
    if (currentNodeIndex.value < processNodes.value.length - 1) {
      setCurrentNode(currentNodeIndex.value + 1);
    }
  }

  // 流程回退
  function moveToPrevNode() {
    if (currentNodeIndex.value > 0) {
      setCurrentNode(currentNodeIndex.value - 1);
    }
  }
  return {
    processNodes,
    currentComponent,
    currentNodeIndex,
    setCurrentNode,
    moveToNextNode,
    moveToPrevNode,
    currentProcess,
    progressStatus,
    isNodeCompletedFn,
    autoSetCurrentNode,
  };
}
