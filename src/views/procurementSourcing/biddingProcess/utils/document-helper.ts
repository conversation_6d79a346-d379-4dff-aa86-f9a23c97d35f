/**
 * 文档处理函数
 */

/**
 * 格式化文件大小
 */
export function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 B'
  
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

/**
 * 获取文件扩展名
 */
export function getFileExtension(filename: string): string {
  return filename.slice((filename.lastIndexOf('.') - 1 >>> 0) + 2)
}

/**
 * 检查文件类型是否允许
 */
export function isAllowedFileType(filename: string, allowedTypes: string[]): boolean {
  const extension = getFileExtension(filename).toLowerCase()
  return allowedTypes.includes(extension)
}

/**
 * 生成文档类型标签
 */
export function getDocumentTypeLabel(type: string): string {
  const typeMap: Record<string, string> = {
    business_license: '营业执照',
    qualification: '资质证书',
    financial_report: '财务报告',
    technical_proposal: '技术方案',
    commercial_proposal: '商务方案',
    legal_statement: '法律声明',
    other: '其他'
  }
  return typeMap[type] || '未知类型'
}

/**
 * 检查文档是否必需
 */
export function isRequiredDocument(type: string): boolean {
  const requiredTypes = ['business_license', 'qualification', 'technical_proposal', 'commercial_proposal']
  return requiredTypes.includes(type)
}

/**
 * 验证文档完整性
 */
export function validateDocumentCompleteness(documents: any[]): {
  isComplete: boolean
  missingTypes: string[]
} {
  const requiredTypes = ['business_license', 'qualification', 'technical_proposal', 'commercial_proposal']
  const submittedTypes = documents.map(doc => doc.type)
  const missingTypes = requiredTypes.filter(type => !submittedTypes.includes(type))
  
  return {
    isComplete: missingTypes.length === 0,
    missingTypes
  }
} 