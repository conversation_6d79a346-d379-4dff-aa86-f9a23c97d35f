import {
  setAccessRules,
  type NodeAccessRule
} from './node-access-control';
import { PROCESS_NODES, PURCHASER_PROGRESS_STATUS } from '../constants/process-status';
import { PROJECT_TYPES } from '../constants/project-types';

/**
 * 节点访问管理器
 * 提供便捷的方法来管理节点访问权限
 */
export class NodeAccessManager {

  /**
   * 预设规则：启用所有默认的特殊访问规则
   */
  static enableDefaultRules(): void {
    const defaultRules: NodeAccessRule[] = [
      // 竞谈项目采购方在投标阶段可以访问开标节点（兼容公开和邀请模式）
      {
        projectTypes: [PROJECT_TYPES.NEGOTIATION, PROJECT_TYPES.BIDDING],
        tenderTypes: ['public', 'invite'],
        allowedStatuses: [
          PURCHASER_PROGRESS_STATUS.NOTICE,
          PURCHASER_PROGRESS_STATUS.TENDER_DOC,
          PURCHASER_PROGRESS_STATUS.REGISTER,
          PURCHASER_PROGRESS_STATUS.QUOTING,
        ],
        targetNode: PROCESS_NODES.OPENING,
        description: '竞谈项目采购方在投标阶段可以访问开标节点（兼容公开和邀请模式）'
      },
      // 竞谈项目采购方在投标阶段可以访问评标节点（兼容公开和邀请模式）
      {
        projectTypes: [PROJECT_TYPES.NEGOTIATION, PROJECT_TYPES.BIDDING],
        tenderTypes: ['public', 'invite'],
        allowedStatuses: [
          PURCHASER_PROGRESS_STATUS.NOTICE,
          PURCHASER_PROGRESS_STATUS.TENDER_DOC,
          PURCHASER_PROGRESS_STATUS.REGISTER,
          PURCHASER_PROGRESS_STATUS.QUOTING,
          PURCHASER_PROGRESS_STATUS.TO_BID_OPEN,
          PURCHASER_PROGRESS_STATUS.BID_OPENED,
          PURCHASER_PROGRESS_STATUS.END_OPENED,
        ],
        targetNode: PROCESS_NODES.EVALUATION,
        description: '竞谈项目采购方在投标阶段可以访问评标节点（兼容公开和邀请模式）'
      },
      // 可以在这里添加更多默认规则
    ];

    setAccessRules(defaultRules);
  }

}

// 导出便捷方法
export const {
  enableDefaultRules,
} = NodeAccessManager;
