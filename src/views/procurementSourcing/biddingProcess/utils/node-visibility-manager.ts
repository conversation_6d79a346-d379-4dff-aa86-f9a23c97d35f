import { setVisibilityRules, type NodeVisibilityRule } from './node-access-control';
import { PROJECT_TYPES } from '../constants/project-types';
import { PROCESS_NODES } from '../constants/process-status';

/**
 * 默认节点可见性规则
 */
const DEFAULT_VISIBILITY_RULES: NodeVisibilityRule[] = [
  {
    // 供应角色在竞谈项目且线下开标时，开标节点不显示
    projectTypes: [PROJECT_TYPES.NEGOTIATION, PROJECT_TYPES.BIDDING], // JZTP
    userRoles: ['supplier'],
    tenderWays: ['OFFLINE'],
    targetNode: PROCESS_NODES.OPENING,
    visible: false,
    description: '供应角色在竞谈项目且线下开标时，开标节点不显示'
  },
  // 可以在这里添加更多的默认规则
  // 例如：
  // {
  //   // 某些项目类型下某些角色不显示特定节点
  //   projectTypes: [PROJECT_TYPES.INQUIRY],
  //   userRoles: ['supplier'],
  //   targetNode: PROCESS_NODES.EVALUATION,
  //   visible: false,
  //   description: '询价采购中供应商不显示评标节点'
  // }
];

/**
 * 启用默认可见性规则
 */
export function enableDefaultVisibilityRules(): void {
  setVisibilityRules(DEFAULT_VISIBILITY_RULES);
}

// 自动启用默认规则
enableDefaultVisibilityRules();
