import { PROJECT_TYPE_CONFIGS, PROCESS_NODE_KEYS } from '../constants/project-types';
import type { UserRole, TenderType } from '../hooks/useBiddingProcessFlow';
import { useBiddingStore } from '@/views/procurementSourcing/biddingProcess/stores';

/**
 * 获取项目类型配置
 */
export function getProjectTypeConfig(projectType: string) {
  return PROJECT_TYPE_CONFIGS[projectType] || null;
}

/**
 * 获取指定项目类型、角色和招标方式下的流程节点列表
 */
export function getProcessNodeKeys(
  projectType: string,
  userRole: UserRole,
  tenderType: TenderType
): string[] {
  const config = getProjectTypeConfig(projectType);
  if (!config) {
    return [];
  }

  const biddingStore = useBiddingStore();

  if (userRole === 'purchaser') { // 采购方
    return config.purchaserNodes[tenderType] || [];
  } else if (userRole === 'supplier') { // 供应方
    return config.supplierNodes[tenderType] || [];
  } else if (userRole === 'bidAgency') { // 招标代理--保持采购节点
    return config.purchaserNodes[tenderType] || [];
  } else if (userRole === 'bidExpert') { // 评标小组成员(负责人+成员)--保持采购节点
    if (biddingStore.isEvaluationMember || biddingStore.isEvaluationCommitteeMember) {
      return config?.evaluationNodes[tenderType] || [];
    }
    return config.purchaserNodes[tenderType] || [];
  }

  // if (userRole === 'purchaser') {
  //   return config.purchaserNodes[tenderType] || [];
  // } else if (userRole === 'supplier') {
  //   return config.supplierNodes[tenderType] || [];
  // }

  return [];
}

/**
 * 判断项目类型是否包含指定节点
 */
export function hasProcessNode(
  projectType: string,
  nodeKey: string,
  userRole: UserRole,
  tenderType: TenderType
): boolean {
  const nodeKeys = getProcessNodeKeys(projectType, userRole, tenderType);
  return nodeKeys.includes(nodeKey);
}
