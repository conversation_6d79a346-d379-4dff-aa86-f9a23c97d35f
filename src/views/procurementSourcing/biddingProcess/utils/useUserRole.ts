import { ref, computed } from 'vue';
import { useUserInfo } from '@/stores/userInfo';
import { storeToRefs } from 'pinia';
import { Session } from '@/utils/storage';

// 身份常量定义
export const IDENTITY_CODES = {
  PURCHASER_IDENTITY: 'PURCHASER_IDENTITY',
  SUPPLIER_IDENTITY: 'SUPPLIER_IDENTITY',
  BID_EXPERT_IDENTITY: 'BID_EXPERT_IDENTITY',
  BID_AGENCY_IDENTITY: 'BID_AGENCY_IDENTITY',
} as const;

// 用户角色类型
export interface UserRoleInfo {
  userId: string;
  userName: string;
  role: 'purchaser' | 'supplier' | 'bidExpert' | 'bidAgency' | 'guest';
  currentIdentity?: {
    id: string | number;
    identityCode: string;
    identityName: string;
  };
}

// hook: 获取用户身份及是否采购方
export function useUserRole() {
  const userInfoStore = useUserInfo();
  const { userInfos } = storeToRefs(userInfoStore);

  // 响应式计算用户信息
  const userInfo = computed<UserRoleInfo>(() => {
    const user = userInfos.value.user || {};
    const userId = user.userId;
    const userName = user.username;

    // 从 Session 获取当前选择的身份ID
    const currentIdentityId = Session.getIdentityId();

    // 默认为采购方
    let role: 'purchaser' | 'supplier' | 'bidExpert' | 'bidAgency' | 'guest' = 'purchaser';
    let currentIdentity = undefined;

    // 如果用户有身份列表且身份ID存在，则根据身份ID查找当前身份
    if (user.sysIdentityList && Array.isArray(user.sysIdentityList) && currentIdentityId) {
      // 查找当前选择的身份
      const foundIdentity = user.sysIdentityList.find((identity: any) =>
        identity.id === currentIdentityId
      );

      if (foundIdentity) {
        currentIdentity = {
          id: foundIdentity.id,
          identityCode: foundIdentity.identityCode,
          identityName: foundIdentity.identityName
        };

        // 根据身份代码确定角色
        switch (foundIdentity.identityCode) {
          case IDENTITY_CODES.SUPPLIER_IDENTITY:
            role = 'supplier';
            break;
          case IDENTITY_CODES.BID_EXPERT_IDENTITY:
            role = 'bidExpert';
            break;
          case IDENTITY_CODES.BID_AGENCY_IDENTITY:
            role = 'bidAgency';
            break;
          case IDENTITY_CODES.PURCHASER_IDENTITY:
          default:
            role = 'purchaser';
            break;
        }
      }
    } else if (user.sysIdentityList && Array.isArray(user.sysIdentityList)) {
      // 兼容旧逻辑：如果没有从Session获取到身份ID，则检查是否有供应方身份
      const hasSupplierIdentity = user.sysIdentityList.some((identity: any) =>
        identity.identityName === '供应方' || identity.identityCode === IDENTITY_CODES.SUPPLIER_IDENTITY
      );

      // if (hasSupplierIdentity) {
      //   role = 'supplier';
      // }
    }

    return {
      userId,
      userName,
      role,
      currentIdentity
    };
  });

  // 是否采购方
  const isPurchaser = computed(() => userInfo.value.role === 'purchaser');
  // 是否供应商
  const isSupplier = computed(() => userInfo.value.role === 'supplier');
  // 是否评标专家
  const isBidExpert = computed(() => userInfo.value.role === 'bidExpert');
  // 是否招标代理机构
  const isBidAgency = computed(() => userInfo.value.role === 'bidAgency');

  return {
    userInfo,
    isPurchaser,
    isSupplier,
    isBidExpert,
    isBidAgency,
  };
}
