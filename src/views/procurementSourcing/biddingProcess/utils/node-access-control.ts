import { PROCESS_NODES, PURCHASER_PROGRESS_STATUS, SUPPLIER_PROGRESS_STATUS } from '../constants/process-status';
import { PROJECT_TYPES } from '../constants/project-types';
import { hasProcessNode } from './project-type-helper';
import type { UserRole, TenderType } from '../hooks/useBiddingProcessFlow';

/**
 * 节点访问规则接口
 */
export interface NodeAccessRule {
  // 项目类型（可选，不指定则适用于所有项目类型）
  projectTypes?: string[];
  // 用户角色（可选，不指定则适用于所有角色）
  userRoles?: UserRole[];
  // 招标类型（可选，不指定则适用于所有招标类型）
  tenderTypes?: TenderType[];
  // 允许访问的状态列表
  allowedStatuses: string[];
  // 目标节点
  targetNode: string;
  // 规则描述
  description?: string;
}

/**
 * 节点可见性规则接口
 */
export interface NodeVisibilityRule {
  // 项目类型（可选，不指定则适用于所有项目类型）
  projectTypes?: string[];
  // 用户角色（可选，不指定则适用于所有角色）
  userRoles?: UserRole[];
  // 招标类型（可选，不指定则适用于所有招标类型）
  tenderTypes?: TenderType[];
  // 开标方式（可选，不指定则适用于所有开标方式）
  tenderWays?: ('ONLINE' | 'OFFLINE')[];
  // 目标节点
  targetNode: string;
  // 是否可见
  visible: boolean;
  // 规则描述
  description?: string;
}

/**
 * 特殊访问规则配置
 * 这些规则定义了在特定条件下可以访问尚未完成的节点
 */
export const SPECIAL_ACCESS_RULES: NodeAccessRule[] = [];

/**
 * 节点可见性规则配置
 * 这些规则定义了在特定条件下节点是否显示
 */
export const NODE_VISIBILITY_RULES: NodeVisibilityRule[] = [
  {
    // 供应角色在竞谈项目且线下开标时，开标节点不显示
    projectTypes: [PROJECT_TYPES.NEGOTIATION, PROJECT_TYPES.BIDDING], // JZTP
    userRoles: ['supplier'],
    tenderWays: ['OFFLINE'],
    targetNode: PROCESS_NODES.OPENING,
    visible: false,
    description: '供应角色在竞谈项目且线下开标时，开标节点不显示'
  }
];

/**
 * 检查节点是否有特殊访问权限
 * @param nodeKey 节点key
 * @param projectType 项目类型
 * @param userRole 用户角色
 * @param tenderType 招标类型
 * @param progressStatus 项目进度状态
 * @returns 是否有特殊访问权限
 */
export function hasSpecialAccess(
  nodeKey: string,
  projectType: string,
  userRole: UserRole,
  tenderType: TenderType,
  progressStatus: string
): boolean {
  // 遍历所有特殊访问规则
  for (const rule of SPECIAL_ACCESS_RULES) {
    // 检查目标节点是否匹配
    if (rule.targetNode !== nodeKey) {
      continue;
    }

    // 检查项目类型是否匹配（如果规则指定了项目类型）
    if (rule.projectTypes && !rule.projectTypes.includes(projectType)) {
      continue;
    }

    // 检查用户角色是否匹配（如果规则指定了用户角色）
    if (rule.userRoles && !rule.userRoles.includes(userRole)) {
      continue;
    }

    // 检查招标类型是否匹配（如果规则指定了招标类型）
    if (rule.tenderTypes && !rule.tenderTypes.includes(tenderType)) {
      continue;
    }

    // 检查当前状态是否在允许的状态列表中
    if (rule.allowedStatuses.includes(progressStatus)) {
      return true;
    }
  }

  return false;
}

/**
 * 检查节点是否可访问（综合判断）
 * @param nodeIndex 节点索引
 * @param nodeKey 节点key
 * @param currentNodeIndex 当前选中节点索引
 * @param actualCurrentNodeIndex 实际当前节点索引
 * @param isNodeCompleted 节点是否已完成
 * @param projectType 项目类型
 * @param userRole 用户角色
 * @param tenderType 招标类型
 * @param progressStatus 项目进度状态
 * @returns 是否可访问
 */
export function isNodeAccessible(
  nodeIndex: number,
  nodeKey: string,
  currentNodeIndex: number,
  actualCurrentNodeIndex: number,
  isNodeCompleted: boolean,
  projectType: string,
  userRole: UserRole,
  tenderType: TenderType,
  progressStatus: string
): boolean {
  // 当前选中的节点总是可访问
  if (currentNodeIndex === nodeIndex) {
    return true;
  }

  // 实际的当前进行节点总是可访问
  if (actualCurrentNodeIndex === nodeIndex) {
    return true;
  }

  // 已完成的节点可访问
  if (isNodeCompleted) {
    return true;
  }

  // 检查是否有特殊访问权限
  if (hasSpecialAccess(nodeKey, projectType, userRole, tenderType, progressStatus)) {
    return true;
  }

  // 默认不可访问
  return false;
}

/**
 * 检查节点是否可见
 * @param nodeKey 节点key
 * @param projectType 项目类型
 * @param userRole 用户角色
 * @param tenderType 招标类型
 * @param tenderWay 开标方式
 * @returns 是否可见
 */
export function isNodeVisible(
  nodeKey: string,
  projectType: string,
  userRole: UserRole,
  tenderType: TenderType,
  tenderWay?: 'ONLINE' | 'OFFLINE'
): boolean {
  // 遍历所有可见性规则
  for (const rule of NODE_VISIBILITY_RULES) {
    // 检查目标节点是否匹配
    if (rule.targetNode !== nodeKey) {
      continue;
    }

    // 检查项目类型是否匹配（如果规则指定了项目类型）
    if (rule.projectTypes && !rule.projectTypes.includes(projectType)) {
      continue;
    }

    // 检查用户角色是否匹配（如果规则指定了用户角色）
    if (rule.userRoles && !rule.userRoles.includes(userRole)) {
      continue;
    }

    // 检查招标类型是否匹配（如果规则指定了招标类型）
    if (rule.tenderTypes && !rule.tenderTypes.includes(tenderType)) {
      continue;
    }

    // 检查开标方式是否匹配（如果规则指定了开标方式）
    if (rule.tenderWays && tenderWay && !rule.tenderWays.includes(tenderWay)) {
      continue;
    }

    // 如果所有条件都匹配，返回规则定义的可见性
    return rule.visible;
  }

  // 如果没有匹配的规则，默认可见
  return true;
}

/**
 * 批量设置节点访问规则
 * @param rules 访问规则数组
 */
export function setAccessRules(rules: NodeAccessRule[]): void {
  // 清空现有规则
  SPECIAL_ACCESS_RULES.length = 0;
  // 添加新规则
  SPECIAL_ACCESS_RULES.push(...rules);
}

/**
 * 批量设置节点可见性规则
 * @param rules 可见性规则数组
 */
export function setVisibilityRules(rules: NodeVisibilityRule[]): void {
  // 清空现有规则
  NODE_VISIBILITY_RULES.length = 0;
  // 添加新规则
  NODE_VISIBILITY_RULES.push(...rules);
}
