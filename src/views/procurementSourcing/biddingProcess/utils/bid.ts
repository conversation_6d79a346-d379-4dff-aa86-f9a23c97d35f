import { useUserInfo } from '/@/stores/userInfo';
import { storeToRefs } from 'pinia';

const stores = useUserInfo();
const { userInfos } = storeToRefs(stores);

/**
 * 将数字转换为中文 比如: 1 -> 一 11 -> 十一 111 -> 一百一十一
 * 不需要零
 */
export function numberToChinese(num: number): string {
  const chinese = ['零', '一', '二', '三', '四', '五', '六', '七', '八', '九'];
  const units = ['', '十', '百', '千', '万', '亿'];
  if (num === 0) return chinese[0];

  // 特殊处理 10-19 的情况
  if (num >= 10 && num < 20) {
    if (num === 10) return '十';
    return '十' + chinese[num % 10];
  }

  let result = '';
  let i = 0;
  while (num > 0) {
    const digit = num % 10;
    if (digit !== 0) {
      result = chinese[digit] + units[i] + result;
    }
    num = Math.floor(num / 10);
    i++;
  }
  return result;
}

/**
 * 将json字符串转换为对象
 */
export function jsonStringToObject(jsonString: string) {
  try {
    return JSON.parse(jsonString || '{}');
  } catch (error) {
    return {};
  }
}

/**
 * 将json字符串转换为数组
 */
export function jsonStringToArray(jsonString: string) {
  try {
    return JSON.parse(jsonString || '[]');
  } catch (error) {
    return [];
  }
}


// 判断是项目成员 & 项目负责人
export function isProjectMember(projectMember: any[]) {
  const loginUserId = userInfos.value?.user?.userId;
  const isProjectMember = projectMember?.some((item) => item.userId === loginUserId && item.memberType === 'PROJECT_MEMBER');
  const isProjectLeader = projectMember?.some((item) => item.userId === loginUserId && item.role === 'PROJECT_LEADER');
  if (isProjectLeader) {
    return 'PROJECT_LEADER';
  }

  if (isProjectMember) {
    return 'PROJECT_MEMBER';
  }

  return undefined;
}

// 判断是评标成员 & 评标组长
export function isEvaluationMember(projectMember: any[]) {
  const loginUserId = userInfos.value?.user?.userId;
  const isEvauationMember = projectMember?.some((item) => item.userId === loginUserId && item.memberType === 'EVALUATION_MEMBER');
  const isEvauationLeader = projectMember?.some((item) => item.userId === loginUserId && item.role === 'EVALUATION_LEADER');

  if (isEvauationMember) {
    return 'EVALUATION_MEMBER';
  }

  if (isEvauationLeader) {
    return 'EVALUATION_LEADER';
  }

  return undefined;
}


/**
 * 将utf8字符串转换为base64字符串
 * @param str
 * @returns
 */
export function utf8ToBase64(str: string): string {
  return window.btoa(unescape(encodeURIComponent(str)));
}


/**
 * 将base64字符串转换为utf8字符串
 * @param str
 * @returns
 */
export function base64ToUtf8(str: string): string {
  return decodeURIComponent(escape(window.atob(str)));
}


// 对象key:value转换成数组对象
export function objectToOptions(obj: any) {
  return Object.keys(obj).map((key) => ({
    label: obj[key],
    value: key,
  }));
}


// 判断值是否是空 null "" undefined "null" "undefined"
export function isEmptyValue(value: any) {
  return value === null || value === '' || value === undefined || value === 'null' || value === 'undefined';
}

/**
 * 获取url参数
 * @param name 参数名
 * @param url 可选，默认使用当前页面url
 * @returns 参数值
 */
export function getParameterByName(name: string, url: string = window.location.href) {
  const regex = new RegExp(`[?&]${name}(=([^&#]*)|&|#|$)`);
  const results = regex.exec(url);
  if (!results) return null;
  if (!results[2]) return '';
  return decodeURIComponent(results[2].replace(/\+/g, ' '));
}