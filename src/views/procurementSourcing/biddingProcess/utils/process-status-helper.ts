/**
 * 流程状态判断工具函数
 */

import {
  PROCESS_NODES,
  PURCHASER_PROGRESS_STATUS,
  SUPPLIER_PROGRESS_STATUS
} from '../constants/process-status'
import type { UserRole } from '../hooks/useBiddingProcessFlow'
import { hasProcessNode } from './project-type-helper'
import { useBiddingStore } from '@/views/procurementSourcing/biddingProcess/stores'
import { PROJECT_TYPES } from '../constants/project-types'

/**
 * 根据用户角色、项目进度状态和项目类型判断流程节点是否已完成
 * @param userRole 用户角色
 * @param progressStatus 项目进度状态
 * @param nodeKey 流程节点key
 * @param projectType 项目类型
 * @param tenderType 招标类型
 * @returns 是否已完成
 */
export function isNodeCompleted(
  userRole: UserRole,
  progressStatus: string,
  nodeKey: string,
  projectType?: string,
  tenderType: 'public' | 'invite' = 'public'
): boolean {
  if (!progressStatus || !nodeKey) {
    return false
  }

  const biddingStore = useBiddingStore();

  if (userRole === 'purchaser') { // 采购方
    return isPurchaserNodeCompleted(progressStatus, nodeKey, projectType, tenderType)
  } else if (userRole === 'supplier') { // 供应方
    return isSupplierNodeCompleted(progressStatus, nodeKey, projectType, tenderType)
  } else if (userRole === 'bidAgency') { // 招标代理--保持采购节点
    return isPurchaserNodeCompleted(progressStatus, nodeKey, projectType, tenderType)
  } else if (userRole === 'bidExpert') { // 评标小组成员(负责人+成员)--保持采购节点
    if (biddingStore.isEvaluationMember || biddingStore.isEvaluationCommitteeMember) {
      return isPurchaserNodeEvaluationCompleted(nodeKey);
    }
    return isPurchaserNodeCompleted(progressStatus, nodeKey, projectType, tenderType)
  }

  return false
}

/**
 * 评标小组成员(负责人+成员)
 * 判断采购方流程节点是否已完成
 */
function isPurchaserNodeEvaluationCompleted(
  nodeKey: string,
): boolean {
  // 检查该项目类型是否包含评标节点
  switch (nodeKey) {
    case PROCESS_NODES.PROCUREMENT:
      return true
    case PROCESS_NODES.ANNOUNCEMENT:
      return true
    case PROCESS_NODES.BIDDING:
      return true
    case PROCESS_NODES.OPENING:
      return true
    default:
      return false
  }
}

/**
 * 判断采购方流程节点是否已完成
 */
function isPurchaserNodeCompleted(
  progressStatus: string,
  nodeKey: string,
  projectType?: string,
  tenderType: 'public' | 'invite' = 'public'
): boolean {
  // 检查该项目类型是否包含评标节点
  const hasEvaluationNode = projectType ? hasProcessNode(projectType, PROCESS_NODES.EVALUATION, 'purchaser', tenderType) : false;
  switch (nodeKey) {
    case PROCESS_NODES.PROCUREMENT:
      // 采购需求：TO_NOTICE状态表示采购需求完成，当前为发标
      return [
        PURCHASER_PROGRESS_STATUS.TO_NOTICE,
        PURCHASER_PROGRESS_STATUS.NOTICE,
        PURCHASER_PROGRESS_STATUS.TENDER_DOC,
        PURCHASER_PROGRESS_STATUS.REGISTER,
        PURCHASER_PROGRESS_STATUS.QUOTING,
        PURCHASER_PROGRESS_STATUS.TO_BID_OPEN,
        PURCHASER_PROGRESS_STATUS.BID_OPENED,
        PURCHASER_PROGRESS_STATUS.END_OPENED,
        PURCHASER_PROGRESS_STATUS.EVALUATING,
        PURCHASER_PROGRESS_STATUS.EVALUATED,
        PURCHASER_PROGRESS_STATUS.AWARDED,
        PURCHASER_PROGRESS_STATUS.BID_WON_PUBLICITY,
        PURCHASER_PROGRESS_STATUS.BID_WON_NOTICE,
        PURCHASER_PROGRESS_STATUS.COMPLETED,
        PURCHASER_PROGRESS_STATUS.FAILED
      ].includes(progressStatus as any)

    case PROCESS_NODES.ANNOUNCEMENT:
      // 发标：根据项目类型判断完成条件
      // 竞谈或招标项目：需要TENDER_DOC状态才算完成
      // 其他项目类型：NOTICE状态即可算完成
      if (projectType === PROJECT_TYPES.NEGOTIATION || projectType === PROJECT_TYPES.BIDDING) {
        // 竞谈或招标项目：状态为TENDER_DOC才算完成
        return [
          PURCHASER_PROGRESS_STATUS.TENDER_DOC,
          PURCHASER_PROGRESS_STATUS.REGISTER,
          PURCHASER_PROGRESS_STATUS.QUOTING,
          PURCHASER_PROGRESS_STATUS.TO_BID_OPEN,
          PURCHASER_PROGRESS_STATUS.BID_OPENED,
          PURCHASER_PROGRESS_STATUS.END_OPENED,
          PURCHASER_PROGRESS_STATUS.EVALUATING,
          PURCHASER_PROGRESS_STATUS.EVALUATED,
          PURCHASER_PROGRESS_STATUS.AWARDED,
          PURCHASER_PROGRESS_STATUS.BID_WON_PUBLICITY,
          PURCHASER_PROGRESS_STATUS.BID_WON_NOTICE,
          PURCHASER_PROGRESS_STATUS.COMPLETED,
          PURCHASER_PROGRESS_STATUS.FAILED
        ].includes(progressStatus as any)
      } else {
        // 其他项目类型：NOTICE状态即可算完成
        return [
          PURCHASER_PROGRESS_STATUS.NOTICE,
          PURCHASER_PROGRESS_STATUS.TENDER_DOC,
          PURCHASER_PROGRESS_STATUS.REGISTER,
          PURCHASER_PROGRESS_STATUS.QUOTING,
          PURCHASER_PROGRESS_STATUS.TO_BID_OPEN,
          PURCHASER_PROGRESS_STATUS.BID_OPENED,
          PURCHASER_PROGRESS_STATUS.END_OPENED,
          PURCHASER_PROGRESS_STATUS.EVALUATING,
          PURCHASER_PROGRESS_STATUS.EVALUATED,
          PURCHASER_PROGRESS_STATUS.AWARDED,
          PURCHASER_PROGRESS_STATUS.BID_WON_PUBLICITY,
          PURCHASER_PROGRESS_STATUS.BID_WON_NOTICE,
          PURCHASER_PROGRESS_STATUS.COMPLETED,
          PURCHASER_PROGRESS_STATUS.FAILED
        ].includes(progressStatus as any)
      }

    case PROCESS_NODES.BIDDING:
      // 投标：当状态为待开标及以后状态时，说明投标已完成
      return [
        PURCHASER_PROGRESS_STATUS.TO_BID_OPEN,
        PURCHASER_PROGRESS_STATUS.BID_OPENED,
        PURCHASER_PROGRESS_STATUS.END_OPENED,
        PURCHASER_PROGRESS_STATUS.EVALUATING,
        PURCHASER_PROGRESS_STATUS.EVALUATED,
        PURCHASER_PROGRESS_STATUS.AWARDED,
        PURCHASER_PROGRESS_STATUS.BID_WON_PUBLICITY,
        PURCHASER_PROGRESS_STATUS.BID_WON_NOTICE,
        PURCHASER_PROGRESS_STATUS.COMPLETED,
        PURCHASER_PROGRESS_STATUS.FAILED
      ].includes(progressStatus as any)

    case PROCESS_NODES.OPENING:
      // 开标：当状态为开标结束及以后状态时，说明开标已完成
      return [
        PURCHASER_PROGRESS_STATUS.END_OPENED,
        PURCHASER_PROGRESS_STATUS.EVALUATING,
        PURCHASER_PROGRESS_STATUS.EVALUATED,
        PURCHASER_PROGRESS_STATUS.AWARDED,
        PURCHASER_PROGRESS_STATUS.BID_WON_PUBLICITY,
        PURCHASER_PROGRESS_STATUS.BID_WON_NOTICE,
        PURCHASER_PROGRESS_STATUS.COMPLETED,
        PURCHASER_PROGRESS_STATUS.FAILED
      ].includes(progressStatus as any)

    case PROCESS_NODES.EVALUATION:
      // 评标节点：只有包含评标节点的项目类型才有此节点
      if (!hasEvaluationNode) return false;
      return [
        PURCHASER_PROGRESS_STATUS.EVALUATED,
        PURCHASER_PROGRESS_STATUS.AWARDED,
        PURCHASER_PROGRESS_STATUS.BID_WON_PUBLICITY,
        PURCHASER_PROGRESS_STATUS.BID_WON_NOTICE,
        PURCHASER_PROGRESS_STATUS.COMPLETED,
        PURCHASER_PROGRESS_STATUS.FAILED
      ].includes(progressStatus as any)

    case PROCESS_NODES.AWARD:
      // 定标完成逻辑：根据是否有评标节点调整判断条件
      if (hasEvaluationNode) {
        // 有评标的情况下，需要评标完成后才能定标
        return [
          PURCHASER_PROGRESS_STATUS.COMPLETED,
          PURCHASER_PROGRESS_STATUS.FAILED
        ].includes(progressStatus as any)
      } else {
        // 无评标的情况下，开标完成后就可以定标
        return [
          PURCHASER_PROGRESS_STATUS.COMPLETED,
          PURCHASER_PROGRESS_STATUS.FAILED
        ].includes(progressStatus as any)
      }
    default:
      return false
  }
}

/**
 * 判断供应商流程节点是否已完成
 */
function isSupplierNodeCompleted(
  progressStatus: string,
  nodeKey: string,
  projectType?: string,
  tenderType: 'public' | 'invite' = 'public'
): boolean {
  switch (nodeKey) {
    case PROCESS_NODES.ANNOUNCEMENT:
      // 采购公告：当状态为竞谈文件已完成及以后状态时，说明采购公告已完成
      return [
        SUPPLIER_PROGRESS_STATUS.NOTICE,
        SUPPLIER_PROGRESS_STATUS.TENDER_DOC,
        SUPPLIER_PROGRESS_STATUS.REGISTER,
        SUPPLIER_PROGRESS_STATUS.QUOTING,
        SUPPLIER_PROGRESS_STATUS.TO_BID_OPEN,
        SUPPLIER_PROGRESS_STATUS.BID_OPENED,
        SUPPLIER_PROGRESS_STATUS.EVALUATING,
        SUPPLIER_PROGRESS_STATUS.END_OPENED,
        SUPPLIER_PROGRESS_STATUS.EVALUATED,
        SUPPLIER_PROGRESS_STATUS.AWARDED,
        SUPPLIER_PROGRESS_STATUS.BID_WON_PUBLICITY,
        SUPPLIER_PROGRESS_STATUS.BID_WON_NOTICE,
        SUPPLIER_PROGRESS_STATUS.COMPLETED,
        SUPPLIER_PROGRESS_STATUS.FAILED
      ].includes(progressStatus as any)

    case PROCESS_NODES.BIDDING:
      // 投标：当状态为待开标及以后状态时，说明投标已完成
      return [
        SUPPLIER_PROGRESS_STATUS.TO_BID_OPEN,
        SUPPLIER_PROGRESS_STATUS.BID_OPENED,
        SUPPLIER_PROGRESS_STATUS.END_OPENED,
        SUPPLIER_PROGRESS_STATUS.EVALUATING,
        SUPPLIER_PROGRESS_STATUS.EVALUATED,
        SUPPLIER_PROGRESS_STATUS.AWARDED,
        SUPPLIER_PROGRESS_STATUS.BID_WON_PUBLICITY,
        SUPPLIER_PROGRESS_STATUS.BID_WON_NOTICE,
        SUPPLIER_PROGRESS_STATUS.COMPLETED,
        SUPPLIER_PROGRESS_STATUS.FAILED
      ].includes(progressStatus as any)

    case PROCESS_NODES.OPENING:
      // 开标：当状态为开标结束及以后状态时，说明开标已完成
      return [
        SUPPLIER_PROGRESS_STATUS.END_OPENED,
        SUPPLIER_PROGRESS_STATUS.EVALUATING,
        SUPPLIER_PROGRESS_STATUS.EVALUATED,
        SUPPLIER_PROGRESS_STATUS.AWARDED,
        SUPPLIER_PROGRESS_STATUS.BID_WON_PUBLICITY,
        SUPPLIER_PROGRESS_STATUS.BID_WON_NOTICE,
        SUPPLIER_PROGRESS_STATUS.COMPLETED,
        SUPPLIER_PROGRESS_STATUS.FAILED
      ].includes(progressStatus as any)

    case PROCESS_NODES.AWARD:
      // 无评标的情况下，开标完成后就可以定标
      return [
        SUPPLIER_PROGRESS_STATUS.COMPLETED,
        SUPPLIER_PROGRESS_STATUS.FAILED
      ].includes(progressStatus as any)
    default:
      return false
  }
}

/**
 * 获取当前活跃的流程节点索引
 * @param userRole 用户角色
 * @param progressStatus 项目进度状态
 * @param processNodes 流程节点列表
 * @param projectType 项目类型
 * @param tenderType 招标类型
 * @returns 当前活跃节点索引
 */
export function getCurrentActiveNodeIndex(
  userRole: UserRole,
  progressStatus: string,
  processNodes: any[],
  projectType?: string,
  tenderType: 'public' | 'invite' = 'public'
): number {
  if (!progressStatus || !processNodes.length) {
    return 0
  }

  // 找到最后一个已完成的节点
  let lastCompletedIndex = -1
  for (let i = 0; i < processNodes.length; i++) {
    if (isNodeCompleted(userRole, progressStatus, processNodes[i].key, projectType, tenderType)) {
      lastCompletedIndex = i
    } else {
      break
    }
  }

  // 如果所有节点都已完成，返回最后一个节点
  if (lastCompletedIndex === processNodes.length - 1) {
    return lastCompletedIndex
  }

  // 否则返回下一个未完成的节点
  return Math.min(lastCompletedIndex + 1, processNodes.length - 1)
}
