/**
 * 时间处理函数
 */

/**
 * 格式化时间
 */
export function formatTime(time: string | Date, format = 'YYYY-MM-DD HH:mm:ss'): string {
  const date = typeof time === 'string' ? new Date(time) : time
  
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  const hours = String(date.getHours()).padStart(2, '0')
  const minutes = String(date.getMinutes()).padStart(2, '0')
  const seconds = String(date.getSeconds()).padStart(2, '0')
  
  return format
    .replace('YYYY', String(year))
    .replace('MM', month)
    .replace('DD', day)
    .replace('HH', hours)
    .replace('mm', minutes)
    .replace('ss', seconds)
}

/**
 * 计算剩余时间
 */
export function getRemainingTime(endTime: string | Date): {
  days: number
  hours: number
  minutes: number
  seconds: number
  total: number
} {
  const end = typeof endTime === 'string' ? new Date(endTime) : endTime
  const now = new Date()
  const total = end.getTime() - now.getTime()
  
  if (total <= 0) {
    return { days: 0, hours: 0, minutes: 0, seconds: 0, total: 0 }
  }
  
  const days = Math.floor(total / (1000 * 60 * 60 * 24))
  const hours = Math.floor((total % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60))
  const minutes = Math.floor((total % (1000 * 60 * 60)) / (1000 * 60))
  const seconds = Math.floor((total % (1000 * 60)) / 1000)
  
  return { days, hours, minutes, seconds, total }
}

/**
 * 检查是否已过期
 */
export function isExpired(endTime: string | Date): boolean {
  const end = typeof endTime === 'string' ? new Date(endTime) : endTime
  return new Date() > end
}

/**
 * 格式化持续时间
 */
export function formatDuration(minutes: number): string {
  if (minutes < 60) {
    return `${minutes}分钟`
  }
  
  const hours = Math.floor(minutes / 60)
  const remainingMinutes = minutes % 60
  
  if (hours < 24) {
    return remainingMinutes > 0 ? `${hours}小时${remainingMinutes}分钟` : `${hours}小时`
  }
  
  const days = Math.floor(hours / 24)
  const remainingHours = hours % 24
  
  let result = `${days}天`
  if (remainingHours > 0) {
    result += `${remainingHours}小时`
  }
  if (remainingMinutes > 0) {
    result += `${remainingMinutes}分钟`
  }
  
  return result
} 