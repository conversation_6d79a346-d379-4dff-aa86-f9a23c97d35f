# 竞标流程样式文件

这个文件夹包含了竞标流程模块的公共样式文件。

## 文件结构

```
styles/
├── index.scss           # 样式入口文件
├── collapse-panel.scss  # 折叠面板样式
└── README.md           # 说明文档
```

## 使用方法

### 1. 引入所有样式
```scss
@import '../../../styles/index.scss';
```

### 2. 引入特定样式
```scss
@import '../../../styles/collapse-panel.scss';
```

## 样式说明

### collapse-panel.scss
包含折叠面板的自定义样式：
- 移除默认边框和圆角
- 自定义标题样式
- 隐藏默认箭头图标
- 自定义图标旋转动画
- 表单内容样式

## 样式类名

### 主要类名
- `.content-section` - 内容区域容器
- `.collapse-title` - 自定义折叠面板标题
- `.collapse-icon` - 自定义折叠图标
- `.form-content` - 表单内容容器

### 使用示例

```vue
<template>
  <div class="content-section">
    <el-collapse class="section-collapse">
      <el-collapse-item>
        <template #title>
          <span class="collapse-title">
            <el-icon class="collapse-icon"><CaretRight /></el-icon>
            标题文字
          </span>
        </template>
        <div class="form-content">
          <!-- 表单内容 -->
        </div>
      </el-collapse-item>
    </el-collapse>
  </div>
</template>

<style lang="scss" scoped>
@import '../../../styles/collapse-panel.scss';
</style>
```

## 注意事项

1. 使用 `:deep()` 来穿透样式作用域
2. 使用 `!important` 来覆盖 Element Plus 的默认样式
3. 确保在模板中使用正确的类名结构 