// 折叠面板公共样式
// Collapse Panel Common Styles

.content-section {
    margin-bottom: 12px;

    :deep(.section-collapse) {
        border: none !important;
        border-radius: 0 !important;

        .el-collapse-item__header {
            background-color: #fff !important;
            height: 32px !important;
            padding: 0 !important;
            line-height: 32px !important;
            color: var(--Color-Text-text-color-primary, #1d2129);
            font-family: 'PingFang SC';
            font-size: 16px;
            font-style: normal;
            font-weight: 600;
            line-height: 24px;
        }

        // 隐藏默认的箭头图标
        .el-collapse-item__arrow {
            display: none;
        }

        .el-collapse-item__wrap {
            border-bottom: 1px solid #EBEEF5 !important;
        }
    }

    :deep(.section-collapse.team-section) {
        .el-collapse-item__wrap {
            border: none !important;
        }
    }

    :deep(.el-collapse-item__content) {
        padding: 0 !important;
    }

    // 自定义标题样式
    .collapse-title {
        display: flex;
        align-items: center;
        gap: 8px;
        padding: 0 !important;
        min-height: auto !important;

        .collapse-icon {
            transition: transform 0.3s ease;
            color: #1d2129;
            font-size: 12px;
        }
    }

    // 当折叠面板展开时，旋转图标
    :deep(.el-collapse-item.is-active) {
        .collapse-title .collapse-icon {
            color: #1d2129;
            transform: rotate(90deg);
        }
    }

    .form-content {
        margin-top: 12px;
    }

    .bid-sections-container {

        // 表格内的控件样式
        :deep(.el-input__wrapper) {
            background-color: transparent !important;
            border: 1px solid transparent !important;
            box-shadow: none !important;
            padding: 4px 8px;

            &:hover,
            &:focus {
                border-color: var(--Color-Primary-color-primary, #0069ff) !important;
                background-color: #fff !important;
            }
        }

        :deep(.el-input__inner) {
            border-radius: var(--Radius-border-radius-small, 2px) !important;
            background: var(--Color-Fill-fill-color-light, #F5F7FA) !important;
            border: none !important;
            color: #1D2129 !important;
            font-size: 14px;

            &::placeholder {
                color: var(--Color-Text-text-color-regular, #4E5969);
                font-family: "PingFang SC";
            }
        }

    }
}

// 可编辑表格样式
.editable-table {
    :deep(.el-table__header) {
        th {
            background: var(--Color-Fill-fill-color-light, #f5f7fa);
            padding: 6px 0;
            border-bottom: 1px solid var(--Color-Border-border-color-light, #EBEEF5);

            .cell {
                color: var(---el-text-color-regular, #505762);
                font-family: 'PingFang SC';
                font-size: 14px;
                font-style: normal;
                font-weight: 600;
            }
        }
    }

    :deep(.el-table__body) {
        tr {
            &:hover {
                background-color: #fff !important;

                td {
                    background-color: #fff !important;
                }
            }

            td {
                border-bottom: 1px solid #EBEEF5;
                padding: 6px 0;
            }
        }
    }

    // 确保 Element Plus 的 hover 类也被覆盖
    :deep(.el-table__row) {
        &:hover {
            background-color: #fff !important;

            td {
                background-color: #fff !important;
            }
        }

        &.hover-row {
            background-color: transparent !important;
        }

        td {
            &.hover-cell {
                background-color: transparent !important;
            }
        }
    }

    // 表格内的控件样式
    .table-select,
    .table-input {
        width: 100%;

        :deep(.el-input__wrapper) {
            background-color: transparent !important;
            border: 1px solid transparent !important;
            box-shadow: none !important;
            padding: 4px 8px;

            &:hover,
            &:focus {
                border-color: var(--Color-Primary-color-primary, #0069ff) !important;
                background-color: #fff !important;
            }
        }

        :deep(.el-input__inner) {
            border-radius: var(--Radius-border-radius-small, 2px) !important;
            background: var(--Color-Fill-fill-color-light, #F5F7FA) !important;
            border: none !important;
            color: #1D2129 !important;
            font-size: 14px;
            height: 24px;

            &::placeholder {
                color: var(--Color-Text-text-color-regular, #4E5969);
                font-family: "PingFang SC";
                font-size: 14px;
            }
        }
    }

    // 操作按钮样式
    .table-actions {
        display: flex;
        gap: 8px;
        align-items: center;

        .action-btn {
            padding: 4px 8px;
            height: 28px;
            font-size: 12px;
            border-radius: 4px;

            &.el-button--primary {
                background: var(--Color-Primary-color-primary, #0069ff);
                border-color: var(--Color-Primary-color-primary, #0069ff);

                &:hover {
                    background: #1677ff;
                    border-color: #1677ff;
                }
            }

            &.el-button--danger {
                background: #ff4d4f;
                border-color: #ff4d4f;

                &:hover {
                    background: #ff7875;
                    border-color: #ff7875;
                }
            }
        }
    }
}


// el-form 表单相关样式
:deep(.el-form .el-form-item) {
    margin-bottom: 20px;

    .el-form-item__label {
        color: var(--Color-Text-text-color-regular, #4E5969);
        font-family: "PingFang SC";
        font-size: 14px;
        font-style: normal;
        font-weight: 400;
        line-height: 32px;
        padding: 0 8px 0 0 !important;
    }

    .el-select {
        width: 100% !important;
    }

    .el-input__inner,
    .el-textarea__inner {
        border-radius: var(--Radius-border-radius-small, 2px) !important;
        background: var(--Color-Fill-fill-color-light, #F5F7FA) !important;
        border: none !important;
        color: #1D2129 !important;
        font-size: 14px;

        &::placeholder {
            color: var(--Color-Text-text-color-regular, #4E5969);
            font-family: "PingFang SC";
            font-size: 14px;
        }
    }

    .el-input__icon {
        color: #1D2129;
        font-size: 12px !important;
    }

}


// el-tag 样式
:deep(.el-tag) {
    &.el-tag--small {
        height: 24px;
    }

    border: none !important;
    text-align: center;
    font-family: "PingFang SC";
    font-size: 12px;
    font-style: normal;
    font-weight: 400;
    padding: 0 10px;
    border-radius: var(--Radius-border-radius-small, 2px);

    &.el-tag--success {
        background: var(--Color-Success-color-success-light-9, #E8FFEA);
        color: var(--Color-Success-color-success, #00B42A);
    }

    &.el-tag--warning {
        color: var(--Color-Error-color-error, #FF3B30);
        background: var(--Color-Error-color-error-light-9, #FFEDE8);
    }

    &.el-tag--info {
        color: var(--Color-Text-text-color-primary, #1D2129);
        background: var(--Color-Info-color-info-light-9, #F4F4F5);
    }
}


// 新建抽屉样式
:deep(.el-drawer) {
    .el-drawer__header {
        border: none;
        padding: 20px 24px 10px 24px;
        margin-bottom: 0;
        color: var(---el-text-color-primary, #1C2026);
        font-family: "PingFang SC";
        font-size: 18px;
        font-style: normal;
        font-weight: 500;
        line-height: 26px;
        display: flex;
        align-items: center;
        justify-content: space-between;
    }

    .el-drawer__body {
        padding: 16px 20px;
    }

    .el-drawer__footer {
        padding: 16px 24px;
        border-top: 1px solid var(--Color-Border-border-color-light, #E4E7ED);
    }
}


// 富文本编辑器样式
.rich-editor-container {
    border: 1px solid var(--Color-Border-border-color-light, #fff);
    border-radius: var(--Radius-border-radius-small, 2px);
    background-color: #F5F7FA;

    :deep(.w-e-toolbar) {
        border-bottom: 1px solid var(--Color-Border-border-color-light, #E4E7ED);
        background-color: var(--Color-Fill-fill-color-light, #F5F7FA);
    }

    :deep(.w-e-text-container) {
        background-color: #F5F7FA;
    }
}

// 文件上传样式
.attachment-upload {
    :deep(.el-upload) {
        display: inline-block;
    }

    :deep(.el-upload__tip) {
        color: var(--Color-Text-text-color-placeholder, #86909C);
        font-size: 12px;
        margin-top: 8px;
        line-height: 1.5;
    }

    :deep(.el-upload-list) {
        margin-top: 12px;
    }

    :deep(.el-upload-list__item) {
        border: 1px solid var(--Color-Border-border-color-light, #E4E7ED);
        border-radius: var(--Radius-border-radius-small, 2px);
        background-color: var(--Color-Fill-fill-color-light, #F5F7FA);
        padding: 8px 12px;
        margin-bottom: 8px;

        .el-upload-list__item-name {
            color: var(--Color-Text-text-color-regular, #4E5969);
            font-size: 14px;
        }
    }
}

// 复选框组样式
:deep(.el-checkbox-group) {
    .el-checkbox {
        margin-right: 24px;
        margin-bottom: 8px;

        .el-checkbox__label {
            color: var(--Color-Text-text-color-regular, #4E5969);
            font-size: 14px;
        }
    }
}

:deep(.custom-drawer) {
    .el-drawer__body {
        padding: 0 24px;
    }
}

.form-section {
    .label-header {
        color: var(--Color-Text-text-color-primary, #1D2129);
        /* bold/medium */
        font-family: "PingFang SC";
        font-size: 16px;
        font-style: normal;
        font-weight: 600;
        line-height: 24px;
        padding: 16px 0 16px 10px;
        position: relative;

        &::before {
            content: '';
            display: block;
            width: 2px;
            height: 14px;
            background-color: #0069FF;
            position: absolute;
            left: 0;
            top: 50%;
            transform: translateY(-50%);
        }
    }

    // :deep( .el-form-item) {
    //     margin-bottom: 0 !important;
    // }
}


.bid-custom-drawer {
    .el-drawer__body {
        padding: 0 24px;
    }
    .form-section {
        border-bottom: 1px solid var(--Color-Border-border-color-light, #ebeef5);
        &:last-child {
            border-bottom: none;
        }
    }
}