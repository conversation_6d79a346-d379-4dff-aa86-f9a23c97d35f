// 竞标流程相关样式入口文件
// Bidding Process Styles Entry

// 折叠面板样式
@import './collapse-panel.scss';


.header-title {
  color: var(--Color-Text-text-color-primary, #1d2129);
  font-family: 'PingFang SC';
  font-size: 16px;
  font-style: normal;
  font-weight: 600;
  line-height: 24px;
  padding-left: 10px;
  position: relative;
  &::before {
    content: '';
    display: inline-block;
    width: 2px;
    height: 14px;
    background: var(--Color-Primary-color-primary, #0069ff);
    margin-right: 8px;
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
  }
}


.bidding-process-search-form {
	margin-bottom: 16px;
	margin-top: 16px;
	width: 100%;
	// display: flex;
	// align-items: center;
	.form-content {
		display: flex;
		justify-content: flex-start;
		width: 100%;
		gap: 20px;
		flex-wrap: wrap;
		.form-item-wrapper {
			// flex: 1;
      width: 26%;
			display: flex;
			align-items: center;
			gap: 8px;
			.form-label {
				white-space: nowrap;
				font-size: 14px;
				width: 110px;
				text-align: right;
			}
			.search-input,
			.search-select {
				width: 100%;
			}
			.el-input__wrapper,
			.el-select .el-input__wrapper {
				background-color: transparent !important;
				border: 1px solid transparent !important;
				box-shadow: none !important;
				padding: 4px 8px;
				&:hover,
				&:focus {
					border-color: var(--Color-Primary-color-primary, #0069ff) !important;
					background-color: #fff !important;
				}
			}
			.el-input__inner {
				border-radius: var(--Radius-border-radius-small, 2px) !important;
				background: var(--Color-Fill-fill-color-light, #F5F7FA) !important;
				border: none !important;
				color: #1D2129 !important;
				font-size: 14px;
				&::placeholder {
					color: var(--Color-Text-text-color-regular, #4E5969);
					font-family: "PingFang SC";
				}
			}
		}
	}
}


.need-hide-table-card .el-card__body {
  padding: 0 !important;
	.dm-filter-root {
		margin: 16px 0 0 0 !important;
	}
	.dm-filter-root .el-form .el-form-item {
		margin-bottom: 16px !important;
	}
}

.is-hide-stage-tabs {
	&.document-review-container {
		padding-top: 0 !important;
	}
	.stage-tabs-container {
		height: 0 !important;
		overflow: hidden !important;
		padding: 0 !important;
		margin: 0 !important;
	}
}