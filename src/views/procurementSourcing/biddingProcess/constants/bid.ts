/**
 * 报价相关常量
 */
import { objectToOptions } from '@/views/procurementSourcing/biddingProcess/utils/bid';


// 查看类型
export const VIEW_TYPE = {
  MATERIAL: 'MATERIAL',
  SUPPLIER: 'SUPPLIER',
} as const;
// 查看类型标签
export const VIEW_TYPE_LABELS = {
  [VIEW_TYPE.MATERIAL]: '按物料查看',
  [VIEW_TYPE.SUPPLIER]: '按供应商查看',
} as const;
// 查看类型选项
export const VIEW_TYPE_OPTIONS = [
  { label: VIEW_TYPE_LABELS[VIEW_TYPE.MATERIAL], value: VIEW_TYPE.MATERIAL },
  { label: VIEW_TYPE_LABELS[VIEW_TYPE.SUPPLIER], value: VIEW_TYPE.SUPPLIER },
];

// 报名审核状态
export const REGISTER_REVIEWED_STATUS = {
  REGISTERED: 'REGISTERED',
  REJECTED: 'REJECTED',
  PASSED: 'PASSED',
  WITHDRAWN: 'WITHDRAWN',
} as const;

export const REGISTER_REVIEWED_STATUS_LABELS = {
  [REGISTER_REVIEWED_STATUS.REGISTERED]: '待审核',
  [REGISTER_REVIEWED_STATUS.REJECTED]: '审核不通过',
  [REGISTER_REVIEWED_STATUS.PASSED]: '审核通过',
  // [REGISTER_REVIEWED_STATUS.WITHDRAWN]: '已撤回',
} as const;

export const REGISTER_REVIEWED_STATUS_OPTIONS = [
  { label: REGISTER_REVIEWED_STATUS_LABELS[REGISTER_REVIEWED_STATUS.REGISTERED], value: REGISTER_REVIEWED_STATUS.REGISTERED },
  { label: REGISTER_REVIEWED_STATUS_LABELS[REGISTER_REVIEWED_STATUS.REJECTED], value: REGISTER_REVIEWED_STATUS.REJECTED },
  { label: REGISTER_REVIEWED_STATUS_LABELS[REGISTER_REVIEWED_STATUS.PASSED], value: REGISTER_REVIEWED_STATUS.PASSED },
  // { label: REGISTER_REVIEWED_STATUS_LABELS[REGISTER_REVIEWED_STATUS.WITHDRAWN], value: REGISTER_REVIEWED_STATUS.WITHDRAWN },
];

export const REGISTER_REVIEWED_STATUS_TAGS = {
  [REGISTER_REVIEWED_STATUS.REGISTERED]: 'warning',
  [REGISTER_REVIEWED_STATUS.REJECTED]: 'danger',
  [REGISTER_REVIEWED_STATUS.PASSED]: 'success',
  [REGISTER_REVIEWED_STATUS.WITHDRAWN]: 'danger',
} as const;

// 保证金审核状态
export const DEPOSIT_REVIEWED_STATUS = {
  UNPAID: 'UNPAID',
  PAID: 'PAID',
  VERIFIED: 'VERIFIED',
  RETURNED: 'RETURNED',
  REJECTED: 'REJECTED',
  WITHDRAWN: 'WITHDRAWN',
} as const;
export const DEPOSIT_REVIEWED_STATUS_LABELS = {
  // [DEPOSIT_REVIEWED_STATUS.UNPAID]: '保证金未缴纳',
  [DEPOSIT_REVIEWED_STATUS.UNPAID]: '待提交',
  [DEPOSIT_REVIEWED_STATUS.PAID]: '待审核',
  [DEPOSIT_REVIEWED_STATUS.VERIFIED]: '审核通过',
  [DEPOSIT_REVIEWED_STATUS.RETURNED]: '已退回',
  [DEPOSIT_REVIEWED_STATUS.REJECTED]: '审核不通过',
  [DEPOSIT_REVIEWED_STATUS.WITHDRAWN]: '已撤回',
} as const;
export const DEPOSIT_REVIEWED_STATUS_TAGS = {
  // [DEPOSIT_REVIEWED_STATUS.UNPAID]: '保证金未缴纳',
  [DEPOSIT_REVIEWED_STATUS.UNPAID]: 'info',
  [DEPOSIT_REVIEWED_STATUS.PAID]: 'warning',
  [DEPOSIT_REVIEWED_STATUS.VERIFIED]: 'success',
  [DEPOSIT_REVIEWED_STATUS.RETURNED]: 'danger',
  [DEPOSIT_REVIEWED_STATUS.REJECTED]: 'danger',
  [DEPOSIT_REVIEWED_STATUS.WITHDRAWN]: 'danger',
} as const;
// export const DEPOSIT_REVIEWED_STATUS_OPTIONS = [
//   { label: DEPOSIT_REVIEWED_STATUS_LABELS[DEPOSIT_REVIEWED_STATUS.UNPAID], value: DEPOSIT_REVIEWED_STATUS.UNPAID },
//   { label: DEPOSIT_REVIEWED_STATUS_LABELS[DEPOSIT_REVIEWED_STATUS.PAID], value: DEPOSIT_REVIEWED_STATUS.PAID },
//   { label: DEPOSIT_REVIEWED_STATUS_LABELS[DEPOSIT_REVIEWED_STATUS.VERIFIED], value: DEPOSIT_REVIEWED_STATUS.VERIFIED },
//   { label: DEPOSIT_REVIEWED_STATUS_LABELS[DEPOSIT_REVIEWED_STATUS.RETURNED], value: DEPOSIT_REVIEWED_STATUS.RETURNED },
//   { label: DEPOSIT_REVIEWED_STATUS_LABELS[DEPOSIT_REVIEWED_STATUS.REJECTED], value: DEPOSIT_REVIEWED_STATUS.REJECTED },
//   { label: DEPOSIT_REVIEWED_STATUS_LABELS[DEPOSIT_REVIEWED_STATUS.WITHDRAWN], value: DEPOSIT_REVIEWED_STATUS.WITHDRAWN },
// ];

// 邀请反馈下拉枚举
export const DEPOSIT_REVIEWED_STATUS_DONE_OPTIONS = [DEPOSIT_REVIEWED_STATUS.PAID, DEPOSIT_REVIEWED_STATUS.VERIFIED, DEPOSIT_REVIEWED_STATUS.RETURNED, DEPOSIT_REVIEWED_STATUS.REJECTED]
export const DEPOSIT_REVIEWED_STATUS_TODO_OPTIONS = [DEPOSIT_REVIEWED_STATUS.WITHDRAWN, DEPOSIT_REVIEWED_STATUS.UNPAID]
export const DEPOSIT_REVIEWED_STATUS_OPTIONS = [
  { label: '已上传', value: 'DONE', enums: DEPOSIT_REVIEWED_STATUS_DONE_OPTIONS },
  { label: '未上传', value: 'TODO', enums: DEPOSIT_REVIEWED_STATUS_TODO_OPTIONS },
] as const;

export const DEPOSIT_REVIEWED_STATUS_AUDIT_ENUMS = {
  [DEPOSIT_REVIEWED_STATUS.PAID]: '待审核',
  [DEPOSIT_REVIEWED_STATUS.REJECTED]: '审核不通过',
  [DEPOSIT_REVIEWED_STATUS.VERIFIED]: '审核通过',
  // [REGISTER_REVIEWED_STATUS.WITHDRAWN]: '已撤回',
} as const;

export const DEPOSIT_REVIEWED_STATUS_AUDIT_OPTIONS = objectToOptions(DEPOSIT_REVIEWED_STATUS_AUDIT_ENUMS);


// 报名类型 首次报价 再次报价
export const BID_TYPE = {
  FIRST_BID: 'FIRST_BID',
  AGAIN_BID: 'AGAIN_BID',
} as const;

export const BID_TYPE_LABELS = {
  [BID_TYPE.FIRST_BID]: '首次报价',
  [BID_TYPE.AGAIN_BID]: '再次报价',
} as const;

// 报名方式  SELF-自主报名、INVITE-邀请报名
export const REGISTER_TYPE = {
  SELF: 'SELF',
  INVITE: 'INVITE',
} as const;

export const REGISTER_TYPE_LABELS = {
  [REGISTER_TYPE.SELF]: '自主报名',
  [REGISTER_TYPE.INVITE]: '邀请报名',
} as const;

// 目前所有的固定字段
export const FILED_ARRAY_OPTION = [
  {
    label: '需求行号',
    value: 'requireNo',
    isReadOnly: true,
    isLock: true,
    isFixed: true,
    isMerge: true,
    isDynamicRenderCell: true,
  },
  {
    label: '物料编码',
    value: 'materialCode',
    isLock: true,
    isFixed: true,
    isMerge: true,
    isDynamicRenderCell: true,
  },
  {
    label: '物料名称',
    value: 'materialName',
    isLock: true,
    isFixed: true,
    isMerge: true,
    isDynamicRenderCell: true,
  },
  {
    label: '规格型号',
    value: 'specModel',
    isLock: true,
    isFixed: true,
    isMerge: true,
    isDynamicRenderCell: true,
  },
  {
    label: '单位',
    value: 'unit',
    isLock: true,
    isFixed: true,
    isMerge: true,
    isDynamicRenderCell: true,
  },
  {
    label: '需求数量',
    value: 'requiredQuantity',
    isLock: true,
    isFixed: true,
    isMerge: true,
    isDynamicRenderCell: true,
  },
  {
    label: '需求牧场',
    value: 'usageLocationId',
    isReadOnly: true,
    isLock: true,
    isFixed: true,
    isMerge: true,
  },
  {
    label: '所在区域',
    value: 'usageLocationRegion',
    isReadOnly: true,
    isLock: true,
    isFixed: true,
    isMerge: true,
    isDynamicRenderCell: true,
  },
  {
    label: '详细地址',
    value: 'usageLocationAddress',
    isReadOnly: true,
    isLock: true,
    isFixed: true,
    isMerge: true,
    isDynamicRenderCell: true,
  },
  {
    label: '质量标准',
    value: 'qualityIndicatorId',
    isReadOnly: true,
    isLock: true,
    isFixed: true,
    isMerge: true,
  },
  {
    label: '可供数量',
    value: 'availableQuantity',
    isReadOnly: true,
  },
  {
    label: '单价',
    value: 'quotePrice',
    isReadOnly: true,
  },
  {
    label: '总价',
    value: 'quoteAmount',
    isReadOnly: true,
    isLock: true,
  },
  {
    label: '报价供应商',
    prop: 'supplierName',
    isSupplier: true,
  },
  // {
  //   label: '出厂价',
  //   value: 'BSTF102580406647',
  // },
  // {
  //   label: '入场价',
  //   value: 'BSTF168471509347',
  // },
  // {
  //   label: '供应商字段1',
  //   value: 'BSTF23551514747',
  // },
  // {
  //   label: '供应商字段2',
  //   value: 'BSTF32366591947',
  // },
];

// 渲染动态表格是一些特殊字段
export const DYNAMIC_RENDER_COLLECT = FILED_ARRAY_OPTION.filter((item: any) => !!item.isDynamicRenderCell).map((item) => item.value);

// 固定字段
export const FIXED_COLLECT = FILED_ARRAY_OPTION.filter((item: any) => !!item.isFixed).map((item) => item.value);

// 字典数组
export const FILED_ARRAY = FILED_ARRAY_OPTION.filter((item: any) => !item.isSupplier).map((item) => item.value);
// 需要锁定的列
export const LOCKED_COLUMNS = FILED_ARRAY_OPTION.filter((item: any) => item.isLock).map((item) => item.value);
// 导出动态字段中的特殊字段
export const SPECIAL_FILEDS = FILED_ARRAY_OPTION.filter((item: any) => item.isReadOnly).map((item) => item.value);
// 需要合并的字段
export const MERGE_COLUMNS = FILED_ARRAY_OPTION.filter((item: any) => item.isMerge).map((item) => item.value);
// 计算小计
// function calculateSubtotal(row: any) {
//   return ((row.availableQuantity || 0) * (row.quotePrice || 0)).toFixed(2);
// }

// 端类型
export const T_TYPE = {
  PURCHASER: 'PURCHASER',
  SUPPLIER: 'SUPPLIER',
} as const;

// 按钮操作类型
export const BTN_O_TYPE = {
  AUDIT: 'AUDIT',
  DETAIL: 'DETAIL',
  AUDIT_CALL: 'AUDIT_CALL',
  DETAIL_CALL: 'DETAIL_CALL',
} as const;

// 导出邀请状态
export const INVITE_STATUS = {
  PENDING: 'PENDING',
  ACCEPTED: 'ACCEPTED',
  WITHDRAWN: 'WITHDRAWN',

  PASSED: 'PASSED',
  REJECTED: 'REJECTED',
} as const;

// 回执审批状态
export const INVITE_STATUS_CALL_STATUS_LABELS = {
  [INVITE_STATUS.PASSED]: '审批通过',
  [INVITE_STATUS.REJECTED]: '审批不通过',
  [INVITE_STATUS.ACCEPTED]: '待审批',
} as const;
export const INVITE_STATUS_CALL_STATUS_TAGS = {
  [INVITE_STATUS.PASSED]: 'success',
  [INVITE_STATUS.REJECTED]: 'danger',
  [INVITE_STATUS.ACCEPTED]: 'warning',
} as const;
// 回执审批状态

// 邀请反馈
export const INVITE_STATUS_FEEDBACK_LABELS = {
  // [INVITE_STATUS.PENDING]: '待反馈',
  // [INVITE_STATUS.WITHDRAWN]: '待反馈',
  // [INVITE_STATUS.ACCEPTED]: '供应商已回执',
  // [INVITE_STATUS.PASSED]: '供应商已回执',
  // [INVITE_STATUS.REJECTED]: '供应商已回执',
  [INVITE_STATUS.PENDING]: '待回执',
  [INVITE_STATUS.WITHDRAWN]: '待回执',
  [INVITE_STATUS.ACCEPTED]: '已回执',
  [INVITE_STATUS.PASSED]: '已回执',
  [INVITE_STATUS.REJECTED]: '已回执',
} as const;
export const INVITE_STATUS_FEEDBACK_TAGS = {
  [INVITE_STATUS.PENDING]: 'warning',
  [INVITE_STATUS.WITHDRAWN]: 'warning',
  [INVITE_STATUS.ACCEPTED]: 'success',
  [INVITE_STATUS.PASSED]: 'success',
  [INVITE_STATUS.REJECTED]: 'success',
} as const;
// 邀请反馈

// 邀请反馈下拉枚举 供应商已回执 待反馈
export const INVITE_STATUS_FEEDBACK_OPTIONS = [
  { label: '已回执', value: 'DONE', enums: [INVITE_STATUS.ACCEPTED, INVITE_STATUS.PASSED, INVITE_STATUS.REJECTED] },
  { label: '待回执', value: 'TODO', enums: [INVITE_STATUS.PENDING, INVITE_STATUS.WITHDRAWN] },
] as const;
// 回执审批状态下拉枚举
export const INVITE_STATUS_CALL_STATUS_OPTIONS = [
  { label: INVITE_STATUS_CALL_STATUS_LABELS[INVITE_STATUS.PASSED], value: INVITE_STATUS.PASSED },
  { label: INVITE_STATUS_CALL_STATUS_LABELS[INVITE_STATUS.REJECTED], value: INVITE_STATUS.REJECTED },
  { label: INVITE_STATUS_CALL_STATUS_LABELS[INVITE_STATUS.ACCEPTED], value: INVITE_STATUS.ACCEPTED },
] as const;

// 供应商列数据
export const SUPPLIER_COLUMN: {
  label: string;
  prop?: string;
  type?: string;
  width?: number;
  ingoreExport?: boolean;
}[] = [
  {
    label: '序号',
    type: 'index',
    width: 60,
    ingoreExport: true,
  },
  {
    label: '供应商名称',
    prop: 'supplierName',
  },
  {
    label: '联系人',
    prop: 'contactPerson',
  },
  {
    label: '报名资料',
    prop: 'lookUp',
    ingoreExport: true,
  },
  {
    label: '报价轮次',
    prop: 'quoteRoundCount',
  },
  {
    label: '报价/物料条数',
    prop: 'count',
  },
  {
    label: '本轮报价总价',
    prop: 'totalQuoteAmount',
  },
  {
    label: '最终报价IP',
    prop: 'quoteIp',
  },
  {
    label: '操作',
    prop: 'action',
    width: 100,
    ingoreExport: true,
  },
] as const;

// 导出字段类型
export const EXPORT_FIELD_TYPE = {
  TEXT: 'TEXT',
  NUM: 'NUM',
  NUM_CALC: 'NUM_CALC',
  ENUM: 'ENUM',
  LINK_FORM: 'LINK_FORM',
  ATTACH: 'ATTACH',
} as const;

// 导出模块类型
export const EXPORT_MODULE_TYPE = {
  // 预审/报名
  REGISTER: 'REGISTER',
  // 保证金
  DEPOSIT: 'DEPOSIT',
  // 报价
  QUOTE: 'QUOTE',
} as const;



// 导出场景类型枚举
export const SCENE_ENUMS = {
  // 直接委托+开标
  ZJWT_KB: 'ZJWT_KB',
  // 直接委托+投标
  ZJWT_TB: 'ZJWT_TB',
  // 询价采购+投标
  XJCG_TB: 'XJCG_TB',

  // 竞争谈判+投标
  JZTP_TB: 'JZTP_TB',
  // 招标+投标
  ZB_TB: 'ZB_TB',

  // 竞争谈判+开标记录
  JZTP_KB: 'JZTP_KBJL',
  // 招标+开标记录
  ZB_KB: 'ZB_KBJL',
  // (竞争谈判+招标)开标记录
  JZTP_ZB_KB: 'JZTP_ZB_KB',
} as const;

// 说明
// 当为 XJCG_TB
// 需要展示报价供应商和报价IP
// 搜索条件只有报价轮/物流名称