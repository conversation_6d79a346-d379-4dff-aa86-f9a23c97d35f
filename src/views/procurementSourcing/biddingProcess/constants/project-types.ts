/**
 * 项目类型常量定义
 */
import { SOURCING_TYPES } from './sourcing-types';

// 项目类型枚举（引用统一的采购类型常量）
export const PROJECT_TYPES = {
  INQUIRY: SOURCING_TYPES.XJCG,        // 询价采购
  NEGOTIATION: SOURCING_TYPES.JZTP,    // 竞争性谈判
  BIDDING: SOURCING_TYPES.ZB,          // 招标采购
  COMPETITIVE: SOURCING_TYPES.JJCG,    // 竞价采购
  DIRECT: SOURCING_TYPES.ZJWT,         // 直接委托
  FAST_INQUIRY: SOURCING_TYPES.KSXJ,   // 快速询价
} as const;

// 项目类型标签
export const PROJECT_TYPE_LABELS = {
  [PROJECT_TYPES.INQUIRY]: '询价采购',
  [PROJECT_TYPES.NEGOTIATION]: '竞争性谈判',
  [PROJECT_TYPES.BIDDING]: '招标采购',
  [PROJECT_TYPES.COMPETITIVE]: '竞价采购',
  [PROJECT_TYPES.DIRECT]: '直接委托',
  [PROJECT_TYPES.FAST_INQUIRY]: '快速询价',
} as const;

// 流程节点枚举（用于配置）
export const PROCESS_NODE_KEYS = {
  PROCUREMENT: 'procurement',
  ANNOUNCEMENT: 'announcement',
  BIDDING: 'bidding',
  OPENING: 'opening',
  EVALUATION: 'evaluation',
  AWARD: 'award',
  CONTRACT: 'contract',
  ARCHIVE: 'archive',
} as const;

// 项目类型配置接口
export interface ProjectTypeConfig {
  type: string;
  label: string;
  description?: string;
  // 使用节点数组来定义该项目类型包含的节点
  purchaserNodes: {
    public: string[];   // 公开招标的节点
    invite: string[];   // 邀请招标的节点
  };
  supplierNodes: {
    public: string[];   // 供应商公开招标的节点
    invite: string[];   // 供应商邀请招标的节点
  };
  evaluationNodes: {
    public: string[];   // 公开招标的节点
    invite: string[];   // 邀请招标的节点
  };
}

// 项目类型配置 - 使用节点数组的方式
export const PROJECT_TYPE_CONFIGS: Record<string, ProjectTypeConfig> = {
  [PROJECT_TYPES.INQUIRY]: {
    type: PROJECT_TYPES.INQUIRY,
    label: PROJECT_TYPE_LABELS[PROJECT_TYPES.INQUIRY],
    description: '通过询价方式进行采购，无需评标环节',
    purchaserNodes: {
      public: [
        PROCESS_NODE_KEYS.PROCUREMENT,
        PROCESS_NODE_KEYS.ANNOUNCEMENT,
        PROCESS_NODE_KEYS.BIDDING,
        PROCESS_NODE_KEYS.OPENING,
        PROCESS_NODE_KEYS.AWARD,
        PROCESS_NODE_KEYS.CONTRACT,
        PROCESS_NODE_KEYS.ARCHIVE
      ],
      invite: [
        PROCESS_NODE_KEYS.PROCUREMENT,
        PROCESS_NODE_KEYS.ANNOUNCEMENT,
        PROCESS_NODE_KEYS.BIDDING,
        PROCESS_NODE_KEYS.OPENING,
        PROCESS_NODE_KEYS.AWARD,
        PROCESS_NODE_KEYS.CONTRACT,
        PROCESS_NODE_KEYS.ARCHIVE
      ]
    },
    supplierNodes: {
      public: [
        PROCESS_NODE_KEYS.ANNOUNCEMENT,
        PROCESS_NODE_KEYS.BIDDING,
        PROCESS_NODE_KEYS.OPENING,
        PROCESS_NODE_KEYS.AWARD
      ],
      invite: [
        PROCESS_NODE_KEYS.ANNOUNCEMENT,
        PROCESS_NODE_KEYS.BIDDING,
        PROCESS_NODE_KEYS.OPENING,
        PROCESS_NODE_KEYS.AWARD
      ]
    },
    evaluationNodes: {
      public: [
        PROCESS_NODE_KEYS.PROCUREMENT,
        PROCESS_NODE_KEYS.ANNOUNCEMENT,
        PROCESS_NODE_KEYS.BIDDING,
        PROCESS_NODE_KEYS.OPENING,
        PROCESS_NODE_KEYS.AWARD,
        PROCESS_NODE_KEYS.CONTRACT,
        PROCESS_NODE_KEYS.ARCHIVE
      ],
      invite: [
        PROCESS_NODE_KEYS.PROCUREMENT,
        PROCESS_NODE_KEYS.ANNOUNCEMENT,
        PROCESS_NODE_KEYS.BIDDING,
        PROCESS_NODE_KEYS.OPENING,
        PROCESS_NODE_KEYS.AWARD,
        PROCESS_NODE_KEYS.CONTRACT,
        PROCESS_NODE_KEYS.ARCHIVE
      ]
    }
  },
  [PROJECT_TYPES.NEGOTIATION]: {
    type: PROJECT_TYPES.NEGOTIATION,
    label: PROJECT_TYPE_LABELS[PROJECT_TYPES.NEGOTIATION],
    description: '通过竞争性谈判方式进行采购，需要评标环节',
    purchaserNodes: {
      public: [
        PROCESS_NODE_KEYS.PROCUREMENT,
        PROCESS_NODE_KEYS.ANNOUNCEMENT,
        PROCESS_NODE_KEYS.BIDDING,
        PROCESS_NODE_KEYS.OPENING,
        PROCESS_NODE_KEYS.EVALUATION,
        PROCESS_NODE_KEYS.AWARD,
        PROCESS_NODE_KEYS.CONTRACT,
        PROCESS_NODE_KEYS.ARCHIVE
      ],
      invite: [
        PROCESS_NODE_KEYS.PROCUREMENT,
        PROCESS_NODE_KEYS.ANNOUNCEMENT,
        PROCESS_NODE_KEYS.BIDDING,
        PROCESS_NODE_KEYS.OPENING,
        PROCESS_NODE_KEYS.EVALUATION,
        PROCESS_NODE_KEYS.AWARD,
        PROCESS_NODE_KEYS.CONTRACT,
        PROCESS_NODE_KEYS.ARCHIVE
      ]
    },
    supplierNodes: {
      public: [
        PROCESS_NODE_KEYS.ANNOUNCEMENT,
        PROCESS_NODE_KEYS.BIDDING,
        PROCESS_NODE_KEYS.OPENING,
        PROCESS_NODE_KEYS.AWARD,
        PROCESS_NODE_KEYS.CONTRACT,
      ],
      invite: [
        PROCESS_NODE_KEYS.ANNOUNCEMENT,
        PROCESS_NODE_KEYS.BIDDING,
        PROCESS_NODE_KEYS.OPENING,
        PROCESS_NODE_KEYS.AWARD,
        PROCESS_NODE_KEYS.CONTRACT,
      ]
    },
    evaluationNodes: {
      public: [
        PROCESS_NODE_KEYS.PROCUREMENT,
        PROCESS_NODE_KEYS.ANNOUNCEMENT,
        PROCESS_NODE_KEYS.BIDDING,
        PROCESS_NODE_KEYS.OPENING,
        PROCESS_NODE_KEYS.EVALUATION,
        PROCESS_NODE_KEYS.AWARD,
        PROCESS_NODE_KEYS.CONTRACT,
        PROCESS_NODE_KEYS.ARCHIVE
      ],
      invite: [
        PROCESS_NODE_KEYS.PROCUREMENT,
        PROCESS_NODE_KEYS.ANNOUNCEMENT,
        PROCESS_NODE_KEYS.BIDDING,
        PROCESS_NODE_KEYS.OPENING,
        PROCESS_NODE_KEYS.EVALUATION,
        PROCESS_NODE_KEYS.AWARD,
        PROCESS_NODE_KEYS.CONTRACT,
        PROCESS_NODE_KEYS.ARCHIVE
      ]
    },
  },
  [PROJECT_TYPES.BIDDING]: {
    type: PROJECT_TYPES.BIDDING,
    label: PROJECT_TYPE_LABELS[PROJECT_TYPES.BIDDING],
    description: '通过招标方式进行采购，需要评标环节',
    purchaserNodes: {
      public: [
        PROCESS_NODE_KEYS.PROCUREMENT,
        PROCESS_NODE_KEYS.ANNOUNCEMENT,
        PROCESS_NODE_KEYS.BIDDING,
        PROCESS_NODE_KEYS.OPENING,
        PROCESS_NODE_KEYS.EVALUATION,
        PROCESS_NODE_KEYS.AWARD,
        PROCESS_NODE_KEYS.CONTRACT,
        PROCESS_NODE_KEYS.ARCHIVE
      ],
      invite: [
        PROCESS_NODE_KEYS.PROCUREMENT,
        PROCESS_NODE_KEYS.ANNOUNCEMENT,
        PROCESS_NODE_KEYS.BIDDING,
        PROCESS_NODE_KEYS.OPENING,
        PROCESS_NODE_KEYS.EVALUATION,
        PROCESS_NODE_KEYS.AWARD,
        PROCESS_NODE_KEYS.CONTRACT,
        PROCESS_NODE_KEYS.ARCHIVE
      ]
    },
    supplierNodes: {
      public: [
        PROCESS_NODE_KEYS.ANNOUNCEMENT,
        PROCESS_NODE_KEYS.BIDDING,
        PROCESS_NODE_KEYS.OPENING,
        PROCESS_NODE_KEYS.AWARD,
        PROCESS_NODE_KEYS.CONTRACT,
      ],
      invite: [
        PROCESS_NODE_KEYS.ANNOUNCEMENT,
        PROCESS_NODE_KEYS.BIDDING,
        PROCESS_NODE_KEYS.OPENING,
        PROCESS_NODE_KEYS.AWARD,
        PROCESS_NODE_KEYS.CONTRACT,
      ]
    },
    evaluationNodes: {
      public: [
        PROCESS_NODE_KEYS.PROCUREMENT,
        PROCESS_NODE_KEYS.ANNOUNCEMENT,
        PROCESS_NODE_KEYS.BIDDING,
        PROCESS_NODE_KEYS.OPENING,
        PROCESS_NODE_KEYS.EVALUATION,
        PROCESS_NODE_KEYS.AWARD,
        PROCESS_NODE_KEYS.CONTRACT,
        PROCESS_NODE_KEYS.ARCHIVE
      ],
      invite: [
        PROCESS_NODE_KEYS.PROCUREMENT,
        PROCESS_NODE_KEYS.ANNOUNCEMENT,
        PROCESS_NODE_KEYS.BIDDING,
        PROCESS_NODE_KEYS.OPENING,
        PROCESS_NODE_KEYS.EVALUATION,
        PROCESS_NODE_KEYS.AWARD,
        PROCESS_NODE_KEYS.CONTRACT,
        PROCESS_NODE_KEYS.ARCHIVE
      ]
    },
  },
  [PROJECT_TYPES.COMPETITIVE]: {
    type: PROJECT_TYPES.COMPETITIVE,
    label: PROJECT_TYPE_LABELS[PROJECT_TYPES.COMPETITIVE],
    description: '',
    purchaserNodes: {
      public: [
        PROCESS_NODE_KEYS.PROCUREMENT,
        PROCESS_NODE_KEYS.ANNOUNCEMENT,
        PROCESS_NODE_KEYS.BIDDING,
        PROCESS_NODE_KEYS.OPENING,
        PROCESS_NODE_KEYS.AWARD,
        PROCESS_NODE_KEYS.CONTRACT,
        PROCESS_NODE_KEYS.ARCHIVE
      ],
      invite: [
        PROCESS_NODE_KEYS.PROCUREMENT,
        PROCESS_NODE_KEYS.ANNOUNCEMENT,
        PROCESS_NODE_KEYS.BIDDING,
        PROCESS_NODE_KEYS.OPENING,
        PROCESS_NODE_KEYS.AWARD,
        PROCESS_NODE_KEYS.CONTRACT,
        PROCESS_NODE_KEYS.ARCHIVE
      ]
    },
    supplierNodes: {
      public: [
        PROCESS_NODE_KEYS.ANNOUNCEMENT,
        PROCESS_NODE_KEYS.BIDDING,
        PROCESS_NODE_KEYS.OPENING,
        PROCESS_NODE_KEYS.AWARD,
        PROCESS_NODE_KEYS.CONTRACT,
      ],
      invite: [
        PROCESS_NODE_KEYS.ANNOUNCEMENT,
        PROCESS_NODE_KEYS.BIDDING,
        PROCESS_NODE_KEYS.OPENING,
        PROCESS_NODE_KEYS.AWARD,
        PROCESS_NODE_KEYS.CONTRACT,
      ]
    },
    evaluationNodes: {
      public: [
        PROCESS_NODE_KEYS.PROCUREMENT,
        PROCESS_NODE_KEYS.ANNOUNCEMENT,
        PROCESS_NODE_KEYS.BIDDING,
        PROCESS_NODE_KEYS.OPENING,
        PROCESS_NODE_KEYS.AWARD,
        PROCESS_NODE_KEYS.CONTRACT,
        PROCESS_NODE_KEYS.ARCHIVE
      ],
      invite: [
        PROCESS_NODE_KEYS.PROCUREMENT,
        PROCESS_NODE_KEYS.ANNOUNCEMENT,
        PROCESS_NODE_KEYS.BIDDING,
        PROCESS_NODE_KEYS.OPENING,
        PROCESS_NODE_KEYS.AWARD,
        PROCESS_NODE_KEYS.CONTRACT,
        PROCESS_NODE_KEYS.ARCHIVE
      ]
    },
  },
  [PROJECT_TYPES.DIRECT]: {
    type: PROJECT_TYPES.DIRECT,
    label: PROJECT_TYPE_LABELS[PROJECT_TYPES.DIRECT],
    description: '',
    purchaserNodes: {
      public: [
        PROCESS_NODE_KEYS.PROCUREMENT,
        PROCESS_NODE_KEYS.ANNOUNCEMENT,
        PROCESS_NODE_KEYS.BIDDING,
        PROCESS_NODE_KEYS.OPENING,
        PROCESS_NODE_KEYS.AWARD,
        PROCESS_NODE_KEYS.CONTRACT,
        PROCESS_NODE_KEYS.ARCHIVE
      ],
      invite: [
        PROCESS_NODE_KEYS.PROCUREMENT,
        PROCESS_NODE_KEYS.ANNOUNCEMENT,
        PROCESS_NODE_KEYS.BIDDING,
        PROCESS_NODE_KEYS.OPENING,
        PROCESS_NODE_KEYS.AWARD,
        PROCESS_NODE_KEYS.CONTRACT,
        PROCESS_NODE_KEYS.ARCHIVE
      ]
    },
    supplierNodes: {
      public: [
        PROCESS_NODE_KEYS.ANNOUNCEMENT,
        PROCESS_NODE_KEYS.BIDDING,
        PROCESS_NODE_KEYS.OPENING,
        PROCESS_NODE_KEYS.AWARD,
        PROCESS_NODE_KEYS.CONTRACT,
      ],
      invite: [
        PROCESS_NODE_KEYS.ANNOUNCEMENT,
        PROCESS_NODE_KEYS.BIDDING,
        PROCESS_NODE_KEYS.OPENING,
        PROCESS_NODE_KEYS.AWARD,
        PROCESS_NODE_KEYS.CONTRACT,
      ]
    },
    evaluationNodes: {
      public: [
        PROCESS_NODE_KEYS.PROCUREMENT,
        PROCESS_NODE_KEYS.ANNOUNCEMENT,
        PROCESS_NODE_KEYS.BIDDING,
        PROCESS_NODE_KEYS.OPENING,
        PROCESS_NODE_KEYS.AWARD,
        PROCESS_NODE_KEYS.CONTRACT,
        PROCESS_NODE_KEYS.ARCHIVE
      ],
      invite: [
        PROCESS_NODE_KEYS.PROCUREMENT,
        PROCESS_NODE_KEYS.ANNOUNCEMENT,
        PROCESS_NODE_KEYS.BIDDING,
        PROCESS_NODE_KEYS.OPENING,
        PROCESS_NODE_KEYS.AWARD,
        PROCESS_NODE_KEYS.CONTRACT,
        PROCESS_NODE_KEYS.ARCHIVE
      ]
    },
  },
  [PROJECT_TYPES.FAST_INQUIRY]: {
    type: PROJECT_TYPES.FAST_INQUIRY,
    label: PROJECT_TYPE_LABELS[PROJECT_TYPES.FAST_INQUIRY],
    description: '',
    purchaserNodes: {
      public: [
        PROCESS_NODE_KEYS.PROCUREMENT,
        PROCESS_NODE_KEYS.ANNOUNCEMENT,
        PROCESS_NODE_KEYS.BIDDING,
        PROCESS_NODE_KEYS.OPENING,
        PROCESS_NODE_KEYS.AWARD,
        PROCESS_NODE_KEYS.CONTRACT,
        PROCESS_NODE_KEYS.ARCHIVE
      ],
      invite: [
        PROCESS_NODE_KEYS.PROCUREMENT,
        PROCESS_NODE_KEYS.ANNOUNCEMENT,
        PROCESS_NODE_KEYS.BIDDING,
        PROCESS_NODE_KEYS.OPENING,
        PROCESS_NODE_KEYS.AWARD,
        PROCESS_NODE_KEYS.CONTRACT,
        PROCESS_NODE_KEYS.ARCHIVE
      ]
    },
    supplierNodes: {
      public: [
        PROCESS_NODE_KEYS.ANNOUNCEMENT,
        PROCESS_NODE_KEYS.BIDDING,
        PROCESS_NODE_KEYS.OPENING,
        PROCESS_NODE_KEYS.AWARD,
        PROCESS_NODE_KEYS.CONTRACT,
      ],
      invite: [
        PROCESS_NODE_KEYS.ANNOUNCEMENT,
        PROCESS_NODE_KEYS.BIDDING,
        PROCESS_NODE_KEYS.OPENING,
        PROCESS_NODE_KEYS.AWARD,
        PROCESS_NODE_KEYS.CONTRACT,
      ]
    },
    evaluationNodes: {
      public: [
        PROCESS_NODE_KEYS.PROCUREMENT,
        PROCESS_NODE_KEYS.ANNOUNCEMENT,
        PROCESS_NODE_KEYS.BIDDING,
        PROCESS_NODE_KEYS.OPENING,
        PROCESS_NODE_KEYS.AWARD,
        PROCESS_NODE_KEYS.CONTRACT,
        PROCESS_NODE_KEYS.ARCHIVE
      ],
      invite: [
        PROCESS_NODE_KEYS.PROCUREMENT,
        PROCESS_NODE_KEYS.ANNOUNCEMENT,
        PROCESS_NODE_KEYS.BIDDING,
        PROCESS_NODE_KEYS.OPENING,
        PROCESS_NODE_KEYS.AWARD,
        PROCESS_NODE_KEYS.CONTRACT,
        PROCESS_NODE_KEYS.ARCHIVE
      ]
    },
  },
};

// 项目类型
export type ProjectType = keyof typeof PROJECT_TYPE_CONFIGS;
