/**
 * 流程状态常量
 */

// 流程节点
export const PROCESS_NODES = {
  PROCUREMENT: 'procurement',
  ANNOUNCEMENT: 'announcement',
  BIDDING: 'bidding',
  OPENING: 'opening',
  EVALUATION: 'evaluation',
  AWARD: 'award',
  CONTRACT: 'contract',
  ARCHIVE: 'archive'
} as const

// 采方状态枚举
export const PURCHASER_PROGRESS_STATUS = {
  TO_NOTICE: 'TO_NOTICE',           // 待发公告 - 采购需求完成，当前为发标
  NOTICE: 'NOTICE',                 // 已发布公告 - 当前投标
  TENDER_DOC: 'TENDER_DOC',         // 竞谈文件已完成 - 发标完成，当前投标
  REGISTER: 'REGISTER',             // 报名中 - 当前投标
  QUOTING: 'QUOTING',               // 报价中 - 当前投标
  TO_BID_OPEN: 'TO_BID_OPEN',       // 待开标 - 投标完成，当前开标
  BID_OPENED: 'BID_OPENED',         // 开标中 - 开标未结束，当前开标
  END_OPENED: 'END_OPENED',         // 开标结束 - 开标完成，当前定标
  EVALUATING: 'EVALUATING',         // 评标中 - 当前评标
  EVALUATED: 'EVALUATED',           // 已评标 - 当前定标
  AWARDED: 'AWARDED',               // 已定标 - 定标完成，当前为定标
  BID_WON_PUBLICITY: 'BID_WON_PUBLICITY', // 已发中标公示 - 定标完成，当前为定标
  BID_WON_NOTICE: 'BID_WON_NOTICE', // 已发中标公告 - 定标完成，当前为定标
  COMPLETED: 'COMPLETED',           // 已完成 - 定标完成，当前为定标
  FAILED: 'FAILED',                  // 已流标 - 定标完成，当前为定标
  CONTRACT: 'CONTRACT',             // 已签约 - 定标完成，当前为定标
  ARCHIVE: 'ARCHIVE'                // 已归档 - 定标完成，当前为定标
} as const

// 供方状态枚举
export const SUPPLIER_PROGRESS_STATUS = {
  TO_NOTICE: 'TO_NOTICE',           // 待发公告 - 采购公告
  NOTICE: 'NOTICE',                 // 已发布公告 - 采购公告完成，当前投标
  TENDER_DOC: 'TENDER_DOC',         // 竞谈文件已完成 - 发标完成，当前投标
  REGISTER: 'REGISTER',             // 报名中 - 采购公告完成，当前投标
  QUOTING: 'QUOTING',               // 报价中 - 采购公告完成，当前投标
  TO_BID_OPEN: 'TO_BID_OPEN',       // 待开标 - 采购公告完成，当前投标
  BID_OPENED: 'BID_OPENED',         // 开标中 - 投标完成，开标未结束，当前开标
  END_OPENED: 'END_OPENED',         // 开标结束 - 开标完成，询价为当前定标、竞谈为当前评标
  EVALUATING: 'EVALUATING',         // 评标中 - 当前评标
  EVALUATED: 'EVALUATED',           // 已评标 - 开标完成，当前定标
  AWARDED: 'AWARDED',               // 已定标 - 定标完成
  BID_WON_PUBLICITY: 'BID_WON_PUBLICITY', // 已发中标公示 - 定标完成
  BID_WON_NOTICE: 'BID_WON_NOTICE', // 已发中标公告 - 定标完成
  COMPLETED: 'COMPLETED',           // 已完成 - 定标完成
  FAILED: 'FAILED',                  // 已流标 - 定标完成
  CONTRACT: 'CONTRACT',             // 已签约 - 定标完成，当前为定标
} as const

// 采方状态标签
export const PURCHASER_STATUS_LABELS = {
  [PURCHASER_PROGRESS_STATUS.TO_NOTICE]: '待发公告',
  [PURCHASER_PROGRESS_STATUS.NOTICE]: '已发布公告',
  [PURCHASER_PROGRESS_STATUS.TENDER_DOC]: '竞谈文件已完成',
  [PURCHASER_PROGRESS_STATUS.REGISTER]: '报名中',
  [PURCHASER_PROGRESS_STATUS.QUOTING]: '报价中',
  [PURCHASER_PROGRESS_STATUS.TO_BID_OPEN]: '待开标',
  [PURCHASER_PROGRESS_STATUS.BID_OPENED]: '开标中',
  [PURCHASER_PROGRESS_STATUS.END_OPENED]: '开标结束',
  [PURCHASER_PROGRESS_STATUS.EVALUATED]: '已评标',
  [PURCHASER_PROGRESS_STATUS.AWARDED]: '已定标',
  [PURCHASER_PROGRESS_STATUS.BID_WON_PUBLICITY]: '已发中标公示',
  [PURCHASER_PROGRESS_STATUS.BID_WON_NOTICE]: '已发中标公告',
  [PURCHASER_PROGRESS_STATUS.COMPLETED]: '已完成',
  [PURCHASER_PROGRESS_STATUS.FAILED]: '已流标'
} as const
