/**
 * 采购寻源方式常量定义
 */

// 采购寻源方式枚举
export const SOURCING_TYPES = {
  XJCG: 'XJCG',     // 询比价
  JJCG: 'JJCG',     // 竞价采购
  ZB: 'ZB',         // 招标
  JZTP: 'JZTP',     // 竞争性谈判
  KSXJ: 'KSXJ',     // 快速询价
  ZJWT: 'ZJWT',     // 直接委托
} as const

// 采购寻源方式类型
export type SourcingType = typeof SOURCING_TYPES[keyof typeof SOURCING_TYPES]

// 竞争性采购类型（逻辑相同的类型）
export const COMPETITIVE_BIDDING_TYPES = [SOURCING_TYPES.JZTP, SOURCING_TYPES.ZB] as const

// 采购寻源方式分组
export const SOURCING_TYPE_GROUPS = {
  // 竞争性采购（JZTP 和 ZB 逻辑相同）
  COMPETITIVE: [SOURCING_TYPES.JZTP, SOURCING_TYPES.ZB],
  // 询价类采购
  INQUIRY: [SOURCING_TYPES.XJCG, SOURCING_TYPES.KSXJ],
  // 其他类型
  OTHER: [SOURCING_TYPES.JJCG, SOURCING_TYPES.ZJWT],
} as const

/**
 * 判断是否为竞争性采购类型
 * @param sourcingType 采购寻源方式
 * @returns 是否为竞争性采购
 */
export function isCompetitiveBiddingType(sourcingType: string): boolean {
  return COMPETITIVE_BIDDING_TYPES.includes(sourcingType as any)
}

/**
 * 判断是否为询价类采购
 * @param sourcingType 采购寻源方式
 * @returns 是否为询价类采购
 */
export function isInquiryType(sourcingType: string): boolean {
  return SOURCING_TYPE_GROUPS.INQUIRY.includes(sourcingType as any)
}

/**
 * 获取采购类型的显示名称
 * @param sourcingType 采购寻源方式
 * @returns 显示名称
 */
export function getSourcingTypeName(sourcingType: string): string {
  const nameMap: Record<string, string> = {
    [SOURCING_TYPES.XJCG]: '询比价',
    [SOURCING_TYPES.JJCG]: '竞价采购',
    [SOURCING_TYPES.ZB]: '招标',
    [SOURCING_TYPES.JZTP]: '竞争性谈判',
    [SOURCING_TYPES.KSXJ]: '快速询价',
    [SOURCING_TYPES.ZJWT]: '直接委托',
  }
  return nameMap[sourcingType] || sourcingType
}
