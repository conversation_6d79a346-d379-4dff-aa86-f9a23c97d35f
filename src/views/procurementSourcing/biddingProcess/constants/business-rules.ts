/**
 * 业务规则常量
 */

// 投标状态
export const BIDDING_STATUS = {
  REGISTERED: 'registered',
  DOCUMENT_SUBMITTED: 'document_submitted',
  DOCUMENT_APPROVED: 'document_approved',
  DOCUMENT_REJECTED: 'document_rejected',
  BOND_PAID: 'bond_paid',
  QUOTED: 'quoted',
  OPENED: 'opened',
  WITHDRAWN: 'withdrawn'
} as const

// 投标状态标签
export const BIDDING_STATUS_LABELS = {
  [BIDDING_STATUS.REGISTERED]: '已报名',
  [BIDDING_STATUS.DOCUMENT_SUBMITTED]: '已提交资料',
  [BIDDING_STATUS.DOCUMENT_APPROVED]: '资料已审核',
  [BIDDING_STATUS.DOCUMENT_REJECTED]: '资料被拒绝',
  [BIDDING_STATUS.BOND_PAID]: '已缴纳保证金',
  [BIDDING_STATUS.QUOTED]: '已报价',
  [BIDDING_STATUS.OPENED]: '已开标',
  [BIDDING_STATUS.WITHDRAWN]: '已撤回'
} as const

// 保证金状态
export const BOND_STATUS = {
  UNPAID: 'unpaid',
  PAID: 'paid',
  REFUNDED: 'refunded',
  FORFEITED: 'forfeited'
} as const

// 保证金状态标签
export const BOND_STATUS_LABELS = {
  [BOND_STATUS.UNPAID]: '未缴纳',
  [BOND_STATUS.PAID]: '已缴纳',
  [BOND_STATUS.REFUNDED]: '已退还',
  [BOND_STATUS.FORFEITED]: '已没收'
} as const

// 评标方法
export const EVALUATION_METHODS = {
  LOWEST_PRICE: 'lowest_price',
  COMPREHENSIVE: 'comprehensive',
  COMPETITIVE_NEGOTIATION: 'competitive_negotiation',
  SINGLE_SOURCE: 'single_source'
} as const

// 评标方法标签
export const EVALUATION_METHOD_LABELS = {
  [EVALUATION_METHODS.LOWEST_PRICE]: '最低价法',
  [EVALUATION_METHODS.COMPREHENSIVE]: '综合评分法',
  [EVALUATION_METHODS.COMPETITIVE_NEGOTIATION]: '竞争性谈判',
  [EVALUATION_METHODS.SINGLE_SOURCE]: '单一来源'
} as const

// 专家级别
export const EXPERT_LEVELS = {
  SENIOR: 'senior',
  INTERMEDIATE: 'intermediate',
  JUNIOR: 'junior'
} as const

// 专家级别标签
export const EXPERT_LEVEL_LABELS = {
  [EXPERT_LEVELS.SENIOR]: '高级专家',
  [EXPERT_LEVELS.INTERMEDIATE]: '中级专家',
  [EXPERT_LEVELS.JUNIOR]: '初级专家'
} as const

// 合同状态
export const CONTRACT_STATUS = {
  DRAFT: 'draft',
  UNDER_REVIEW: 'under_review',
  APPROVED: 'approved',
  REJECTED: 'rejected',
  SIGNED: 'signed',
  EFFECTIVE: 'effective',
  SUSPENDED: 'suspended',
  TERMINATED: 'terminated',
  COMPLETED: 'completed'
} as const

// 合同状态标签
export const CONTRACT_STATUS_LABELS = {
  [CONTRACT_STATUS.DRAFT]: '草稿',
  [CONTRACT_STATUS.UNDER_REVIEW]: '审核中',
  [CONTRACT_STATUS.APPROVED]: '已批准',
  [CONTRACT_STATUS.REJECTED]: '已拒绝',
  [CONTRACT_STATUS.SIGNED]: '已签署',
  [CONTRACT_STATUS.EFFECTIVE]: '已生效',
  [CONTRACT_STATUS.SUSPENDED]: '已暂停',
  [CONTRACT_STATUS.TERMINATED]: '已终止',
  [CONTRACT_STATUS.COMPLETED]: '已完成'
} as const

// 时间限制（小时）
export const TIME_LIMITS = {
  BIDDING_REGISTRATION: 24 * 7, // 投标报名期限：7天
  DOCUMENT_REVIEW: 24 * 3, // 资料审核期限：3天
  BOND_PAYMENT: 24 * 2, // 保证金缴纳期限：2天
  QUOTATION_SUBMIT: 24 * 1, // 报价提交期限：1天
  EVALUATION_PERIOD: 24 * 5, // 评标期限：5天
  OBJECTION_PERIOD: 24 * 3, // 异议期限：3天
  CONTRACT_SIGNING: 24 * 15 // 合同签订期限：15天
} as const

// 最小投标人数量
export const MIN_BIDDER_COUNT = 3

// 最大专家数量
export const MAX_EXPERT_COUNT = 7

// 保证金比例范围
export const BOND_RATIO_RANGE = {
  MIN: 0.01, // 1%
  MAX: 0.05  // 5%
} as const 