/**
 * 文档类型常量
 */

// 文档类型
export const DOCUMENT_TYPES = {
  BUSINESS_LICENSE: 'business_license',
  QUALIFICATION: 'qualification',
  FINANCIAL_REPORT: 'financial_report',
  TECHNICAL_PROPOSAL: 'technical_proposal',
  COMMERCIAL_PROPOSAL: 'commercial_proposal',
  LEGAL_STATEMENT: 'legal_statement',
  OTHER: 'other'
} as const

// 文档类型标签
export const DOCUMENT_TYPE_LABELS = {
  [DOCUMENT_TYPES.BUSINESS_LICENSE]: '营业执照',
  [DOCUMENT_TYPES.QUALIFICATION]: '资质证书',
  [DOCUMENT_TYPES.FINANCIAL_REPORT]: '财务报告',
  [DOCUMENT_TYPES.TECHNICAL_PROPOSAL]: '技术方案',
  [DOCUMENT_TYPES.COMMERCIAL_PROPOSAL]: '商务方案',
  [DOCUMENT_TYPES.LEGAL_STATEMENT]: '法律声明',
  [DOCUMENT_TYPES.OTHER]: '其他'
} as const

// 审核状态
export const REVIEW_STATUS = {
  PENDING: 'pending',
  APPROVED: 'approved',
  REJECTED: 'rejected',
  NEED_SUPPLEMENT: 'need_supplement'
} as const

// 审核状态标签
export const REVIEW_STATUS_LABELS = {
  [REVIEW_STATUS.PENDING]: '待审核',
  [REVIEW_STATUS.APPROVED]: '已通过',
  [REVIEW_STATUS.REJECTED]: '已拒绝',
  [REVIEW_STATUS.NEED_SUPPLEMENT]: '需要补充'
} as const

// 允许的文件类型
export const ALLOWED_FILE_TYPES = [
  'pdf', 'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx',
  'jpg', 'jpeg', 'png', 'gif', 'bmp',
  'zip', 'rar', '7z'
] as const

// 文件大小限制（MB）
export const FILE_SIZE_LIMITS = {
  IMAGE: 5,
  DOCUMENT: 20,
  ARCHIVE: 100
} as const

// 必需文档类型
export const REQUIRED_DOCUMENT_TYPES = [
  DOCUMENT_TYPES.BUSINESS_LICENSE,
  DOCUMENT_TYPES.QUALIFICATION,
  DOCUMENT_TYPES.TECHNICAL_PROPOSAL,
  DOCUMENT_TYPES.COMMERCIAL_PROPOSAL
] as const 