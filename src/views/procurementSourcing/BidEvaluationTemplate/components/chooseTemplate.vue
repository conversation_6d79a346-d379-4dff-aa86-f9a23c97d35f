<template>
  <el-drawer
    v-model="visible"
    title="选择评分模版"
    size="960px"
    :before-close="handleClose"
    destroy-on-close
  >
    <div class="choose-template-drawer">
      <div class="table-content">
        <yun-pro-table
          ref="tableRef"
          v-model:pagination="pagination"
          v-model:searchData="searchForm"
          auto-height
          :table-props="{ rowKey: 'id' }"
          :search-fields="searchFields"
          :table-columns="tableColumns"
          :remote-method="remoteMethod"
          :layout="'whole'"
          @reset="resetTable"
        >
          <template #t_radio="{ row }">
            <el-radio
              :label="row.id"
              :model-value="selectedId"
              @change="() => handleRadioChange(row)"
            >
              {{ '' }}
            </el-radio>
          </template>
        </yun-pro-table>
      </div>

      <!-- 操作按钮 -->
      <div class="drawer-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleConfirm" :loading="loading">
          确定
        </el-button>
      </div>
    </div>
  </el-drawer>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import { ElMessage } from 'yun-design';
import { getBidEvaluationTemplateList } from '@/api/purchasing/bidEvaluationTemplate';

interface Props {
  visible: boolean;
}
const props = defineProps<Props>();
const emits = defineEmits<{
  'update:visible': [value: boolean];
  'confirm': [data: any[]];
}>();

const tableRef = ref();
const selectData = ref<any>(null);
const selectedId = ref<string | number | null>(null);
const loading = ref(false);

const visible = computed({
  get: () => props.visible,
  set: (value) => emits('update:visible', value)
});

const pagination = ref({ page: 1, size: 10, total: 0 });
const searchForm = ref({});

const searchFields = [
  { label: '模版编号', prop: 'templateCode', component: 'el-input', placeholder: '请输入模版编号' },
  { label: '模版名称', prop: 'templateName', component: 'el-input', placeholder: '请输入模版名称' },
];
const tableColumns = [
  { type: 'radio', fixed: 'left', width: 50 },
  { label: '序号', prop: 'index', type: 'index', width: 60 },
  { label: '模版编号', prop: 'templateCode', width: 150 },
  { label: '评标模版名称', prop: 'templateName', minWidth: 200 },
  { label: '评标模版描述', prop: 'templateDesc', minWidth: 300, showOverflowTooltip: true },
];

const remoteMethod = async ({ searchData, pagination }) => {
  const { data } = await getBidEvaluationTemplateList({
    ...searchData,
    current: pagination?.page,
    size: pagination?.size
  });
  return data;
};

function resetTable() {
  pagination.value.page = 1;
  selectedId.value = null;
  selectData.value = null;
}

const handleConfirm = () => {
  if (!selectData.value) {
    ElMessage.warning('请选择评分模版');
    return;
  }
  visible.value = false;
  emits('confirm', selectData.value);
};

const handleClose = () => {
  resetTable();
  visible.value = false;
  selectedId.value = null;
  selectData.value = null;
}

const handleRadioChange = (row) => {
  selectedId.value = row.id;
  selectData.value = row;
};
</script>

<style lang="scss" scoped>
.choose-template-drawer {
  height: 100%;
  display: flex;
  flex-direction: column;

  .table-content {
    flex: 1;
    overflow-y: auto;
    padding-right: 16px;
    height: calc(100vh - 200px);
    :deep(.yun-pro-table) {
      height: 100%;
    }
    :deep(.el-radio__inner) {
      margin-bottom: 8px !important;
    }
  }

  .drawer-footer {
    padding: 16px 0;
    border-top: 1px solid #E4E7ED;
    text-align: right;
    background-color: #fff;
  }
}
</style>
