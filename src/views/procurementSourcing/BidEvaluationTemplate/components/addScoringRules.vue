<template>
  <div class="add-scoring-rules">
    <!-- 新增评分节点按钮 -->
    <div v-if="!isViewMode" class="mb-4">
      <el-button @click="openDrawer('addNode')">
        <el-icon><Plus /></el-icon>
        新增评分节点
      </el-button>
      <el-button @click="openDrawer('chooseTemplate')" v-if="!fromTemplate">
        选择评分模版
      </el-button>
    </div>

    <!-- 新增节点抽屉 -->
    <AddScoringNodeDrawer
      :visible="drawerVisible === 'addNode'"
      :existing-node-names="existingNodeNames"
      @update:visible="val => { if (!val) drawerVisible = null }"
      @submit="handleAddNodeDrawerSubmit"
    />

    <!-- 评审项展示 -->
    <div class="scoring-node-block" v-if="reviewGroups.length">
      <div class="scoring-node-header flex items-center mb-2">
        <span class="node-badge">评审项</span>
      </div>
      <div v-for="group in reviewGroups" :key="group.nodeName" class="scoring-node-body">
        <p class="node-name-content">
          <span class="node-name mr-1">{{ group.nodeName }}</span>
          <el-button v-if="!isViewMode" type="text" @click="removeNode('REVIEW', group.nodeName)" icon="delete" style="color: #FF3B30"></el-button>
        </p>
        <el-table :data="group.items" style="width: 100%">
          <el-table-column label="序号" width="76" align="center">
            <template #default="{ $index }">{{ $index + 1 }}</template>
          </el-table-column>
          <el-table-column label="评审项名称" prop="itemName" width="200">
            <template #default="{ row }">
              <el-input v-if="!isViewMode" v-model="row.itemName" placeholder="最多输入30字" maxlength="30" @input="updateRules" />
              <span v-else>{{ row.itemName }}</span>
            </template>
          </el-table-column>
          <el-table-column label="评审项详情" prop="itemDescription" min-width="200">
            <template #default="{ row }">
              <el-input v-if="!isViewMode" v-model="row.itemDescription" placeholder="请输入" maxlength="200" @input="updateRules" />
              <span v-else>{{ row.itemDescription }}</span>
            </template>
          </el-table-column>
          <el-table-column v-if="!isViewMode" label="操作" width="140">
            <template #default="{ $index }">
              <el-button type="text" @click="removeItem('REVIEW', group.nodeName, $index)" style="color: #FF3B30">删除</el-button>
              <el-button type="text" @click="addItem('REVIEW', group.nodeName)">新增一行</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>

    <!-- 评分项展示 -->
    <div class="scoring-node-block" v-if="scoreGroups.length">
      <div class="scoring-node-header flex items-center mb-2">
        <span class="node-badge">评分项</span>
      </div>
      <div v-for="group in scoreGroups" :key="group.nodeName" class="scoring-node-body">
        <p class="node-name-content">
          <span class="node-name mr-1">{{ group.nodeName }}</span>
          <span v-if="group.items.length && group.items[0].totalScore !== undefined" class="ml-2 text-gray-500">{{ group.items[0].totalScore }}</span>
          <el-button v-if="!isViewMode" type="text" @click="removeNode('SCORE', group.nodeName)" icon="delete" style="color: #FF3B30"></el-button>
        </p>
        <el-table :data="group.items" style="width: 100%">
          <el-table-column label="序号" width="76" align="center">
            <template #default="{ $index }">{{ $index + 1 }}</template>
          </el-table-column>
          <el-table-column label="评分项名称" prop="itemName" width="200">
            <template #default="{ row }">
              <el-input v-if="!isViewMode" v-model="row.itemName" placeholder="最多输入30字" maxlength="30" @input="updateRules" />
              <span v-else>{{ row.itemName }}</span>
            </template>
          </el-table-column>
          <el-table-column label="评分项详情" prop="itemDescription" min-width="200">
            <template #default="{ row }">
              <el-input v-if="!isViewMode" v-model="row.itemDescription" placeholder="请输入" maxlength="200" @input="updateRules" />
              <span v-else>{{ row.itemDescription }}</span>
            </template>
          </el-table-column>
          <el-table-column label="分值范围" width="300">
            <template #default="{ row, $index }">
              <div v-if="!isViewMode" class="flex items-center">
                <el-input v-model.number="row.minScore" placeholder="最小分" type="number" style="width: 140px" @input="row.minScore = Number(formatScoreInput(row.minScore)); validateScoreRange(row, group.items[0]?.totalScore, $index); updateRules()" :class="{'is-error': invalidScoreFields[getScoreKey(row, $index)]}" maxlength="10" step="0.0001" />
                <span class="mx-1">-</span>
                <el-input v-model.number="row.maxScore" placeholder="最大分" type="number" style="width: 140px" @input="row.maxScore = Number(formatScoreInput(row.maxScore)); validateScoreRange(row, group.items[0]?.totalScore, $index); updateRules()" :class="{'is-error': invalidScoreFields[getScoreKey(row, $index)]}" maxlength="10" step="0.0001" />
              </div>
              <span v-else>{{ row.minScore }} - {{ row.maxScore }}</span>
            </template>
          </el-table-column>
          <el-table-column v-if="!isViewMode" label="操作" width="140">
            <template #default="{ $index }">
              <el-button type="text" @click="removeItem('SCORE', group.nodeName, $index)" style="color: #FF3B30">删除</el-button>
              <el-button type="text" @click="addItem('SCORE', group.nodeName, group.items[0])">新增一行</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>

    <div v-if="!reviewGroups.length && !scoreGroups.length" class="text-center text-gray-400 py-8">暂无评分细则</div>

    <!-- 选择模版抽屉 -->
    <ChooseTemplate
      :visible="drawerVisible === 'chooseTemplate'"
      @update:visible="val => { if (!val) drawerVisible = null }"
      @confirm="handleTemplateSelected"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, watch, computed, reactive, defineExpose } from 'vue'
import { Plus } from '@element-plus/icons-vue'
import { ElMessage } from 'yun-design'
import AddScoringNodeDrawer from './addScoringNodeDrawer.vue'
import ChooseTemplate from './chooseTemplate.vue'
import { getBidEvaluationTemplateDetail } from '@/api/purchasing/bidEvaluationTemplate';
import type { ScoringRule } from '../types';

const props = defineProps<{
  modelValue: ScoringRule[]
  isViewMode?: boolean
  fromTemplate?: true
}>()
const emit = defineEmits(['update:modelValue'])

const rules = ref<ScoringRule[]>(props.modelValue ?? [])

watch(() => props.modelValue, (val) => {
  rules.value = val ?? []
})

// 分组逻辑
const reviewGroups = computed(() => {
  const groups: Record<string, ScoringRule[]> = {}
  rules.value.filter(r => r.type === 'REVIEW').forEach(item => {
    if (!groups[item.nodeName]) groups[item.nodeName] = []
    groups[item.nodeName].push(item)
  })
  return Object.entries(groups).map(([nodeName, items]) => ({ nodeName, items }))
})
const scoreGroups = computed(() => {
  const groups: Record<string, ScoringRule[]> = {}
  rules.value.filter(r => r.type === 'SCORE').forEach(item => {
    if (!groups[item.nodeName]) groups[item.nodeName] = []
    groups[item.nodeName].push(item)
  })
  return Object.entries(groups).map(([nodeName, items]) => ({ nodeName, items }))
})

const drawerVisible = ref<'addNode' | 'chooseTemplate' | null>(null)
const openDrawer = (type: 'addNode' | 'chooseTemplate') => {
  drawerVisible.value = type
}
const removeNode = (type: 'REVIEW' | 'SCORE', nodeName: string) => {
  rules.value = rules.value.filter(r => !(r.type === type && r.nodeName === nodeName))
  updateRules()
}
const addItem = (type: 'REVIEW' | 'SCORE', nodeName: string, item?: object) => {
  rules.value.push({
    sectionId: '',
    type,
    nodeName,
    itemName: '',
    itemDescription: '',
    maxScore: type === 'SCORE' ? 0 : undefined,
    minScore: type === 'SCORE' ? 0 : undefined,
    weight: type === 'SCORE' ? item?.weight : 0,
    vetoThreshold: type === 'REVIEW' ? 0 : undefined,
    totalScore: type === 'SCORE' ? item?.totalScore : 0,
  })
  updateRules()
  validateAllScoreRanges()
}
const removeItem = (type: 'REVIEW' | 'SCORE', nodeName: string, index: number) => {
  const idx = rules.value.findIndex((r, i) => r.type === type && r.nodeName === nodeName && reviewOrScoreIndex(type, nodeName, i) === index)
  if (idx !== -1) {
    rules.value.splice(idx, 1)
    updateRules()
    validateAllScoreRanges()
  }
}
const reviewOrScoreIndex = (type: 'REVIEW' | 'SCORE', nodeName: string, idx: number) => {
  // 计算同组下的index
  let count = -1
  for (let i = 0; i <= idx; i++) {
    if (rules.value[i].type === type && rules.value[i].nodeName === nodeName) count++
  }
  return count
}
const updateRules = () => {
  emit('update:modelValue', rules.value)
}
const handleTemplateSelected = async (selectedTemplates: any[]) => {
  let data;
  if (!selectedTemplates.id) {
    return;
  }
  const templateId = selectedTemplates?.id;
  if (!templateId) return;
  try {
    const res = await getBidEvaluationTemplateDetail(templateId);
    data = res.data;
  } catch (e) {
    ElMessage.error('获取模版详情失败');
    return;
  }
  // 处理回显
  const flatRules: ScoringRule[] = [];
  if (data) {
    // 处理评审项
    if (data.templateReviewItemsByNode) {
      Object.entries(data.templateReviewItemsByNode).forEach(([nodeName, items]: [string, any[]]) => {
        items.forEach(item => {
          flatRules.push({
            sectionId: '',
            type: 'REVIEW',
            nodeName,
            itemName: item.name,
            itemDescription: item.detail,
            vetoThreshold: item.rejectCount,
          });
        });
      });
    }
    // 处理评分项
    if (data.templateScoreItemsByNode) {
      Object.entries(data.templateScoreItemsByNode).forEach(([nodeName, items]: [string, any[]]) => {
        items.forEach(item => {
          flatRules.push({
            sectionId: '',
            type: 'SCORE',
            nodeName,
            itemName: item.name,
            itemDescription: item.detail,
            maxScore: item.maxScore,
            minScore: item.minScore,
            weight: item.weight,
            totalScore: item.totalScore,
          });
        });
      });
    }
  }
  rules.value = flatRules;
  updateRules();
  drawerVisible.value = null;
  ElMessage.success('模版数据加载成功');
};

// 计算同类别下已存在的环节名称，传给AddScoringNodeDrawer防止重名
const existingNodeNames = computed(() => {
  const result: Record<'REVIEW'|'SCORE', string[]> = { REVIEW: [], SCORE: [] };
  rules.value.forEach(item => {
    if (!result[item.type].includes(item.nodeName)) {
      result[item.type].push(item.nodeName)
    }
  })
  return result
})

// AddScoringNodeDrawer提交后，新增节点
const handleAddNodeDrawerSubmit = ({ type, nodeName, totalScore, weight, vetoThreshold }: { type: 'REVIEW' | 'SCORE', nodeName: string, totalScore?: number, weight?: number, vetoThreshold?: number }) => {
  rules.value.push({
    sectionId: '',
    type,
    nodeName,
    itemName: '',
    itemDescription: '',
    maxScore: type === 'SCORE' ? 0 : undefined,
    minScore: type === 'SCORE' ? 0 : undefined,
    weight: type === 'SCORE' ? weight : 0,
    vetoThreshold: type === 'REVIEW' ? vetoThreshold : undefined,
    totalScore: type === 'SCORE' ? (totalScore ?? 100) : undefined,
  })
  updateRules()
  drawerVisible.value = null
}

const invalidScoreFields = reactive<Record<string, boolean>>({});
const getScoreKey = (row: ScoringRule, idx: number) => `${row.nodeName}_${idx}`;

const formatScoreInput = (val: number | string) => {
  if (typeof val === 'number') val = val.toString();
  if (!val) return val;
  // 保留4位小数
  let [intPart, decPart] = val.split('.');
  if (decPart !== undefined) decPart = decPart.slice(0, 4);
  let result = decPart !== undefined ? `${intPart}.${decPart}` : intPart;
  // 总长度不超过10位
  if (result.length > 10) result = result.slice(0, 10);
  return result;
};

const validateScoreRange = (row: ScoringRule, groupTotalScore?: number, idx?: number) => {
  const key = getScoreKey(row, idx ?? 0);
  let invalid = false;
  if (row.maxScore !== undefined && row.minScore !== undefined) {
    if (!(row.minScore < row.maxScore && (groupTotalScore === undefined || row.maxScore <= groupTotalScore))) {
      invalid = true;
    }
  }
  invalidScoreFields[key] = invalid;
  return !invalid;
};

const validateAllScoreRanges = () => {
  Object.keys(invalidScoreFields).forEach(key => delete invalidScoreFields[key]);
  scoreGroups.value.forEach(group => {
    group.items.forEach((row, idx) => {
      validateScoreRange(row, group.items[0]?.totalScore, idx);
    });
  });
};

defineExpose({
  invalidScoreFields
});
</script>

<style lang="scss" scoped>
.node-badge {
  display: inline-block;
  background: #DCDFE6;
  color: #4E5969;
  font-size: 12px;
  font-weight: 400;
  border-radius: 2px;
  padding: 0px 8px;
  line-height: 20px;
}
.scoring-node-block {
  border: 1px solid #E4E7ED;
  margin-bottom: 16px;
  border-radius: 4px;
  background: #fff;
}
.scoring-node-header {
  font-size: 14px;
  padding: 8px;
  box-shadow: 0px 0px 6px 0px rgba(0, 0, 0, 0.12);
  border-bottom: 1px solid #E4E7ED;
  border-radius: 4px 4px 0 0;
}
.scoring-node-body {
  padding: 0 55px 8px 55px;
  .node-name-content {
    display: flex;
    align-items: center;
    .node-name {
      font-size: 14px;
      color: #4E5969;
    }
  }
}
::v-deep .is-error .el-input__wrapper {
  border-color: #f56c6c !important;
  box-shadow: 0 0 0 2px rgba(245, 108, 108, 0.1);
}
::v-deep .is-error .el-input__inner {
  border-color: #f56c6c !important;
}
</style>
