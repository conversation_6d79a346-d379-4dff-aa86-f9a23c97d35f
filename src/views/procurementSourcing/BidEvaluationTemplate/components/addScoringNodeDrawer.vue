<template>
  <el-drawer
    v-model="visible"
    title="新增评分节点"
    size="480px"
    :before-close="handleClose"
    destroy-on-close
  >
    <div class="add-scoring-node-drawer">
      <el-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        label-width="160px"
        class="scoring-node-form"
      >
        <el-form-item label="环节类别" prop="type">
          <el-radio-group v-model="formData.type">
            <el-radio label="REVIEW">评审项</el-radio>
            <el-radio label="SCORE">评分项</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item label="环节名称" prop="nodeName">
          <el-input
            v-model="formData.nodeName"
            placeholder="请输入环节名称"
            maxlength="20"
          />
        </el-form-item>

        <!-- 评审项特有字段 -->
        <template v-if="formData.type === 'REVIEW'">
          <el-form-item label="不符合否决投标数量" prop="vetoThreshold">
            <el-input
              v-model="formData.vetoThreshold"
              placeholder="请输入不符合否决投标数量"
              type="number"
            >
            </el-input>
            <p class="tip">注意：设置后，评标专家评出不符合后据此设置数量直接否决</p>
          </el-form-item>
        </template>

        <!-- 评分项特有字段 -->
        <template v-if="formData.type === 'SCORE'">
          <el-form-item label="环节总分" prop="totalScore">
            <el-input
              v-model="formData.totalScore"
              placeholder="请输入环节总分"
              type="number"
            >
            </el-input>
          </el-form-item>

          <el-form-item label="权重" prop="weight">
            <el-input
              v-model="formData.weight"
              placeholder="请输入权重"
              type="number"
            >
            </el-input>
          </el-form-item>
        </template>
      </el-form>

      <!-- 操作按钮 -->
      <div class="drawer-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="loading">
          确定
        </el-button>
      </div>
    </div>
  </el-drawer>
</template>

<script setup lang="ts">
import { ref, reactive, computed } from 'vue';
import { ElMessage } from 'yun-design';
import type { ScoringRule, ScoringItem } from '../types';

interface Props {
  visible: boolean;
  existingNodeNames?: Record<'REVIEW'|'SCORE', string[]>;
}
const props = defineProps<Props>();
const emits = defineEmits<{
  'update:visible': [value: boolean];
  'submit': [data: ScoringRule];
}>();

const formRef = ref();
const loading = ref(false);
const formData = reactive({
  type: 'REVIEW' as 'REVIEW' | 'SCORE',
  nodeName: '',
  totalScore: '',
  weight: '',
  vetoThreshold: '',
});

const formRules = computed(() => {
  const baseRules = {
    type: [
      { required: true, message: '请选择环节类别', trigger: 'change' }
    ],
    nodeName: [
      { required: true, message: '请输入环节名称', trigger: 'blur' },
      {
        validator: (rule: any, value: string, callback: any) => {
          const names = props.existingNodeNames?.[formData.type] || [];
          if (names.includes(value)) {
            callback(new Error('该类别下环节名称已存在'));
          } else {
            callback();
          }
        },
        trigger: 'blur'
      }
    ],
  };
  if (formData.type === 'REVIEW') {
    return {
      ...baseRules,
      vetoThreshold: [
        { pattern: /^\d+$/, message: '请输入有效的整数', trigger: 'blur' }
      ],
    };
  } else {
    return {
      ...baseRules,
      totalScore: [
        { required: true, message: '请输入环节总分', trigger: 'blur' },
        { pattern: /^(?=.{1,10}$)\d+(\.\d+)?$/, message: '最多10位数字(含小数点)', trigger: 'blur' }
      ],
      weight: [
        { required: true, message: '请输入权重', trigger: 'blur' },
        { pattern: /^\d+(\.\d+)?$/, message: '请输入有效的数字', trigger: 'blur' }
      ],
    };
  }
});

const visible = computed({
  get: () => props.visible,
  set: (value) => emits('update:visible', value)
});

function resetAndClose() {
  Object.assign(formData, {
    type: 'REVIEW' as 'REVIEW' | 'SCORE',
    nodeName: '',
    totalScore: '',
    weight: '',
    vetoThreshold: '',
  });
  formRef.value?.clearValidate();
  visible.value = false;
}

const handleClose = resetAndClose;

const handleSubmit = async () => {
  if (!formRef.value) return;
  try {
    await formRef.value.validate();
    loading.value = true;
    const submitData = {
      type: formData.type,
      nodeName: formData.nodeName,
      totalScore: formData.type === 'SCORE' ? Number(formData.totalScore) : undefined,
      weight: formData.type === 'SCORE' ? Number(formData.weight) : undefined,
      vetoThreshold: formData.type === 'REVIEW' ? Number(formData.vetoThreshold) : undefined,
    };
    emits('submit', submitData);
    ElMessage.success('评分节点添加成功');
    resetAndClose();
  } catch (error) {
    // 校验失败不处理
  } finally {
    loading.value = false;
  }
};
</script>

<style lang="scss" scoped>
.add-scoring-node-drawer {
  height: 100%;
  display: flex;
  flex-direction: column;

  .scoring-node-form {
    flex: 1;
    overflow-y: auto;
    padding-right: 16px;
    :deep(.el-form-item) {
      margin-bottom: 24px;
    }
    // 让最后一个表单项无下边距
    :deep(.el-form-item:last-of-type) {
      margin-bottom: 0 !important;
    }
    .tip {
      font-size: 12px;
      color: #aaaaaa;
    }
  }

  .drawer-footer {
    padding: 16px 0;
    border-top: 1px solid #E4E7ED;
    text-align: right;
    background-color: #fff;

    .el-button {
      margin-left: 12px;
    }
  }
}

:deep(.el-drawer__body) {
  padding: 20px;
  height: calc(100% - 60px);
  overflow: hidden;
}
</style>
