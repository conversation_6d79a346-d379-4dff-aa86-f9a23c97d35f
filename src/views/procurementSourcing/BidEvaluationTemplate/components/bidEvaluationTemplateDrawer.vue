<template>
  <div>
    <!-- 抽屉模式 -->
    <el-drawer
      v-model="visible"
      :title="drawerTitle"
      size="80%"
      :before-close="handleClose"
      destroy-on-close
    >
      <div class="bid-evaluation-template-drawer">
        <el-form
          ref="formRef"
          :model="formData"
          :rules="rules"
          label-width="140px"
          class="template-form"
        >
          <!-- 模版名称 -->
          <el-form-item label="模版名称" prop="templateName">
            <el-input
              v-model="formData.templateName"
              placeholder="请输入模版名称"
              :disabled="isViewMode"
            />
          </el-form-item>

          <!-- 模版描述 -->
          <el-form-item label="模版描述" prop="templateDesc">
            <el-input
              v-model="formData.templateDesc"
              type="textarea"
              placeholder="请输入模版描述"
              :rows="3"
              maxlength="500"
              show-word-limit
              :disabled="isViewMode"
            />
          </el-form-item>

          <!-- 评分细则 -->
          <el-form-item label="评分细则" prop="scoringRules">
            <div class="scoring-rules-section">
              <AddScoringRules
                ref="addScoringRulesRef"
                v-model="formData.scoringRules"
                :is-view-mode="isViewMode"
                :from-template="true"
              />
            </div>
          </el-form-item>
        </el-form>

        <!-- 操作按钮 -->
        <div v-if="!isViewMode" class="drawer-footer">
          <el-button @click="handleClose">取消</el-button>
          <el-button type="primary" @click="handleSubmit" :loading="loading">
            保存
          </el-button>
        </div>
        <div v-else class="drawer-footer">
          <el-button @click="handleClose">关闭</el-button>
        </div>
      </div>
    </el-drawer>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed } from 'vue';
import { ElMessage } from 'yun-design';
import AddScoringRules from './addScoringRules.vue';
import { createBidEvaluationTemplate, getBidEvaluationTemplateDetail } from '@/api/purchasing/bidEvaluationTemplate';

// Emits
const emits = defineEmits<{
  refresh: [];
}>();

// 抽屉状态
const visible = ref(false);
const loading = ref(false);
const isViewMode = ref(false);
const currentRow = ref<any>(null);
const addScoringRulesRef = ref();

// 表单引用
const formRef = ref();

// 表单数据
const formData = reactive({
  templateCode: '',
  templateName: '',
  templateDesc: '',
  scoringRules: [],
});

// 表单验证规则
const rules = {
  templateName: [
    { required: true, message: '请输入模版名称', trigger: 'blur' },
    { max: 100, message: '模版名称长度不能超过50个字符', trigger: 'blur' }
  ],
  templateDesc: [
    { max: 500, message: '模版描述长度不能超过50个字符', trigger: 'blur' }
  ],
};

// 抽屉标题
const drawerTitle = computed(() => {
  if (isViewMode.value) {
    return '查看评标模版';
  }
  return currentRow.value ? '编辑评标模版' : '新建评标模版';
});

// 数据结构转换函数
const backendToFrontend = (data) => {
  // 扁平化结构
  const scoringRules = [];
  // 处理评审项
  if (data.templateReviewItemsByNode && typeof data.templateReviewItemsByNode === 'object') {
    Object.entries(data.templateReviewItemsByNode).forEach(([nodeName, items]) => {
      (items || []).forEach(item => {
        scoringRules.push({
          lotId: item.lotId || '',
          type: 'REVIEW',
          nodeName,
          itemName: item.name,
          itemDescription: item.detail,
          vetoThreshold: item.rejectCount,
        });
      });
    });
  }
  // 处理评分项
  if (data.templateScoreItemsByNode && typeof data.templateScoreItemsByNode === 'object') {
    Object.entries(data.templateScoreItemsByNode).forEach(([nodeName, items]) => {
      (items || []).forEach(item => {
        scoringRules.push({
          lotId: item.lotId || '',
          type: 'SCORE',
          nodeName,
          itemName: item.name,
          itemDescription: item.detail,
          minScore: item.minScore,
          maxScore: item.maxScore,
          weight: item.weight,
          totalScore: item.totalScore,
        });
      });
    });
  }
  return {
    id: data.id,
    templateName: data.templateName,
    templateDesc: data.templateDesc,
    scoringRules
  };
}

const frontendToBackend = (formData) => {
  const templateReviewItems = [];
  const templateScoreItems = [];
  (formData.scoringRules || []).forEach(rule => {
    if (rule.type === 'REVIEW') {
      templateReviewItems.push({
        name: rule.itemName,
        detail: rule.itemDescription,
        node: rule.nodeName,
        rejectCount: rule.vetoThreshold
      });
    } else if (rule.type === 'SCORE') {
      templateScoreItems.push({
        name: rule.itemName,
        detail: rule.itemDescription,
        node: rule.nodeName,
        totalScore: rule.totalScore,
        weight: rule.weight,
        minScore: rule.minScore,
        maxScore: rule.maxScore
      });
    }
  });
  return {
    id: formData.id,
    templateName: formData.templateName,
    templateDesc: formData.templateDesc,
    templateReviewItems,
    templateScoreItems
  };
}

// 显示抽屉
const show = async (row?: any, mode: 'create' | 'edit' | 'view' = 'create') => {
  visible.value = true;
  isViewMode.value = mode === 'view';
  currentRow.value = row;

  if (row) {
    // 编辑或查看模式，填充表单数据
    const { data } = await getBidEvaluationTemplateDetail(row.id)
    const frontendData = backendToFrontend(data);
    Object.assign(formData, frontendData);
  } else {
    // 新建模式，重置表单
    resetForm();
  }
};

// 重置表单
const resetForm = () => {
  Object.assign(formData, {
    id: '',
    templateCode: '',
    templateName: '',
    templateDesc: '',
    scoringRules: [],
  });
  formRef.value?.clearValidate();
};

// 关闭抽屉
const handleClose = () => {
  visible.value = false;
  resetForm();
  currentRow.value = null;
  isViewMode.value = false;
};

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return;
  if (addScoringRulesRef.value.invalidScoreFields && Object.values(addScoringRulesRef.value.invalidScoreFields).some(Boolean)) {
    ElMessage.warning('存在分值范围校验失败，修改后进行保存！');
    return;
  }
  try {
    await formRef.value.validate();
    loading.value = true;
    const backendData = frontendToBackend(formData);
    await createBidEvaluationTemplate(backendData);
    ElMessage.success(currentRow.value ? '更新成功' : '创建成功');
    emits('refresh');
    handleClose();
  } catch (error) {
    console.error('表单验证失败:', error);
  } finally {
    loading.value = false;
  }
};

// 暴露方法
defineExpose({
  show,
  close: handleClose,
});
</script>

<style lang="scss" scoped>
.bid-evaluation-template-drawer {
  height: 100%;
  display: flex;
  flex-direction: column;

  .template-form {
    flex: 1;
    overflow-y: auto;
    padding-right: 16px;

    .scoring-rules-section {
      width: 100%;
    }
  }

  .drawer-footer {
    padding: 16px 0;
    border-top: 1px solid #E4E7ED;
    text-align: right;
    background-color: #fff;

    .el-button {
      margin-left: 12px;
    }
  }
}

:deep(.el-drawer__body) {
  padding: 20px;
  height: calc(100% - 60px);
  overflow: hidden;
}
</style> 