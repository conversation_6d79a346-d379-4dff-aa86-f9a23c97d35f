<template>
  <div class="bid-evaluation-template-container">
    <yun-pro-table
      ref="tableRef"
      v-model:pagination="state.page"
      v-model:searchData="state.searchData"
      :search-fields="searchFields"
      :layout="'whole'"
      :auto-height="true"
      :table-columns="tableColumns"
      :remote-method="remoteMethod"
      :table-props="tablePropsObj"
    >
      <template #tableHeaderLeft>
        <el-button
          type="primary"
          @click="openCreateDrawer()"
        >
          新建模版
        </el-button>
      </template>
      <template #t_action="scope">
        <el-button
          type="text"
          @click="handleView(scope.row, 'view')"
        >
          查看
        </el-button>
        <el-button
          type="text"
          @click="handleView(scope.row, null)"
        >
          编辑
        </el-button>
        <el-button
          @click="handleDelete(scope.row)"
          type="text"
          style="color: #FF3B30"
        >
          删除
        </el-button>
      </template>
    </yun-pro-table>

    <!-- 新建/编辑模版抽屉 -->
    <BidEvaluationTemplateDrawer
      ref="templateDrawerRef"
      @refresh="refreshTable"
    />
  </div>
</template>

<script setup name="bidEvaluationTemplate" lang="ts">
import { ref, reactive } from 'vue';
import { ElMessage, ElMessageBox } from 'yun-design';
import BidEvaluationTemplateDrawer from './components/bidEvaluationTemplateDrawer.vue';
import { 
  getBidEvaluationTemplateList, 
  deleteBidEvaluationTemplate 
} from '@/api/purchasing/bidEvaluationTemplate';

// 抽屉引用
const templateDrawerRef = ref();

// 表格引用
const tableRef = ref();

// 状态管理
const state = reactive({
  page: {
    total: 0,
    page: 1,
    size: 20,
  },
  searchData: {},
  loading: false,
});

// 搜索字段配置
const searchFields = ref([
  {
    prop: 'templateCode',
    label: '模版编号',
    type: 'input',
    placeholder: '请输入模版编号',
    colProps: { span: 12 },
  },
  {
    prop: 'templateName',
    label: '模版名称',
    type: 'input',
    placeholder: '请输入模版名称',
    colProps: { span: 12 },
  },
]);

// 表格列配置
const tableColumns = ref([
  {
    label: '序号',
    prop: 'index',
    type: 'index',
  },
  {
    prop: 'templateCode',
    label: '模版编号',
    width: 150,
  },
  {
    prop: 'templateName',
    label: '评标模版名称',
    width: 200,
  },
  {
    prop: 'templateDesc',
    label: '评标模版描述',
    minWidth: 300,
    showOverflowTooltip: true,
  },
  {
    prop: 'createTime',
    label: '创建时间',
    width: 180,
  },
  {
    prop: 'updateTime',
    label: '更新时间',
    width: 180,
  },
  {
    label: '操作',
    prop: 'action',
    width: '140px',
    fixed: 'right',
  }
]);

// 表格属性配置
const tablePropsObj = ref({
  stripe: false,
  border: true,
});

// API调用
const remoteMethod = async ({ searchData, pagination }) => {
  try {
    const { data } = await getBidEvaluationTemplateList({
      ...searchData,
      current: pagination?.page,
      size: pagination?.size
    });
    return data;
  } catch (error) {
    console.error('获取评标模版列表失败:', error);
    return {
      records: [],
      total: 0,
      current: pagination?.page || 1,
      size: pagination?.size || 20,
    };
  }
};

// 打开新建模版抽屉
const openCreateDrawer = () => {
  templateDrawerRef.value?.show(null);
};

// 查看模版
const handleView = (row, type) => {
  templateDrawerRef.value?.show(row, type);
};

// 删除模版
const handleDelete = async (row) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除模版"${row.templateName}"吗？此操作不可恢复。`,
      '删除确认',
      {
        confirmButtonText: '确定删除',
        cancelButtonText: '取消',
        type: 'warning',
        confirmButtonClass: 'el-button--danger'
      }
    );

    await deleteBidEvaluationTemplate(row.id);
    
    ElMessage.success('删除成功');
    refreshTable();
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败');
    }
  }
};

// 刷新表格
const refreshTable = () => {
  state.page.page = 1;
  tableRef.value?.getData?.();
};
</script>

<style lang="scss" scoped>
.bid-evaluation-template-container {
  width: 100%;
  height: calc(100vh - 88px);
  
  :deep(.el-form .el-form-item:last-of-type) {
    margin-bottom: 24px !important;
  }
}
</style> 