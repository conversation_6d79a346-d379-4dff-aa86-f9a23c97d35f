// 标段信息
export interface LotInfo {
  id: string;
  sectionName: string;
}

// 评分项目
export interface ScoringItem {
  itemName: string;
  itemDetail: string;
  minScore?: string;
  maxScore?: string;
}

// 评标模版
export interface BidEvaluationTemplate {
  id?: string;
  templateCode: string;
  templateName: string;
  templateDescription?: string;
  scoringRules: ScoringRule[];
  createTime?: string;
  updateTime?: string;
}

export interface ScoringRule {
  sectionId: string;
  type: 'REVIEW' | 'SCORE';
  nodeName: string;
  itemName: string;
  itemDescription: string;
  maxScore?: number;
  minScore?: number;
  weight?: number;
  vetoThreshold?: number;
  totalScore?: number;
}
