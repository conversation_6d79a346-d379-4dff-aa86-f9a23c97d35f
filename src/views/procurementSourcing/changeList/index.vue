<template>
  <div class="change-list-container">
    <div class="back">
      <el-button
        type="text"
        size="small"
        icon="ArrowLeft"
        @click="router.back()"
      >
        返回
      </el-button>
    </div>

    <yun-pro-table
      ref="tableRef"
      v-model:pagination="state.page"
      v-model:searchData="state.searchData"
      :search-fields="searchFields"
      :layout="'whole'"
      :auto-height="true"
      :table-columns="tableColumns"
      :remote-method="remoteMethod"
      :table-props="tablePropsObj"
    >
      <template #tableHeaderLeft>
        <el-button
          type="primary"
          @click="handleAddChange"
        >
          新增变更
        </el-button>
      </template>
      <template #t_approvedStatus="{ row }">
        <el-tag
          v-if="row.approvedStatus"
          :type="getStatusTagType(row.approvedStatus)"
          size="small"
        >
          {{ getStatusText(row.approvedStatus) }}
        </el-tag>
        <span v-else>--</span>
      </template>
      <template #t_action="{ row }">
        <el-button
          type="text"
          size="small"
          @click="handleView(row, changeDrawerRef?.openView)"
        >
          查看
        </el-button>
        <el-button
          type="text"
          size="small"
          v-if="['APPROVE_REJECT', 'APPROVE_REVOKE'].includes(row.approvedStatus)"
          @click="handleEdit(row, changeDrawerRef?.openView)"
        >
          编辑
        </el-button>
        <el-button
          type="text"
          size="small"
          v-if="['TO_APPROVE', 'APPROVING'].includes(row.approvedStatus)"
          @click="handleRevoke(row, refreshTable)"
        >
          撤销
        </el-button>
      </template>
    </yun-pro-table>

    <!-- 变更抽屉 -->
    <ChangeDrawer
      ref="changeDrawerRef"
      @success="handleChangeSuccess"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { noticeChangePageApi } from '@/api/purchasing/noticeChange';
import useOptions from './hooks/useOptions';
import useHandle from './hooks/useHandle';
import ChangeDrawer from './components/ChangeDrawer.vue';

const router = useRouter();
const route = useRoute();

const { searchFields, tableColumns, tablePropsObj } = useOptions();

const { handleView, handleEdit, handleRevoke, getStatusTagType, getStatusText } = useHandle();

const tableRef = ref();
const changeDrawerRef = ref();

const state = reactive({
  page: {
    total: 0,
    page: 1,
    size: 20,
  },
  searchData: {
    projectId: route.query.projectId || route.query.projectCode, // 从路径获取项目ID
    changeBy: '',
    changeTypeEnums: [],
    approvedStatusList: [],
  },
  loading: false,
});

// 远程加载数据
const remoteMethod = async ({ searchData, pagination }: { searchData: any; pagination: any }) => {
  try {
    state.loading = true;
    const { data } = await noticeChangePageApi(
      {
        ...searchData,
        projectId: state.searchData.projectId, // 确保projectId传递
      },
      {
        current: pagination?.page || 1,
        size: pagination?.size || 20,
      }
    );
    return data;
  } catch (error) {
    console.error('加载变更列表失败:', error);
    return {
      records: [],
      total: 0,
    };
  } finally {
    state.loading = false;
  }
};

// 刷新表格
const refreshTable = () => {
  tableRef.value?.getData?.();
};

// 新增变更
const handleAddChange = () => {
  const projectInfo = {
    projectId: state.searchData.projectId,
    projectName: '当前项目名称', // TODO: 从实际数据获取
    projectCode: state.searchData.projectId,
  };
  changeDrawerRef.value?.openAdd(projectInfo);
};

// 变更操作成功回调
const handleChangeSuccess = () => {
  refreshTable();
};

onMounted(() => {});
</script>

<style scoped lang="scss">
.change-list-container {
  width: 100%;
  height: calc(100vh - 88px);
  background: #fff;
  padding: 20px;

  .back {
    margin-bottom: 16px;
  }
}
</style>
