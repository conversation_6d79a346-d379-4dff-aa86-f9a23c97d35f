import { useRouter } from 'vue-router';
import { ElMessage, ElMessageBox } from 'yun-design';
import { cancelApprovePlan } from '@/api/purchasing/plan.ts';

export default function useHandle() {
  const router = useRouter();

  // 查看变更详情
  const handleView = (row: any, openDrawer?: (data: any, viewMode: boolean) => void) => {
    if (openDrawer && typeof openDrawer === 'function') {
      // 如果传入了打开抽屉的方法，使用抽屉模式
      openDrawer(row, true);
    } else {
      // 否则跳转到详情页面
      router.push({
        path: '/changeList/detail',
        query: {
          id: row.id,
          type: 'view'
        }
      });
    }
  };

  // 编辑变更
  const handleEdit = (row: any, openDrawer?: (data: any, viewMode: boolean) => void) => {
    if (openDrawer && typeof openDrawer === 'function') {
      // 如果传入了打开抽屉的方法，使用抽屉模式
      openDrawer(row, true);
    } 
  };

  // 获取状态标签类型
  const getStatusTagType = (status: string): string => {
    const typeMap: Record<string, string> = {
      TO_APPROVE: 'info',
      APPROVING: 'warning',
      APPROVE: 'success',
      APPROVE_REJECT: 'danger',
      APPROVE_REVOKE: 'info'
    };
    return typeMap[status] || 'info';
  };

  // 获取状态文本
  const getStatusText = (status: string): string => {
    const textMap: Record<string, string> = {
      TO_APPROVE: '待审批',
      APPROVING: '审批中',
      APPROVE: '已审批',
      APPROVE_REJECT: '审批驳回',
      APPROVE_REVOKE: '审批撤销'
    };
    return textMap[status] || status;
  };

  // 获取变更类型文本
  const getChangeTypeText = (type: string): string => {
    const typeMap: Record<string, string> = {
      CHANGE_NOTICE: '变更公告',
      CHANGE_INVITE: '变更邀请函',
      CHANGE_PROJECT_TIME: '变更项目时间',
      CHANGE_OPENER: '变更开标人',
      CHANGE_BID: '变更定标',
      CHANGE_BID_PUBLICITY: '变更中标公示',
      CHANGE_BID_NOTICE: '变更中标公告'
    };
    return typeMap[type] || type;
  };

  // 撤销审批
  const handleRevoke = async (row: any, refreshCallback?: () => void) => {
    try {
      ElMessageBox.confirm('确定要撤销审批吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(() => {
          cancelApprovePlan({ bizType: 'SRM_CHANGE_AUDIT', bizKey: row.id }).then(() => {
            refreshCallback()
            ElMessage({
              type: 'success',
              message: '撤销审批成功！',
            });
          });
        })
        .catch(() => { });
    } catch (error) {
      if (error !== 'cancel') {
        console.error('撤销审批失败:', error);
      }
    }
  };

  return {
    handleView,
    handleEdit,
    getStatusTagType,
    getStatusText,
    getChangeTypeText,
    handleRevoke
  };
} 