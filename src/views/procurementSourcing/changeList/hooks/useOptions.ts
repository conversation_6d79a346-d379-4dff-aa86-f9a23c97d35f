import { computed } from 'vue';

export default function useOptions() {
  // 变更类型枚举
  const changeTypeEnums = [
    { label: '变更公告', value: 'CHANGE_NOTICE' },
    { label: '变更邀请函', value: 'CHANGE_INVITE' },
    { label: '变更项目时间', value: 'CHANGE_PROJECT_TIME' },
    { label: '变更文件', value: 'CHANGE_BID_DOC' },
    { label: '变更开标人', value: 'CHANGE_OPENER' },
    { label: '变更定标', value: 'CHANGE_BID' },
    { label: '变更中标公示', value: 'CHANGE_BID_PUBLICITY' },
    { label: '变更中标公告', value: 'CHANGE_BID_NOTICE' }
  ];

  // 审核状态枚举
  const approvedStatusList = [
    { label: '待审批', value: 'TO_APPROVE' },
    { label: '审批中', value: 'APPROVING' },
    { label: '审批通过', value: 'APPROVE' },
    { label: '审批驳回', value: 'APPROVE_REJECT' },
    { label: '审批撤销', value: 'APPROVE_REVOKE' }
  ];

  const searchFields = computed(() => {
    return [
      {
        label: '变更人',
        prop: 'changeBy',
        component: 'el-input',
        componentAttrs: {
          placeholder: '请输入变更人',
          clearable: true
        }
      },
      {
        label: '变更类型',
        prop: 'changeTypeEnums',
        component: 'el-select',
        subComponent: 'el-option',
        attrs: {
          multiple: true,
          filterable: true,
          clearable: true,
          collapseTags: true,
          placeholder: '请选择变更类型'
        },
        enums: changeTypeEnums
      },
      {
        label: '审核状态',
        prop: 'approvedStatusList',
        component: 'el-select',
        subComponent: 'el-option',
        attrs: {
          multiple: true,
          filterable: true,
          clearable: true,
          collapseTags: true,
          placeholder: '请选择审核状态'
        },
        enums: approvedStatusList
      }
    ];
  });

  const tableColumns = [
    {
      label: '序号',
      prop: 'index',
      type: 'index',
    },
    {
      label: '项目名称',
      prop: 'projectName',
      showOverflowTooltip: true
    },
    {
      label: '变更方式',
      prop: 'changeType',
      formatter: (row) => {
        const changeType = changeTypeEnums.find(item => item.value === row.changeType);
        return changeType ? changeType.label : row.changeType || '-';
      }
    },
    {
      label: '变更人',
      prop: 'changeByName',
    },
    {
      label: '变更时间',
      prop: 'changeTime',
      sortable: true
    },
    {
      label: '审核状态',
      prop: 'approvedStatus',
    },
    {
      label: '操作',
      prop: 'action',
      fixed: 'right'
    }
  ];

  const tablePropsObj = computed(() => {
    return {
      stripe: false,
      border: true,
      showHeader: true,
      defaultExpandAll: false,
      highlightCurrentRow: true,
			showTableSetting: true,
    };
  });

  return {
    searchFields,
    tableColumns,
    tablePropsObj,
    changeTypeEnums,
    approvedStatusList
  };
} 