<template>
  <div class="change-bid-result-container">
    <!-- 复用定标结果组件 -->
    <AwardResult
      ref="awardResultRef"
      :is-change-mode="true"
      :is-view-mode="props.isViewMode"
      :sectionId="props.sectionId"
      :form-data="awardFormData"
      @update:form-data="handleAwardDataUpdate"
      @change-submit="handleChangeSubmit"
    />
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import AwardResult from '@/views/procurementSourcing/biddingProcess/components/award/AwardResult/detail.vue';

interface Props {
  formData: any;
  isViewMode?: boolean;
  projectInfo?: any;
  sectionId?:string
}

const props = withDefaults(defineProps<Props>(), {
  isViewMode: false
});

const emit = defineEmits<{
  (e: 'update:form-data', data: any): void;
  (e: 'submit-success', data: any): void; // 新增：向父组件通知提交成功
}>();

// 组件引用
const awardResultRef = ref();

// 加载状态
const submitting = ref(false);

// 防止重复调用的标志
const isSubmitting = ref(false);

// 定标数据
const awardFormData = ref<any>({});

// 处理定标数据更新（移除自动同步，避免循环更新）
const handleAwardDataUpdate = (data: any) => {
  awardFormData.value = data;
  // 不再自动同步到父组件，避免循环更新
};

// 处理AwardResult组件的变更提交（从富文本编辑器触发）
const handleChangeSubmit = async (data: { content: string; submitData: any }) => {
  try {
    submitting.value = true;

    // 使用已经通过 assembleSubmitData 转换的数据
    const finalSubmitData = data.submitData;
  
    // 通知父组件提交成功
    emit('submit-success', finalSubmitData);
    
    // 同时更新表单数据
    emit('update:form-data', finalSubmitData);

  } catch (error) {
  } finally {
    submitting.value = false;
  }
};

// 表单验证（被父组件调用）
const validate = async () => {
  if (isSubmitting.value) {
    return false;
  }

  try {
    isSubmitting.value = true;
    
    // 1. 先验证定标数据
    const isAwardDataValid = await awardResultRef.value?.validateAwardData();
    if (!isAwardDataValid) {
      return false;
    }

    return false;
  } catch (error) {
    console.error('验证失败:', error);
    throw error;
  } finally {
    isSubmitting.value = false;
  }
};


// 导出验证方法和数据获取方法
defineExpose({
  validate,
});
</script>

<style scoped lang="scss">
.change-bid-result-container {
  .form-section {
    margin-bottom: 30px;
    padding: 20px;
    background: #fafafa;
    border-radius: 8px;
    
    .section-title {
      font-size: 14px;
      font-weight: 600;
      color: #333;
      margin-bottom: 20px;
      border-left: 3px solid #409eff;
      padding-left: 12px;
    }
  }

  .form-actions-wrapper {
    width: 100%;
    box-sizing: border-box;
    background-color: #fff;
    z-index: 1000;
    position: sticky;
    bottom: -20px;
    left: 0;
    right: 0;
    padding: 16px 24px;
    display: flex;
    gap: 12px;
    border-top: 1px solid var(--Color-Border-border-color-light, #e4e7ed);

    .form-actions {
      display: flex;
      justify-content: flex-start;
      gap: 12px;
      width: 100%;
    }
  }

  :deep(.el-form-item__label) {
    font-weight: 500;
    color: #666;
  }
}
</style> 