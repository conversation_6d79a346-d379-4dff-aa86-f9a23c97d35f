<template>
  <div class="change-winner-notification-container">
    <!-- 复用中标公告组件 -->
    <WinnerAnnouncement
      ref="winnerAnnouncementRef"
      :is-change-mode="true"
      :is-view-mode="props.isViewMode"
      :form-data="announcementFormData"
      @update:form-data="handleAnnouncementDataUpdate"
      @change-submit="handleChangeSubmit"
    />
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { ElMessage } from 'yun-design';
import WinnerAnnouncement from '@/views/procurementSourcing/biddingProcess/components/award/WinnerAnnouncement/index.vue';

interface Props {
  formData: any;
  isViewMode?: boolean;
  projectInfo?: any;
}

const props = withDefaults(defineProps<Props>(), {
  isViewMode: false,
});

const emit = defineEmits<{
  (e: 'update:form-data', data: any): void;
  (e: 'submit-success', data: any): void; // 向父组件通知提交成功
}>();

// 组件引用
const winnerAnnouncementRef = ref();

// 加载状态
const submitting = ref(false);

// 防止重复调用的标志
const isSubmitting = ref(false);

// 公告数据
const announcementFormData = ref<any>({});

// 处理公告数据更新
const handleAnnouncementDataUpdate = (data: any) => {
  announcementFormData.value = data;
  // 不再自动同步到父组件，避免循环更新
};

// 处理WinnerAnnouncement组件的变更提交（从富文本编辑器触发）
const handleChangeSubmit = async (data: { content: string; submitData: any }) => {
  try {
    submitting.value = true;

    // 使用已经通过 assembleSubmitData 转换的数据
    const finalSubmitData = data.submitData;

    // 通知父组件提交成功
    emit('submit-success', finalSubmitData);

    // 同时更新表单数据
    emit('update:form-data', finalSubmitData);
  } catch (error) {
  } finally {
    submitting.value = false;
  }
};

// 获取当前完整数据（用于父组件获取数据）
const getCurrentData = () => {
  const announcementData = winnerAnnouncementRef.value?.getAnnouncementData();
  return announcementData;
};

// 表单验证（被父组件调用）
const validate = async () => {
  if (isSubmitting.value) {
    return false;
  }

  try {
    isSubmitting.value = true;

    // 先验证公告数据
    const isAnnouncementDataValid = await winnerAnnouncementRef.value?.validateAnnouncementData();
    if (!isAnnouncementDataValid) {
      return false;
    }

    return false;
  } catch (error) {
    console.error('验证失败:', error);
    throw error;
  } finally {
    isSubmitting.value = false;
  }
};

// 触发提交流程的方法（供父组件在验证通过后调用）
const triggerSubmit = async () => {
  if (isSubmitting.value) {
    console.log('正在处理中，跳过重复调用');
    return;
  }

  try {
    isSubmitting.value = true;

    // 调用 WinnerAnnouncement 的提交方法，这会触发富文本编辑器
    await winnerAnnouncementRef.value?.handleSubmit();
  } catch (error) {
    console.error('触发提交失败:', error);
    throw error;
  } finally {
    isSubmitting.value = false;
  }
};

// 导出验证方法和数据获取方法
defineExpose({
  validate,
  getCurrentData,
  triggerSubmit,
});
</script>

<style scoped lang="scss">
.change-winner-notification-container {
  :deep(.el-form-item__label) {
    font-weight: 500;
    color: #666;
  }
}
</style>
