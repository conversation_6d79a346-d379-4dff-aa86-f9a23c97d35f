<template>
  <div class="change-project-time-container">
    <el-form
      ref="formRef"
      :model="localForm"
      :rules="formRules"
      label-width="140px"
    >
      <el-row :gutter="24" v-if="((['XJCG'].includes(props.sourcingType) && !isInviteMode) || (['JZTP', 'ZB'].includes(props.sourcingType) && isNeedPreQualification)) && isPublic">
        <el-col :span="12">
          <el-form-item
            label="报名起止时间"
            prop="registrationTimeRange"
          >
            <el-date-picker
              v-model="localForm.registrationTimeRange"
              type="datetimerange"
              range-separator="至"
              start-placeholder="开始时间"
              end-placeholder="结束时间"
              format="YYYY-MM-DD HH:mm:ss"
              value-format="YYYY-MM-DD HH:mm:ss"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="24" v-if="((['XJCG'].includes(props.sourcingType) && !isInviteMode) || (['JZTP', 'ZB'].includes(props.sourcingType) && isNeedPreQualification)) && isPublic">
        <el-col :span="12">
          <el-form-item
            label="资格预审起止时间"
            prop="auditTimeRange"
          >
            <el-date-picker
              v-model="localForm.auditTimeRange"
              type="datetimerange"
              range-separator="至"
              start-placeholder="开始时间"
              end-placeholder="结束时间"
              format="YYYY-MM-DD HH:mm:ss"
              value-format="YYYY-MM-DD HH:mm:ss"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="24" v-if="['XJCG'].includes(props.sourcingType)">
        <el-col :span="12">
          <el-form-item
            label="报价截止时间"
            prop="quoteEndTime"
          >
            <el-date-picker
              v-model="localForm.quoteEndTime"
              type="datetime"
              placeholder="请选择报价截止时间"
              format="YYYY-MM-DD HH:mm:ss"
              value-format="YYYY-MM-DD HH:mm:ss"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="24">
        <el-col :span="12">
          <el-form-item
            label="开标时间"
            prop="bidOpenTime"
          >
            <el-date-picker
              v-model="localForm.bidOpenTime"
              type="datetime"
              placeholder="请选择开标时间"
              format="YYYY-MM-DD HH:mm:ss"
              value-format="YYYY-MM-DD HH:mm:ss"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
      </el-row>
      <template v-if="['JZTP', 'ZB'].includes(props.sourcingType)">
        <el-row :gutter="24">
          <el-col :span="12">
            <el-form-item
              label="文件获取起止时间"
              prop="fileObtainTimeRange"
            >
              <el-date-picker
                v-model="localForm.fileObtainTimeRange"
                type="datetimerange"
                range-separator="至"
                start-placeholder="文件获取开始时间"
                end-placeholder="文件获取结束时间"
                style="width: 100%"
                format="YYYY-MM-DD HH:mm:ss"
                value-format="YYYY-MM-DD HH:mm:ss"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="24">
          <el-col :span="12">
            <el-form-item
              label="标书费缴纳起止时间"
              prop="bidDocPayTimeRange"
            >
              <el-date-picker
                v-model="localForm.bidDocPayTimeRange"
                type="datetimerange"
                range-separator="至"
                start-placeholder="标书费缴纳开始时间"
                end-placeholder="标书费缴纳结束时间"
                style="width: 100%"
                format="YYYY-MM-DD HH:mm:ss"
                value-format="YYYY-MM-DD HH:mm:ss"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="24">
          <el-col :span="12">
            <el-form-item
              label="投标起止时间"
              prop="quoteTimeRange"
            >
              <el-date-picker
                v-model="localForm.quoteTimeRange"
                type="datetimerange"
                range-separator="至"
                start-placeholder="投标开始时间"
                end-placeholder="投标结束时间"
                style="width: 100%"
                format="YYYY-MM-DD HH:mm:ss"
                value-format="YYYY-MM-DD HH:mm:ss"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </template>
      <el-row :gutter="24">
        <el-col :span="14">
          <el-form-item
            label="变更原因"
            prop="changeDigest"
            required
          >
            <el-input
              v-model="localForm.changeDigest"
              type="textarea"
              :rows="4"
              placeholder="请输入变更原因"
              maxlength="500"
              show-word-limit
            />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </div>
</template>

<script setup lang="ts">
import type { FormInstance } from 'element-plus'
import { getNoticeChangeDetailInfo } from '@/api/purchasing/noticeChange'
import { useBiddingStore } from '@/views/procurementSourcing/biddingProcess/stores';

import { getAnnouncementData} from '@/views/procurementSourcing/biddingProcess/api';

const biddingStore = useBiddingStore();

const noticeId = computed(() => biddingStore?.noticeId);
const isPublic = computed(() => biddingStore?.projectDetail?.inviteMethod !== 'INVITE');

interface PurchaseDateDemand {
  registerStartTime?: string
  registerEndTime?: string
  auditStartTime?: string
  auditEndTime?: string
  quoteStartTime?: string
  quoteEndTime?: string
  bidOpenTime?: string
  fileObtainStartTime?: string
  fileObtainEndTime?: string
  bidDocPayStartTime?: string
  bidDocPayEndTime?: string
}

interface FormData {
  changeDigest: string;
  purchaseDateDemand: PurchaseDateDemand;
  noticeId?: string;
  projectId?: string;
  changeType?: string;
}

interface Props {
  formData?: FormData
  isViewMode?: boolean
  projectInfo?: any
  sourcingType?: any
}

const props = withDefaults(defineProps<Props>(), {
  isViewMode: false,
});

const emit = defineEmits<{
  (e: 'update:form-data', data: FormData): void;
}>();

// 资格预审标识 1-需要 其他-不需要
const isNeedPreQualification = computed(() => [1, true]?.includes(biddingStore?.projectDetail?.preQualification));

// 邀请方式 'INVITE'-邀请 'PUBLICITY'-公开
const isInviteMode = computed(() => biddingStore?.projectDetail?.inviteMethod === 'INVITE');

// 表单引用
const formRef = ref<FormInstance>();

// 加载状态
const loading = ref(false);

// 本地表单数据 - 使用时间范围和单独时间字段
const localForm = reactive({
  registrationTimeRange: null as string[] | null, // [开始时间, 结束时间]
  auditTimeRange: null as string[] | null, // [开始时间, 结束时间]
  fileObtainTimeRange: null as string[] | null, // [开始时间, 结束时间]
  bidDocPayTimeRange: null as string[] | null, // [开始时间, 结束时间]
  quoteTimeRange: null as string[] | null, // [开始时间, 结束时间]
  quoteEndTime: '',
  bidOpenTime: '',
  changeDigest: '',
});

// 存储原始项目的开标时间（用于验证）
const originalBidOpenTime = ref<string>('');

// 表单验证规则
const formRules = {
  changeDigest: [{ required: true, message: '请输入变更原因', trigger: 'blur' }],
  quoteEndTime: [
    {
      validator: (rule: any, value: string, callback: any) => {
        if (!value || value.trim() === '') {
          callback();
          return;
        }

        // 获取用于比较的开标时间
        let compareBidOpenTime = '';
        
        // 如果输入框中的开标时间存在，使用输入框的值
        if (localForm.bidOpenTime && localForm.bidOpenTime.trim() !== '') {
          compareBidOpenTime = localForm.bidOpenTime;
        } else {
          // 如果开标时间输入框不存在，则取暂存的项目开标时间
          compareBidOpenTime = originalBidOpenTime.value;
        }

        // 如果都没有开标时间，则不进行验证
        if (!compareBidOpenTime || compareBidOpenTime.trim() === '') {
          callback();
          return;
        }

        // 比较时间
        const quoteEndTime = new Date(value).getTime();
        const bidOpenTime = new Date(compareBidOpenTime).getTime();

        if (quoteEndTime >= bidOpenTime) {
          callback(new Error('报价截止时间必须小于开标时间'));
        } else {
          callback();
        }
      },
      trigger: 'change'
    }
  ],
  bidOpenTime: [
    { required: true, message: '请选择开标时间', trigger: 'change' },
    {
      validator: (rule: any, value: string, callback: any) => {
        if (!value || value.trim() === '') {
          callback();
          return;
        }
        // 如果投标起止时间存在，验证开标时间必须大于投标截止时间
        if (['JZTP', 'ZB'].includes(props.sourcingType) && localForm.quoteTimeRange && localForm.quoteTimeRange.length > 1) {
          const quoteEndTime = new Date(localForm.quoteTimeRange[1]).getTime();
          const bidOpenTime = new Date(value).getTime();

          if (bidOpenTime <= quoteEndTime) {
            callback(new Error('开标时间必须大于投标截止时间'));
          } else {
            callback();
          }
        } else {
          callback();
        }
        // 如果报价截止时间存在，验证开标时间必须大于报价截止时间
        if (localForm.quoteEndTime && localForm.quoteEndTime.trim() !== '') {
          const quoteEndTime = new Date(localForm.quoteEndTime).getTime();
          const bidOpenTime = new Date(value).getTime();

          if (bidOpenTime <= quoteEndTime) {
            callback(new Error('开标时间必须大于报价截止时间'));
          } else {
            callback();
          }
        } else {
          callback();
        }
      },
      trigger: 'change'
    }
  ],
  registrationTimeRange: [
    { required: true, message: '请选择报名起止时间', trigger: 'change' },
  ],
  auditTimeRange: [
    { required: true, message: '请选择报名起止时间', trigger: 'change' },
  ],
  quoteTimeRange: [
    { required: true, message: '请选择报名起止时间', trigger: 'change' },
  ]
};

// 计算组装后的数据
const assembledData = computed<FormData>(() => {
  const purchaseDateDemand: PurchaseDateDemand = {};

  // 处理报名起止时间范围
  if (localForm.registrationTimeRange && Array.isArray(localForm.registrationTimeRange) && localForm.registrationTimeRange.length === 2) {
    purchaseDateDemand.registerStartTime = localForm.registrationTimeRange[0];
    purchaseDateDemand.registerEndTime = localForm.registrationTimeRange[1];
  }

  // 处理资格预审起止时间范围
  if (localForm.auditTimeRange && Array.isArray(localForm.auditTimeRange) && localForm.auditTimeRange.length === 2) {
    purchaseDateDemand.auditStartTime = localForm.auditTimeRange[0];
    purchaseDateDemand.auditEndTime = localForm.auditTimeRange[1];
  }

  // 处理单独的时间字段
  if (localForm.quoteEndTime && localForm.quoteEndTime.trim() !== '') {
    purchaseDateDemand.quoteEndTime = localForm.quoteEndTime;
  }

  if (localForm.bidOpenTime && localForm.bidOpenTime.trim() !== '') {
    purchaseDateDemand.bidOpenTime = localForm.bidOpenTime;
  }

  // 处理文件获取起止时间范围
  if (localForm.fileObtainTimeRange && Array.isArray(localForm.fileObtainTimeRange) && localForm.fileObtainTimeRange.length === 2) {
    purchaseDateDemand.fileObtainStartTime = localForm.fileObtainTimeRange[0]
    purchaseDateDemand.fileObtainEndTime = localForm.fileObtainTimeRange[1]
  }

  // 处理标书费缴纳起止时间范围
  if (localForm.bidDocPayTimeRange && Array.isArray(localForm.bidDocPayTimeRange) && localForm.bidDocPayTimeRange.length === 2) {
    purchaseDateDemand.bidDocPayStartTime = localForm.bidDocPayTimeRange[0]
    purchaseDateDemand.bidDocPayEndTime = localForm.bidDocPayTimeRange[1]
  }

  // 处理投标起止时间范围
  if (localForm.quoteTimeRange && Array.isArray(localForm.quoteTimeRange) && localForm.quoteTimeRange.length === 2) {
    purchaseDateDemand.quoteStartTime = localForm.quoteTimeRange[0]
    purchaseDateDemand.quoteEndTime = localForm.quoteTimeRange[1]
  }

  return {
    changeDigest: localForm.changeDigest,
    purchaseDateDemand,
  };
});

// 防止循环更新的标志
let isUpdating = false;

// 监听本地表单变化，同步到父组件
// watch(
//   localForm,
//   () => {
//     if (!isUpdating) {
//       emit('update:form-data', assembledData.value);
//     }
//   },
//   { deep: true }
// );

// 表单验证
const validate = async (): Promise<boolean> => {
  try {
    await formRef.value?.validate();
    return true;
  } catch (error) {
    throw error;
  }
};

// 封装时间数据回填函数
const fillTimeData = (purchaseDateDemand: PurchaseDateDemand) => {
  if (!purchaseDateDemand) return;

  // 单独的时间字段
  localForm.quoteEndTime = purchaseDateDemand.quoteEndTime || '';
  localForm.bidOpenTime = purchaseDateDemand.bidOpenTime || '';

  // 组装报名起止时间范围
  if (purchaseDateDemand.registerStartTime && purchaseDateDemand.registerEndTime) {
    localForm.registrationTimeRange = [purchaseDateDemand.registerStartTime, purchaseDateDemand.registerEndTime];
  } else {
    localForm.registrationTimeRange = null;
  }

  // 组装资格预审起止时间范围
  if (purchaseDateDemand.auditStartTime && purchaseDateDemand.auditEndTime) {
    localForm.auditTimeRange = [purchaseDateDemand.auditStartTime, purchaseDateDemand.auditEndTime];
  } else {
    localForm.auditTimeRange = null;
  }

  // 组装文件获取起止时间范围
  if (purchaseDateDemand.fileObtainStartTime && purchaseDateDemand.fileObtainEndTime) {
    localForm.fileObtainTimeRange = [purchaseDateDemand.fileObtainStartTime, purchaseDateDemand.fileObtainEndTime];
  } else {
    localForm.fileObtainTimeRange = null;
  }

  // 组装标书费缴纳起止时间范围
  if (purchaseDateDemand.bidDocPayStartTime && purchaseDateDemand.bidDocPayEndTime) {
    localForm.bidDocPayTimeRange = [purchaseDateDemand.bidDocPayStartTime, purchaseDateDemand.bidDocPayEndTime];
  } else {
    localForm.bidDocPayTimeRange = null;
  }

  // 组装投标起止时间范围
  if (purchaseDateDemand.quoteStartTime && purchaseDateDemand.quoteEndTime) {
    localForm.quoteTimeRange = [purchaseDateDemand.quoteStartTime, purchaseDateDemand.quoteEndTime];
  } else {
    localForm.quoteTimeRange = null;
  }

  // 存储原始开标时间（用于验证）
  if (purchaseDateDemand.bidOpenTime) {
    originalBidOpenTime.value = purchaseDateDemand.bidOpenTime;
  }
};
// 监听父组件数据变化
watch(
  () => props.formData,
  (newVal: FormData | undefined) => {
    if (newVal && !isUpdating) {
      isUpdating = true

      try {
        // 从组装格式解构到本地表单格式
        localForm.changeDigest = newVal.changeDigest || ''

        if (newVal.purchaseDateDemand) {
          const { purchaseDateDemand } = newVal

          // 组装报名起止时间范围
          if (purchaseDateDemand.registerStartTime && purchaseDateDemand.registerEndTime) {
            localForm.registrationTimeRange = [
              purchaseDateDemand.registerStartTime,
              purchaseDateDemand.registerEndTime
            ]
          } else {
            localForm.registrationTimeRange = null
          }

          // 组装资格预审起止时间范围
          if (purchaseDateDemand.auditStartTime && purchaseDateDemand.auditEndTime) {
            localForm.auditTimeRange = [
              purchaseDateDemand.auditStartTime,
              purchaseDateDemand.auditEndTime
            ]
          } else {
            localForm.auditTimeRange = null
          }
          // 组装文件获取起止时间范围
          if (purchaseDateDemand.fileObtainStartTime && purchaseDateDemand.fileObtainEndTime) {
            localForm.fileObtainTimeRange = [
              purchaseDateDemand.fileObtainStartTime,
              purchaseDateDemand.fileObtainEndTime
            ]
          } else {
            localForm.fileObtainTimeRange = null
          }

          // 组装标书费缴纳起止时间范围
          if (purchaseDateDemand.bidDocPayStartTime && purchaseDateDemand.bidDocPayEndTime) {
            localForm.bidDocPayTimeRange = [
              purchaseDateDemand.bidDocPayStartTime,
              purchaseDateDemand.bidDocPayEndTime
            ]
          } else {
            localForm.bidDocPayTimeRange = null
          }

          // 组装投标起止时间范围
          if (purchaseDateDemand.quoteStartTime && purchaseDateDemand.quoteEndTime) {
            localForm.quoteTimeRange = [
              purchaseDateDemand.quoteStartTime,
              purchaseDateDemand.quoteEndTime
            ]
          } else {
            localForm.quoteTimeRange = null
          }

          // 单独的时间字段
          localForm.quoteEndTime = purchaseDateDemand.quoteEndTime || ''
          localForm.bidOpenTime = purchaseDateDemand.bidOpenTime || ''
        }
      } finally {
        // 使用 nextTick 确保所有更新完成后再重置标志
        nextTick(() => {
          isUpdating = false
        })
      }
    }
  },
  { immediate: true, deep: true }
)

// 获取详情数据
const fetchDetailData = async () => {
  if (!props.formData || !props.formData.noticeId) {
    return;
  }

  try {
    loading.value = true;

    const params = {
      noticeId: props.formData.noticeId,
      projectId: props.formData.projectId || 0,
      changeTypeEnum: props.formData.changeType || '',
    };

    const response = await getNoticeChangeDetailInfo(params);

    if (response && response.data) {
      const detailData = response.data;

      // 填充变更原因
      localForm.changeDigest = detailData.changeDigest || '';

      // 使用封装的函数回填时间数据
      if (detailData.purchaseDateDemand) {
        fillTimeData(detailData.purchaseDateDemand);
      }
    }
  } catch (error) {
    console.error('获取详情数据失败:', error);
  } finally {
    loading.value = false;
  }
};

// 获取组装后的数据
const getFormData = (): FormData => {
  return assembledData.value;
};

// 初始化
onMounted(async () => {
  // 如果是查看模式，获取详情数据
  if (props.isViewMode) {
    await fetchDetailData();
  } else {
    if(!noticeId.value) { return; }
    const { data } = await getAnnouncementData(noticeId.value);
    // 使用封装的函数回填时间数据
    if (data && data.purchaseDateDemand) {
      fillTimeData(data.purchaseDateDemand);
    }
  }
});

// 监听父组件数据变化
watch(
  () => props.formData,
  (newVal: FormData | undefined) => {
    if (newVal && !isUpdating) {
      isUpdating = true;

      try {
        // 从组装格式解构到本地表单格式
        localForm.changeDigest = newVal.changeDigest || '';

        if (newVal.purchaseDateDemand) {
          // 使用封装的函数回填时间数据
          fillTimeData(newVal.purchaseDateDemand);
        }
      } finally {
        // 使用 nextTick 确保所有更新完成后再重置标志
        nextTick(() => {
          isUpdating = false;
        });
      }
    }
  },
  { immediate: true, deep: true }
);


// 导出验证方法和数据获取方法
defineExpose({
  validate,
  getFormData,
  fetchDetailData,
  assembledData,
});
</script>

<style scoped lang="scss">
.change-project-time-container {
  .form-section {
    margin-bottom: 30px;
    padding: 20px;
    background: #fafafa;
    border-radius: 8px;

    .section-title {
      font-size: 14px;
      font-weight: 600;
      color: #333;
      margin-bottom: 20px;
      border-left: 3px solid #409eff;
      padding-left: 12px;
    }
  }

  .time-tip {
    color: #e6a23c;
    font-size: 12px;
    margin-top: 4px;
  }

  :deep(.el-form-item__label) {
    font-weight: 500;
    color: #666;
  }

  :deep(.el-date-editor) {
    width: 100%;
  }
}
</style>
