<template>
  <el-drawer
    v-model="visible"
    size="80%"
    v-if="visible && !isApprovalDetail"
    destroy-on-close
    :before-close="handleClose"
    custom-class="custom-drawer"
  >
    <div class="project-header">
      <div class="left-section">{{ projectName || '--' }}</div>
      <div class="right-section">
        <el-icon
          class="cursor-pointer"
          @click.stop="visible = false"
          ><Close
        /></el-icon>
      </div>
    </div>

    <div class="change-drawer-container">
      <!-- 变更类型选择 -->
      <div
        v-if="!isViewMode"
        class="change-type-section"
      >
        <div class="section-title">变更方式</div>

        <el-form
          ref="changeTypeFormRef"
          :model="changeForm"
          :rules="changeTypeRules"
          label-width="100px"
          class="change-type-form"
        >
          <el-form-item
            label="变更方式"
            prop="changeType"
            required
          >
            <el-radio-group
              v-model="changeForm.changeType"
              :disabled="isViewMode"
              @change="handleChangeTypeChange"
            >
              <el-radio
                v-for="item in changeTypeOptions"
                :key="item.value"
                :label="item.value"
              >
                {{ item.label }}
              </el-radio>
            </el-radio-group>
          </el-form-item>
        </el-form>
      </div>

      <!-- 变更基础信息 -->
      <div
        v-if="changeForm.changeType"
        class="change-basic-section"
      >
        <div class="section-title">变更内容</div>
        <component
          :is="currentChangeComponent"
          ref="changeComponentRef"
          :form-data="changeForm"
          :is-view-mode="isViewMode"
          :project-info="projectInfo"
          :sectionId="changeForm.sectionId"
          :sourcing-type="projectDetail.sourcingType"
          @update:form-data="handleFormDataUpdate"
          @submit-success="handleSave(false)"
        />
      </div>
    </div>

    <!-- 抽屉底部操作按钮 -->
    <template #footer>
      <div class="drawer-footer">
        <el-button @click="handleClose">
          {{ isViewMode ? '关闭' : '取消' }}
        </el-button>
        <el-button
          v-if="['APPROVE_REJECT', 'APPROVE_REVOKE'].includes(changeForm.approvedStatus || '') || !isViewMode"
          type="primary"
          :loading="saveLoading"
          @click="handleSave"
        >
          保存
        </el-button>
      </div>
    </template>
  </el-drawer>

  <!-- 变更基础信息-兼容审批流查看详情 -->
  <div v-if="changeForm.changeType && isApprovalDetail && projectId">
    <div
      class="project-header"
      style="padding-left: 0"
    >
      <div class="left-section">{{ projectName || '--' }}</div>
      <div class="right-section"></div>
    </div>
    <div class="change-basic-section">
      <div class="section-title">变更内容</div>
      <component
        :is="currentChangeComponent"
        ref="changeComponentRef"
        :sectionId="changeForm.sectionId"
        :form-data="{ ...changeForm }"
        :is-view-mode="isViewMode"
        :project-info="projectInfo"
        :sourcing-type="projectDetail.sourcingType"
        @update:form-data="handleFormDataUpdate"
        @submit-success="handleSave(false)"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, defineAsyncComponent, onMounted, nextTick } from 'vue';
import { ElMessage } from 'yun-design';
import { addNoticeChange, updateNoticeChange } from '@/api/purchasing/noticeChange';
import { useBiddingStore } from '@/views/procurementSourcing/biddingProcess/stores';
import { SOURCING_TYPES } from '@/views/procurementSourcing/biddingProcess/constants/sourcing-types';
import { useRoute } from 'vue-router';

const biddingStore = useBiddingStore();

const noticeId = computed(() => biddingStore?.noticeId);
const projectId = computed(() => biddingStore?.projectId);

// 异步导入各种变更类型组件
const ChangeAnnouncement = defineAsyncComponent(() => import('./ChangeAnnouncement.vue'));
const ChangeInvite = defineAsyncComponent(() => import('./ChangeInvite.vue'));
const ChangeProjectTime = defineAsyncComponent(() => import('./ChangeProjectTime.vue'));
const ChangeFile = defineAsyncComponent(() => import('./ChangeFile.vue'));
const ChangeOpener = defineAsyncComponent(() => import('./ChangeOpener.vue'));
const ChangeBidResult = defineAsyncComponent(() => import('./ChangeBidResult.vue'));
const ChangeWinnerPublicity = defineAsyncComponent(() => import('./ChangeWinnerPublicity.vue'));
const ChangeWinnerNotification = defineAsyncComponent(() => import('./ChangeWinnerNotification.vue'));

interface Props {
  projectInfo?: {
    projectId?: string | number;
    projectName?: string;
    projectCode?: string;
  };
}

const props = withDefaults(defineProps<Props>(), {
  projectInfo: () => ({}),
});

const emit = defineEmits<{
  (e: 'success'): void;
}>();


const projectDetail = computed(() => biddingStore?.projectDetail || {});

// 抽屉状态
const visible = ref(false);
const isViewMode = ref(false);
const saveLoading = ref(false);

// 表单引用
const changeTypeFormRef = ref();
const changeComponentRef = ref();

// 变更类型选项（原始）
const allChangeTypeOptions = [
  { label: '变更公告', value: 'CHANGE_NOTICE' },
  { label: '变更邀请函', value: 'CHANGE_INVITE' },
  { label: '变更项目时间', value: 'CHANGE_PROJECT_TIME' },
  { label: '变更文件', value: 'CHANGE_BID_DOC' },
  { label: '变更开标人', value: 'CHANGE_OPENER' },
  { label: '变更定标', value: 'CHANGE_BID' },
  { label: '变更中标公示', value: 'CHANGE_BID_PUBLICITY' },
  { label: '变更中标公告', value: 'CHANGE_BID_NOTICE' },
];
const projectName = computed(() => projectDetail.value?.projectName || '');
const sourcingType = computed(() => projectDetail.value?.sourcingType || '');

// 变更表单数据
const changeForm = reactive({
  id: null,
  changeType: '',
  projectId: props.projectInfo.projectId,
  projectName: props.projectInfo.projectName,
  projectCode: props.projectInfo.projectCode,
  changeBy: '',
  changeByName: '',
  changeTime: '',
  changeStatus: '',
  sectionId: undefined,
  noticeId: undefined,
  approvedStatus: undefined,
  // 其他字段根据变更类型动态添加
});

const changeTypeOptions = computed(() => {
  let filteredOptions = allChangeTypeOptions;

  // 根据邀请方式过滤
  if (projectDetail.value?.inviteMethod === 'PUBLICITY') {
    // PUBLICITY: 不包含变更邀请函
    filteredOptions = filteredOptions.filter(opt => opt.value !== 'CHANGE_INVITE');

    // 如果有变更公告选项，默认选择
    if (filteredOptions.some(opt => opt.value === 'CHANGE_NOTICE')) {
      nextTick(() => {
        !isApprovalDetail.value && (changeForm.changeType = 'CHANGE_NOTICE');
      });
    }
  } else {
    // 其他: 不包含变更公告、变更中标公告
    filteredOptions = filteredOptions.filter(opt =>
      !['CHANGE_NOTICE', 'CHANGE_BID_NOTICE'].includes(opt.value)
    );

    // 如果有变更邀请函选项，默认选择
    if (filteredOptions.some(opt => opt.value === 'CHANGE_INVITE')) {
      nextTick(() => {
        !isApprovalDetail.value && (changeForm.changeType = 'CHANGE_INVITE');
      });
    }
  }

  if (sourcingType.value === SOURCING_TYPES.XJCG) {
    filteredOptions = filteredOptions.filter(opt => opt.value !== 'CHANGE_BID_DOC');
  }

  return filteredOptions;
});

// 表单验证规则
const changeTypeRules = {
  changeType: [{ required: true, message: '请选择变更方式', trigger: 'change' }],
};

// 当前变更组件
const currentChangeComponent = computed(() => {
  const componentMap: Record<string, any> = {
    CHANGE_NOTICE: ChangeAnnouncement,
    CHANGE_INVITE: ChangeInvite,
    CHANGE_PROJECT_TIME: ChangeProjectTime,
    CHANGE_BID_DOC: ChangeFile,
    CHANGE_OPENER: ChangeOpener,
    CHANGE_BID: ChangeBidResult,
    CHANGE_BID_PUBLICITY: ChangeWinnerPublicity,
    CHANGE_BID_NOTICE: ChangeWinnerNotification,
  };
  return componentMap[changeForm.changeType] || null;
});

// 变更类型改变事件
const handleChangeTypeChange = (value: string) => {
  // 清理之前的表单数据，保留基础信息
  const basicInfo = {
    id: changeForm.id,
    changeType: value,
    projectId: changeForm.projectId,
    projectName: changeForm.projectName,
    projectCode: changeForm.projectCode,
    changeBy: changeForm.changeBy,
    changeByName: changeForm.changeByName,
    changeTime: changeForm.changeTime,
    changeStatus: changeForm.changeStatus,
    sectionId: changeForm.sectionId,
    noticeId: changeForm.noticeId,
    approvedStatus: changeForm.approvedStatus,
  };
  Object.assign(changeForm, basicInfo);
};

// 表单数据更新事件
const handleFormDataUpdate = (data: any) => {
  Object.assign(changeForm, data);
};

// 保存变更
const handleSave = async (isValid = true) => {
  try {
    // 验证变更类型表单
    await changeTypeFormRef.value?.validate();

    // 验证具体变更组件表单
    if(changeForm.changeType === 'CHANGE_BID_DOC') {
      const isValid = await changeComponentRef.value?.cnDocumentRef?.mainFormRef?.validate();
      if(!isValid) {
        return false;
      }
    } else {
      if (changeComponentRef.value?.validate && isValid) {
        const isValid = await changeComponentRef.value.validate();
        if (!isValid) {
          return false;
        }
      }
      if(changeForm.changeType === 'CHANGE_PROJECT_TIME') {
        Object.assign(changeForm, changeComponentRef.value?.assembledData);
      }
    }
    saveLoading.value = true;

    // 准备提交数据
    let submitData = {
      ...(changeForm.changeType === 'CHANGE_BID_DOC'
      ? changeComponentRef.value?.cnDocumentRef?.fileData || {}
      : changeForm),
      noticeId: noticeId.value,
      projectId: projectId.value,
      changeTypeEnum: changeForm.changeType,
      changeTime: new Date().toISOString(),
    };

    let result;
    if (changeForm.id) {
      // 更新变更
      result = await updateNoticeChange(submitData);
    } else {
      // 新增变更
      result = await addNoticeChange(submitData);
    }

    if (result.code === 0) {
      ElMessage.success('保存成功');
      visible.value = false;
      emit('success');
    }
  } catch (error) {
    console.log(error, 'error')
  } finally {
    saveLoading.value = false;
  }
};

// 关闭抽屉
const handleClose = () => {
  visible.value = false;
  // 重置表单数据
  resetForm();
};

// 重置表单
const resetForm = () => {
  Object.assign(changeForm, {
    id: null,
    changeType: '',
    projectId: props.projectInfo.projectId,
    projectName: props.projectInfo.projectName,
    projectCode: props.projectInfo.projectCode,
    changeBy: '',
    changeByName: '',
    changeTime: '',
    changeStatus: 'DRAFT',
    sectionId: undefined,
    noticeId: undefined,
    approvedStatus: undefined,
  });
  changeTypeFormRef.value?.clearValidate();
};

// 打开抽屉 - 新增模式
const openAdd = (projectInfo?: any) => {
  isViewMode.value = false;
  if (projectInfo) {
    changeForm.projectId = projectInfo.projectId;
    changeForm.projectName = projectInfo.projectName;
    changeForm.projectCode = projectInfo.projectCode;
    changeForm.changeType = projectDetail.value?.inviteMethod === 'INVITE' ? 'CHANGE_INVITE' : 'CHANGE_NOTICE';
    changeForm.sectionId = undefined;
  }
  visible.value = true;
};

// 打开抽屉 - 查看/编辑模式
const openView = (data: any, viewMode = true) => {
  isViewMode.value = viewMode;
  changeForm.changeType = data.changeType;
  changeForm.projectId = projectId.value;
  changeForm.noticeId = noticeId.value;
  changeForm.sectionId = data.sectionId;
  changeForm.approvedStatus = data.approvedStatus;
  visible.value = true;
};

// 导出方法
defineExpose({
  openAdd,
  openView,
});

const route = useRoute();
// 判断场景是否是来源于查询审批流详情
const isApprovalDetail = computed(() => route.query?.scene === 'APPROVAL_IN_CHANGE');
const changeType = computed<string>(() => route.query?.changeType as string);
onMounted(async () => {
  await biddingStore.initProjectDetail();

  if (isApprovalDetail.value) {
    const changeTypeKeys = changeTypeOptions.value?.map((item) => item.value);
    if (!changeTypeKeys?.includes(changeType.value) || !changeType.value) {
      Object.assign(changeForm, {
        id: null,
        changeType: null,
      });
      ElMessage.error('变更类型有误');
      return;
    }
    isViewMode.value = true;

    Object.assign(changeForm, {
      id: null,
      changeType: changeType.value,
      projectId: projectDetail.value.id,
      projectName: projectDetail.value.projectName,
      projectCode: projectDetail.value.projectCode,
      noticeId: noticeId.value,
    });
  }
});
</script>

<style scoped lang="scss">
.change-drawer-container {
  padding: 12px 20px;

  .change-type-section,
  .change-basic-section {
    margin-bottom: 30px;

    .section-title {
      color: var(--Color-Text-text-color-primary, #1d2129);
      font-family: 'PingFang SC';
      font-size: 16px;
      font-style: normal;
      font-weight: 600;
      line-height: 24px;
      padding-left: 10px;
      position: relative;
      margin-bottom: 16px;

      &::before {
        content: '';
        position: absolute;
        left: 0;
        top: 50%;
        transform: translateY(-50%);
        width: 2px;
        height: 14px;
        background: var(--Color-Primary-color-primary, #0069ff);
      }
    }
  }

  .project-info {
    background: #f5f7fa;
    padding: 12px 16px;
    border-radius: 6px;
    margin-bottom: 20px;

    .project-label {
      font-weight: 500;
      color: #666;
    }

    .project-value {
      color: #333;
    }
  }

  .change-type-form {
    :deep(.el-radio-group) {
      display: flex;
      flex-wrap: wrap;
      gap: 16px;

      .el-radio {
        margin-right: 0;
        white-space: nowrap;
      }
    }
  }
}

:deep(.el-drawer) {
  .el-drawer__body {
    padding: 0;
    overflow-y: auto;
  }
}

.project-header {
  padding: 20px 24px 10px 24px;
  display: flex;
  justify-content: space-between;
  align-items: center;

  .left-section {
    color: #170961;
    font-family: 'PingFang SC';
    font-size: 20px;
    font-style: normal;
    font-weight: 500;
    line-height: 28px;
    padding: 8px 12px;
    background: linear-gradient(90deg, #faf4ff 0%, #f4faff 100%);
  }

  .right-section {
    flex: 1;
    display: flex;
    justify-content: flex-end;
    align-items: center;
    color: #7e8694;
    font-size: 18px;
  }
}
</style>
