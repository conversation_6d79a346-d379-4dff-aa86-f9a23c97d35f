<template>
  <div class="change-opener-container">
    <el-form
      ref="formRef"
      :model="localForm"
      :rules="formRules"
      label-width="140px"
    >
      <el-row :gutter="24">
        <el-col :span="12">
          <el-form-item
            label="开标人"
            prop="bidOpener"
            required
          >
            <el-select
              v-model="localForm.bidOpener"
              placeholder="请选择开标人"
              style="width: 100%"
              filterable
              clearable
            >
              <el-option
                v-for="item in userOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="24">
        <el-col :span="14">
          <el-form-item
            label="变更原因"
            prop="changeDigest"
            required
          >
            <el-input
              v-model="localForm.changeDigest"
              type="textarea"
              :rows="4"
              placeholder="请输入变更原因"
              maxlength="500"
              show-word-limit
            />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </div>
</template>

<script setup lang="ts">
import type { FormInstance } from 'element-plus'
import { getUserListApi } from '@/api/purchasing/proposal'
import { getNoticeChangeDetailInfo } from '@/api/purchasing/noticeChange'
import { useBiddingStore } from '@/views/procurementSourcing/biddingProcess/stores';

const biddingStore = useBiddingStore();

const projectDetail = computed(() => biddingStore?.projectDetail || {});

interface FormData {
  bidOpener: string
  changeDigest: string
}

interface Props {
  formData?: FormData
  isViewMode?: boolean
  projectInfo?: any
}

const props = withDefaults(defineProps<Props>(), {
  isViewMode: false,
})

const emit = defineEmits<{
  (e: 'update:form-data', data: FormData): void
}>()

// 表单引用
const formRef = ref<FormInstance>()

// 本地表单数据
const localForm = reactive({
  bidOpener: '',
  changeDigest: '',
})

// 用户选项列表
const userOptions = ref<Array<{ value: string; label: string }>>([])

// 加载状态
const loading = ref(false)

// 获取用户列表
const getUserList = async () => {
  try {
    const res = await getUserListApi()
    if (res.code === 0) {
      userOptions.value = res.data.records?.map((item: any) => ({
        value: item.userId,
        label: item.name,
      }))
    }
  } catch (error) {
    console.error('获取用户列表失败:', error)
  }
}

// 获取详情数据
const fetchDetailData = async () => {
  if (!props.formData || !props.formData.noticeId) {
    return
  }

  try {
    loading.value = true
    
    const params = {
      noticeId: props.formData.noticeId,
      projectId: props.formData.projectId,
      changeTypeEnum: props.formData.changeType,
    }

    const response = await getNoticeChangeDetailInfo(params)
    
    if (response && response.data) {
      const detailData = response.data
      
      localForm.bidOpener = detailData.bidOpener
      
      // 填充变更原因
      localForm.changeDigest = detailData.changeDigest || ''
    }
  } catch (error) {
    console.error('获取详情数据失败:', error)
  } finally {
    loading.value = false
  }
}

// 表单验证规则
const formRules = {
  bidOpener: [{ required: true, message: '请选择开标人', trigger: 'change' }],
  changeDigest: [{ required: true, message: '请输入变更原因', trigger: 'blur' }],
}

// 防止循环更新的标志
let isUpdating = false

// 监听本地表单变化，同步到父组件
watch(
  localForm,
  () => {
    if (!isUpdating) {
      emit('update:form-data', { ...localForm })
    }
  },
  { deep: true }
)

// 监听父组件数据变化
watch(
  () => props.formData,
  (newVal: FormData | undefined) => {
    if (newVal && !isUpdating) {
      isUpdating = true
      
      try {
        localForm.bidOpener = newVal.bidOpener || ''
        localForm.changeDigest = newVal.changeDigest || ''
      } finally {
        nextTick(() => {
          isUpdating = false
        })
      }
    }
  },
  { immediate: true, deep: true }
)

// 表单验证
const validate = async (): Promise<boolean> => {
  try {
    await formRef.value?.validate()
    return true
  } catch (error) {
    throw error
  }
}

// 获取表单数据
const getFormData = (): FormData => {
  return { ...localForm }
}

// 组件挂载时获取用户列表
onMounted(async () => {
  await getUserList()
  // 如果是查看模式，获取详情数据
  if (props.isViewMode) {
    await fetchDetailData()
  } else {
    localForm.bidOpener = projectDetail.value.projectMemberList?.find((item: any) => item.role === "PROJECT_LEADER")?.userId;
  }
})

// 导出验证方法和数据获取方法
defineExpose({
  validate,
  getFormData,
  fetchDetailData,
})
</script>

<style scoped lang="scss">
.change-opener-container {
  .form-section {
    margin-bottom: 30px;
    padding: 20px;
    background: #fafafa;
    border-radius: 8px;

    .section-title {
      font-size: 14px;
      font-weight: 600;
      color: #333;
      margin-bottom: 20px;
      border-left: 3px solid #409eff;
      padding-left: 12px;
    }
  }

  :deep(.el-form-item__label) {
    font-weight: 500;
    color: #666;
  }
}
</style>
