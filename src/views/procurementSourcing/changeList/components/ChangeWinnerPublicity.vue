<template>
  <div class="change-winner-publicity-container">
    <!-- 复用中标公示组件 -->
    <WinnerPublicity
      ref="winnerPublicityRef"
      :is-change-mode="true"
      :is-view-mode="props.isViewMode"
      :form-data="publicityFormData"
      @update:form-data="handlePublicityDataUpdate"
      @change-submit="handleChangeSubmit"
    />
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { ElMessage } from 'yun-design';
import WinnerPublicity from '@/views/procurementSourcing/biddingProcess/components/award/WinnerPublicity/index.vue';

interface Props {
  formData: any;
  isViewMode?: boolean;
  projectInfo?: any;
}

const props = withDefaults(defineProps<Props>(), {
  isViewMode: false
});

const emit = defineEmits<{
  (e: 'update:form-data', data: any): void;
  (e: 'submit-success', data: any): void; // 向父组件通知提交成功
}>();

// 组件引用
const winnerPublicityRef = ref();

// 加载状态
const submitting = ref(false);

// 防止重复调用的标志
const isSubmitting = ref(false);

// 公示数据
const publicityFormData = ref<any>({});

// 处理公示数据更新
const handlePublicityDataUpdate = (data: any) => {
  publicityFormData.value = data;
  // 不再自动同步到父组件，避免循环更新
};

// 处理WinnerPublicity组件的变更提交（从富文本编辑器触发）
const handleChangeSubmit = async (data: { content: string; submitData: any }) => {
  try {
    submitting.value = true;

    // 使用已经通过 assembleSubmitData 转换的数据
    const finalSubmitData = data.submitData;
  
    // 通知父组件提交成功
    emit('submit-success', finalSubmitData);
    
    // 同时更新表单数据
    emit('update:form-data', finalSubmitData);

  } catch (error) {
  } finally {
    submitting.value = false;
  }
};

// 获取当前完整数据（用于父组件获取数据）
const getCurrentData = () => {
  const publicityData = winnerPublicityRef.value?.getPublicityData();
  return publicityData;
};

// 表单验证（被父组件调用）
const validate = async () => {
  if (isSubmitting.value) {
    return false;
  }

  try {
    isSubmitting.value = true;
    
    // 先验证公示数据
    const isPublicityDataValid = await winnerPublicityRef.value?.validatePublicityData();
    if (!isPublicityDataValid) {
      return false;
    }

    return false;
  } catch (error) {
    console.error('验证失败:', error);
    throw error;
  } finally {
    isSubmitting.value = false;
  }
};

// 触发提交流程的方法（供父组件在验证通过后调用）
const triggerSubmit = async () => {
  if (isSubmitting.value) {
    console.log('正在处理中，跳过重复调用');
    return;
  }

  try {
    isSubmitting.value = true;
    
    // 调用 WinnerPublicity 的提交方法，这会触发富文本编辑器
    await winnerPublicityRef.value?.handleSubmit();
  } catch (error) {
    console.error('触发提交失败:', error);
    throw error;
  } finally {
    isSubmitting.value = false;
  }
};

// 导出验证方法和数据获取方法
defineExpose({
  validate,
  getCurrentData,
  triggerSubmit
});
</script>

<style scoped lang="scss">
.change-winner-publicity-container {
  :deep(.el-form-item__label) {
    font-weight: 500;
    color: #666;
  }
}
</style> 