/*
 * @Author: chentinghong <EMAIL>
 * @Date: 2025-05-09 14:43:34
 * @LastEditors: chentinghong <EMAIL>
 * @LastEditTime: 2025-05-26 17:47:29
 * @FilePath: \fe-dcrg-admin\src\views\preview\hooks\useDict.jsx
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import { ref, computed } from 'vue';
import { useDict as useGlobalDict } from '@/hooks/dict';

interface DictItem {
	label: string;
	value: string;
	[key: string]: any;
}

const dictMap = ref<Record<string, DictItem[]>>({});

const getDict = computed(() => (str: string) => {
	if (!str) return [];
	if (dictMap.value[str]) return dictMap.value[str];
	const value = useGlobalDict(str)?.[str]?.value?.map((e: any) => ({
		...e,
		value: e.value?.toString(),
	}));
	if (value?.length) dictMap.value[str] = value;
	return value || [];
});

export default getDict;
