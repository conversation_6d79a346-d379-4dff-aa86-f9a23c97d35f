import { ref, computed } from 'vue';
import { ElImage } from 'element-plus';
import { useApiConfig } from '@/hooks/useApiConfig';
import { useApiOption } from '../hooks/useOption';
// no unused imports
import getDict from '../hooks/useDict';

import moment from 'moment';

const { getApiOptions, apiDataMap } = useApiOption();
// 递归查找匹配的组件
function findMatchingWidget(widgetList, fieldName) {
	for (const widget of widgetList) {
		// 检查当前组件
		if (widget.options?.name === fieldName) {
			return widget;
		}
		// 递归检查子组件
		for (let key in widget) {
			if (Array.isArray(widget[key])) {
				const found = findMatchingWidget(widget[key], fieldName);
				if (found) return found;
			}
		}
		// 不检查子表组件
		if (widget.type === 'enhanced-table') {
			continue;
		}
	}
	return null;
}

const optionMap = {};
export const useTable = (tableInfo, widgetList) => {
	const columns = computed(() => {
		const arr = [];
		tableInfo.value?.fieldList.forEach((item) => {
			if (item.gridItem === '0' || item.attrName === 'id') return;
			const columnsItem = {
				label: item.fieldComment || item.attrName,
				prop: item.attrName,
				sortable: item.gridSort === '1',
				showOverflowTooltip: !['picture-upload', 'file-upload'].includes(item.formType),
			};
			if (['picture-upload', 'file-upload'].includes(item.formType)) {
				columnsItem.width = 200;
			}
			const openWindow = (url) => {
				window.open(url, '_blank');
			};
			columnsItem.render = ({ row }) => {
				const value = row[item.attrName];
				if (item.formType === 'table') {
					const mainKey = item.ext?.tableConfig.find((e) => e.isShow)?.prop || '';
					return Array.isArray(value) ? value.map((e) => e[mainKey]).join('、') : '--';
				}
				if (item.formType === 'picture-upload') {
					if (!value?.length) return '--';
					const imgs = value.map((e) => e.url);
					return (
						<div style={{ overflow: 'auto', width: '100%' }}>
							{imgs.map((e, i) => (
								<ElImage
									src={e}
									preview-src-list={imgs}
									initial-index={i}
									style={{ height: '22px', marginRight: '5px' }}
									preview-teleported
								/>
							))}
						</div>
					);
				}
				if (item.formType === 'file-upload') {
					if (!value?.length) return '--';
					return (
						<div style="color: var(--el-color-primary);">
							{value?.map((e, i) => (
								<div
									style="cursor: pointer;"
									onClick={() => openWindow(e.url)}
								>
									{e.name}
								</div>
							))}
						</div>
					);
				}
				if (item.formType === 'rich-editor') {
					return <div v-html={value}></div>;
				}
				if (item.formType === 'time') {
					return value ? moment(value).format('HH:mm:ss') : '--';
				}
				if (item.formType.includes('range')) {
					return Array.isArray(value) ? value.join('-') : '--';
				}
				if (item.formType === 'switch') {
					return (
						<el-switch
							modelValue={value}
							disabled
						></el-switch>
					);
				}
				const matchingWidget = findMatchingWidget(widgetList.value || [], item.fieldName);
				let options = [];
				if (matchingWidget) {
					if (matchingWidget.options?.dataSourceType === 'dict') {
						options = getDict.value(item.fieldDict) || [];
					} else if (matchingWidget.options?.dataSourceType === 'custom') {
						options = matchingWidget.options?.optionItems || [];
					} else if (matchingWidget.options?.dataSourceType === 'api' && matchingWidget.options?.apiId) {
						options = getApiOptions(matchingWidget.options.apiId).value;
					}
				}
				if (['radio', 'checkbox', 'select'].includes(item.formType)) {
					// 根据组件的数据源类型设置选项
					if (Array.isArray(value)) {
						return value.map((item2) => options?.find((item3) => item3.value == item2)?.label).join('、');
					}
					return options?.find((item2) => item2.value == value)?.label || '--';
				}
				if (item.formType === 'cascader') {
					if (!value) return '--';
					const config = apiDataMap.value[matchingWidget?.options?.apiId] || {};
					const { labelField, valueField, childrenField, disabledField } = config;
					// 如果是数组，说明是多选
					if (Array.isArray(value)) {
						return value
							.map((v) => {
								// 如果是字符串，说明是逗号分隔的路径
								if (typeof v === 'string') {
									const path = v.split(',');
									let currentOptions = options;
									const labels = [];

									// 遍历路径找到对应的标签
									for (const val of path) {
										const option = currentOptions.find((opt) => opt[valueField || 'value'] == val);
										if (option) {
											labels.push(option[labelField || 'label']);
											currentOptions = option[childrenField || 'children'] || [];
										}
									}
									return labels.join(' / ');
								}
								return v;
							})
							.join('、');
					}

					// 如果是字符串，说明是单选
					if (typeof value === 'string') {
						// 递归查找函数
						const findPath = (options, targetValue, path = []) => {
							for (const option of options) {
								// 如果找到目标值，返回完整路径
								if (option[valueField || 'value'] == targetValue) {
									return [...path, option[labelField || 'label']];
								}
								// 如果有子节点，递归查找
								if (option[childrenField || 'children'] && option[childrenField || 'children'].length > 0) {
									const result = findPath(option[childrenField || 'children'], targetValue, [...path, option[labelField || 'label']]);
									if (result) return result;
								}
							}
							return null;
						};

						// 查找完整路径
						const path = findPath(options, value);
						return path ? path.join(' / ') : '--';
					}

					return '--';
				}
				return value || '--';
			};
			arr.push(columnsItem);
		});
		return [
			{
				type: 'selection',
				width: 52,
				fixed: 'left',
			},
			...arr,
			{
				label: '操作',
				prop: 'action',
				width: 150,
				fixed: 'right',
			},
		];
	});
	const searchFields = computed(() => {
		const arr = [];
		tableInfo.value?.fieldList.forEach((field) => {
			if (field.queryItem === '0') return;
			let columnsItem;
			if (field.queryFormType === 'select') {
				// 查找匹配的组件
				const matchingWidget = findMatchingWidget(widgetList.value || [], field.fieldName);

				// 根据组件的数据源类型设置选项
				let options = [];
				if (matchingWidget) {
					if (matchingWidget.options?.dataSourceType === 'dict') {
						options = getDict.value(field.fieldDict) || [];
					} else if (matchingWidget.options?.dataSourceType === 'custom') {
						options = matchingWidget.options?.optionItems || [];
					} else if (matchingWidget.options?.dataSourceType === 'api') {
						options = getApiOptions(matchingWidget.options?.apiId).value || [];
					}
				}

				columnsItem = {
					label: `${field.fieldComment ? field.fieldComment : ` ${field.attrName}`}`,
					prop: field.attrName,
					component: 'el-select',
					componentAttrs: {
						style: { width: '100%' },
						clearable: true,
						multiple: true,
						filterable: true,
						collapseTags: true,
						placeholder: `请选择${field.fieldComment ? field.fieldComment : ` ${field.attrName}`}`,
					},
					subComponent: 'el-option',
					options: options,
				};
			} else if (field.queryFormType === 'date') {
				columnsItem = {
					label: `${field.fieldComment ? field.fieldComment : ` ${field.attrName}`}`,
					prop: field.attrName,
					component: 'el-date-picker',
					componentAttrs: {
						type: 'daterange',
						startPlaceholder: '开始时间',
						endPlaceholder: '结束时间',
						valueFormat: 'YYYY-MM-DD',
						format: 'YYYY-MM-DD',
						clearable: true,
					},
				};
			} else if (field.queryFormType === 'datetime') {
				columnsItem = {
					label: `${field.fieldComment ? field.fieldComment : ` ${field.attrName}`}`,
					prop: field.attrName,
					component: 'el-date-picker',
					componentAttrs: {
						type: 'datetimerange',
						valueFormat: 'YYYY-MM-DD HH:mm:ss',
						format: 'YYYY-MM-DD HH:mm:ss',
						clearable: true,
						startPlaceholder: '开始时间',
						endPlaceholder: '结束时间',
						defaultTime: [new Date(2000, 1, 1, 0, 0, 0), new Date(2000, 2, 1, 23, 59, 59)],
					},
				};
			} else {
				columnsItem = {
					label: `${field.fieldComment ? field.fieldComment : ` ${field.attrName}`}`,
					prop: field.attrName,
					component: 'el-input',
					componentAttrs: {
						clearable: true,
						placeholder: `请输入${field.fieldComment ? field.fieldComment : ` ${field.attrName}`}`,
					},
				};
			}

			arr.push(columnsItem);
		});

		return [...arr];
	});
	return {
		columns,
		searchFields,
	};
};
