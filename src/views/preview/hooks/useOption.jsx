import { ref, computed } from 'vue';
// import { fetchList } from '@/api/lowcode/lowcode-api-source/index';
// import request from '/@/utils/request';
import getDict from './useDict';

// const optionsMap = {};
export const apiConfigList = ref([]);
export const apiDataMap = ref({});
// let p = null;
// function fetchApiList() {
//   return fetchList(
//     {},
//     {
//       current: 1,
//       size: 1000,
//     }
//   ).then((res) => {
//     apiConfigList.value = res.data?.records || [];
//   });
// }
// function buildParams(params) {
//   const result = {};
//   params.forEach((param) => {
//     if (param.type === 'static') {
//       result[param.key] = param.value;
//     }
//   });
//   return result;
// }
function setValueOfString(options) {
  if (!Array.isArray(options)) return [];
  return options.map((e) => {
    if (typeof e === 'string') {
      return { label: e, value: e.toString() };
    }
    const item = { ...e };
    item.value = item.value.toString();
    if (item.children) {
      item.children = setValueOfString(item.children);
    }
    return item;
  });
}
export function useApiOption() {
  // if (!p) {
  //   p = fetchApiList();
  // }
  function getApiOptions(id) {
    console.log('getApiOptions', id);
    // if (optionsMap[id]) return optionsMap[id];
    // const options = ref([]);
    // optionsMap[id] = options;
    // p.then(() => {
    //   const apiConfig = apiConfigList.value.find((api) => api.id === id);
    //   apiDataMap.value[id] = apiConfig;
    //   if (!apiConfig) {
    //     optionsMap[id] = options;
    //     return;
    //   }
    //   const { apiName, params: p = [], method = 'get', dataPath, labelField, valueField, childrenField, disabledField } = apiConfig;
    //   let data = {};
    //   let params = {};
    //   if (method.toLowerCase() === 'post') {
    //     data = buildParams(p);
    //   } else {
    //     params = buildParams(p);
    //   }
    //   request({
    //     url: apiName.startsWith('/api/') ? apiName.replace('/api/', '/') : apiName,
    //     method,
    //     params,
    //     data,
    //   }).then((res) => {
    //     let result = res;
    //     if (dataPath) {
    //       const paths = dataPath.split('.');
    //       for (const path of paths) {
    //         result = result?.[path];
    //         if (result === undefined) break;
    //       }
    //     }
    //     options.value = Array.isArray(result)
    //       ? result.map((e) => ({
    //           ...e,
    //           disabled: disabledField ? e[disabledField] : false,
    //           label: labelField ? e[labelField] : '',
    //           value: valueField ? e[valueField] : '',
    //           children: childrenField ? e[childrenField] : undefined,
    //         }))
    //       : [];
    //     return options;
    //   });
    // });
    // return options;
    return {
      value: [],
    };
  }
  const getOptions = computed(() => (widget) => {
    // 根据数据源类型获取options
    let options = [];

    if (['checkbox', 'radio', 'select', 'cascader'].includes(widget.type) && widget.options?.dataSourceType === 'dict' && widget.options?.dictType) {
      options = getDict.value(widget.options.dictType) || [];
    } else if (['checkbox', 'radio', 'select', 'cascader'].includes(widget.type) && widget.options?.dataSourceType === 'api') {
      options = widget.options.apiId ? getApiOptions(widget.options.apiId).value : [];
    } else {
      options = setValueOfString(widget.options.optionItems || []);
    }
    return options;
  });

  const casProps = computed(() => (widget) => {
    if (widget.options?.dataSourceType !== 'api' || !widget.options?.apiId) return {};
    const config = apiDataMap.value[widget.options.apiId] || {};
    const { labelField, valueField, childrenField, disabledField } = config;
    return {
      label: labelField,
      value: valueField,
      children: childrenField,
      disabled: disabledField,
    };
  });
  return { getApiOptions, getOptions, casProps, apiConfigList, apiDataMap };
}
