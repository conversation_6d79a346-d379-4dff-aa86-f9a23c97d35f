import { ref, computed } from 'vue';

// 类型定义
interface Column {
	prop: string;
	label: string;
	type: string;
	span?: number;
	attrs?: {
		options?: Array<{ label: string; value: any }>;
		type?: string;
		effect?: string;
		min?: number;
		max?: number;
		step?: number;
		showInput?: boolean;
		range?: boolean;
		showStops?: boolean;
		format?: string;
		currency?: string;
		decimals?: number;
		showIndex?: boolean;
		columns?: Column[];
		dictType?: string;
		dataSourceType?: 'custom' | 'dict' | 'api';
		apiId?: string;
	};
}

interface Group {
	title: string;
	columns: Column[];
}

interface WidgetOption {
	name: string;
	label: string;
	type?: string;
	defaultValue?: any;
	optionItems?: Array<{ label: string; value: any }>;
	dictType?: string;
	format?: string;
	[key: string]: any;
}

interface Widget {
	type: string;
	formItemFlag?: boolean;
	options: WidgetOption;
	widgetList?: Widget[];
	cols?: Array<{ widgetList: Widget[] }>;
}

interface DetailProps {
	widgetList?: Widget[];
}

// 获取列类型配置
function getColumnType(type: string | undefined, options: Array<{ label: string; value: any }> = [], widgetOptions: any = {}) {
	if (!type) return { type: 'text' };

	type ColumnTypeAttrs = {
		options?: Array<{ label: string; value: any }>;
		type?: string;
		effect?: string;
		min?: number;
		max?: number;
		step?: number;
		showInput?: boolean;
		range?: boolean;
		showStops?: boolean;
		format?: string;
		currency?: string;
		decimals?: number;
		dataSourceType?: 'custom' | 'dict' | 'api';
		apiId?: string;
		dictType?: string;
	};

	const typeMap: {
		[key: string]: {
			type: string;
			attrs?: ColumnTypeAttrs;
		};
	} = {
		select: {
			type: 'enums',
			attrs: {
				options,
				dataSourceType: widgetOptions.dataSourceType as 'custom' | 'dict' | 'api' | undefined,
				apiId: widgetOptions.apiId,
				dictType: widgetOptions.dictType,
			},
		},
		radio: {
			type: 'enums',
			attrs: {
				options,
				dataSourceType: widgetOptions.dataSourceType as 'custom' | 'dict' | 'api' | undefined,
				apiId: widgetOptions.apiId,
				dictType: widgetOptions.dictType,
			},
		},
		checkbox: {
			type: 'enums',
			attrs: {
				options,
				dataSourceType: widgetOptions.dataSourceType as 'custom' | 'dict' | 'api' | undefined,
				apiId: widgetOptions.apiId,
				dictType: widgetOptions.dictType,
			},
		},
		cascader: {
			type: 'enums',
			attrs: {
				options,
				dataSourceType: widgetOptions.dataSourceType as 'custom' | 'dict' | 'api' | undefined,
				apiId: widgetOptions.apiId,
				dictType: widgetOptions.dictType,
			},
		},
		switch: { type: 'switch' },
		datetime: {
			type: 'datetime',
			attrs: {
				format: widgetOptions.format || 'YYYY-MM-DD HH:mm:ss',
				range: widgetOptions.type === 'datetimerange',
			},
		},
		'datetime-range': {
			type: 'datetime',
			attrs: {
				format: widgetOptions.format || 'YYYY-MM-DD HH:mm:ss',
				range: true,
			},
		},
		time: {
			type: 'datetime',
			attrs: {
				format: widgetOptions.format || 'HH:mm:ss',
				range: widgetOptions.type === 'timerange',
			},
		},
		'time-range': {
			type: 'datetime',
			attrs: {
				format: widgetOptions.format || 'HH:mm:ss',
				range: true,
			},
		},
		'date-range': {
			type: 'datetime',
			attrs: {
				format: widgetOptions.format || 'YYYY-MM-DD',
				range: true,
			},
		},
		money: {
			type: 'money',
			attrs: {
				currency: widgetOptions.currency || '¥',
				decimals: widgetOptions.decimals || 2,
			},
		},
		percent: { type: 'percent' },
		tag: {
			type: 'tag',
			attrs: {
				type: widgetOptions.type || 'info',
				effect: widgetOptions.effect || 'light',
			},
		},
		'picture-upload': { type: 'images' },
		'file-upload': { type: 'files' },
		'rich-editor': { type: 'editor' },
		slider: {
			type: 'slider',
			attrs: {
				min: widgetOptions.min ?? 0,
				max: widgetOptions.max ?? 100,
				step: widgetOptions.step ?? 1,
				showInput: widgetOptions.showInput ?? false,
				range: widgetOptions.range ?? false,
				showStops: widgetOptions.showStops ?? false,
			},
		},
	};

	return typeMap[type] || { type: 'text' };
}

// 下划线转驼峰
function toCamelCase(str: string): string {
	return str.replace(/_([a-z])/g, (_, letter) => letter.toUpperCase());
}

// 处理明细表的列配置
function processTableColumns(widgets: Widget[] = []): Column[] {
	const columns: Column[] = [];

	widgets.forEach((widget) => {
		if (widget.formItemFlag) {
			const { type, options } = widget;
			const { name, label, optionItems = [], dictType, dataSourceType, apiId } = options;
			const columnType = getColumnType(type, optionItems, options);

			// 处理字典类型或API数据源
			if (dictType || dataSourceType === 'api') {
				columnType.attrs = {
					...columnType.attrs,
					dictType,
					dataSourceType,
					apiId,
				};
			}

			columns.push({
				prop: toCamelCase(name.split('.')[1]),
				label: label,
				type: columnType.type,
				attrs: columnType.attrs,
			});
		}
	});

	return columns;
}

// 递归提取表单项并按组分类
function extractFormGroups(widgets: Widget[] = []): Group[] {
	const groups: Group[] = [];
	let currentGroup: Group | null = null;

	function processWidget(widget: Widget) {
		// 处理明细表组件
		if (widget.type === 'enhanced-table') {
			if (currentGroup) {
				groups.push(currentGroup);
			}
			// 创建明细表分组
			currentGroup = {
				title: widget.options.label || '明细信息',
				columns: [
					{
						prop: toCamelCase(widget.options.name),
						label: '',
						type: 'table',
						attrs: {
							showIndex: widget.options.showIndex,
							columns: processTableColumns(widget.widgetList || []),
						},
					},
				],
			};
			groups.push(currentGroup);
			currentGroup = null;
			return;
		}

		// 处理分组标题
		if (widget.type === 'static-group') {
			if (currentGroup) {
				groups.push(currentGroup);
			}
			currentGroup = {
				title: widget.options.textContent || '',
				columns: [],
			};
			return;
		}

		// 确保有一个当前分组
		if (!currentGroup) {
			currentGroup = {
				title: '',
				columns: [],
			};
		}

		if (widget.formItemFlag) {
			const { type, options } = widget;
			const { name, label, optionItems = [], dictType, dataSourceType, apiId } = options;
			const columnType = getColumnType(type, optionItems, options);

			// 处理字典类型或API数据源
			if (dictType || dataSourceType === 'api') {
				columnType.attrs = {
					...columnType.attrs,
					dictType,
					dataSourceType,
					apiId,
				};
			}
			currentGroup.columns.push({
				prop: toCamelCase(name),
				label: label,
				type: columnType.type,
				attrs: columnType.attrs,
			});
		}

		// 处理嵌套的 widgetList
		if (widget.widgetList && widget.type !== 'enhanced-table') {
			widget.widgetList.forEach(processWidget);
		}

		// 处理 grid 布局中的列
		if (widget.cols) {
			widget.cols.forEach((col) => {
				if (col.widgetList) {
					col.widgetList.forEach(processWidget);
				}
			});
		}
	}

	widgets.forEach(processWidget);

	// 添加最后一个分组
	if (currentGroup) {
		groups.push(currentGroup);
	}

	return groups;
}

// 生成groups字符串的方法
export function generateGroupsStr(widgetList: Widget[] = []): string {
	const rawGroups = extractFormGroups(widgetList);

	const generateGroups = rawGroups.map((group) => ({
		...group,
		columns: group.columns.map((column) => {
			const { dictType, dataSourceType, apiId } = column.attrs || {};
			if (dictType || dataSourceType === 'api') {
				return {
					...column,
					attrs: {
						...column.attrs,
						options: [],
					},
				};
			}
			return column;
		}),
	}));

	// 格式化并处理字符串
	let str = JSON.stringify(generateGroups, null, 2)
		// 先处理属性名的引号
		.replace(/"([^"]+)":/g, '$1:')
		// 处理字符串值的引号
		.replace(/"([^"]+)"/g, (match, p1) => {
			// 如果值中包含单引号或特殊字符，使用双引号
			if (p1.includes("'") || p1.includes('"') || p1.includes('\\')) {
				return `"${p1.replace(/"/g, '\\"')}"`;
			}
			// 如果值是空字符串，使用单引号
			if (p1 === '') {
				return "''";
			}
			// 否则使用单引号
			return `'${p1}'`;
		})
		// 处理特殊情况：修复可能的引号不匹配
		.replace(/:\s*(['"])(.*?)\1/g, (match, quote, content) => {
			// 确保值中的引号与外部引号匹配
			if (content.includes(quote)) {
				const otherQuote = quote === '"' ? "'" : '"';
				return `: ${otherQuote}${content}${otherQuote}`;
			}
			return match;
		})
		// 移除多余的空行
		.replace(/\n\s+\n/g, '\n')
		// 确保数组中的逗号后面有空格
		.replace(/,([^\s])/g, ', $1')
		// 修复可能存在的引号不匹配问题
		.replace(/(['"])([^'"]*?)(['"])/g, (match, open, content, close) => {
			if (open !== close) {
				return `${open}${content}${open}`;
			}
			return match;
		});

	return str;
}

export const useDetail = (props: DetailProps) => {
	const visible = ref(false);
	const formRef = ref(null);
	const form = ref<Record<string, any>>({});

	const groups = computed(() => {
		const list = props.widgetList ? extractFormGroups(props.widgetList) : [];
		// 处理字典数据
		return list.map((group) => ({
			...group,
			columns: group.columns.map((column) => {
				// 如果是字典类型或API数据源，初始化options为空数组
				const { dictType, dataSourceType, apiId } = column.attrs || {};
				if (dictType || dataSourceType === 'api') {
					return {
						...column,
						attrs: {
							...column.attrs,
							options: [],
						},
					};
				}
				return column;
			}),
		}));
	});

	return {
		groups, // 用于平台预览
		generateGroupsStr, // 用于代码生成
		visible,
		formRef,
		form,
	};
};
