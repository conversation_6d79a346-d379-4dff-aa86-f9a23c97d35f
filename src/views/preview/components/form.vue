<template>
  <yun-drawer
    v-model="visible"
    destroy-on-close
    modal
    custom-class="form-drawer"
    close-on-click-modal
    confirm-button-text="确定"
    cancel-button-text="取消"
    :confirm-button-disabled="loading"
    :title="title"
    :size="size"
    @confirm="confirmHandler"
    @close="closeHandler"
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      :label-position="formConfig?.labelPosition || 'top'"
      :label-width="formConfig?.labelWidth"
    >
      <form-widget-renderer :widget-list="widgetList" :form="form" :rules="rules" />
    </el-form>
  </yun-drawer>
</template>

<script setup lang="ts">
import { ref, computed, defineExpose, nextTick } from 'vue';
import { addObj, putObj, getObj } from '@/api/gen/list';
import { useUserInfo } from '@/stores/userInfo';
import moment from 'moment';
import FormWidgetRenderer from './form-widget-renderer.vue';

interface Props {
  tableInfo: {
    functionName: string;
    fieldList: Array<{
      attrName: string;
      formType: string;
    }>;
  };
  formConfig?: {
    labelPosition?: string;
    labelWidth?: string | number;
  };
  widgetList?: Array<any>;
  tables?: Array<any>;
}

const props = withDefaults(defineProps<Props>(), {
  tableInfo: () => ({
    functionName: '',
    fieldList: [],
  }),
  tables: () => [],
  formConfig: () => ({}),
  widgetList: () => [],
});

// 状态管理
const visible = ref(false);
const loading = ref(false);
const isEdit = ref(false);
const formRef = ref();
const form = ref<Record<string, any>>({});
const rules = ref<Record<string, any>>({});

// 计算属性
const title = computed(() => (isEdit.value ? '编辑' : '新增'));
const size = computed(() => 'X-large');

// 用户信息
const userStore = useUserInfo();
const emits = defineEmits(['getData']);

// 关闭处理
function closeHandler() {
  visible.value = false;
}

// 格式化表单数据
async function formatFrom(row: Record<string, any>) {
  form.value = {};
  if (!row.id) return;
  const res = await getObj(props.tableInfo.functionName, row.id);
  form.value = res.data;
  const { fieldList } = props.tableInfo || {};
  fieldList.forEach((e) => {
    if (e.formType === 'checkbox' && !row[e.attrName]) {
      form.value[e.attrName] = [];
    }
    if (e.formType === 'time' && row[e.attrName]) {
      form.value[e.attrName] = moment(row[e.attrName]).format('HH:mm:ss');
    }
    if (e.formType === 'cascader' && row[e.attrName]) {
      if (Array.isArray(row[e.attrName])) {
        form.value[e.attrName] = row[e.attrName].map((e) => {
          if (Array.isArray(e)) {
            return e;
          }
          return e.split(',');
        });
      }
    }
  });
  console.log('form.value', form.value);
}
function toCamelCase(str: string) {
  return str.replace(/_([a-z])/g, (_, c) => c.toUpperCase());
}
// 格式化提交参数
function formatParams() {
  const { name, userId } = userStore.userInfos?.user || {};
  const baseParams = {};
  const { fieldList } = props.tableInfo || {};
  fieldList.forEach((e) => {
    if (e.formType === 'time' && form.value[e.attrName]) {
      baseParams[e.attrName] = moment('2000-01-01 ' + form.value[e.attrName]).format(
        'YYYY-MM-DD HH:mm:ss'
      );
    }
    if (e.formType === 'cascader' && form.value[e.attrName]) {
      if (Array.isArray(form.value[e.attrName])) {
        baseParams[e.attrName] = form.value[e.attrName].map((e) => {
          if (Array.isArray(e)) {
            return e.join(',');
          }
          return e;
        });
      }
    }
  });
  props.tables.forEach((e) => {
    const { tableName } = e;
    if (form.value[tableName]) {
      form.value[toCamelCase(tableName) + 'List'] = form.value[tableName];
      delete form.value[tableName];
    }
  });
  if (!isEdit.value) {
    return {
      ...form.value,
      ...baseParams,
    };
  }

  return {
    ...form.value,
    ...baseParams,
  };
}

// 确认处理
async function confirmHandler(done: () => void, loading: { value: boolean }) {
  try {
    await formRef.value?.validate();
    loading.value = true;
    const api = isEdit.value ? putObj : addObj;
    const params = formatParams();
    await api(props.tableInfo.functionName, params);
    done();
    emits('getData', form.value);
  } catch (error) {
    console.error('Form validation or submission failed:', error);
  } finally {
    loading.value = false;
  }
}

// 显示表单
function show(row?: Record<string, any>) {
  visible.value = true;
  isEdit.value = !!row;
  formatFrom(row || {});
  nextTick(() => {
    formRef.value?.clearValidate();
  });
}

defineExpose({
  show,
});
</script>

<style scoped lang="scss">
.form-drawer {
  :deep(.el-form .el-form-item:last-of-type) {
    margin-bottom: 24px !important;
  }
}
</style>

<style lang="scss">
.form-drawer {
  .el-form .el-form-item:last-of-type {
    margin-bottom: 24px !important;
  }
}
</style>
