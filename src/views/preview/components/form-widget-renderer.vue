<template>
  <template v-for="widget in widgetList">
    <!-- 容器类型组件 -->
    <template v-if="widget.category === 'container'">
      <!-- 栅格布局 -->
      <el-row v-if="widget.type.startsWith('grid-col')" :gutter="widget.options?.gutter">
        <el-col v-for="col in widget.cols" :key="col.id" :span="col.options?.span">
          <form-widget-renderer
            :key="col.id"
            :widget-list="col.widgetList"
            :form="form"
            :rules="rules"
          />
        </el-col>
      </el-row>

      <!-- 标签页 -->
      <el-tabs v-else-if="widget.type === 'tab'" :type="widget.displayType">
        <el-tab-pane v-for="tab in widget.tabs" :key="tab.id" :label="tab.options?.label">
          <form-widget-renderer
            :key="tab.id"
            :widget-list="tab.widgetList"
            :form="form"
            :rules="rules"
          />
        </el-tab-pane>
      </el-tabs>

      <!-- 表格布局 -->
      <el-row v-else-if="widget.type === 'table'" :gutter="widget.options?.gutter">
        <template v-for="row in widget.rows">
          <el-col v-for="cell in row.cols" :key="cell.id" :span="cell.options?.span">
            <form-widget-renderer
              :key="cell.id"
              :widget-list="cell.widgetList"
              :form="form"
              :rules="rules"
            />
          </el-col>
        </template>
      </el-row>

      <!-- 明细表 enhanced-table -->
      <template v-else-if="widget.type === 'enhanced-table'">
        <static-group :textContent="widget.options.label"></static-group>
        <div class="w-full overflow-x-auto">
          <el-table
            :data="form[toCamelCase(widget.options.name) + 'List'] || []"
            :border="true"
            stripe
            class="w-full dark:bg-gray-800"
            :header-cell-class-name="'!bg-gray-50 dark:!bg-gray-700'"
            style="min-width: 600px"
            :show-overflow-tooltip="false"
          >
            <!-- 序号列 -->
            <el-table-column
              v-if="widget.options.showIndex"
              type="index"
              label="序号"
              width="60"
              align="center"
              fixed
            />
            <!-- 动态字段列 -->
            <el-table-column
              v-for="colWidget in widget.widgetList"
              :key="colWidget.id"
              :label="colWidget.options.label"
              :prop="toCamelCase(colWidget.options.name)"
              :min-width="
                colWidget.options.columnWidth || widget.options.columnWidth || '160px'
              "
            >
              <template #default="{ row, $index }">
                <form-widget-renderer
                  :widget-list="[colWidget]"
                  :form="row"
                  :rules="rules"
                  :parent-widget="widget"
                />
              </template>
            </el-table-column>
            <!-- 操作列：删除 -->
            <el-table-column label="操作" fixed="right" width="80" align="center">
              <template #default="{ $index }">
                <el-button
                  type="danger"
                  size="small"
                  icon="Delete"
                  @click="
                    handleRemoveRow(toCamelCase(widget.options?.name + 'List'), $index)
                  "
                  >删除</el-button
                >
              </template>
            </el-table-column>
          </el-table>
        </div>
        <!-- 添加按钮 -->
        <el-button
          style="width: 100%; margin-top: 5px"
          :icon="Plus"
          @click="handleAddRow(toCamelCase(widget.options?.name) + 'List')"
        >
          添加
        </el-button>
      </template>
    </template>

    <!-- 表单字段组件 -->
    <template v-else>
      <template v-if="parentWidget && parentWidget.type === 'enhanced-table'">
        <el-input
          v-if="widget.type === 'input'"
          v-model="form[toCamelCase(getEndStr(widget.options.name))]"
          v-bind="widget.options"
        />
        <el-input
          v-else-if="widget.type === 'textarea'"
          v-model="form[toCamelCase(getEndStr(widget.options.name))]"
          type="textarea"
          v-bind="widget.options"
        />
        <el-input-number
          v-else-if="widget.type === 'number'"
          v-model="form[toCamelCase(getEndStr(widget.options.name))]"
          v-bind="widget.options"
        />
        <el-radio-group
          v-else-if="widget.type === 'radio'"
          v-model="form[toCamelCase(getEndStr(widget.options.name))]"
          v-bind="widget.options"
        >
          <el-radio
            v-for="option in getOptions(widget)"
            :key="option.value"
            :label="option.value"
          >
            {{ option.label }}
          </el-radio>
        </el-radio-group>
        <el-checkbox-group
          v-else-if="widget.type === 'checkbox'"
          v-model="form[toCamelCase(getEndStr(widget.options.name))]"
          v-bind="widget.options"
        >
          <el-checkbox
            v-for="option in getOptions(widget)"
            :key="option.value"
            :label="option.value"
          >
            {{ option.label }}
          </el-checkbox>
        </el-checkbox-group>
        <el-select
          v-else-if="
            widget.type === 'select' && widget.options?.dataSourceType !== 'datasource'
          "
          style="width: 100%"
          v-model="form[toCamelCase(getEndStr(widget.options.name))]"
          :filterable="true"
          v-bind="widget.options"
        >
          <el-option
            v-for="option in getOptions(widget)"
            :key="option.value"
            :label="option.label"
            :value="option.value"
          />
        </el-select>
        <data-select
          v-else-if="
            widget.type === 'select' && widget.options?.dataSourceType === 'datasource'
          "
          v-model="form[toCamelCase(getEndStr(widget.options.name))]"
          :data-source="widget.options.datasource"
          :multiple="widget.options.multiple"
          :placeholder="widget.options.placeholder"
          v-bind="widget.options"
        />
        <el-date-picker
          v-else-if="
            ['date', 'datetime', 'date-range', 'datetime-range'].includes(widget.type)
          "
          v-model="form[toCamelCase(getEndStr(widget.options.name))]"
          :type="widget.options.type"
          :class="[!!widget.options.autoFullWidth ? 'auto-full-width' : '']"
          :readonly="widget.options.readonly"
          :disabled="widget.options.disabled"
          :size="widget.options.size || 'default'"
          :clearable="widget.options.clearable"
          :editable="widget.options.editable"
          :format="widget.options.format || getDateFormat(widget.options.type)"
          :value-format="getDateFormat(widget.options.type)"
          :placeholder="widget.options.placeholder"
          :start-placeholder="widget.options.startPlaceholder"
          :end-placeholder="widget.options.endPlaceholder"
          :defaultTime="getDefaultTime(widget.options.type)"
        />
        <el-time-picker
          v-else-if="['time', 'time-range'].includes(widget.type)"
          v-model="form[toCamelCase(getEndStr(widget.options.name))]"
          :class="[!!widget.options.autoFullWidth ? 'auto-full-width' : '']"
          :disabled="widget.options.disabled"
          :readonly="widget.options.readonly"
          :size="widget.options.size || 'default'"
          :clearable="widget.options.clearable"
          :editable="widget.options.editable"
          :format="widget.options.format || getDateFormat(widget.type)"
          value-format="HH:mm:ss"
          :placeholder="widget.options.placeholder"
          :start-placeholder="widget.options.startPlaceholder"
          :end-placeholder="widget.options.endPlaceholder"
          :is-range="widget.type === 'time-range'"
        />
        <el-switch
          v-else-if="widget.type === 'switch'"
          v-model="form[toCamelCase(getEndStr(widget.options.name))]"
          v-bind="widget.options"
        />
        <el-rate
          v-else-if="widget.type === 'rate'"
          v-model="form[toCamelCase(getEndStr(widget.options.name))]"
          v-bind="widget.options"
        />
        <el-color-picker
          v-else-if="widget.type === 'color'"
          v-model="form[toCamelCase(getEndStr(widget.options.name))]"
          v-bind="widget.options"
        />
        <el-slider
          v-else-if="widget.type === 'slider'"
          v-model="form[toCamelCase(getEndStr(widget.options.name))]"
          v-bind="widget.options"
        />
        <el-cascader
          v-else-if="widget.type === 'cascader'"
          v-model="form[toCamelCase(getEndStr(widget.options.name))]"
          style="width: 100%"
          :options="getOptions(widget)"
          :filterable="true"
          :props="{
            multiple: widget.options.multiple,
            value: 'value',
            label: 'label',
            children: 'children',
            checkStrictly: widget.options.checkStrictly,
            emitPath: widget.options.multiple ? true : widget.options.emitPath,
            expandTrigger: widget.options.expandTrigger || 'hover',
          }"
          v-bind="widget.options"
        />
        <upload
          v-else-if="widget.type === 'picture-upload'"
          v-model="form[toCamelCase(getEndStr(widget.options.name))]"
          type="image"
          v-bind="{ ...widget.options, name: 'file' }"
        />
        <upload
          v-else-if="widget.type === 'file-upload'"
          v-model="form[toCamelCase(getEndStr(widget.options.name))]"
          type="button"
          v-bind="{ ...widget.options, name: 'file' }"
        />
        <editor
          v-else-if="widget.type === 'rich-editor'"
          v-model:getHtml="form[toCamelCase(getEndStr(widget.options.name))]"
          v-bind="widget.options"
        />
        <component
          v-else-if="widget.type === 'dynamic-component'"
          :is="realComponent(widget) || 'div'"
          v-model:[getDynamicWidget(widget)?.modelValue]="
            form[toCamelCase(getEndStr(widget.options.name))]
          "
          v-bind="{ ...widget.options, ...extProps(widget) }"
        />
        <component
          v-else
          :is="widget.component || 'div'"
          v-bind="widget.options"
        ></component>
      </template>
      <template v-else>
        <StaticGroup
          v-if="widget.type === 'static-group'"
          :textContent="widget.options.label"
        />
        <el-alert v-else-if="widget.type === 'alert'" v-bind="widget.options" />
        <el-divider v-else-if="widget.type === 'divider'" v-bind="widget.options">{{
          widget.options?.label
        }}</el-divider>
        <el-form-item
          v-else
          :label="widget.options?.label"
          :prop="toCamelCase(widget.options?.name)"
          :rules="getFieldRules(widget)"
        >
          <el-input
            v-if="widget.type === 'input'"
            v-model="form[toCamelCase(widget.options.name)]"
            v-bind="widget.options"
          />
          <el-input
            v-else-if="widget.type === 'textarea'"
            v-model="form[toCamelCase(widget.options.name)]"
            type="textarea"
            v-bind="widget.options"
          />
          <el-input-number
            v-else-if="widget.type === 'number'"
            v-model="form[toCamelCase(widget.options.name)]"
            v-bind="widget.options"
          />
          <el-radio-group
            v-else-if="widget.type === 'radio'"
            v-model="form[toCamelCase(widget.options.name)]"
            v-bind="widget.options"
          >
            <el-radio
              v-for="option in getOptions(widget)"
              :key="option.value"
              :label="option.value"
            >
              {{ option.label }}
            </el-radio>
          </el-radio-group>
          <el-checkbox-group
            v-else-if="widget.type === 'checkbox'"
            v-model="form[toCamelCase(widget.options.name)]"
            v-bind="widget.options"
          >
            <el-checkbox
              v-for="option in getOptions(widget)"
              :key="option.value"
              :label="option.value"
            >
              {{ option.label }}
            </el-checkbox>
          </el-checkbox-group>
          <el-select
            v-else-if="
              widget.type === 'select' && widget.options?.dataSourceType !== 'datasource'
            "
            style="width: 100%"
            v-model="form[toCamelCase(widget.options.name)]"
            :filterable="true"
            v-bind="widget.options"
          >
            <el-option
              v-for="option in getOptions(widget)"
              :key="option.value"
              :label="option.label"
              :value="option.value"
            />
          </el-select>
          <data-select
            v-else-if="
              widget.type === 'select' && widget.options?.dataSourceType === 'datasource'
            "
            v-model="form[toCamelCase(widget.options.name)]"
            :data-source="widget.options.datasource"
            :multiple="widget.options.multiple"
            :placeholder="widget.options.placeholder"
            v-bind="widget.options"
          />
          <el-date-picker
            v-else-if="
              ['date', 'datetime', 'date-range', 'datetime-range'].includes(widget.type)
            "
            v-model="form[toCamelCase(widget.options.name)]"
            :type="widget.options.type"
            :class="[!!widget.options.autoFullWidth ? 'auto-full-width' : '']"
            :readonly="widget.options.readonly"
            :disabled="widget.options.disabled"
            :size="widget.options.size || 'default'"
            :clearable="widget.options.clearable"
            :editable="widget.options.editable"
            :format="widget.options.format || getDateFormat(widget.options.type)"
            :value-format="getDateFormat(widget.options.type)"
            :placeholder="widget.options.placeholder"
            :start-placeholder="widget.options.startPlaceholder"
            :end-placeholder="widget.options.endPlaceholder"
            :defaultTime="getDefaultTime(widget.options.type)"
          />
          <el-time-picker
            v-else-if="['time', 'time-range'].includes(widget.type)"
            v-model="form[toCamelCase(widget.options.name)]"
            :class="[!!widget.options.autoFullWidth ? 'auto-full-width' : '']"
            :disabled="widget.options.disabled"
            :readonly="widget.options.readonly"
            :size="widget.options.size || 'default'"
            :clearable="widget.options.clearable"
            :editable="widget.options.editable"
            :format="widget.options.format || getDateFormat(widget.type)"
            value-format="HH:mm:ss"
            :placeholder="widget.options.placeholder"
            :start-placeholder="widget.options.startPlaceholder"
            :end-placeholder="widget.options.endPlaceholder"
            :is-range="widget.type === 'time-range'"
          />
          <el-switch
            v-else-if="widget.type === 'switch'"
            v-model="form[toCamelCase(widget.options.name)]"
            v-bind="widget.options"
          />
          <el-rate
            v-else-if="widget.type === 'rate'"
            v-model="form[toCamelCase(widget.options.name)]"
            v-bind="widget.options"
          />
          <el-color-picker
            v-else-if="widget.type === 'color'"
            v-model="form[toCamelCase(widget.options.name)]"
            v-bind="widget.options"
          />
          <el-slider
            v-else-if="widget.type === 'slider'"
            v-model="form[toCamelCase(widget.options.name)]"
            v-bind="widget.options"
          />
          <el-cascader
            v-else-if="widget.type === 'cascader'"
            style="width: 100%"
            v-model="form[toCamelCase(widget.options.name)]"
            :options="getOptions(widget)"
            :filterable="true"
            :props="{
              multiple: widget.options.multiple,
              value: 'value',
              label: 'label',
              children: 'children',
              checkStrictly: widget.options.checkStrictly,
              emitPath: widget.options.multiple ? true : widget.options.emitPath,
              expandTrigger: widget.options.expandTrigger || 'hover',
              ...casProps(widget),
            }"
            v-bind="widget.options"
          />
          <upload
            v-else-if="widget.type === 'picture-upload'"
            v-model="form[toCamelCase(widget.options.name)]"
            type="image"
            v-bind="{ ...widget.options, name: 'file' }"
          />
          <upload
            v-else-if="widget.type === 'file-upload'"
            v-model="form[toCamelCase(widget.options.name)]"
            type="button"
            v-bind="{ ...widget.options, name: 'file' }"
          />
          <editor
            v-else-if="widget.type === 'rich-editor'"
            v-model:getHtml="form[toCamelCase(widget.options.name)]"
            v-bind="widget.options"
          />
          <component
            :is="realComponent(widget) || 'div'"
            v-else-if="widget.type === 'dynamic-component'"
            v-model:[getDynamicWidget(widget)?.modelValue]="
              form[toCamelCase(widget.options.name)]
            "
            v-bind="{ ...widget.options, ...extProps(widget) }"
          />
          <component
            v-else-if="widget.type !== 'static-group'"
            :is="widget.component || 'div'"
            v-bind="widget.options"
          ></component>
        </el-form-item>
      </template>
    </template>
  </template>
</template>

<script setup lang="ts">
import StaticGroup from '@/views/vform/components/form-designer/form-widget/components/static-group.vue';
import { reactive, ref, computed, watch, watchEffect } from 'vue';
import DataSelect from '@/components/dataSelect/index.vue';
import { rule } from '@/utils/validate';
import { useApiOption } from '../hooks/useOption';
import {
  componentMap,
  getDynamicWidget,
} from '@/views/vform/components/form-designer/widget-panel/dynamicWidgetsConfig';
// no unused imports
import getDict from '../hooks/useDict';

const { getApiOptions, apiDataMap, getOptions, casProps } = useApiOption();
function toCamelCase(str: string) {
  return str.replace(/_([a-z])/g, (_, c) => c.toUpperCase());
}
function getEndStr(str: string) {
  return str.split('.').pop();
}

interface WidgetOptions {
  name: string;
  label: string;
  required: boolean;
  validation: string;
  multiple?: boolean;
  checkStrictly?: boolean;
  emitPath?: boolean;
  expandTrigger?: 'hover' | 'click';
  optionItems?: Array<{
    value: string | number;
    label: string;
    children?: Array<any>;
  }>;
  [key: string]: any;
}

interface Widget {
  id: string;
  type: string;
  category?: string;
  options: WidgetOptions;
  cols?: any[];
  rows?: any[];
  tabs?: any[];
  widgetList?: any[];
}

const props = defineProps<{
  widgetList: Widget[];
  form: Record<string, any>;
  rules?: Record<string, any>;
  parentWidget?: Widget;
}>();
const defaultTime = [new Date(2000, 1, 1, 0, 0, 0), new Date(2000, 2, 1, 23, 59, 59)];
const realComponent = computed(() => (widget) => {
  return componentMap[widget.options.componentType];
});
const extProps = computed(() => (widget) => {
  const attrs = {};
  const configs = widget.optionsConfig || {};
  for (const key in configs) {
    const value = configs[key];
    attrs[key] = getOptions.value({
      type: 'select',
      options: value,
    });
    if (value.dataSourceType === 'api') {
      attrs.props = {
        ...(widget.options.props || {}),
        ...casProps.value({
          type: 'select',
          options: value,
        }),
      };
    }
  }
  return attrs;
});
function getDateFormat(type: string) {
  if (['time', 'time-range'].includes(type)) return 'HH:mm:ss';
  // 只要包含 time 字样的都加时分秒
  if (type === 'datetime' || type === 'datetimerange') {
    return 'YYYY-MM-DD HH:mm:ss';
  }
  return 'YYYY-MM-DD';
}

function getDefaultTime(type: string) {
  if (type === 'datetimerange') return defaultTime;
  // if (type === 'datetime') return new Date(2000, 1, 1, 0, 0, 0);
  return undefined;
}

// 获取字段验证规则
const getFieldRules = (widget: Widget) => {
  const rules = [];
  if (widget.options.required) {
    rules.push({
      required: true,
      message: widget.options.requiredHint || '此项必填',
      trigger: ['blur', 'change'],
    });
  }
  if (widget.options.validation) {
    rules.push({
      validator: rule[widget.options.validation],
      trigger: ['blur', 'change'],
    });
  }
  return rules;
};

/**
 * 生成明细表新行的初始数据
 */
function getEnhancedTableRow(widget: Widget): Record<string, any> {
  const row: Record<string, any> = {};
  if (Array.isArray(widget.widgetList)) {
    for (const colWidget of widget.widgetList) {
      const name = colWidget.options?.name;
      if (!name) continue;
      // 优先用 defaultValue
      let value = colWidget.options?.defaultValue;
      if (value === undefined) {
        if (colWidget.type === 'number') value = 0;
        else value = null;
      }
      row[toCamelCase(getEndStr(name))] = value;
    }
  }
  return row;
}

/**
 * 明细表添加行
 */
function handleAddRow(tableName: string) {
  if (!props.form[tableName]) props.form[tableName] = [];
  // 找到当前 widget
  const widget = props.widgetList.find(
    (w) =>
      toCamelCase(w.options?.name) + 'List' === tableName && w.type === 'enhanced-table'
  );
  if (!widget) return;
  props.form[tableName].push(getEnhancedTableRow(widget));
}

/**
 * 明细表删除行
 */
function handleRemoveRow(tableName: string, index: number) {
  if (!props.form[tableName]) return;
  props.form[tableName].splice(index, 1);
}
</script>

<style scoped lang="scss">
.el-form-item {
  margin-bottom: 18px;
}
</style>
