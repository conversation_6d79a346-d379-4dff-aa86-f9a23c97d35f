<template>
	<yun-drawer
		v-model="visible"
		:show-cancel-button="false"
		destroy-on-close
		title="详情"
		size="X-large"
		@confirm="handleConfirm"
	>
		<Detail :groups="groups" :data="form"/>
	</yun-drawer>
</template>

<script setup lang="ts">
import Detail from '@/components/Detail/index.vue';
import { getObj } from '@/api/gen/list';
import { useDetail } from '../hooks/useDetail';

interface Props {
  tableInfo?:  {
    functionName: string;
    fieldList: Array<{
      attrName: string;
      formType: string;
    }>;
  };
	widgetList?: Array<{
		type: string;
		formItemFlag?: boolean;
		options: {
			name: string;
			label: string;
			type?: string;
			defaultValue?: any;
			optionItems?: Array<{ label: string; value: any }>;
			dictType?: string;
			format?: string;
			[key: string]: any;
		};
		widgetList?: any[];
		cols?: Array<{ widgetList: any[] }>;
	}>;
}

interface FormData {
	[key: string]: any;
}

const props = defineProps<Props>();

const { groups, visible, form } = useDetail(props);

async function handleConfirm(done: () => void) {
	done();
}

async function show(row: FormData) {
  const res = await getObj(props.tableInfo?.functionName, row.id);
  form.value = res.data;
	// form.value = row;
	visible.value = true;
}

defineExpose({
	show,
});
</script>

<style scoped lang="scss">
</style>
