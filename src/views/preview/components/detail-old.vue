<template>
  <yun-drawer
    v-model="visible"
    :show-cancel-button="false"
    destroy-on-close
    title="详情"
    :size="sizeMap[formLayout || 1]"
    @confirm="handleConfirm"
  >
    <yun-pro-detail
      :detail="form"
      :columns="columns"
      :config="{
        descriptions: { labelPosition: 'top', column: +formLayout }
      }"
    />
  </yun-drawer>
</template>
<script setup>
import { computed } from 'vue';
import { useDetail } from '../hooks/useDetail';
import { sizeMap } from '../const';

const props = defineProps({
  tableInfo: {
    type: Object,
    default: () => ({}),
  },
});
const formLayout = computed(() => props.tableInfo.formLayout || 1);
const {
  columns, visible, form,
} = useDetail(props);
async function handleConfirm(done) {
  done();
}
function formatFrom(row) {
  const imgs = props.tableInfo.fieldList.filter((e) => e.formType === 'upload-img').reduce((a, b) => {
    const value = row[b.attrName];
    a[b.attrName] = Array.isArray(value) && value.length > 0 ? value.map((e) => {
      const url = e?.url || e;
      if (url.startsWith('http')) return url;
      
      const proxyPath = import.meta.env.VITE_SAAS_PROXY_PATH.endsWith('/') 
        ? import.meta.env.VITE_SAAS_PROXY_PATH.slice(0, -1) 
        : import.meta.env.VITE_SAAS_PROXY_PATH;
      const separator = url.startsWith('/') ? '' : '/';
      return `${proxyPath}${separator}${url}`;
    }) : null;
    return a;
  }, {});
  return {
    ...row,
    ...imgs,
  };
}
function show(row) {
  form.value = formatFrom(row);
  visible.value = true;
}

defineExpose({
  show,
});
</script>
<style scoped lang="scss">
:deep(.yun-descriptions) {
  padding: 0;
}
</style>
