<template>
  <div class="table-content">
    <yun-pro-table
      ref="proTableRef"
      v-model:pagination="pagination"
      v-model:filter-data="filterTableData"
      v-model:searchData="searchData"
      :table-columns="columns"
      :search-fields="searchFields"
      :auto-height="true"
      :remote-method="remoteMethod"
      :table-props="tablePropsObj"
      layout="whole"
    >
      <template #tableHeaderLeft>
        <el-button :icon="Plus" type="primary" @click="handleAdd()"> 新增 </el-button>
        <el-button @click="handleExport()"> 导出 </el-button>
        <el-button @click="handleImport()"> 导入 </el-button>
      </template>
      <template #t_action="{ row }">
        <el-button type="text" size="small" @click.prevent="handleEdit(row)">
          编辑
        </el-button>
        <el-button type="text" size="small" @click.prevent="handleView(row)">
          查看
        </el-button>
        <el-button type="text" size="small" @click.prevent="handleDelete(row)">
          删除
        </el-button>
      </template>
    </yun-pro-table>
    <Form
      ref="formRef"
      :table-info="tableInfo"
      :formConfig="formConfig"
      :widgetList="widgetList"
      :tables="tableInfoData"
      @getData="getData"
    />
    <Detail ref="detailRef" :table-info="tableInfo" :widgetList="widgetList" />
    <!-- 导入组件 -->
    <yun-import
      v-model="importVisible"
      dialog-tips="温馨提示:单次最多只能导入1000条数据"
      upload-tips="只能上传.xls,.xlsx文件"
      :download-function="handleDownTemplate"
      :upload-function="importFn"
      :template-urls="templateUrls"
    />
  </div>
</template>

<script setup lang="jsx">
import { ref, computed } from 'vue';
import { useRoute } from 'vue-router';
import { useProTable } from '@ylz-use/core';
import { ElMessage } from 'yun-design';
import { YunDialogConfirm } from '@ylz-material/dialog';
import moment from 'moment';
import {
  fetchList,
  removeObj,
  exportData,
  downloadTemp,
  importData,
} from '@/api/gen/list';
import { fetchFormList } from '@/api/gen/table';
import { useGenForm } from '@/stores/genForm';
import { useTable } from './hooks/useTable';
import Form from './components/form.vue';
import Detail from './components/detail.vue';

const formRef = ref();
const detailRef = ref();
const route = useRoute();
const searchData = ref({});
const genForm = useGenForm();
const menuInfo = genForm.getMenuInfo();
const formInfo = ref({});
// const { dsName, tableName } = route.query;
const { dsName = 'master', tableName, tableInfo: tableInfoData } = menuInfo;

const formConfig = computed(() => {
  return formInfo.value?.formConfig;
});
const widgetList = computed(() => {
  return formInfo.value?.widgetList;
});
const tableInfo = computed(() => {
  return tableInfoData.find((e) => e.tableName === tableName);
});
const { columns, searchFields } = useTable(tableInfo, widgetList);
const fieldListMap = computed(() => {
  const map = {};
  tableInfo.value?.fieldList.forEach((item) => {
    map[item.attrName] = item;
  });
  return map;
});
const {
  pagination,
  remoteMethod,
  tableProps,
  proTableRef,
  filterTableData,
} = useProTable({
  apiFn: async (...rest) => {
    return fetchList(tableInfo.value.functionName, ...rest);
  },
  paramsHandler(params) {
    // const { createTime } = params;
    // delete params.createTime;
    Object.keys(params).forEach((key) => {
      if (fieldListMap.value[key]?.queryFormType === 'date') {
        if (params[key]?.length) {
          params[`${key}Start`] = moment(params[key][0])
            .startOf('day')
            .format('YYYY-MM-DD');
          params[`${key}End`] = moment(params[key][1]).endOf('day').format('YYYY-MM-DD');
          delete params[key];
        }
      }
      if (fieldListMap.value[key]?.queryFormType === 'datetime') {
        if (params[key]?.length) {
          params[`${key}Start`] = moment(params[key][0]).format('YYYY-MM-DD HH:mm:ss');
          params[`${key}End`] = moment(params[key][1]).format('YYYY-MM-DD HH:mm:ss');
          delete params[key];
        }
      }
      if (fieldListMap.value[key]?.queryFormType === 'select') {
        if (Array.isArray(params[key])) {
          params[`${key}List`] = params[key];
          delete params[key];
        }
      }
    });
    return {
      ...params,
    };
  },
  querysHandler(query) {
    return {
      ...query,
      current: pagination.value.page,
      size: pagination.value.size,
    };
  },
  responseHandler(result) {
    // 列表显示异常时自定义返回数据格式
    return result.data;
  },
  plugins: {
    config: {
      columns: columns.value,
      searchFields: searchFields.value,
    },
    list: ['SEARCH_PLUS'],
  },
});

const tablePropsObj = computed(() => {
  return {
    ...tableProps,
    // 去除斑马纹
    stripe: false,
    // 设置表头样式
    headerCellStyle: {
      backgroundColor: '#f5f7fa',
      color: '#303133',
      fontWeight: 'bold',
      height: '40px',
    },
    // 设置单元格样式
    cellStyle: {
      padding: '0',
      height: '40px',
      'vertical-align': 'middle',
    },
    // 设置行样式
    rowStyle: {
      height: '40px',
    },
    // 设置表格行高
    rowHeight: 40,
  };
});
async function getFormInfo() {
  const params = {
    menuId: menuInfo.menuId,
    dsName: menuInfo.dsName || 'master',
    tableName: menuInfo.tableName,
  };
  const res = await fetchFormList(params);
  const json = res.data?.records?.[0]?.formInfo;
  if (json) {
    formInfo.value = JSON.parse(json);
  }
  console.log('formInfo', formInfo.value);
}
getFormInfo();
function getData() {
  proTableRef.value?.getData();
}
function handleEdit(row) {
  formRef.value?.show(row);
}
function handleAdd() {
  formRef.value?.show();
}
function paramsHandler() {
  const params = { ...searchData.value };
  Object.keys(params).forEach((key) => {
    if (fieldListMap.value[key]?.queryFormType === 'date') {
      if (params[key]?.length) {
        params[`${key}Start`] = moment(params[key][0])
          .startOf('day')
          .format('YYYY-MM-DD');
        params[`${key}End`] = moment(params[key][1]).endOf('day').format('YYYY-MM-DD');
        delete params[key];
      }
    }
    if (fieldListMap.value[key]?.queryFormType === 'datetime') {
      if (params[key]?.length) {
        params[`${key}Start`] = moment(params[key][0]).format('YYYY-MM-DD HH:mm:ss');
        params[`${key}End`] = moment(params[key][1]).format('YYYY-MM-DD HH:mm:ss');
        delete params[key];
      }
    }
    if (fieldListMap.value[key]?.queryFormType === 'select') {
      if (Array.isArray(params[key])) {
        params[`${key}List`] = params[key];
        delete params[key];
      }
    }
  });
  return {
    ...params,
  };
}
const importVisible = ref(false);
const templateUrls = ref([
  {
    label: '模板下载',
    value: '',
  },
]);

const handleImport = () => {
  importVisible.value = true;
};

// eslint-disable-next-line
function beforeUploadCheckHandler(file) {
  if (!/\.(xls|xlsx)$/.test(file.name)) {
    ElMessage({ type: 'warning', message: '文件格式不正确!' });
    return false;
  }
  return true;
}

async function importFn(files) {
  if (files.length === 0) {
    ElMessage({ type: 'warning', message: '请先上传文件' });
    return;
  }
  if (files.some((i) => !beforeUploadCheckHandler(i.raw))) {
    return;
  }
  const sendData = new FormData();
  sendData.append('file', files[0].raw);
  importVisible.value = false;
  const res = await importData(tableInfo.value.functionName, sendData);
  if (res.bizCode !== '0') {
    ElMessage.error(`${res.msg || '导入失败'}`);
  } else {
    ElMessage.success('导入成功');
  }
  getData();
}
async function handleExport() {
  const data = await exportData(tableInfo.value.functionName, paramsHandler(), {});

  // 创建下载链接
  const url = window.URL.createObjectURL(data);
  const a = document.createElement('a');
  a.href = url;
  a.download = `${tableInfo.value.functionName}_export_${moment().format(
    'YYYYMMDDHHmmss'
  )}.xlsx`; // 设置下载文件名
  document.body.appendChild(a);
  a.click();

  // 释放URL对象
  window.URL.revokeObjectURL(url);
  document.body.removeChild(a);
  ElMessage.success('导出成功');
}
function handleView(row) {
  detailRef.value?.show(row);
}
function handleDelete(row) {
  YunDialogConfirm('此操作将删除该项, 是否继续?', '删除确认', {
    type: 'warning', // info, success, warning, error
    size: 'small', // small|medium|info|large|X-large
    showCancelButton: true,
    showConfirmButton: true,
    cancelButtonText: '取消',
    confirmButtonText: '确定',
    appendToBody: false,
    hasFooter: true,
    cancelButtonHandler(close, isLoading) {
      close();
    },
    async confirmButtonHandler(close, isLoading) {
      isLoading.value = true;
      try {
        await removeObj(tableInfo.value.functionName, [row.id]);
        ElMessage.success('删除成功');
        getData();
      } finally {
        close();
        isLoading.value = false;
      }
    },
    beforeClose(close) {
      close();
    },
  });
}

function hasTableInfo() {
  const awaitFn = (resolve) => {
    setTimeout(() => {
      if (tableInfo.value) {
        resolve();
      } else {
        awaitFn();
      }
    }, 10);
  };
  return new Promise((resolve) => {
    awaitFn(resolve);
  });
}

async function handleDownTemplate() {
  if (!tableInfo.value) {
    await hasTableInfo();
  }
  const data = await downloadTemp(tableInfo.value.functionName);
  // 创建下载链接
  const url = window.URL.createObjectURL(data);
  const a = document.createElement('a');
  a.href = url;
  a.download = `${tableInfo.value.functionName}_导入模板_${moment().format(
    'YYYYMMDDHHmmss'
  )}.xlsx`; // 设置下载文件名
  document.body.appendChild(a);
  a.click();

  // 释放URL对象
  window.URL.revokeObjectURL(url);
  document.body.removeChild(a);
}
</script>

<style lang="scss">
.el-upload-list--picture-card {
  flex-wrap: wrap;
}
</style>
<style scoped lang="scss">
.table-content {
  width: 100%;
  height: calc(100vh - 55px);
  :deep(.el-form .el-form-item:last-of-type) {
    margin-bottom: 24px !important;
  }
  :deep(.el-switch) {
    height: fit-content !important;
  }
  // 修改表头背景颜色
  :deep(.el-table__header-wrapper thead th) {
    background-color: #f5f7fa; // 示例颜色，可以根据实际需要调整
    color: #303133; // 表头文字颜色
  }
  // 去除斑马纹
  :deep(.el-table--striped .el-table__body tr.el-table__row--striped td) {
    background-color: transparent;
  }
  :deep(.el-table__body tr:nth-child(even) > td) {
    background-color: transparent;
  }
}
</style>
