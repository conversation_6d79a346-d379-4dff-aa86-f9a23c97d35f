<template>
  <div class="option-items-pane">
    <!-- 数据源类型选择 -->
    <el-form-item label="数据源类型">
      <el-select
        :model-value="props.selectedWidget?.options?.dataSourceType"
        class="full-width-input"
        @update:model-value="handleDataSourceTypeChange"
      >
        <el-option label="自定义选项" value="custom" />
        <el-option label="数据字典" value="dict" />
        <el-option label="接口类型" value="api" />
      </el-select>
    </el-form-item>

    <!-- 接口类型配置 -->
    <template v-if="props.selectedWidget?.options?.dataSourceType === 'api'">
      <el-form-item label="选择api">
        <el-select
          v-model="props.selectedWidget.options.apiId"
          class="full-width-input"
        >
          <el-option v-for="item in apiOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
        </el-select>
      </el-form-item>
    </template>

    <!-- 自定义选项配置 -->
    <template v-if="props.selectedWidget?.options?.dataSourceType === 'custom'">
      <div class="custom-options-container">
        <!-- 选项列表 -->
        <draggable
          tag="ul"
          :list="props.selectedWidget?.options?.optionItems"
          item-key="value"
          handle=".drag-item"
          class="option-list"
        >
          <template #item="{ element, index }">
            <li class="option-item">
              <el-row :gutter="8" align="middle">
                <el-col :span="1">
                  <i class="el-icon-rank drag-item"></i>
                </el-col>
                <el-col :span="8">
                  <el-input
                    v-model="element.label"
                    size="small"
                    placeholder="选项标签"
                  ></el-input>
                </el-col>
                <el-col :span="8">
                  <el-input
                    v-model="element.value"
                    size="small"
                    placeholder="选项值"
                  ></el-input>
                </el-col>
                <el-col :span="4">
                  <el-checkbox v-model="element.disabled">禁用</el-checkbox>
                </el-col>
                <el-col :span="3">
                  <el-button
                    type="text"
                    icon="Delete"
                    circle
                    size="small"
                    @click="deleteOption(index)"
                  ></el-button>
                </el-col>
              </el-row>
            </li>
          </template>
        </draggable>

        <!-- 操作按钮 -->
        <div class="action-buttons">
          <el-button type="primary" @click="addOption" size="small">添加选项</el-button>
          <el-button type="primary" @click="importOptions" size="small"
            >导入选项</el-button
          >
          <el-button type="danger" @click="resetDefault" size="small">重置</el-button>
        </div>
      </div>
    </template>

    <!-- 数据字典配置 -->
    <template v-if="props.selectedWidget?.options?.dataSourceType === 'dict'">
      <el-form-item label="选择字典">
        <el-select
          :model-value="props.selectedWidget?.options?.dictType"
          class="full-width-input"
          @update:model-value="handleDictChange"
          filterable
        >
          <el-option
            v-for="dict in dictList"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
    </template>

    <!-- 数据源配置 -->
    <template v-if="props.selectedWidget?.options?.dataSourceType === 'datasource'">
      <el-form-item label="选择数据表">
        <table-select
          :model-value="props.selectedWidget?.options?.datasource?.tableName"
          @update:model-value="handleTableChange"
        />
      </el-form-item>

      <el-form-item v-if="props.selectedWidget?.options?.datasource?.tableName">
        <el-button type="primary" @click="openTableFieldConfig">配置字段</el-button>
      </el-form-item>
    </template>

    <!-- 字段配置弹窗 -->
    <el-dialog
      v-model="showFieldConfigDialog"
      title="字段配置"
      width="80%"
      append-to-body
    >
      <el-table :data="fieldList" border>
        <el-table-column type="index" width="50" align="center" label="序号" />
        <el-table-column prop="fieldName" label="字段名" width="180" />
        <el-table-column prop="fieldComment" label="字段注释" width="180" />
        <el-table-column prop="fieldType" label="字段类型" width="120" />
        <el-table-column label="显示字段" width="100" align="center">
          <template #default="{ row }">
            <el-radio
              :model-value="selectedLabelField"
              :label="row.fieldName"
              @change="() => handleSelectLabelField(row.fieldName)"
              >{{ '' }}</el-radio
            >
          </template>
        </el-table-column>
        <el-table-column label="存储字段" width="100" align="center">
          <template #default="{ row }">
            <el-radio
              :model-value="selectedValueField"
              :label="row.fieldName"
              @change="() => handleSelectValueField(row.fieldName)"
              >{{ '' }}</el-radio
            >
          </template>
        </el-table-column>
        <el-table-column label="列表显示" width="80" align="center">
          <template #default="{ row }">
            <el-checkbox v-model="row.gridItem" true-label="1" false-label="0" />
          </template>
        </el-table-column>
        <el-table-column label="查询显示" width="80" align="center">
          <template #default="{ row }">
            <el-checkbox v-model="row.queryItem" true-label="1" false-label="0" />
          </template>
        </el-table-column>
        <el-table-column prop="listLabel" label="列表名称">
          <template #default="{ row }">
            <el-input v-model="row.listLabel" />
          </template>
        </el-table-column>
        <el-table-column prop="queryFormType" label="查询组件类型" width="150">
          <template #default="{ row }">
            <el-select
              v-model="row.queryFormType"
              @change="handleQueryFormTypeChange(row)"
            >
              <el-option label="文本框" value="text" />
              <el-option label="下拉框" value="select" />
              <el-option label="日期选择器" value="date" />
              <el-option label="日期范围" value="daterange" />
            </el-select>
          </template>
        </el-table-column>
        <el-table-column prop="queryType" label="查询方式" width="120">
          <template #default="{ row }">
            <el-select v-model="row.queryType">
              <el-option label="=" value="=" />
              <el-option label="!=" value="!=" />
              <el-option label=">" value=">" />
              <el-option label=">=" value=">=" />
              <el-option label="<" value="<" />
              <el-option label="<=" value="<=" />
              <el-option label="LIKE" value="LIKE" />
              <el-option label="BETWEEN" value="BETWEEN" />
              <el-option label="IN" value="IN" />
            </el-select>
          </template>
        </el-table-column>
        <el-table-column prop="formType" label="表单类型" width="150">
          <template #default="{ row }">
            <el-select v-model="row.formType">
              <el-option
                v-for="field in formTypeList"
                :key="field.type"
                :label="field.label"
                :value="field.type"
              />
            </el-select>
          </template>
        </el-table-column>
      </el-table>
      <template #footer>
        <el-button @click="showFieldConfigDialog = false">取 消</el-button>
        <el-button type="primary" @click="saveFieldConfig">确 定</el-button>
      </template>
    </el-dialog>

    <!-- 导入选项弹窗 -->
    <el-dialog
      v-model="showImportDialogFlag"
      title="导入选项"
      width="600px"
      append-to-body
    >
      <el-form>
        <el-form-item>
          <el-input
            v-model="optionLines"
            type="textarea"
            :rows="10"
            placeholder="请输入选项，每行一个。格式：选项值,选项标签"
          ></el-input>
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="showImportDialogFlag = false">取 消</el-button>
        <el-button type="primary" @click="saveOptions">确 定</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted, defineAsyncComponent, nextTick } from 'vue';
import { useStorage } from '@vueuse/core';
import { ElMessage } from 'yun-design';
import { fetchDictList } from '/@/api/gen/table';
import { useTableColumnInfoApi } from '/@/api/gen/table';
import { formFields } from '@/views/vform/components/form-designer/widget-panel/widgetsConfig';
import { useDict } from '/@/hooks/dict';
import { fetchList } from '@/api/lowcode/lowcode-api-source/index';

interface OptionItem {
  label: string;
  value: string | number;
  disabled?: boolean;
}

interface TableField {
  fieldName: string;
  fieldComment: string;
  fieldType: string;
  gridItem: '0' | '1';
  queryItem: '0' | '1';
  listLabel: string;
  queryFormType: string;
  queryType: string;
  formType: string;
}

interface DataSource {
  tableName: string;
  labelField: string;
  valueField: string;
  fields?: TableField[];
}

interface OptionModel {
  dataSourceType: 'custom' | 'dict' | 'datasource' | 'api';
  dictType: string;
  datasource: DataSource;
  optionItems: OptionItem[];
  defaultValue?: string | string[];
  multiple?: boolean;
  name?: string;
  apiId?: string;
}

// Props 定义
const props = defineProps<{
  designer: Record<string, any>;
  selectedWidget: {
    type: string;
    options: OptionModel;
  };
}>();

// 组件引用
const tableSelect = defineAsyncComponent(
  () => import('/@/components/tableSelect/index.vue')
);

// 响应式状态
const dataSourceType = ref<'custom' | 'dict' | 'datasource' | 'api'>('custom');
const showImportDialogFlag = ref(false);
const showFieldConfigDialog = ref(false);
const optionLines = ref('');
const dictList = ref<{ label: string; value: string }[]>([]);
const fieldList = ref<TableField[]>([]);
const loading = ref(false);
const formTypeList = formFields;
const separator = useStorage('option-separator', ',');

// 字段配置弹窗：显示字段和存储字段的选中状态
const selectedLabelField = ref('');
const selectedValueField = ref('');

// 接口配置相关
const apiOptions = ref<{ label: string; value: string }[]>([]);

// 字段类型映射
const fieldTypeToFormType: Record<string, string> = {
  varchar: 'text',
  char: 'text',
  text: 'textarea',
  int: 'number',
  bigint: 'number',
  float: 'number',
  double: 'number',
  decimal: 'number',
  datetime: 'datetime',
  date: 'date',
  time: 'time',
};

// 初始化 optionModel
const optionModel = computed<OptionModel>(() => {
  if (!props.selectedWidget?.options) {
    return {
      dataSourceType: 'custom',
      dictType: '',
      datasource: {
        tableName: '',
        labelField: '',
        valueField: '',
      },
      optionItems: [],
    };
  }

  const options = props.selectedWidget.options;

  // 确保所有必要的属性都存在
  return {
    dataSourceType: options.dataSourceType || 'custom',
    dictType: options.dictType || '',
    datasource: {
      tableName: options.datasource?.tableName || '',
      labelField: options.datasource?.labelField || '',
      valueField: options.datasource?.valueField || '',
      fields: options.datasource?.fields || [],
    },
    optionItems: options.optionItems || [],
    multiple: options.multiple,
    name: options.name,
    apiId: options.apiId,
  };
});

// 监听选中组件的变化
watch(
  () => props.selectedWidget,
  (widget) => {
    if (widget) {
      dataSourceType.value = widget.options?.dataSourceType || 'custom';
    }
  },
  { immediate: true }
);

// 加载字典列表
const loadDictList = async () => {
  try {
    const res = await fetchDictList();
    dictList.value = res.data.map((item: any) => ({
      label: item.description,
      value: item.dictType,
    }));
  } catch (error) {
    ElMessage.error('加载字典列表失败');
  }
};

// 加载表字段信息
const loadTableFields = async (tableName: string, dsName: string = 'master') => {
  if (!tableName) return;

  // 优先使用已存在的字段配置
  const existFields = props.selectedWidget?.options?.datasource?.fields;
  if (Array.isArray(existFields) && existFields.length > 0) {
    fieldList.value = JSON.parse(JSON.stringify(existFields));
    return;
  }

  loading.value = true;
  try {
    const res = await useTableColumnInfoApi(tableName, dsName);
    fieldList.value = (res.data || []).map((field: any) => ({
      fieldName: field.name,
      fieldComment: field.comment || field.metadata?.COLUMN_COMMENT || field.name,
      fieldType: field.typeName || field.metadata?.DATA_TYPE || 'varchar',
      gridItem: '1',
      queryItem: '0',
      listLabel: field.comment || field.metadata?.COLUMN_COMMENT || field.name,
      queryFormType: 'text',
      queryType:
        fieldTypeToFormType[
          (field.typeName || field.metadata?.DATA_TYPE || '').toLowerCase()
        ] === 'daterange'
          ? 'BETWEEN'
          : '=',
      formType:
        fieldTypeToFormType[
          (field.typeName || field.metadata?.DATA_TYPE || '').toLowerCase()
        ] || 'input',
    }));
  } catch (error) {
    ElMessage.error('加载表字段失败');
  } finally {
    loading.value = false;
  }
};

// 数据源类型变更处理
const handleDataSourceTypeChange = (type: 'custom' | 'dict' | 'datasource' | 'api') => {
  if (!props.selectedWidget?.options) return;

  props.selectedWidget.options.dataSourceType = type;
  if (type === 'custom' && !props.selectedWidget.options.optionItems) {
    props.selectedWidget.options.optionItems = [];
  }
};

// 字典变更处理
const handleDictChange = (dictType: string) => {
  if (!dictType || !props.selectedWidget?.options) return;

  // 动态获取字典项
  const dictRefs = useDict(dictType);
  // 由于 useDict 返回的是 { [dictType]: Ref<Array> } 结构
  const dictArrRef = dictRefs[dictType];
  if (dictArrRef) {
    watch(
      dictArrRef,
      (newVal) => {
        if (Array.isArray(newVal)) {
          props.selectedWidget.options.optionItems = newVal.map((item: any) => ({
            label: item.label,
            value: item.value,
            disabled: false,
          }));
        }
      },
      { immediate: true }
    );
    // 记录当前字典类型
    props.selectedWidget.options.dictType = dictType;
  }
};

// 表格变更处理
const handleTableChange = async (tableName: string) => {
  if (!tableName) return;
  if (!props.selectedWidget?.options?.datasource) {
    props.selectedWidget.options.datasource = {
      tableName: '',
      labelField: '',
      valueField: '',
      fields: [],
    };
  }
  props.selectedWidget.options.datasource.tableName = tableName;
  await loadTableFields(tableName, 'master');
};

// 打开表字段配置
const openTableFieldConfig = () => {
  if (!props.selectedWidget?.options?.datasource?.tableName) {
    ElMessage.warning('请先选择数据表');
    return;
  }
  showFieldConfigDialog.value = true;
};

// 打开弹窗时初始化radio选中
watch(showFieldConfigDialog, (val) => {
  if (val) {
    // 默认选中
    selectedLabelField.value =
      props.selectedWidget?.options?.datasource?.labelField || '';
    selectedValueField.value =
      props.selectedWidget?.options?.datasource?.valueField || 'id';
    // 如果没有默认，自动选第一个
    nextTick(() => {
      if (!selectedLabelField.value && fieldList.value.length) {
        selectedLabelField.value = fieldList.value[0].fieldName;
      }
      if (!selectedValueField.value && fieldList.value.length) {
        // 优先选id，否则第一个
        const idField = fieldList.value.find((f) => f.fieldName === 'id');
        selectedValueField.value = idField ? 'id' : fieldList.value[0].fieldName;
      }
    });
  }
});

function handleSelectLabelField(fieldName: string) {
  selectedLabelField.value = fieldName;
}
function handleSelectValueField(fieldName: string) {
  selectedValueField.value = fieldName;
}

// 保存字段配置
const saveFieldConfig = () => {
  if (!props.selectedWidget?.options?.datasource) return;

  const selectedFields = fieldList.value.filter((f) => f.gridItem === '1');
  if (selectedFields.length === 0) {
    ElMessage.warning('请至少选择一个显示字段');
    return;
  }

  props.selectedWidget.options.datasource.fields = JSON.parse(
    JSON.stringify(fieldList.value)
  );
  props.selectedWidget.options.datasource.labelField = selectedLabelField.value;
  props.selectedWidget.options.datasource.valueField = selectedValueField.value;

  showFieldConfigDialog.value = false;
};

// 删除选项
const deleteOption = (index: number) => {
  if (!props.selectedWidget?.options?.optionItems) return;
  props.selectedWidget.options.optionItems.splice(index, 1);
};

// 添加选项
const addOption = () => {
  if (!props.selectedWidget?.options) {
    return;
  }

  // 确保 optionItems 数组存在
  if (!Array.isArray(props.selectedWidget.options.optionItems)) {
    props.selectedWidget.options.optionItems = [];
  }

  const newValue = props.selectedWidget.options.optionItems.length + 1;
  props.selectedWidget.options.optionItems.push({
    label: `选项${newValue}`,
    value: newValue.toString(),
    disabled: false,
  });
};

// 导入选项
const importOptions = () => {
  if (!props.selectedWidget?.options?.optionItems) return;

  optionLines.value = props.selectedWidget.options.optionItems
    .map((opt) => {
      if (opt.value === opt.label) {
        return opt.value;
      }
      return `${opt.value}${separator.value}${opt.label}`;
    })
    .join('\n');

  showImportDialogFlag.value = true;
};

// 保存导入的选项
const saveOptions = () => {
  if (!props.selectedWidget?.options) return;

  const lineArray = optionLines.value.split('\n');
  props.selectedWidget.options.optionItems = lineArray
    .filter((line) => line.trim())
    .map((line) => {
      const parts = line.split(separator.value);
      return parts.length > 1
        ? { value: parts[0], label: parts[1] }
        : { value: line, label: line };
    });

  showImportDialogFlag.value = false;
};

// 重置默认值
const resetDefault = () => {
  if (!props.selectedWidget?.options) return;

  props.selectedWidget.options.defaultValue =
    props.selectedWidget.type === 'checkbox' ||
    (props.selectedWidget.type === 'select' && props.selectedWidget.options.multiple)
      ? []
      : '';

  emitDefaultValueChange();
};

// 触发默认值变更事件
const emitDefaultValueChange = () => {
  if (props.designer?.formWidget) {
    const fieldWidget = props.designer.formWidget.getWidgetRef(
      props.selectedWidget.options.name || ''
    );
    fieldWidget?.refreshDefaultValue?.();
  }
};

// 监听 fieldList，自动同步 queryType
function handleQueryFormTypeChange(field: any) {
  if (field.queryFormType === 'daterange') {
    field.queryType = 'BETWEEN';
  } else {
    field.queryType = '=';
  }
}

// 获取api列表
function fetchApiOptions() {
  fetchList({}, {
    current: 1,
    size: 1000,
  }).then((res) => {
    apiOptions.value = res.data.records?.map((item: any) => ({
      label: item.name,
      value: item.id,
    })) || [];
  });
}

// 初始化
onMounted(() => {
  loadDictList();
  fetchApiOptions();
  // 如果初始为数据源且有表名，自动加载字段
  if (
    props.selectedWidget?.options?.dataSourceType === 'datasource' &&
    props.selectedWidget?.options?.datasource?.tableName
  ) {
    loadTableFields(props.selectedWidget.options.datasource.tableName, 'master');
  }
  if (props.selectedWidget?.options?.datasource?.fields) {
    fieldList.value = JSON.parse(
      JSON.stringify(props.selectedWidget.options.datasource.fields)
    );
  }
});

// 导出需要的属性和方法
defineExpose({
  dataSourceType,
  showImportDialogFlag,
  showFieldConfigDialog,
  optionLines,
  dictList,
  fieldList,
  loading,
  formTypeList,
  separator,
  optionModel,
  handleDataSourceTypeChange,
  handleDictChange,
  handleTableChange,
  openTableFieldConfig,
  saveFieldConfig,
  deleteOption,
  addOption,
  importOptions,
  saveOptions,
  resetDefault,
  emitDefaultValueChange,
  handleQueryFormTypeChange,
  fetchApiOptions,
});
</script>

<style lang="scss" scoped>
.option-items-pane {
  width: 100%;

  .custom-options-container {
    margin-top: 16px;

    .option-list {
      list-style: none;
      padding: 0;
      margin: 0 0 16px 0;

      .option-item {
        padding: 8px;
        margin-bottom: 8px;
        border: 1px solid var(--el-border-color);
        border-radius: 4px;
        background-color: var(--el-bg-color);

        &:hover {
          background-color: var(--el-fill-color-light);
        }

        .drag-item {
          cursor: move;
          color: var(--el-text-color-secondary);
        }
      }
    }

    .action-buttons {
      display: flex;
      gap: 8px;
    }
  }
}

.full-width-input {
  width: 100% !important;
}

li.ghost {
  background: var(--el-bg-color);
  border: 2px dotted var(--el-color-primary);
}

.drag-option {
  cursor: move;
}

.dialog-footer {
  text-align: right;
  margin-top: 16px;
}

:deep(.el-input--small.el-input--prefix .el-input__inner) {
  padding-left: 35px !important;
}
</style>
