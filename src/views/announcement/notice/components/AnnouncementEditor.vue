<template>
  <yun-drawer
    v-model="visible"
    :title="currentTitle"
    size="large"
    destroy-on-close
    :show-confirm-button="false"
    cancel-button-text="关闭"
    @cancel="handleCancel"
  >
    <div
      class="editor-container"
      v-loading="loading"
    >
      <Editor
        style="height: calc(100vh - 120px); overflow-y: hidden"
        v-model="content"
        :defaultConfig="editorConfig"
        mode="default"
        @onCreated="handleCreated"
        @onChange="handleChange"
      />
    </div>
  </yun-drawer>
</template>

<script setup lang="ts">
import { ref, shallowRef, onBeforeUnmount } from 'vue';
import { Editor } from '@wangeditor/editor-for-vue';
import '@wangeditor/editor/dist/css/style.css';
import type { IDomEditor, IEditorConfig } from '@wangeditor/editor';
import { NOTICE_TYPE_ENUM_OBJ, NOTICE_TYPE_ENUM } from '../const';
import { getAnnouncementData } from '@/views/procurementSourcing/biddingProcess/api';
import { getTenderBidById } from '@/views/procurementSourcing/biddingProcess/api/award';

const props = defineProps({
  noticeType: {
    type: String,
    default: '',
  },
  readonly: {
    type: Boolean,
    default: false,
  },
});

// 组件属性
const visible = ref(false);
const loading = ref(false);
const content = ref<any>('');
const currentRow = ref<any>({
  noticeId: 232,
  projectId: 274,
});
const editorRef = shallowRef<IDomEditor>();
const currentTitle = computed(() => {
  return `查看${NOTICE_TYPE_ENUM_OBJ[props.noticeType]}`;
});

// 编辑器配置
const editorConfig: Partial<IEditorConfig> = {
  placeholder: '请输入公告内容...',
  autoFocus: false,
  scroll: true,
  readOnly: true,
  MENU_CONF: {
    uploadImage: {
      server: '/api/upload',
      fieldName: 'file',
    },
  },
};

// 编辑器生命周期
function handleCreated(editor: IDomEditor) {
  editorRef.value = editor;
}

function handleChange(editor: IDomEditor) {
  content.value = editor.getHtml();
}

// 处理取消
function handleCancel() {
  // 可以在这里添加取消逻辑
}

// 加载 采购公告 / 采购邀请函 公告内容
async function loadPurchaseInviteNoticeContent() {
  try {
    const res = await getAnnouncementData(currentRow.value.noticeId);
    content.value = res?.data?.noticeContent || null;
    // eslint-disable-next-line no-empty
  } catch (error) {}
}

// 加载 中标公告 / 中标公示
async function loadBidNoticeContent() {
  try {
    const res = await getTenderBidById(currentRow.value.noticeId, currentRow.value.projectId);
    const { noticeContent, publicityContent } = res?.data || {};
    if (NOTICE_TYPE_ENUM.BID_ANNOUNCEMENT === props.noticeType) {
      content.value = publicityContent || null;
    } else if (NOTICE_TYPE_ENUM.BID_NOTICE === props.noticeType) {
      content.value = noticeContent || null;
    }
    // eslint-disable-next-line no-empty
  } catch (error) {}
}

// 根据 公告类型 加载 公告内容
async function loadNoticeContent() {
  if ([NOTICE_TYPE_ENUM.PURCHASE_ANNOUNCEMENT, NOTICE_TYPE_ENUM.PURCHASE_INVITATION].includes(props.noticeType)) {
    await loadPurchaseInviteNoticeContent();
  } else if ([NOTICE_TYPE_ENUM.BID_ANNOUNCEMENT, NOTICE_TYPE_ENUM.BID_NOTICE].includes(props.noticeType)) {
    await loadBidNoticeContent();
  }
}

// 显示抽屉
async function show(row: any) {
  // eslint-disable-next-line no-console
  console.log('row', row);
  currentRow.value = row;
  loading.value = true;
  visible.value = true;
  content.value = '';
  await loadNoticeContent();
  // 等待编辑器创建完成后设置内容
  setTimeout(() => {
    if (editorRef.value) {
      editorRef.value.setHtml(content.value);
      loading.value = false;
    }
  }, 100);
}

// 组件卸载时销毁编辑器
onBeforeUnmount(() => {
  const editor = editorRef.value;
  if (editor == null) return;
  editor.destroy();
});

// 暴露方法
defineExpose({
  show,
});
</script>

<style lang="scss" scoped>
.editor-container {
  height: 100%;
  display: flex;
  flex-direction: column;
  border: 1px solid #ccc;
  border-radius: 4px;
  overflow: hidden;
}

:deep(.w-e-toolbar) {
  border-bottom: 1px solid #ccc !important;
}

:deep(.w-e-text-container) {
  flex: 1;
}

:deep(.w-e-text-placeholder) {
  color: #999;
  font-style: italic;
}
</style>
