import { objectToOptions } from "@/views/procurementSourcing/biddingProcess/utils";

// 导出公告类型枚举 采购公告 采购邀请函 中标公示 中标公告 招募公告
export const NOTICE_TYPE_ENUM = {
  PURCHASE_ANNOUNCEMENT: "PURCHASE_ANNOUNCEMENT",
  PURCHASE_INVITATION: "PURCHASE_INVITATION",
  BID_ANNOUNCEMENT: "BID_ANNOUNCEMENT",
  BID_NOTICE: "BID_NOTICE",
  RECRUITMENT: "RECRUITMENT",
}

export const NOTICE_TYPE_ENUM_OBJ = {
  [NOTICE_TYPE_ENUM.PURCHASE_ANNOUNCEMENT]: '采购公告',
  [NOTICE_TYPE_ENUM.PURCHASE_INVITATION]: '采购邀请函',
  [NOTICE_TYPE_ENUM.BID_ANNOUNCEMENT]: '中标公示',
  [NOTICE_TYPE_ENUM.BID_NOTICE]: '中标公告',
  [NOTICE_TYPE_ENUM.RECRUITMENT]: '招募公告',
}
export const NOTICE_TYPE_OPTIONS = objectToOptions(NOTICE_TYPE_ENUM_OBJ);

// 导出寻源方式枚举
// XJCG("询价采购"),
// JZTP("竞争性谈判"),
// ZJWT("直接委托"),
// JJCG("竞价采购"),
// KSXJ("快速询价"),
// QZXJ("青贮询价"),
// ZB("招标");
export const SOURCING_METHOD_ENUM = {
  XJCG: "XJCG",
  JZTP: "JZTP",
  ZJWT: "ZJWT",
  JJCG: "JJCG",
  KSXJ: "KSXJ",
  QZXJ: "QZXJ",
  ZB: "ZB",
}
export const SOURCING_METHOD_ENUM_OBJ = {
  [SOURCING_METHOD_ENUM.XJCG]: "询价采购",
  [SOURCING_METHOD_ENUM.JZTP]: "竞争性谈判",
  [SOURCING_METHOD_ENUM.ZJWT]: "直接委托",
  [SOURCING_METHOD_ENUM.JJCG]: "竞价采购",
  [SOURCING_METHOD_ENUM.KSXJ]: "快速询价",
  [SOURCING_METHOD_ENUM.QZXJ]: "青贮询价",
  [SOURCING_METHOD_ENUM.ZB]: "招标",
}
export const SOURCING_METHOD_OPTIONS = objectToOptions(SOURCING_METHOD_ENUM_OBJ);


// 导出 公示状态枚举
export const PUBLICITY_STATUS_ENUM = {
  UNPUBLISHED: "UNPUBLISHED",
  PUBLISHED: "PUBLISHED",
  EXPIRED: "EXPIRED",
}

export const PUBLICITY_STATUS_ENUM_OBJ = {
  [PUBLICITY_STATUS_ENUM.UNPUBLISHED]: "未公示",
  [PUBLICITY_STATUS_ENUM.PUBLISHED]: "已公示",
  [PUBLICITY_STATUS_ENUM.EXPIRED]: "已过期",
}
export const PUBLICITY_STATUS_OPTIONS = objectToOptions(PUBLICITY_STATUS_ENUM_OBJ);