<template>
  <div
    class="notice-container flex flex-col"
    v-loading="loading"
  >
    <div style="">
      <el-radio-group
        v-model="noticeType"
        @change="refresh"
      >
        <el-radio-button
          v-for="item in NOTICE_TYPE_OPTIONS"
          :key="item.value"
          :label="item.value"
        >
          {{ item.label }}
        </el-radio-button>
      </el-radio-group>
    </div>
    <div
      class="flex-1 need-hide-table-card editable-table"
      v-if="columns[noticeType]"
    >
      <yun-pro-table
        ref="proTableRef"
        v-model:pagination="pagination"
        v-model:filter-data="filterTableData"
        v-model:searchData="searchData"
        :table-columns="columns[noticeType]"
        :search-fields="searchFields"
        :auto-height="true"
        :remote-method="remoteMethod"
        :table-props="tablePropsObj"
        layout="whole"
      >
        <template #t_action="{ row }">
          <el-button
            type="text"
            size="small"
            @click="viewNotice(row)"
          >
            查看公告
          </el-button>
        </template>
      </yun-pro-table>
    </div>
    <!-- 公告内容编辑抽屉 -->
    <AnnouncementEditor
      ref="announcementEditorRef"
      :notice-type="noticeType"
    />
  </div>
</template>

<script lang="ts" setup>
import { ref, computed } from 'vue';
import { useProTable } from '@ylz-use/core';
import { useColumns } from './hooks/useColumns';
import { NOTICE_TYPE_OPTIONS, NOTICE_TYPE_ENUM } from './const';
import { noticePage, invitePage, publicityPage, publicNoticePage } from '@/api/announcement/notice';
// @ts-ignore
import AnnouncementEditor from './components/AnnouncementEditor.vue';

const loading = ref(false);
// 抽屉组件引用
const announcementEditorRef = ref();
const noticeType = ref(NOTICE_TYPE_ENUM.PURCHASE_ANNOUNCEMENT);
// const formRef = ref();
const pageApiFn = computed(() => {
  if (noticeType.value === NOTICE_TYPE_ENUM.PURCHASE_INVITATION) {
    return invitePage;
  }
  if (noticeType.value === NOTICE_TYPE_ENUM.BID_ANNOUNCEMENT) {
    return publicityPage;
  }
  if (noticeType.value === NOTICE_TYPE_ENUM.BID_NOTICE) {
    return publicNoticePage;
  }

  if (noticeType.value === NOTICE_TYPE_ENUM.PURCHASE_ANNOUNCEMENT) {
    return noticePage;
  }
  return noticePage;
});
const { columns, searchFields, searchData } = useColumns();
const { remoteMethod, tableProps, proTableRef, pagination, filterTableData, reLoad } = useProTable({
  apiFn: pageApiFn,
  responseHandler(result: any) {
    const data = result.data?.records || [];
    return data.map((item: any) => {
      const files = item?.attachmentList || [];
      item.noticeAttachment = files.map((file: any) => ({
        url: file.filePath,
        name: file.fileName,
      }));
      return item;
    });
  },
  customTotalHandler(data: any) {
    return data?.data?.total;
  },
  paramsHandler(params: any) {
    const { bidOpenTime, publishTime, ...rest } = params;
    return {
      bidOpenStartTime: bidOpenTime ? bidOpenTime[0] : undefined,
      bidOpenEndTime: bidOpenTime ? bidOpenTime[1] : undefined,
      publishStartTime: publishTime ? publishTime[0] : undefined,
      publishEndTime: publishTime ? publishTime[1] : undefined,

      current: pagination.value.page,
      size: pagination.value.size || pagination.value.pageSize,
      ...rest,
    };
  },
  // querysHandler() {
  //   const querysData = {
  //     current: pagination.value.page,
  //     size: pagination.value.size || pagination.value.pageSize,
  //   };
  //   return querysData;
  // },
  plugins: {
    config: {
      columns: columns.value,
      searchFields: searchFields.value,
    },
    list: ['SEARCH_PLUS'],
  },
});
const tablePropsObj = computed(() => ({
  ...tableProps.value,
  stripe: true,
  border: true,
  // headerCellStyle: {
  //   backgroundColor: '#f5f7fa',
  //   color: '#303133',
  //   fontWeight: 'bold',
  //   'vertical-align': 'middle',
  //   // height: '40px',
  // },
  // cellStyle: {
  //   padding: '0',
  //   // height: '40px',
  //   'vertical-align': 'middle',
  // },
  // rowStyle: {
  //   // height: '40px',
  //   'vertical-align': 'middle',
  // },
  // rowHeight: 40,
}));
// eslint-disable-next-line @typescript-eslint/no-unused-vars, no-unused-vars
function viewNotice(row: any) {
  announcementEditorRef.value?.show({
    noticeId: row.id,
    projectId: row.projectId,
  });
}

async function refresh() {
  loading.value = true;
  // proTableRef.value?.getData();
  await reLoad();
  setTimeout(() => {
    loading.value = false;
  }, 200);
}
</script>

<style lang="scss" scoped>
.notice-container {
  height: calc(100vh - 88px);
  box-sizing: border-box;
  padding: 20px;
  background: #fff;
}
</style>
