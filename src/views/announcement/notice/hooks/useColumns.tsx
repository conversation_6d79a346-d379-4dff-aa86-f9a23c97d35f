import { ref, computed } from 'vue';
import { NOTICE_TYPE_ENUM, SOURCING_METHOD_OPTIONS, PUBLICITY_STATUS_OPTIONS } from '../const';
// import CommonTag from '@/views/procurementSourcing/biddingProcess/components/BidEvaluation/components/CommonTag.vue';
import FileRenderer from '/@/components/Detail/renderers/FileRenderer.vue';

export function useColumns() {
  const searchFields = ref([
    // {
    //   label: '公告类型',
    //   prop: 'roundNo',
    //   type: 'select',
    //   componentAttrs: {
    //     // filterable: true,
    //     // multiple: true,
    //     clearable: false,
    //   },
    //   options: SOURCING_METHOD_OPTIONS,
    // },
    {
      label: '项目名称/编码',
      prop: 'searchContent',
      type: 'input',
      componentAttrs: {
        clearable: true,
      },
    },
    {
      label: '寻源方式',
      prop: 'sourcingType',
      type: 'select',
      componentAttrs: {
        // filterable: true,
        multiple: true,
        clearable: false,
      },
      options: SOURCING_METHOD_OPTIONS,
    },
    {
      label: '开标时间',
      prop: 'bidOpenTime',
      component: 'el-date-picker',
      fieldMapToTime: ['bidOpenStartTime', 'bidOpenEndTime'],
      colAttrs: {
        span: 12,
      },
      componentAttrs: {
        startPlaceholder: '开始时间',
        endPlaceholder: '截止时间',
        type: 'datetimerange',
        format: 'YYYY-MM-DD HH:mm:ss',
        valueFormat: 'YYYY-MM-DD HH:mm:ss',
        clearable: true,
        defaultTime: [new Date(2000, 1, 1, 0, 0, 0), new Date(2000, 2, 1, 23, 59, 59)],
      },
    },
    {
      label: '发布人',
      prop: 'projectLeaderName',
      type: 'input',
      componentAttrs: {
        clearable: true,
      },
    },
    {
      label: '发布时间',
      prop: 'publishTime',
      component: 'el-date-picker',
      fieldMapToTime: ['publishStartTime', 'publishEndTime'],
      colAttrs: {
        span: 12,
      },
      componentAttrs: {
        startPlaceholder: '开始时间',
        endPlaceholder: '截止时间',
        type: 'datetimerange',
        format: 'YYYY-MM-DD HH:mm:ss',
        valueFormat: 'YYYY-MM-DD HH:mm:ss',
        clearable: true,
        defaultTime: [new Date(2000, 1, 1, 0, 0, 0), new Date(2000, 2, 1, 23, 59, 59)],
      },
    },
    {
      label: '公告标题',
      prop: 'noticeTitle',
      type: 'input',
      componentAttrs: {
        clearable: true,
      },
    },
  ]);
  const searchData = ref({});
  const columns = computed(() => {
    return {
      [NOTICE_TYPE_ENUM.PURCHASE_ANNOUNCEMENT]: [
        // {
        //   label: '序号',
        //   type: 'index',
        //   width: '60px',
        // },
        // {
        //   label: '公告类型',
        //   prop: 'noticeType',
        //   enums: SOURCING_METHOD_OPTIONS,
        // },
        {
          label: '寻源方式',
          prop: 'sourcingMethod',
          enums: SOURCING_METHOD_OPTIONS,
        },
        {
          label: '项目编号',
          prop: 'projectCode',
        },
        {
          label: '项目名称',
          prop: 'projectName',
        },
        {
          label: '公告标题',
          prop: 'noticeTitle',
        },
        {
          label: '公告附件',
          prop: 'noticeAttachment',
          render: ({ row }: any) => {
            return row?.noticeAttachment?.length ? <FileRenderer value={row?.noticeAttachment} /> : '--';
          },
          width: '200px',
        },
        {
          label: '发布人',
          prop: 'publisher',
        },
        {
          label: '发布时间',
          prop: 'publishTime',
          width: '170px',
        },
        {
          label: '报名开始时间',
          prop: 'registrationStartTime',
          width: '170px',
        },
        {
          label: '报名截止时间',
          prop: 'registrationEndTime',
          width: '170px',
        },
        {
          label: '开标时间',
          prop: 'bidOpeningTime',
          width: '170px',
        },
        {
          label: '联系人',
          prop: 'contactPerson',
        },
        {
          label: '联系电话',
          prop: 'contactPhone',
        },
        {
          label: '操作',
          prop: 'action',
          width: '120px',
          fixed: 'right',
        },
      ],
      [NOTICE_TYPE_ENUM.PURCHASE_INVITATION]: [
        // {
        //   label: '序号',
        //   type: 'index',
        //   width: '60px',
        // },
        // {
        //   label: '公告类型',
        //   prop: 'noticeType',
        //   enums: SOURCING_METHOD_OPTIONS,
        // },
        {
          label: '寻源方式',
          prop: 'sourcingMethod',
          enums: SOURCING_METHOD_OPTIONS,
        },
        {
          label: '项目编号',
          prop: 'projectCode',
        },
        {
          label: '项目名称',
          prop: 'projectName',
        },
        {
          label: '公告标题',
          prop: 'noticeTitle',
        },
        {
          label: '公告附件',
          prop: 'noticeAttachment',
          render: ({ row }: any) => {
            return row?.noticeAttachment?.length ? <FileRenderer value={row?.noticeAttachment} /> : '--';
          },
          width: '200px',
        },
        {
          label: '发布人',
          prop: 'publisher',
        },
        {
          label: '发布时间',
          prop: 'publishTime',
          width: '170px',
        },
        {
          label: '报名开始时间',
          prop: 'registrationStartTime',
          width: '170px',
        },
        {
          label: '报名截止时间',
          prop: 'registrationEndTime',
          width: '170px',
        },
        {
          label: '开标时间',
          prop: 'bidOpeningTime',
          width: '170px',
        },
        {
          label: '联系人',
          prop: 'contactPerson',
        },
        {
          label: '联系电话',
          prop: 'contactPhone',
        },
        {
          label: '操作',
          prop: 'action',
          width: '120px',
          fixed: 'right',
        },
      ],
      [NOTICE_TYPE_ENUM.BID_ANNOUNCEMENT]: [
        // {
        //   label: '序号',
        //   type: 'index',
        //   width: '60px',
        // },
        // {
        //   label: '公告类型',
        //   prop: 'noticeType',
        //   enums: SOURCING_METHOD_OPTIONS,
        // },
        {
          label: '寻源方式',
          prop: 'sourcingMethod',
          enums: SOURCING_METHOD_OPTIONS,
        },
        {
          label: '项目编号',
          prop: 'projectCode',
        },
        {
          label: '项目名称',
          prop: 'projectName',
        },
        {
          label: '公示标题',
          prop: 'publicityTitle',
        },
        {
          label: '公示附件',
          prop: 'noticeAttachment',
          render: ({ row }: any) => {
            return row?.noticeAttachment?.length ? <FileRenderer value={row?.noticeAttachment} /> : '--';
          },
          width: '200px',
        },
        {
          label: '发布人',
          prop: 'publisher',
        },
        {
          label: '发布时间',
          prop: 'publishTime',
          width: '170px',
        },
        {
          label: '公示开始时间',
          prop: 'publicityStartTime',
          width: '170px',
        },
        {
          label: '公示截止时间',
          prop: 'publicityEndTime',
          width: '170px',
        },
        {
          label: '公示状态',
          prop: 'publicityStatus',
          enums: PUBLICITY_STATUS_OPTIONS,
        },
        {
          label: '操作',
          prop: 'action',
          width: '120px',
          fixed: 'right',
        },
      ],
      [NOTICE_TYPE_ENUM.BID_NOTICE]: [
        // {
        //   label: '序号',
        //   type: 'index',
        //   width: '60px',
        // },
        // {
        //   label: '公告类型',
        //   prop: 'noticeType',
        //   enums: SOURCING_METHOD_OPTIONS,
        // },
        {
          label: '寻源方式',
          prop: 'sourcingMethod',
          enums: SOURCING_METHOD_OPTIONS,
        },
        {
          label: '项目编号',
          prop: 'projectCode',
        },
        {
          label: '项目名称',
          prop: 'projectName',
        },
        {
          label: '公告标题',
          prop: 'noticeTitle',
        },
        {
          label: '公告附件',
          prop: 'noticeAttachment',
          render: ({ row }: any) => {
            return row?.noticeAttachment?.length ? <FileRenderer value={row?.noticeAttachment} /> : '--';
          },
          width: '200px',
        },
        {
          label: '发布人',
          prop: 'publisher',
        },
        {
          label: '发布时间',
          prop: 'publishTime',
          width: '170px',
        },
        {
          label: '操作',
          prop: 'action',
          width: '120px',
          fixed: 'right',
        },
      ],
      [NOTICE_TYPE_ENUM.RECRUITMENT]: [
        // {
        //   label: '序号',
        //   type: 'index',
        //   width: '60px',
        // },
        {
          label: '公告类型',
          prop: 'expertExtractionStatus1',
          enums: SOURCING_METHOD_OPTIONS,
        },
        {
          label: '招募项目编号',
          prop: 'expertExtractionStatus2',
          enums: SOURCING_METHOD_OPTIONS,
        },
        {
          label: '招募项目标题',
          prop: 'sectionName',
        },
        {
          label: '招募类型',
          prop: 'projectName',
        },
        {
          label: '招募方式',
          prop: 'projectName',
        },
        {
          label: '招募附件',
          prop: 'projectName',
        },
        {
          label: '发布人',
          prop: 'projectName',
        },
        {
          label: '发布时间',
          prop: 'projectName',
        },
        {
          label: '操作',
          prop: 'action',
          width: '120px',
          fixed: 'right',
        },
      ],
    };
  });

  return {
    columns,
    searchFields,
    searchData,
  };
}
