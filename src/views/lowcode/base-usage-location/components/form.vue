<template>
	<yun-drawer
		v-model="visible"
		destroy-on-close
		modal
		custom-class="form-drawer"
		close-on-click-modal
		confirm-button-text="确定"
		cancel-button-text="取消"
		:confirm-button-disabled="loading"
		:title="title"
		:size="size"
		@confirm="confirmHandler"
		@close="closeHandler"
	>
		<el-form
			ref="vForm"
			:model="formData"
			:rules="rules"
			:label-width="120"
			label-position="top"
		>
			<el-row :gutter="12">
				<el-col :span="24">
					<el-form-item
						label="选择组织"
						prop="deptId"
					>
						<el-cascader
							v-model="formData.deptId"
							style="width: 100%"
							clearable
							showAllLevels
							:options="treeDeptData"
							:props="{
								multiple: false,
								value: 'id',
								label: 'name',
								children: 'children',
								checkStrictly: true,
								emitPath: false,
								expandTrigger: 'hover',
								showAllLevels: false,
							}"
							@change="handleChange"
						/>
					</el-form-item>
				</el-col>
				<el-col :span="24">
					<el-form-item
						label="牧场名称"
						prop="locationName"
					>
						<el-input
							v-model="formData.locationName"
							type="text"
							clearable
							:maxlength="50"
							icon="custom-search"
						/>
					</el-form-item>
				</el-col>
				<el-col :span="24">
					<el-form-item
						label="省市区"
						prop="district"
					>
						<el-cascader
							v-model="formData.district"
							style="width: 100%"
							clearable
							showAllLevels
							:options="districtOptions"
							:props="{
								multiple: false,
								value: 'value',
								label: 'label',
								children: 'children',
								checkStrictly: false,
								emitPath: false,
								expandTrigger: 'hover',
							}"
						/>
					</el-form-item>
				</el-col>
				<el-col :span="24">
					<el-form-item
						label="详细地址"
						prop="address"
					>
						<el-input
							v-model="formData.address"
							type="text"
							:maxlength="100"
							clearable
							icon="custom-search"
						/>
					</el-form-item>
				</el-col>
			</el-row>
		</el-form>
	</yun-drawer>
</template>

<script setup lang="ts">
import { ref, reactive, computed, nextTick, watch } from 'vue';
import { ElMessage } from 'yun-design';
import { Plus, Delete } from '@element-plus/icons-vue';
import { useDict } from '@/hooks/dict';
import { addObj, putObj, getObj } from '@/api/lowcode/base-usage-location/index';
import { useUserInfo } from '@/stores/userInfo';
import { rule } from '@/utils/validate';
import { Session } from '@/utils/storage';
import moment from 'moment';
import { getDeptTree, getTreeItem } from '../hooks/useTable';

// 状态管理
const visible = ref(false);
const loading = ref(false);
const isEdit = ref(false);
const vForm = ref();
const formData = reactive({
	deptId: '',
	locationName: '',
	district: [],
	address: '',
});
const rules = reactive({
	deptId: [
		{
			required: true,
			message: '此项必填',
			trigger: ['blur', 'change'],
		},
	],
	locationName: [
		{
			required: true,
			message: '此项必填',
			trigger: ['blur', 'change'],
		},
	],
	district: [
		{
			required: true,
			message: '此项必填',
			trigger: ['blur', 'change'],
		},
	],
	address: [
		{
			required: true,
			message: '此项必填',
			trigger: ['blur', 'change'],
		},
	],
});

// 计算属性
const title = computed(() => (isEdit.value ? '编辑' : '新增'));
const size = computed(() => 'small');

const { treeDeptData, getTree } = getDeptTree();
// 用户信息
const userStore = useUserInfo();
const emits = defineEmits(['getData']);

// 为每个API组件创建响应式变量
const districtOptions = ref([]);

// 为每个API组件创建获取数据的方法
function fetchDistrictOptions(options, query, formData) {
	let requestUrl = 'https://oss-public.yunlizhi.cn/frontend/yun-design/area.json';
	const method = 'GET';

	// 构建请求参数
	const requestParams = {};

	const token = Session.getToken();
	const tenantId = Session.getTenant();
	// 构建请求配置
	const requestConfig = {
		method,
		headers: {
			'Content-Type': 'application/json',
			'TENANT-ID': tenantId,
			AUTHORIZATION: `Bearer ${token}`,
		},
	};

	// 如果是GET请求，将参数附加到URL
	if (method === 'GET') {
		const queryParams = new URLSearchParams();
		Object.entries(requestParams).forEach(([key, value]) => {
			if (value !== undefined && value !== null) {
				queryParams.append(key, String(value));
			}
		});
		const queryString = queryParams.toString();
		if (queryString) {
			requestUrl += (requestUrl.includes('?') ? '&' : '?') + queryString;
		}
	} else {
		// 非GET请求，将参数放在body中
		requestConfig.body = JSON.stringify(requestParams);
	}

	// 发送请求并处理响应
	fetch(requestUrl, requestConfig)
		.then((response) => {
			if (!response.ok) {
				throw new Error('API request failed');
			}
			return response.json();
		})
		.then((data) => {
			// 根据配置的路径获取数据
			let result = data;

			// 处理数据
			const processData = (items) => {
				return items.map((item) => ({
					label: item['areaName'],
					value: item['areaCode'],

					children: item['children'] ? processData(item['children']) : undefined,
				}));
			};

			options.value = Array.isArray(result) ? processData(result) : [];
		})
		.catch((error) => {
			console.error('Failed to fetch API options:', error);
			options.value = [];
		});
}

// 初始化时获取所有API数据
fetchDistrictOptions(districtOptions);

// 监听 districtOptions 变化，处理导入数据的 district 字段
watch(districtOptions, (newOptions) => {
	if (newOptions.length > 0 && formData.district && typeof formData.district === 'string' && formData.district.indexOf('/') !== -1) {
		const cleanedDistrict = formData.district.replace(/\s*\/\s*/g, '/');
		const districtNames = cleanedDistrict.split('/');
		const realDistrictValues = findDistrictByNames(districtNames, newOptions);
		
		if (realDistrictValues.length > 0) {
			formData.district = realDistrictValues;
		}
	}
}, { deep: true });

// 关闭处理
function closeHandler() {
	visible.value = false;
}

// 根据地区名称查找真实的地区值
function findDistrictByNames(districtNames: string[], options: any[]): string[] {
	const result: string[] = [];
	
	function findInLevel(names: string[], levelOptions: any[], currentLevel: number): boolean {
		if (currentLevel >= names.length) return true;
		
		const currentName = names[currentLevel];
		
		for (const option of levelOptions) {
			if (option.label === currentName) {
				result.push(option.value);
				
				// 如果还有下一级，继续查找
				if (currentLevel + 1 < names.length && option.children) {
					if (findInLevel(names, option.children, currentLevel + 1)) {
						return true;
					}
				} else if (currentLevel + 1 >= names.length) {
					// 已经找到所有级别
					return true;
				}
				
				// 如果没找到下一级，移除当前值并继续查找
				result.pop();
			}
		}
		
		return false;
	}
	
	findInLevel(districtNames, options, 0);
	return result;
}

// 格式化表单数据
async function formatFrom(row: Record<string, any>) {
	for (let key in formData) {
		delete formData[key];
	}
	if (!row.id) return;
	const res = await getObj(row.id);
	Object.assign(formData, res.data);
	if (row.district) {
		if (Array.isArray(row.district)) {
			formData.district = row.district.map((item) => {
				if (Array.isArray(item)) {
					return item;
				}
				return item.split(',');
			});
		} else if (typeof row.district === 'string' && row.district.indexOf('/') !== -1) {
			const cleanedDistrict = row.district.replace(/\s*\/\s*/g, '/');
			const districtNames = cleanedDistrict.split('/');
			if (districtOptions.value.length > 0) {
				const realDistrictValues = findDistrictByNames(districtNames, districtOptions.value);
				if (realDistrictValues.length > 0) {
					formData.district = realDistrictValues;
				} else {
					// 如果没找到匹配的，保持原值
					formData.district = row.district;
				}
			} else {
				formData.district = row.district;
			}
		} else {
			formData.district = row.district;
		}
	}
}

// 格式化提交参数
function formatParams() {
	const baseParams = {};

	// 处理特殊字段类型
	if (Array.isArray(formData.district)) {
		baseParams.district = formData.district.map((item) => {
			if (Array.isArray(item)) {
				return item.join(',');
			}
			return item;
		});
	}

	return {
		...formData,
		...baseParams,
	};
}

// 确认处理
async function confirmHandler(done: () => void, loading: { value: boolean }) {
	try {
		await vForm.value?.validate();
		loading.value = true;
		const api = isEdit.value ? putObj : addObj;
		const params = formatParams();
		await api(params);
		done();
		emits('getData', formData);
	} finally {
		loading.value = false;
	}
}

// 显示表单
function show(row?: Record<string, any>) {
	visible.value = true;
	isEdit.value = !!row;
	formatFrom(row || {});
	getTree();
	nextTick(() => {
		vForm.value?.clearValidate();
	});
}

// 日期时间范围默认值
const defaultTime = [new Date(2000, 1, 1, 0, 0, 0), new Date(2000, 2, 1, 23, 59, 59)];

// 字典数据
const getDict = computed(() => (str: string) => {
	if (!str) return [];
	const value = useDict(str)?.[str]?.value;
	return value;
});

// 生成明细表新行的初始数据
function getEnhancedTableRow(widget: Widget): Record<string, any> {
	const row: Record<string, any> = {};
	if (Array.isArray(widget.widgetList)) {
		for (const colWidget of widget.widgetList) {
			const name = colWidget.options?.name;
			if (!name) continue;
			let value = colWidget.options?.defaultValue;
			if (value === undefined) {
				if (colWidget.type === 'number') value = 0;
				else value = '';
			}
			row[name] = value;
		}
	}
	return row;
}

// 添加明细表行
function handleAddRow(tableName: string) {
	if (!formData[tableName]) {
		formData[tableName] = [];
	}

	formData[tableName].push({});
}

// 删除明细表行
function handleRemoveRow(tableName: string, index: number) {
	if (!formData[tableName]) return;
	formData[tableName].splice(index, 1);
}

function handleChange(val) {
	if (val) {
		formData.deptName =
			getTreeItem(treeDeptData.value, formData.deptId, {
				value: 'id',
			})?.name ?? formData.deptName;
	}
}
// 暴露方法
defineExpose({
	show,
	formData,
	rules,
});
</script>

<style lang="scss">
.form-drawer {
	.el-form .el-form-item:last-of-type {
		margin-bottom: 24px !important;
	}
}
</style>

<style lang="scss" scoped>
.el-form-item {
	margin-bottom: 18px;
}

.table-header {
	margin-bottom: 15px;
	.label {
		font-weight: bold;
		font-size: 18px;
		padding-left: 8px;
		border-left: 4px solid var(--el-color-primary);
		line-height: 1.2;
	}
}

:deep(.el-form-item__label) {
	font-weight: normal;
}

:deep(.el-table) {
	.el-table__header-wrapper {
		th {
			background-color: var(--el-fill-color-light);
		}
	}
}
</style>
