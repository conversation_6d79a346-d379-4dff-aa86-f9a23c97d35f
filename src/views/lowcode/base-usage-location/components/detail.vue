<template>
	<yun-drawer
		v-model="visible"
		:show-cancel-button="false"
		destroy-on-close
		title="详情"
		size="small"
		@confirm="handleConfirm"
	>
		<Detail :groups="processedGroups" :data="form"/>
	</yun-drawer>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue';
import Detail from '@/components/Detail/index.vue';
import { getObj } from '@/api/lowcode/base-usage-location/index';
import { getDeptTree, getTreeItem, fetchDistrictOptions, findPath } from '../hooks/useTable';

const { treeDeptData, getTree } = getDeptTree();

const districtOptions = ref([]);
fetchDistrictOptions(districtOptions);

interface FormData {
	[key: string]: any;
}

// 静态生成的分组配置
const processedGroups = [
	{
    title: "",
		columns: [
			{
				prop: 'deptName',
				label: '组织',
				type: 'text',
				span: 3,
			},
			{
				prop: 'locationName',
				label: '牧场名称',
				type: 'text',
				span: 3,
			},
			{
				prop: 'districtName',
				label: '省市区',
				type: 'text',
				span: 3,
			},
			{
				prop: 'address',
				label: '详细地址',
				type: 'text',
				span: 3,
			}
		]
	}
];

const visible = ref(false);
const form = ref<FormData>({});
watch(
	() => [districtOptions.value, form.value],
	([tree, data]) => {
		if (tree.length && data.district && !data.districtName) {
			form.value.districtName = data.district.indexOf('/') !== -1 ? data.district :(findPath(tree, data.district) || []).join(' / ');
		}
	},
	{
		deep: true,
	}
);

async function handleConfirm(done: () => void) {
	done();
}

async function show(row: FormData) {
	await getTree();
	const res = await getObj(row.id);
	form.value = {
		...res.data,
		deptName:
			getTreeItem(treeDeptData.value, res.data.deptId, {
				value: 'id',
			})?.name ?? '-',
	};
	visible.value = true;
}

defineExpose({
	show,
});
</script>

<style scoped lang="scss">
</style>