import { ref, computed } from 'vue';
import moment from 'moment';
import { useDict } from '@/hooks/dict';
import { ElImage } from 'element-plus';
import { Session } from '/@/utils/storage';
import { deptTree } from '@/api/admin/dept';

const getDict = computed(() => (str) => {
	if (!str) return [];
	const value = useDict(str)?.[str]?.value;
	return value;
});
export const getTreeItem = (tree: any[], val: string | number, config?: { value?: string; children?: string }): any => {
	const { value = 'value', children = 'children' } = config || {};
	let item = {};
	for (let i = 0, l = tree.length; i < l; i += 1) {
		if (tree[i][value] === val) {
			item = tree[i];
			break;
		} else {
			if (tree[i]?.[children]?.length) {
				item = getTreeItem(tree[i][children], val, config);
				if (Object.keys(item).length) {
					break;
				}
			}
		}
	}
	return item;
};
export const findPath = (options, targetValue, path = []) => {
	for (const option of options) {
		// 如果找到目标值，返回完整路径
		if (option.value == targetValue) {
			return [...path, option.label];
		}
		// 如果有子节点，递归查找
		if (option.children && option.children.length > 0) {
			const result = findPath(option.children, targetValue, [...path, option.label]);
			if (result) return result;
		}
	}
	return null;
};
export function fetchDistrictOptions(options) {
	let requestUrl = 'https://oss-public.yunlizhi.cn/frontend/yun-design/area.json';
	const method = 'GET';

	// 构建请求参数
	const requestParams = {

	};

	const token = Session.getToken();
	const tenantId = Session.getTenant();
	// 构建请求配置
	const requestConfig = {
		method,
		headers: {
			'Content-Type': 'application/json',
			'TENANT-ID': tenantId,
			AUTHORIZATION: `Bearer ${token}`,
		},
	};

	// 如果是GET请求，将参数附加到URL
	if (method === 'GET') {
		const queryParams = new URLSearchParams();
		Object.entries(requestParams).forEach(([key, value]) => {
			if (value !== undefined && value !== null) {
				queryParams.append(key, String(value));
			}
		});
		const queryString = queryParams.toString();
		if (queryString) {
			requestUrl += (requestUrl.includes('?') ? '&' : '?') + queryString;
		}
	} else {
		// 非GET请求，将参数放在body中
		requestConfig.body = JSON.stringify(requestParams);
	}

	// 发送请求并处理响应
	fetch(requestUrl, requestConfig)
    .then(response => {
			if (!response.ok) {
				throw new Error('API request failed');
			}
			return response.json();
		})
    .then(data => {
			// 根据配置的路径获取数据
			let result = data;

			// 处理数据
			const processData = (items) => {
        return items.map(item => ({
					label: item['areaName'],
					value: item['areaCode'],

          children: item['children'] ? 
            processData(item['children']) : undefined
				}));
			};

			options.value = Array.isArray(result) ? processData(result) : [];
		})
    .catch(error => {
			console.error('Failed to fetch API options:', error);
			options.value = [];
		});
}

export function getDeptTree() {
	const treeDeptData = ref([]);
	async function getTree() {
		const resp = await deptTree();
		treeDeptData.value = resp.data;
	}
	return {
		treeDeptData,
		getTree,
	};
}
export const useTable = () => {
	const districtOptions = ref([]);
	fetchDistrictOptions(districtOptions);
	const { treeDeptData, getTree } = getDeptTree();
	getTree();
	const searchFields = computed(() => [
		{
			prop: 'locationName',
			label: '牧场名称',
			component: 'el-input',
			componentAttrs: {
				clearable: true,
			},
		},
		{
			prop: 'deptId',
			label: '所属组织',
			component: 'el-cascader',
			attrs: {
				options: treeDeptData.value,
				filterable: true,
				props: {
					value: 'id',
					label: 'name',
					children: 'children',
					checkStrictly: true,
					emitPath: false,
					showAllLevels: false,
				},
			},
		},
		{
			prop: 'address',
			label: '详细地址',
			component: 'el-input',
			componentAttrs: {
				placeholder: '请输入详细地址',
				clearable: true,
			},
		},
	]);

	const columns = computed(() => [
		{
			label: '序号',
			type: 'index',
		},
		{
			label: '牧场编码',
			prop: 'locationCode',
		},
		{
			label: '牧场名称',
			prop: 'locationName',
		},
		{
			label: '所属组织',
			prop: 'deptId',
			formatter(row) {
				return getTreeItem(treeDeptData.value, row.deptId, {
					value: 'id',
				})?.name ?? '--'
			}
		},
		{
			label: '省市区',
			prop: 'district',
			formatter(row){
				const value = row['district'];
				const options = districtOptions.value;
				if (!value) return '--';

				// 如果是数组，说明是多选
				if (Array.isArray(value)) {
					return value
						.map((v) => {
							// 如果是字符串，说明是逗号分隔的路径
							if (typeof v === 'string') {
								const path = v.split(',');
								let currentOptions = options;
								const labels = [];

								// 遍历路径找到对应的标签
								for (const val of path) {
									const option = currentOptions.find((opt) => opt.value == val);
									if (option) {
										labels.push(option.label);
										currentOptions = option.children || [];
									}
								}
								return labels.join(' / ');
							}
							return v;
						})
						.join('、');
				}

				// 如果是字符串，说明是单选，数据由页面新增而来
				if (typeof value === 'string' && value.indexOf('/') === -1) {
					// 递归查找函数
					// 查找完整路径
					const path = findPath(options, value);
					return path ? path.join(' / ') : '--';
				}

				// 如果是字符串，说明是单选，数据由页面导入而来
				if (typeof value === 'string' && value.indexOf('/') !== -1) {
					return value;
				}

				return '--';
			}
		},
		{
			label: '详细地址',
			prop: 'address',
		},
		{
			label: '创建人',
			prop: 'createBy',
		},
		{
			label: '创建时间',
			prop: 'createTime',
      formatter(row) {
        return row.createTime?.split(' ')[0];
      }
		},
		{
			label: '修改人',
			prop: 'updateBy',
		},	
		{
			label: '修改时间',
			prop: 'updateTime',
      formatter(row) {
        return row.updateTime?.split(' ')[0];
      }
		},
		// {
		// 	label: '状态',
		// 	prop: 'status',
    //   formatter(row){
		// 		const value = row['status'];
    //     const options = getDict.value('enable_status') || [];;
		// 		if (Array.isArray(value)) {
		// 			return value.map((item) => options?.find((opt) => opt.value == item)?.label).join('、');
		// 		}
		// 		return options?.find((item) => item.value == value)?.label || '--';
    //   }
		// },
		{
      prop: "action",
      fixed: "right",
			width: 150,
      label: "操作",
		},
	])
	return {
		columns,
		searchFields,
	};
};