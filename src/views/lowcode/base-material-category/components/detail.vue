<template>
  <yun-drawer
    v-model="visible"
    :show-cancel-button="false"
    destroy-on-close
    title="详情"
    size="X-large"
    @confirm="handleConfirm"
  >
    <Detail :groups="processedGroups" :data="form"/>
  </yun-drawer>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import Detail from '@/components/Detail/index.vue';
import { getObj } from '@/api/lowcode/base-material-category/index';

interface FormData {
  [key: string]: any;
}

// 静态生成的分组配置
const processedGroups = [
  {
    title: "",
    columns: [
      {
        prop: 'categoryCode',
        label: '物料分类编码',
        type: 'text'
      },
      {
        prop: 'categoryName',
        label: '物料分类名称',
        type: 'text'
      },
      {
        prop: 'parentCategoryCode',
        label: '上级物料分类编码',
        type: 'text',
      },
      {
        prop: 'parentCategoryName',
        label: '上级物料分类名称',
        type: 'text'
      },
      {
        prop: 'dataSource',
        label: '来源标识',
        type: 'text',
      },
      {
        prop: 'status',
        label: '状态',
        type: 'text',
      }
    ]
  }
];

const visible = ref(false);
const form = ref<FormData>({});

async function handleConfirm(done: () => void) {
  done();
}

async function show(row: FormData) {
  const res = await getObj(row.id);
  form.value = res.data;
  form.value.status = res.data.status === 'DISABLED' ? '禁用' : '启用';
  visible.value = true;
}

defineExpose({
  show,
});
</script>

<style scoped lang="scss">
</style>