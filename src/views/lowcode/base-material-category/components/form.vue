<template>
	<yun-drawer
		v-model="visible"
		destroy-on-close
		modal
		custom-class="form-drawer"
		close-on-click-modal
		confirm-button-text="确定"
		cancel-button-text="取消"
		:confirm-button-disabled="loading"
		:title="title"
		:size="size"
		@confirm="confirmHandler"
		@close="closeHandler"
	>
		<el-form
			ref="vForm"
			:model="formData"
			:rules="rules"
			:label-width="120"
			label-position="top"
		>
			<el-row :gutter="12">
				<el-col :span="12">
					<el-form-item
						label="物料分类编码"
						prop="categoryCode"
					>
						<el-input
							placeholder="请输入物料分类编码"
							v-model="formData.categoryCode"
							type="text"
							clearable
							icon="custom-search"
						/>
					</el-form-item>
				</el-col>
				<el-col :span="12">
					<el-form-item
						label="物料分类名称"
						prop="categoryName"
					>
						<el-input
							placeholder="请输入物料分类名称"
							v-model="formData.categoryName"
							type="text"
							clearable
							icon="custom-search"
						/>
					</el-form-item>
				</el-col>
				<el-col :span="12">
					<el-form-item
						label="上级物料分类编码"
						prop="parentCategoryCode"
					>
						<el-cascader
							ref="cascader"
							v-model="formData.parentCategoryCode"
							style="width: 100%"
							clearable
							showAllLevels
							placeholder="请选择上级物料分类编码"
							:options="treeOptions"
							:props="{
								multiple: false,
								value: 'value',
								label: 'label',
								children: 'children',
								checkStrictly: true,
								emitPath: false,
								expandTrigger: 'hover',
							}"
							filterable
							@change="handleChange"
						/>
					</el-form-item>
				</el-col>
				<el-col :span="12">
					<el-form-item
						label="状态"
						prop="status"
					>
						<el-radio-group v-model="formData.status">
							<el-radio
								v-for="option in statusOptions"
								:key="option.value"
								:label="option.value"
							>
								{{ option.label }}
							</el-radio>
						</el-radio-group>
					</el-form-item>
				</el-col>
				<!-- <el-col :span="8">
					<el-form-item
						label="上级物料分类名称"
						prop="parentCategoryName"
					>
						<el-input
							placeholder="请输入上级物料分类名称"
							v-model="formData.parentCategoryName"
							type="text"
							clearable
							icon="custom-search"
						/>
					</el-form-item>
				</el-col> -->
				<!-- <el-col :span="8">
					<el-form-item
						label="来源标识"
						prop="dataSource"
					>
						<el-select
							v-model="formData.dataSource"
							placeholder="请选择来源标识"
							style="width: 100%"
							clearable
							:multipleLimit="0"
						>
							<el-option
								v-for="option in getDict('data_source')"
								:key="option.value"
								:label="option.label"
								:value="option.value"
							/>
						</el-select>
					</el-form-item>
				</el-col> -->

				<el-col :span="24">
					<el-form-item label="备注" prop="remark">
						<el-input
							placeholder="请输入备注"
							v-model="formData.remark"
							type="textarea"
							clearable
							row="3"
							icon="custom-search"
						/>
					</el-form-item>
				</el-col>

				</el-row>
		</el-form>
	</yun-drawer>
</template>

<script setup lang="ts">
import { ref, reactive, computed, nextTick } from 'vue';
import { ElMessage } from 'yun-design';
import { Plus, Delete } from '@element-plus/icons-vue';
import { useDict } from '@/hooks/dict';
import { addObj, putObj, getObj, getTreeData } from '@/api/lowcode/base-material-category/index';
import { useUserInfo } from '@/stores/userInfo';
import { rule } from '@/utils/validate';
import { Session } from '@/utils/storage';
import moment from 'moment';

// 定义表单数据接口
interface FormData {
	categoryCode?: string;
	categoryName?: string;
	parentCategoryCode?: string | string[];
	parentCategoryName?: string;
	status?: string;
	[key: string]: any;
}

// 定义树形数据接口
interface TreeOption {
	label: string;
	value: string;
	children?: TreeOption[];
}

// 状态选项
const statusOptions = [
	{ label: '禁用', value: 'DISABLED' },
	{ label: '启用', value: 'ENABLED' }
];

// 状态管理
const visible = ref(false);
const loading = ref(false);
const isEdit = ref(false);
const vForm = ref();
const formData = reactive<FormData>({status: 'ENABLED'});
const treeOptions = ref<TreeOption[]>([]); // 树形数据选项
const cascader = ref();

const rules = reactive({
	categoryCode: [
		{
			required: true,
			message: '此项必填',
			trigger: ['blur', 'change'],
		},
		{ max: 30, message: '长度在30个字符内' }
	],
	categoryName: [
		{
			required: true,
			message: '此项必填',
			trigger: ['blur', 'change'],
		},
		{ max: 30, message: '长度在30个字符内' }
	],
});

// 计算属性
const title = computed(() => (isEdit.value ? '编辑' : '新增'));
const size = computed(() => 'large');

// 用户信息
const userStore = useUserInfo();
const emits = defineEmits(['getData']);

// 获取树形数据
async function fetchTreeData() {
	try {
		const res = await getTreeData();
		treeOptions.value = transformTreeData(res.data || []);
	} catch (error) {
		console.error('获取树形数据失败:', error);
		treeOptions.value = [];
	}
}

// 转换树形数据格式
function transformTreeData(data: any[]): TreeOption[] {
	return data.map(item => ({
		label: item.categoryName,
		value: item.categoryCode,
		children: item.children ? transformTreeData(item.children) : undefined
	}));
}

// 关闭处理
function closeHandler() {
	visible.value = false;
}

// 格式化表单数据
async function formatFrom(row: Record<string, any>) {
	for (let key in formData) {
		delete formData[key];
	}
	formData['status'] = 'ENABLED';
	if (!row.id) return;
	const res = await getObj(row.id);
	Object.assign(formData, res.data);
	if (Array.isArray(row.parentCategoryCode)) {
		formData.parentCategoryCode = row.parentCategoryCode.map((item) => {
			if (Array.isArray(item)) {
				return item;
			}
			return item.split(',');
		});
	}
}

// 格式化提交参数
function formatParams() {
	const baseParams: any = {};

	// 处理特殊字段类型
	if (Array.isArray(formData.parentCategoryCode)) {
		baseParams.parentCategoryCode = formData.parentCategoryCode.map((item: any) => {
			if (Array.isArray(item)) {
				return item.join(',');
			}
			return item;
		});
	}

	return {
		...formData,
		...baseParams,
	};
}

// 确认处理
async function confirmHandler(done: () => void, loading: { value: boolean }) {
	try {
		await vForm.value?.validate();
		loading.value = true;
		const api = isEdit.value ? putObj : addObj;
		const params = formatParams();
		console.log(params, 'paramsparams')
		await api(params);
		ElMessage.success(isEdit.value ? '编辑成功' : '新增成功');
		done();
		emits('getData', formData);
	} catch (error) {
		ElMessage.error('表单验证或提交失败');
	} finally {
		loading.value = false;
	}
}

// 显示表单
async function show(row?: Record<string, any>) {
	visible.value = true;
	isEdit.value = !!row;
	await fetchTreeData(); // 获取树形数据
	formatFrom(row || {});
	nextTick(() => {
		vForm.value?.clearValidate();
	});
}

// 日期时间范围默认值
const defaultTime = [new Date(2000, 1, 1, 0, 0, 0), new Date(2000, 2, 1, 23, 59, 59)];

// 字典数据
const getDict = computed(() => (str: string) => {
	if (!str) return [];
	const value = useDict(str)?.[str]?.value;
	return value;
});

// 生成明细表新行的初始数据（目前未使用，保留备用）
// function getEnhancedTableRow(widget: any): Record<string, any> {
// 	const row: Record<string, any> = {};
// 	if (Array.isArray(widget.widgetList)) {
// 		for (const colWidget of widget.widgetList) {
// 			const name = colWidget.options?.name;
// 			if (!name) continue;
// 			let value = colWidget.options?.defaultValue;
// 			if (value === undefined) {
// 				if (colWidget.type === 'number') value = 0;
// 				else value = '';
// 			}
// 			row[name] = value;
// 		}
// 	}
// 	return row;
// }

// 添加明细表行
function handleAddRow(tableName: string) {
	if (!formData[tableName]) {
		formData[tableName] = [];
	}

	formData[tableName].push({});
}

// 删除明细表行
function handleRemoveRow(tableName: string, index: number) {
	if (!formData[tableName]) return;
	formData[tableName].splice(index, 1);
}

function handleChange() {
	let labels = cascader.value.inputValue?.split(' / ') || [];
	let labelsLength = labels.length;
	formData.parentCategoryName = labelsLength ? labels[labelsLength - 1] : '';
}

// 暴露方法
defineExpose({
	show,
	formData,
	rules,
});
</script>

<style lang="scss">
.form-drawer {
	.el-form .el-form-item:last-of-type {
		margin-bottom: 24px !important;
	}
}
</style>

<style lang="scss" scoped>
.el-form-item {
	margin-bottom: 18px;
}

.table-header {
	margin-bottom: 15px;
	.label {
		font-weight: bold;
		font-size: 18px;
		padding-left: 8px;
		border-left: 4px solid var(--el-color-primary);
		line-height: 1.2;
	}
}

:deep(.el-form-item__label) {
	font-weight: normal;
}

:deep(.el-table) {
	.el-table__header-wrapper {
		th {
			background-color: var(--el-fill-color-light);
		}
	}
}
</style>
