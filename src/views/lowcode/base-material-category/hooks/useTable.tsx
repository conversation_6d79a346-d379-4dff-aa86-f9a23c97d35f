import { ref, computed } from 'vue';
import moment from 'moment';
import { useDict } from '@/hooks/dict';
import { ElImage } from 'element-plus';
import { Session } from '/@/utils/storage';

const getDict = computed(() => (str) => {
  if (!str) return [];
  const value = useDict(str)?.[str]?.value;
  return value;
});

const statusTypes = [{ label: '禁用', value: 'DISABLED' }, { label: '启用', value: 'ENABLED' }]

export const useTable = () => {
  const searchFields = computed(() => [
    {
      prop: 'categoryCode',
      label: '物料分类编码',
      component: 'el-input',
      componentAttrs: {
        placeholder: '请选择物料分类编码',
        clearable: true,
      }
    },
    {
      prop: 'categoryName',
      label: '物料分类名称',
      component: 'el-input',
      componentAttrs: {
        placeholder: '请选择物料分类名称',
        clearable: true,
      }
    },
    {
      label: '状态',
      prop: 'status',
      component: 'el-select',
      enums: statusTypes,
      componentAttrs: {
        placeholder: '请选择状态',
        clearable: true,
        filterable: true,
        multiple: true,
        collapseTags: true,
      },
    },
  ]);

  const columns = computed(() => [
    {
      label: '序号',
      type: 'index',
    },
    {
      label: '物料分类编码',
      prop: 'categoryCode',
    },
    {
      label: '物料分类名称',
      prop: 'categoryName',
    },
    // {
    //   label: '上级物料分类编码',
    //   prop: 'parentCategoryCode',
      // formatter(row){
      //   const value = row['parentCategoryCode'];
      //   const options = [{'label':'select 1','value':'1','children':[{'label':'child 1','value':'11'}]},{'label':'select 2','value':'2'},{'label':'select 3','value':'3'}];
      //   if (!value) return '--';

      //   // 如果是数组，说明是多选
      //   if (Array.isArray(value)) {
      //     return value
      //       .map((v) => {
      //         // 如果是字符串，说明是逗号分隔的路径
      //         if (typeof v === 'string') {
      //           const path = v.split(',');
      //           let currentOptions = options;
      //           const labels = [];

      //           // 遍历路径找到对应的标签
      //           for (const val of path) {
      //             const option = currentOptions.find((opt) => opt.value == val);
      //             if (option) {
      //               labels.push(option.label);
      //               currentOptions = option.children || [];
      //             }
      //           }
      //           return labels.join(' / ');
      //         }
      //         return v;
      //       })
      //       .join('、');
      //   }

      //   // 如果是字符串，说明是单选
      //   if (typeof value === 'string') {
      //     // 递归查找函数
      //     const findPath = (options, targetValue, path = []) => {
      //       for (const option of options) {
      //         // 如果找到目标值，返回完整路径
      //         if (option.value == targetValue) {
      //           return [...path, option.label];
      //         }
      //         // 如果有子节点，递归查找
      //         if (option.children && option.children.length > 0) {
      //           const result = findPath(option.children, targetValue, [...path, option.label]);
      //           if (result) return result;
      //         }
      //       }
      //       return null;
      //     };

      //     // 查找完整路径
      //     const path = findPath(options, value);
      //     return path ? path.join(' / ') : '--';
      //   }

      //   return '--';
      // }
    // },
    {
      label: '上级物料分类名称',
      prop: 'parentCategoryName',
    },
    {
      label: '来源标识',
      prop: 'dataSource',
    },
    {
      label: '状态',
      prop: 'status',
      formatter(row){
        const value = row['status'];
        const options = statusTypes;
        return options?.find((item) => item.value == value)?.label || '--';
      }
    },
    {
      label: '创建人',
      prop: 'createBy',
    },
    {
      label: '创建时间',
      prop: 'createTime',
      formatter(row) {
        return row.createTime?.split(' ')[0];
      }
    },
    {
      label: '修改人',
      prop: 'updateBy',
    },
    {
      label: '修改时间',
      prop: 'updateTime',
      formatter(row) {
        return row.updateTime?.split(' ')[0];
      }
    },
    {
      prop: "action",
      fixed: "right",
      width: 150,
      label: "操作",
    },
  ])
  return {
    columns,
    searchFields,
  };
};