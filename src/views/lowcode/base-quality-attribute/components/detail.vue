<template>
  <yun-drawer
    v-model="visible"
    :show-cancel-button="false"
    destroy-on-close
    title="详情"
    size="X-large"
    @confirm="handleConfirm"
  >
    <Detail :groups="processedGroups" :data="form"/>
  </yun-drawer>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import Detail from '@/components/Detail/index.vue';
import { getObj } from '@/api/lowcode/base-quality-attribute/index';
import { STATUS_LIST } from '@/views/lowcode/base-quality-attribute/hooks/const';

interface FormData {
  [key: string]: any;
}

// 静态生成的分组配置
const processedGroups = [
  {
    title: "",
    columns: [
      // {
      //   prop: 'status',
      //   label: '状态（DISABLED-禁用、ENABLED-启用）',
      //   type: 'enums',
      //   attrs: {
      //     options: [],
      //     dataSourceType: 'dict',
      //     dictType: 'enable_status'
      //   }
      // },
      // {
      //   prop: 'attributeCode',
      //   label: '属性编码',
      //   type: 'text'
      // },
      {
        prop: 'attributeName',
        label: '质量属性名称',
        type: 'text'
      },
      {
        prop: 'description',
        label: '属性说明',
        type: 'text'
      },
			{
				prop: 'status',
				label: '状态',
				type: 'enums',
				attrs: {
					options: STATUS_LIST,
				}
			},
    ]
  },
  // {
  //   title: '质量属性值',
  //   columns: [
  //     {
  //       prop: 'baseQualityAttributeValue',
  //       label: '',
  //       type: 'table',
  //       attrs: {
  //         showIndex: true,
  //         columns: [
  //           {
  //             prop: 'valueContent',
  //             label: '属性值内容',
  //             type: 'text'
  //           },
  //           {
  //             prop: 'materialCode',
  //             label: '适用物料编码',
  //             type: 'enums',
  //             attrs: {
  //               options: [
  //                 {
  //                   label: 'select 1',
  //                   value: 1
  //                 },
  //                 {
  //                   label: 'select 2',
  //                   value: 2
  //                 },
  //                 {
  //                   label: 'select 3',
  //                   value: 3
  //                 }
  //               ],
  //               dataSourceType: 'api',
  //               apiConfig: {
  //                 apiName: '/api/admin/baseMaterialInfo/page?size=1000&current=1',
  //                 method: 'POST',
  //                 params: [],
  //                 response: {
  //                   dataPath: 'data.records',
  //                   labelField: 'materialName',
  //                   valueField: 'materialCode',
  //                   childrenField: ''
  //                 }
  //               },
  //               dictType: ''
  //             }
  //           },
  //           {
  //             prop: 'rejectionStandard',
  //             label: '拒收标准',
  //             type: 'text'
  //           },
  //           {
  //             prop: 'penaltyStandard',
  //             label: '扣款标准',
  //             type: 'text'
  //           }
  //         ]
  //       }
  //     }
  //   ]
  // }
];

const visible = ref(false);
const form = ref<FormData>({});

async function handleConfirm(done: () => void) {
  done();
}

async function show(row: FormData) {
  const res = await getObj(row.id);
  form.value = res.data;
  visible.value = true;
}

defineExpose({
  show,
});
</script>

<style scoped lang="scss">
</style>
