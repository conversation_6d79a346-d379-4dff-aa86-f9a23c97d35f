<template>
  <yun-drawer
    v-model="visible"
    destroy-on-close
    modal
    custom-class="form-drawer"
    close-on-click-modal
    confirm-button-text="确定"
    cancel-button-text="取消"
    :confirm-button-disabled="loading"
    :title="title"
    :size="size"
    @confirm="confirmHandler"
    @close="closeHandler"
  >
    <el-form
      ref="vForm"
      :model="formData"
      :rules="rules"
      label-width="120"
      label-position="top"
    >
      <el-row :gutter="12">
<!--        <el-col :span="8">-->
<!--          <el-form-item label="状态（DISABLED-禁用、ENABLED-启用）" prop="status">-->
<!--            <el-select v-model="formData.status" style="width: 100%;" clearable :multipleLimit="0">-->
<!--                <el-option-->
<!--                  v-for="option in getDict('enable_status')"-->
<!--                  :key="option.value"-->
<!--                  :label="option.label"-->
<!--                  :value="option.value"-->
<!--                />-->
<!--            </el-select>-->
<!--          </el-form-item>-->
<!--        </el-col>-->
<!--        <el-col :span="8">-->
<!--          <el-form-item label="属性编码" prop="attributeCode">-->
<!--            <el-input v-model="formData.attributeCode" type="text" clearable icon="custom-search" />-->
<!--          </el-form-item>-->
<!--        </el-col>-->
        <el-col :span="8">
          <el-form-item label="质量属性名称" prop="attributeName">
            <el-input
              v-model="formData.attributeName"
              type="text"
              clearable
              maxlength="30"
              show-word-limit
              placeholder="请输入质量属性名称"
            />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="属性说明" prop="description">
            <el-input
              v-model="formData.description"
              type="textarea"
              :rows="3"
              clearable
              maxlength="100"
              show-word-limit
              placeholder="请输入属性说明"
            />
          </el-form-item>
        </el-col>
      </el-row>
<!--      <div class="table-header">-->
<!--        <div class="label">质量属性值</div>-->
<!--      </div>-->
<!--      <div class="w-full overflow-x-auto">-->
<!--        <el-table-->
<!--          :data="formData.baseQualityAttributeValueList || []"-->
<!--          :border="true"-->
<!--          stripe-->
<!--          class="w-full dark:bg-gray-800"-->
<!--          :header-cell-class-name="'!bg-gray-50 dark:!bg-gray-700'"-->
<!--          style="min-width: 600px"-->
<!--          :show-overflow-tooltip="false"-->
<!--        >-->
<!--          <el-table-column-->
<!--            type="index"-->
<!--            label="序号"-->
<!--            width="60"-->
<!--            align="center"-->
<!--            fixed-->
<!--          />-->
<!--          <el-table-column-->
<!--            label="属性值内容"-->
<!--            prop="valueContent"-->
<!--            min-width="200px"-->
<!--          >-->
<!--            <template #default="{ row }">-->
<!--              <el-input v-model="row.valueContent" type="text" clearable icon="custom-search" />-->
<!--            </template>-->
<!--          </el-table-column>-->
<!--          <el-table-column-->
<!--            label="适用物料编码"-->
<!--            prop="materialCode"-->
<!--            min-width="200px"-->
<!--          >-->
<!--            <template #default="{ row }">-->
<!--              <el-select v-model="row.materialCode" style="width: 100%;" clearable :multipleLimit="0">-->
<!--                <el-option-->
<!--                  v-for="option in materialCodeOptions"-->
<!--                  :key="option.value"-->
<!--                  :label="option.label"-->
<!--                  :value="option.value"-->
<!--                />-->
<!--              </el-select>-->
<!--            </template>-->
<!--          </el-table-column>-->
<!--          <el-table-column-->
<!--            label="拒收标准"-->
<!--            prop="rejectionStandard"-->
<!--            min-width="200px"-->
<!--          >-->
<!--            <template #default="{ row }">-->
<!--              <el-input v-model="row.rejectionStandard" type="text" clearable icon="custom-search" />-->
<!--            </template>-->
<!--          </el-table-column>-->
<!--          <el-table-column-->
<!--            label="扣款标准"-->
<!--            prop="penaltyStandard"-->
<!--            min-width="200px"-->
<!--          >-->
<!--            <template #default="{ row }">-->
<!--              <el-input v-model="row.penaltyStandard" type="text" clearable icon="custom-search" />-->
<!--            </template>-->
<!--          </el-table-column>-->
<!--          <el-table-column-->
<!--            label="操作"-->
<!--            fixed="right"-->
<!--            width="100"-->
<!--            align="center"-->
<!--          >-->
<!--            <template #default="{ $index }">-->
<!--              <el-button-->
<!--                type="danger"-->
<!--                size="small"-->
<!--                icon="Delete"-->
<!--                @click="handleRemoveRow('baseQualityAttributeValueList', $index)"-->
<!--              >删除</el-button>-->
<!--            </template>-->
<!--          </el-table-column>-->
<!--        </el-table>-->
<!--      </div>-->
<!--      <el-button-->
<!--        style="width: 100%; margin-top: 5px"-->
<!--        :icon="Plus"-->
<!--        @click="handleAddRow('baseQualityAttributeValueList')"-->
<!--      >-->
<!--        添加-->
<!--      </el-button>-->
    </el-form>
  </yun-drawer>
</template>

<script setup lang="ts">
import { ref, reactive, computed, nextTick } from 'vue';
import { ElMessage } from 'yun-design';
import { Plus, Delete } from '@element-plus/icons-vue';
import { useDict } from '@/hooks/dict';
import { addObj, putObj, getObj } from '@/api/lowcode/base-quality-attribute/index';
import { useUserInfo } from '@/stores/userInfo';
import { rule } from '@/utils/validate';
import { Session } from '@/utils/storage';
import moment from 'moment';

// 状态管理
const visible = ref(false);
const loading = ref(false);
const isEdit = ref(false);
const vForm = ref();
const formData = reactive({});
const rules = reactive({
  attributeName: [
    { required: true, message: '请输入质量属性名称', trigger: 'blur' },
    { max: 30, message: '质量属性名称不能超过30个字符', trigger: 'blur' }
  ],
  description: [
    { required: true, message: '请输入属性说明', trigger: 'blur' },
    { max: 100, message: '属性说明不能超过100个字符', trigger: 'blur' }
  ]
});

// 计算属性
const title = computed(() => (isEdit.value ? '编辑' : '新增'));
const size = computed(() => 'X-large');

// 用户信息
const userStore = useUserInfo();
const emits = defineEmits(['getData']);

// 为每个API组件创建响应式变量
const materialCodeOptions = ref([]);

// 为每个API组件创建获取数据的方法
function fetchMaterialCodeOptions(options, query, formData) {
  let requestUrl = '/api/admin/baseMaterialInfo/page?size=1000&current=1';
  const method = 'POST';

  // 构建请求参数
  const requestParams = {

  };

  const token = Session.getToken();
  const tenantId = Session.getTenant();
  // 构建请求配置
  const requestConfig = {
    method,
    headers: {
      'Content-Type': 'application/json',
      'TENANT-ID': tenantId,
      AUTHORIZATION: `Bearer ${token}`,
    },
  };

  // 如果是GET请求，将参数附加到URL
  if (method === 'GET') {
    const queryParams = new URLSearchParams();
    Object.entries(requestParams).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        queryParams.append(key, String(value));
      }
    });
    const queryString = queryParams.toString();
    if (queryString) {
      requestUrl += (requestUrl.includes('?') ? '&' : '?') + queryString;
    }
  } else {
    // 非GET请求，将参数放在body中
    requestConfig.body = JSON.stringify(requestParams);
  }

  // 发送请求并处理响应
  fetch(requestUrl, requestConfig)
    .then(response => {
      if (!response.ok) {
        throw new Error('API request failed');
      }
      return response.json();
    })
    .then(data => {
      // 根据配置的路径获取数据
      let result = data;

      const paths = 'data.records'.split('.');
      for (const path of paths) {
        result = result[path];
        if (result === undefined) break;
      }

      // 处理数据
      const processData = (items) => {
        return items.map(item => ({
          label: item['materialName'],
          value: item['materialCode'],

        }));
      };

      options.value = Array.isArray(result) ? processData(result) : [];
    })
    .catch(error => {
      console.error('Failed to fetch API options:', error);
      options.value = [];
    });
}

// 初始化时获取所有API数据
fetchMaterialCodeOptions(materialCodeOptions);

// 关闭处理
function closeHandler() {
  visible.value = false;
}

// 格式化表单数据
async function formatFrom(row: Record<string, any>) {
  for(let key in formData) {
    delete formData[key];
  }
  if (!row.id) return;
  const res = await getObj(row.id);
  Object.assign(formData, res.data);

}

// 格式化提交参数
function formatParams() {
  const baseParams = {};

  // 处理特殊字段类型


  return {
    ...formData,
    ...baseParams,
  };
}

// 确认处理
async function confirmHandler(done: () => void, loading: { value: boolean }) {
  try {
    await vForm.value?.validate();
    loading.value = true;
    const api = isEdit.value ? putObj : addObj;
    const params = formatParams();
    await api(params);
    done();
    emits('getData', formData);
  } catch (error) {
    ElMessage.error('表单验证或提交失败');
  } finally {
    loading.value = false;
  }
}

// 显示表单
function show(row?: Record<string, any>) {
  visible.value = true;
  isEdit.value = !!row;
  formatFrom(row || {});
  nextTick(() => {
    vForm.value?.clearValidate();
  });
}

// 日期时间范围默认值
const defaultTime = [new Date(2000, 1, 1, 0, 0, 0), new Date(2000, 2, 1, 23, 59, 59)];

// 字典数据
const getDict = computed(() => (str: string) => {
  if (!str) return [];
  const value = useDict(str)?.[str]?.value;
  return value;
});

// 生成明细表新行的初始数据
function getEnhancedTableRow(widget: Widget): Record<string, any> {
  const row: Record<string, any> = {};
  if (Array.isArray(widget.widgetList)) {
    for (const colWidget of widget.widgetList) {
      const name = colWidget.options?.name;
      if (!name) continue;
      let value = colWidget.options?.defaultValue;
      if (value === undefined) {
        if (colWidget.type === 'number') value = 0;
        else value = '';
      }
      row[name] = value;
    }
  }
  return row;
}

// 添加明细表行
function handleAddRow(tableName: string) {
  if (!formData[tableName]) {
    formData[tableName] = [];
  }

  formData[tableName].push({});
}

// 删除明细表行
function handleRemoveRow(tableName: string, index: number) {
  if (!formData[tableName]) return;
  formData[tableName].splice(index, 1);
}

// 暴露方法
defineExpose({
  show,
  formData,
  rules
});
</script>

<style lang="scss">
.form-drawer {
  .el-form .el-form-item:last-of-type {
    margin-bottom: 24px !important;
  }
}
</style>

<style lang="scss" scoped>
.el-form-item {
  margin-bottom: 18px;
}

.table-header {
  margin-bottom: 15px;
  .label {
    font-weight: bold;
    font-size: 18px;
    padding-left: 8px;
    border-left: 4px solid var(--el-color-primary);
    line-height: 1.2;
  }
}

:deep(.el-form-item__label) {
  font-weight: normal;
}

:deep(.el-table) {
  .el-table__header-wrapper {
    th {
      background-color: var(--el-fill-color-light);
    }
  }
}
</style>
