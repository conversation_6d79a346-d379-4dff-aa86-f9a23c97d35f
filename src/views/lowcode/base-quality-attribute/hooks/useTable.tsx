import { ref, computed } from 'vue';
import moment from 'moment';
import { useDict } from '@/hooks/dict';
import { ElImage } from 'element-plus';
import { Session } from '/@/utils/storage';
import { STATUS_LIST } from '@/views/lowcode/base-quality-attribute/hooks/const';

const getDict = computed(() => (str) => {
  if (!str) return [];
  const value = useDict(str)?.[str]?.value;
  return value;
});

export const useTable = () => {



  const searchFields = computed(() => [
    {
      prop: 'attributeName',
      label: '属性名称',
      component: 'el-input',
      componentAttrs: {
        placeholder: '请选择属性名称',
        clearable: true,
      }
    },
		{
			label: '状态',
			prop: 'status',
			component: 'el-select',
			enums: STATUS_LIST,
			componentAttrs: {
				placeholder: '请选择状态',
				clearable: true,
				filterable: true,
				multiple: true,
				collapseTags: true,
			},
		},
  ]);

  const columns = computed(() => [
    {
      label: '序号',
      type: 'index',
    },
		{
			label: '质量属性名称',
			prop: 'attributeName',
		},
		{
			label: '属性说明',
			prop: 'description',
		},
    {
      label: '状态',
      prop: 'status',
      enums: STATUS_LIST
    },
    // {
    //   label: '属性编码',
    //   prop: 'attributeCode',
    // },
    {
      label: '创建人',
      prop: 'createBy',
    },
    {
      label: '创建时间',
      prop: 'createTime',
      formatter(row) {
        return row.createTime?.split(' ')[0];
      }
    },
    {
      label: '修改人',
      prop: 'updateBy',
    },
    {
      label: '修改时间',
      prop: 'updateTime',
      formatter(row) {
        return row.updateTime?.split(' ')[0];
      }
    },
    {
      prop: "action",
      fixed: "right",
      width: 180,
      label: "操作",
    },
  ])
  return {
    columns,
    searchFields,
  };
};
