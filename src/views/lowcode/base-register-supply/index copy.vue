<template>
  <div class="table-content">
    <yun-pro-table
      ref="proTableRef"
      v-model:pagination="pagination"
      v-model:filter-data="filterTableData"
      v-model:searchData="searchData"
      :table-columns="columns"
      :search-fields="searchFields"
      :auto-height="true"
      :remote-method="remoteMethod"
      :table-props="tablePropsObj"
      layout="whole"
    >
      <template #t_action="{ row }">
        <el-button
          type="text"
          size="small"
          @click.prevent="handleView(row)"
        >
          查看
        </el-button>
        <el-button
          type="text"
          size="small"
          @click.prevent="handleEdit(row)"
        >
          编辑
        </el-button>
        <el-button
          type="text"
          size="small"
          @click.prevent="handleAudit(row)"
        >
          审核
        </el-button>
      </template>
    </yun-pro-table>
    
    <!-- 表单组件 -->
    <Form
      ref="formRef"
      @getData="getData"
    />
    
    <!-- 详情组件 -->
    <Detail ref="detailRef" />
    
    <!-- 审核组件 -->
    <AuditDialog ref="auditRef" @getData="getData" />
  </div>
</template>

<script setup lang="ts">
import {
  ref, reactive, computed
} from 'vue';
import { useProTable } from '@ylz-use/core';
import moment from 'moment';
import { ElMessage } from 'yun-design';
import { fetchList } from '@/api/lowcode/base-register-supply/index';
import { useTable } from './hooks/useTable';
import Form from './components/form.vue';
import Detail from './components/detail.vue';
import AuditDialog from './components/audit.vue';

const formRef = ref();
const detailRef = ref();
const auditRef = ref();
const searchData = reactive({});

const { columns, searchFields } = useTable();

const dateFieldKeys = computed(() => {
  return [];
});
const datetimeFieldKeys = computed(() => {
  return [];
});
const selectFieldKeys = computed(() => {
  return ['supplierType', 'businessNature', 'status'];
});

const {
  pagination, remoteMethod, tableProps, proTableRef, filterTableData,
} = useProTable({
  apiFn: fetchList,
  paramsHandler(params: any) {
    Object.keys(params).forEach((key) => {
      if (dateFieldKeys.value.includes(key)) {
        if (params[key]?.length) {
          params[`${key}Start`] = moment(params[key][0]).startOf('day').format('YYYY-MM-DD');
          params[`${key}End`] = moment(params[key][1]).endOf('day').format('YYYY-MM-DD');
          delete params[key];
        }
      }
      if (datetimeFieldKeys.value.includes(key)) {
        if (params[key]?.length) {
          params[`${key}Start`] = moment(params[key][0]).format('YYYY-MM-DD HH:mm:ss');
          params[`${key}End`] = moment(params[key][1]).format('YYYY-MM-DD HH:mm:ss');
          delete params[key];
        }
      }
      if (selectFieldKeys.value.includes(key)) {
        if (Array.isArray(params[key])) {
          params[`${key}List`] = params[key];
          delete params[key];
        }
      }
    });
    return {
      ...params
    }
  },
  querysHandler(query: any) {
    return {
      ...query,
      current: pagination.value.page,
      size: pagination.value.size,
    }
  },
  responseHandler(result: any) {
    return result.data;
  },
  plugins: {
    config: {
      columns: columns.value,
      searchFields: searchFields.value,
    },
    list: ['SEARCH_PLUS'],
  },
});

const tablePropsObj = computed(() => {
  return {
    ...tableProps,
    stripe: false,
    headerCellStyle: {
      backgroundColor: '#f5f7fa',
      color: '#303133',
      fontWeight: 'bold',
      height: '40px',
    },
    cellStyle: {
      padding: '0',
      height: '40px',
      'vertical-align': 'middle',
    },
    rowStyle: {
      height: '40px',
    },
    rowHeight: 40,
  };
});

function getData() {
  proTableRef.value?.getData();
}

function handleEdit(row: any) {
  formRef.value?.show(row);
}

function handleView(row: any) {
  detailRef.value?.show(row);
}

function handleAudit(row: any) {
  auditRef.value?.show(row);
}
</script>

<style lang="scss" scoped>
.table-content {
  width: 100%;
  height: calc(100vh - 88px);
  :deep(.el-form .el-form-item:last-of-type) {
    margin-bottom: 24px !important;
  }
}
</style>