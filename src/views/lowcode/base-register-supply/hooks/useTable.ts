import { ref, computed } from 'vue';

export const useTable = () => {
  // 搜索字段配置
  const searchFields = computed(() => [
    {
      prop: 'supplierName',
      label: '供应商名称',
      component: 'el-input',
      componentAttrs: {
        placeholder: '请输入',
        clearable: true,
      }
    },
    {
      prop: 'supplierType',
      label: '供应商类型',
      component: 'el-select',
      componentAttrs: {
        placeholder: '请输入',
        clearable: true,
        options: [
          { label: '原材料', value: '原材料' },
          { label: '设备', value: '设备' },
          { label: '服务', value: '服务' }
        ]
      }
    },
    {
      prop: 'businessNature',
      label: '企业性质',
      component: 'el-select',
      componentAttrs: {
        placeholder: '请输入',
        clearable: true,
        options: [
          { label: '私营企业', value: '私营企业' },
          { label: '国有企业', value: '国有企业' },
          { label: '合资企业', value: '合资企业' },
          { label: '外资企业', value: '外资企业' }
        ]
      }
    },
    {
      prop: 'status',
      label: '状态',
      component: 'el-select',
      componentAttrs: {
        placeholder: '请输入',
        clearable: true,
        options: [
          { label: '待审核', value: '待审核' },
          { label: '审核通过', value: '审核通过' },
          { label: '审核不通过', value: '审核不通过' }
        ]
      }
    }
  ]);

  // 表格列配置 - 根据原型图调整字段
  const columns = computed(() => [
    {
      label: '序号',
      type: 'index',
      width: 60
    },
    {
      label: '供应商名称',
      prop: 'supplierName',
      minWidth: 150
    },
    {
      label: '统一信用代码',
      prop: 'unifiedSocialCreditCode',
      minWidth: 150
    },
    {
      label: '简称',
      prop: 'abbreviation',
      minWidth: 120
    },
    {
      label: '注册资本',
      prop: 'registeredCapital',
      minWidth: 120
    },
    {
      label: '成立日期',
      prop: 'establishmentDate',
      minWidth: 120
    },
    {
      label: '联系人',
      prop: 'contactPerson',
      minWidth: 100
    },
    {
      label: '联系电话',
      prop: 'contactPhone',
      minWidth: 130
    },
    {
      label: '供应商类型',
      prop: 'supplierType',
      minWidth: 120
    },
    {
      label: '状态',
      prop: 'status',
      minWidth: 100,
      formatter: (row: any) => {
        const statusMap: { [key: string]: { text: string; type: string } } = {
          '待审核': { text: '待审核', type: 'warning' },
          '审核通过': { text: '审核通过', type: 'success' },
          '审核不通过': { text: '审核不通过', type: 'danger' }
        };
        const statusInfo = statusMap[row.status] || { text: row.status, type: 'info' };
        return statusInfo.text;
      }
    },
    {
      prop: "action",
      fixed: "right",
      width: 200,
      label: "操作",
    }
  ]);

  return {
    columns,
    searchFields,
  };
}; 