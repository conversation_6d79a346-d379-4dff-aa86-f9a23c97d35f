<template>
  <yun-drawer
    v-model="visible"
    title="供应商审核"
    class="audit-drawer"
    size="X-large"
    :close-on-click-modal="false"
  >
    <template #footer>
      <div class="audit-footer">
        <el-button
          type="primary"
          :loading="loading"
          @click="handleAudit('APPROVED')"
        >
          审核通过
        </el-button>
        <el-button
          :loading="loading"
          @click="handleAudit('REJECTED')"
        >
          审核不通过
        </el-button>
        <el-button @click="visible = false">
          取消
        </el-button>
      </div>
    </template>
    <div v-if="supplierData">
      <!-- 供应商详情展示 -->
      <Detail :groups="processedGroups" :data="supplierData"/>
      
      <!-- 审核操作区域 -->
      <div class="audit-section">
        <div class="audit-header">
          <div class="label">审核操作</div>
        </div>
        
        <el-form
          ref="auditForm"
          :model="auditData"
          :rules="auditRules"
          label-width="120px"
        >
          <el-row :gutter="20">
            <el-col :span="24">
              <el-form-item label="审核意见" prop="auditRemark">
                <el-input
                  v-model="auditData.auditRemark"
                  type="textarea"
                  :rows="4"
                  placeholder="请输入审核意见"
                  maxlength="500"
                  show-word-limit
                />
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>

      </div>
    </div>
    
    <div v-else class="loading-container">
      <el-empty description="暂无数据" />
    </div>
  </yun-drawer>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue';
import { ElMessage, ElMessageBox } from 'yun-design';
import { getObj, auditSupplier } from '@/api/lowcode/base-register-supply/index';
import Detail from '@/components/Detail/index.vue';
import type { FormInstance, FormRules } from 'element-plus';

const emit = defineEmits(['getData']);

const visible = ref(false);
const loading = ref(false);
const supplierData = ref<any>(null);
const auditForm = ref<FormInstance>();

const auditData = reactive({
  id: '',
  auditRemark: '',
});

const auditRules = reactive<FormRules>({
  auditRemark: [
    { required: true, message: '请输入审核意见', trigger: 'blur' }
  ]
});

// 静态生成的分组配置 - 与详情组件保持一致
const processedGroups = [
  {
    title: "基础信息",
    columns: [
      {
        prop: 'supplierName',
        label: '供应商名称',
        type: 'text'
      },
      {
        prop: 'abbreviation',
        label: '供应商简称',
        type: 'text'
      },
      {
        prop: 'unifiedSocialCreditCode',
        label: '统一社会信用代码',
        type: 'text'
      },
      {
        prop: 'supplierType',
        label: '供应商类型',
        type: 'text'
      },
      {
        prop: 'supplierStatus',
        label: '供应商状态',
        type: 'text'
      },
      {
        prop: 'businessNature',
        label: '企业性质',
        type: 'text'
      },
      {
        prop: 'supplierCategory',
        label: '供应商分类',
        type: 'text'
      },
      {
        prop: 'registeredCapital',
        label: '注册资本',
        type: 'text'
      },
      {
        prop: 'establishmentDate',
        label: '成立日期',
        type: 'text'
      },
      {
        prop: 'legalRepresentative',
        label: '法人代表',
        type: 'text'
      },
      {
        prop: 'legalIdCard',
        label: '法人身份证号',
        type: 'text'
      },
      {
        prop: 'legalPhone',
        label: '法人电话',
        type: 'text'
      },
      {
        prop: 'serviceStartDate',
        label: '服务开始时间',
        type: 'text'
      },
      {
        prop: 'serviceEndDate',
        label: '服务结束时间',
        type: 'text'
      },
      {
        prop: 'registeredAddress',
        label: '注册地址',
        type: 'text'
      },
      {
        prop: 'postalCode',
        label: '邮政编码',
        type: 'text'
      },
      {
        prop: 'fax',
        label: '传真',
        type: 'text'
      },
      {
        prop: 'annualRevenue',
        label: '年平均营业额',
        type: 'text'
      },
      {
        prop: 'companyProfile',
        label: '公司简介',
        type: 'textarea'
      },
      {
        prop: 'businessScope',
        label: '经营范围',
        type: 'textarea'
      },
      {
        prop: 'basicRemark',
        label: '备注',
        type: 'textarea'
      }
    ]
  },
  {
    title: '联系人信息',
    columns: [
      {
        prop: 'contactList',
        label: '',
        type: 'table',
        attrs: {
          showIndex: true,
          columns: [
            {
              prop: 'contactName',
              label: '姓名',
              type: 'text'
            },
            {
              prop: 'contactPhone',
              label: '电话',
              type: 'text'
            },
            {
              prop: 'idCard',
              label: '身份证',
              type: 'text'
            },
            {
              prop: 'generateAccount',
              label: '是否生成登录账号',
              type: 'enums',
              attrs: {
                options: [
                  { label: '是', value: 'YES' },
                  { label: '否', value: 'NO' }
                ]
              }
            },
            {
              prop: 'contactRemark',
              label: '备注',
              type: 'text'
            }
          ]
        }
      }
    ]
  },
  {
    title: '资质信息',
    columns: [
      {
        prop: 'qualificationList',
        label: '',
        type: 'table',
        attrs: {
          showIndex: true,
          columns: [
            {
              prop: 'certificateType',
              label: '证书类型',
              type: 'text'
            },
            {
              prop: 'certificateName',
              label: '证书名称',
              type: 'text'
            },
            {
              prop: 'certificateNumber',
              label: '证书号码',
              type: 'text'
            },
            {
              prop: 'effectiveDate',
              label: '生效日期',
              type: 'text'
            },
            {
              prop: 'expiryDate',
              label: '失效日期',
              type: 'text'
            },
            {
              prop: 'qualificationRemark',
              label: '备注',
              type: 'text'
            }
          ]
        }
      }
    ]
  },
  {
    title: '银行信息',
    columns: [
      {
        prop: 'bankList',
        label: '',
        type: 'table',
        attrs: {
          showIndex: true,
          columns: [
            {
              prop: 'bankName',
              label: '开户行',
              type: 'text'
            },
            {
              prop: 'bankAddress',
              label: '开户行地址',
              type: 'text'
            },
            {
              prop: 'bankCode',
              label: '联行号',
              type: 'text'
            },
            {
              prop: 'accountName',
              label: '开户名',
              type: 'text'
            },
            {
              prop: 'accountNumber',
              label: '银行账号',
              type: 'text'
            },
            {
              prop: 'bankRemark',
              label: '备注',
              type: 'text'
            }
          ]
        }
      }
    ]
  },
  {
    title: '主营物料',
    columns: [
      {
        prop: 'materialList',
        label: '',
        type: 'table',
        attrs: {
          showIndex: true,
          columns: [
            {
              prop: 'materialCode',
              label: '物料编码',
              type: 'text'
            },
            {
              prop: 'materialName',
              label: '物料名称',
              type: 'text'
            },
            {
              prop: 'spec',
              label: '规格型号',
              type: 'text'
            },
            {
              prop: 'unit',
              label: '单位',
              type: 'text'
            },
            {
              prop: 'materialRemark',
              label: '备注',
              type: 'text'
            }
          ]
        }
      }
    ]
  }
];

// 显示审核弹窗
const show = async (row: any) => {
  try {
    visible.value = true;
    supplierData.value = null;
    auditData.id = row.id;
    auditData.auditRemark = '';
    
    const response: any = await getObj(row.id);
    if (response.data) {
      supplierData.value = response.data;
    } else {
      ElMessage.error('获取供应商详情失败');
    }
  } catch (error) {
    ElMessage.error('获取供应商详情失败');
    console.error('获取供应商详情失败:', error);
  }
};

// 处理审核
const handleAudit = async (auditStatus: 'APPROVED' | 'REJECTED') => {
  try {
    await auditForm.value?.validate();
    
    const actionText = auditStatus === 'APPROVED' ? '审核通过' : '审核不通过';
    
    await ElMessageBox.confirm(
      `确定要${actionText}该供应商吗？`,
      '审核确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    );
    
    loading.value = true;
    
    await auditSupplier({
      id: auditData.id,
      auditStatus,
      auditRemark: auditData.auditRemark,
    });
    
    ElMessage.success(`${actionText}成功`);
    visible.value = false;
    emit('getData');
  } catch (error: any) {
    if (error !== 'cancel') {
      ElMessage.error('审核操作失败');
      console.error('审核操作失败:', error);
    }
  } finally {
    loading.value = false;
  }
};

defineExpose({
  show,
});
</script>

<style lang="scss" scoped>
.audit-drawer {
  :deep(.el-drawer__body) {
    padding: 20px;
  }
}

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}

.audit-section {
  margin-top: 30px;
  padding-top: 20px;
  border-top: 1px solid var(--el-border-color-light);
}

.audit-header {
  margin-bottom: 20px;
  .label {
    font-weight: bold;
    font-size: 18px;
    padding-left: 8px;
    border-left: 4px solid var(--el-color-primary);
    line-height: 1.2;
  }
}

.audit-footer {
  display: flex;
  justify-content: flex-end;
  gap: 16px;
}
</style> 