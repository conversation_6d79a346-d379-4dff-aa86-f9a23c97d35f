<template>
  <yun-drawer
    v-model="visible"
    :show-cancel-button="false"
    destroy-on-close
    title="详情"
    size="X-large"
    @confirm="handleConfirm"
  >
    <Detail :groups="processedGroups" :data="form"/>
  </yun-drawer>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import Detail from '@/components/Detail/index.vue';
import { getObj } from '@/api/lowcode/srm-procurement-plan-detail/index';

interface FormData {
  [key: string]: any;
}

// 静态生成的分组配置
const processedGroups = [
  {
    title: "",
    columns: [
      {
        prop: 'serviceTypeId',
        label: '服务类型ID',
        type: 'text'
      },
      {
        prop: 'planId',
        label: '采购计划ID',
        type: 'text'
      },
      {
        prop: 'materialCode',
        label: '物料编码',
        type: 'text'
      },
      {
        prop: 'materialName',
        label: '物料名称',
        type: 'text'
      },
      {
        prop: 'specModel',
        label: '规格型号',
        type: 'text'
      },
      {
        prop: 'unit',
        label: '单位',
        type: 'text'
      },
      {
        prop: 'requiredQuantity',
        label: '需求数量',
        type: 'text'
      },
      {
        prop: 'unitPrice',
        label: '计划单价',
        type: 'text'
      },
      {
        prop: 'totalPrice',
        label: '计划总价',
        type: 'text'
      },
      {
        prop: 'listPrice',
        label: '上单价',
        type: 'text'
      },
      {
        prop: 'budgetPrice',
        label: '预算价',
        type: 'text'
      },
      {
        prop: 'qualityIndicatorId',
        label: '质量指标ID',
        type: 'text'
      },
      {
        prop: 'paymentMethodId',
        label: '支付方式ID',
        type: 'text'
      },
      {
        prop: 'note',
        label: '备注',
        type: 'text'
      }
    ]
  },
  {
    title: '采购计划字段值',
    columns: [
      {
        prop: 'srmProcurementPlanFieldValue',
        label: '',
        type: 'table',
        attrs: {
          showIndex: true,
          columns: [
            {
              prop: 'planDetailId',
              label: '采购计划明细ID',
              type: 'text'
            },
            {
              prop: 'serviceTypeId',
              label: '服务类型ID',
              type: 'text'
            },
            {
              prop: 'fieldId',
              label: '字段ID',
              type: 'text'
            },
            {
              prop: 'fieldCode',
              label: '字段编码',
              type: 'text'
            },
            {
              prop: 'fieldType',
              label: '字段类型（string-字符串、int-整数、decimal-小数、date-日期、datetime-日期时间、enum-枚举）',
              type: 'text'
            },
            {
              prop: 'fieldValue',
              label: '字段值',
              type: 'text'
            }
          ]
        }
      }
    ]
  }
];

const visible = ref(false);
const form = ref<FormData>({});

async function handleConfirm(done: () => void) {
  done();
}

async function show(row: FormData) {
  const res = await getObj(row.id);
  form.value = res.data;
  visible.value = true;
}

defineExpose({
  show,
});
</script>

<style scoped lang="scss">
</style>