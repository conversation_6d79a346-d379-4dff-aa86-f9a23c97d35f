<template>
  <yun-drawer
    v-model="visible"
    destroy-on-close
    modal
    custom-class="form-drawer"
    close-on-click-modal
    confirm-button-text="确定"
    cancel-button-text="取消"
    :confirm-button-disabled="loading"
    :title="title"
    :size="size"
    @confirm="confirmHandler"
    @close="closeHandler"
  >
    <el-form
      ref="vForm"
      :model="formData"
      :rules="rules"
      label-width="120"
      label-position="top"
    >
      <el-row :gutter="12">
        <el-col :span="8">
          <el-form-item label="服务类型ID" prop="serviceTypeId">
            <el-input-number v-model="formData.serviceTypeId" :defaultValue="0" :min="-100000000000" :max="100000000000" :precision="0" :step="1" controlsPosition="right" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="采购计划ID" prop="planId">
            <el-input-number v-model="formData.planId" :defaultValue="0" :min="-100000000000" :max="100000000000" :precision="0" :step="1" controlsPosition="right" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="物料编码" prop="materialCode">
            <el-input v-model="formData.materialCode" type="text" clearable icon="custom-search" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="物料名称" prop="materialName">
            <el-input v-model="formData.materialName" type="text" clearable icon="custom-search" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="规格型号" prop="specModel">
            <el-input v-model="formData.specModel" type="text" clearable icon="custom-search" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="单位" prop="unit">
            <el-input v-model="formData.unit" type="text" clearable icon="custom-search" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="需求数量" prop="requiredQuantity">
            <el-input-number v-model="formData.requiredQuantity" :defaultValue="0" :min="-100000000000" :max="100000000000" :precision="0" :step="1" controlsPosition="right" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="计划单价" prop="unitPrice">
            <el-input-number v-model="formData.unitPrice" :defaultValue="0" :min="-100000000000" :max="100000000000" :precision="0" :step="1" controlsPosition="right" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="计划总价" prop="totalPrice">
            <el-input-number v-model="formData.totalPrice" :defaultValue="0" :min="-100000000000" :max="100000000000" :precision="0" :step="1" controlsPosition="right" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="上单价" prop="listPrice">
            <el-input-number v-model="formData.listPrice" :defaultValue="0" :min="-100000000000" :max="100000000000" :precision="0" :step="1" controlsPosition="right" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="预算价" prop="budgetPrice">
            <el-input-number v-model="formData.budgetPrice" :defaultValue="0" :min="-100000000000" :max="100000000000" :precision="0" :step="1" controlsPosition="right" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="质量指标ID" prop="qualityIndicatorId">
            <el-input-number v-model="formData.qualityIndicatorId" :defaultValue="0" :min="-100000000000" :max="100000000000" :precision="0" :step="1" controlsPosition="right" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="支付方式ID" prop="paymentMethodId">
            <el-input-number v-model="formData.paymentMethodId" :defaultValue="0" :min="-100000000000" :max="100000000000" :precision="0" :step="1" controlsPosition="right" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="备注" prop="note">
            <el-input v-model="formData.note" type="textarea" :rows="3" />
          </el-form-item>
        </el-col>
        <el-col :span="8">

        </el-col>
      </el-row>
      <div class="table-header">
        <div class="label">采购计划字段值</div>
      </div>
      <div class="w-full overflow-x-auto">
        <el-table
          :data="formData.srmProcurementPlanFieldValueList || []"
          :border="true"
          stripe
          class="w-full dark:bg-gray-800"
          :header-cell-class-name="'!bg-gray-50 dark:!bg-gray-700'"
          style="min-width: 600px"
          :show-overflow-tooltip="false"
        >
          <el-table-column
            type="index"
            label="序号"
            width="60"
            align="center"
            fixed
          />
          <el-table-column
            label="采购计划明细ID"
            prop="planDetailId"
            min-width="200px"
          >
            <template #default="{ row }">
              <el-input-number v-model="row.planDetailId" :defaultValue="0" :min="-100000000000" :max="100000000000" :precision="0" :step="1" controlsPosition="right" />
            </template>
          </el-table-column>
          <el-table-column
            label="服务类型ID"
            prop="serviceTypeId"
            min-width="200px"
          >
            <template #default="{ row }">
              <el-input-number v-model="row.serviceTypeId" :defaultValue="0" :min="-100000000000" :max="100000000000" :precision="0" :step="1" controlsPosition="right" />
            </template>
          </el-table-column>
          <el-table-column
            label="字段ID"
            prop="fieldId"
            min-width="200px"
          >
            <template #default="{ row }">
              <el-input-number v-model="row.fieldId" :defaultValue="0" :min="-100000000000" :max="100000000000" :precision="0" :step="1" controlsPosition="right" />
            </template>
          </el-table-column>
          <el-table-column
            label="字段编码"
            prop="fieldCode"
            min-width="200px"
          >
            <template #default="{ row }">
              <el-input v-model="row.fieldCode" type="text" clearable icon="custom-search" />
            </template>
          </el-table-column>
          <el-table-column
            label="字段类型（string-字符串、int-整数、decimal-小数、date-日期、datetime-日期时间、enum-枚举）"
            prop="fieldType"
            min-width="200px"
          >
            <template #default="{ row }">
              <el-input v-model="row.fieldType" type="text" clearable icon="custom-search" />
            </template>
          </el-table-column>
          <el-table-column
            label="字段值"
            prop="fieldValue"
            min-width="200px"
          >
            <template #default="{ row }">
              <el-input v-model="row.fieldValue" type="textarea" :rows="3" />
            </template>
          </el-table-column>
          <el-table-column
            label="操作"
            fixed="right"
            width="100"
            align="center"
          >
            <template #default="{ $index }">
              <el-button
                type="danger"
                size="small"
                icon="Delete"
                @click="handleRemoveRow('srmProcurementPlanFieldValueList', $index)"
              >删除</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <el-button
        style="width: 100%; margin-top: 5px"
        :icon="Plus"
        @click="handleAddRow('srmProcurementPlanFieldValueList')"
      >
        添加
      </el-button>
    </el-form>
  </yun-drawer>
</template>

<script setup lang="ts">
import { ref, reactive, computed, nextTick } from 'vue';
import { ElMessage } from 'yun-design';
import { Plus, Delete } from '@element-plus/icons-vue';
import { useDict } from '@/hooks/dict';
import { addObj, putObj, getObj } from '@/api/lowcode/srm-procurement-plan-detail/index';
import { useUserInfo } from '@/stores/userInfo';
import { rule } from '@/utils/validate';
import { Session } from '@/utils/storage';
import moment from 'moment';

// 状态管理
const visible = ref(false);
const loading = ref(false);
const isEdit = ref(false);
const vForm = ref();
const formData = reactive({});
const rules = reactive({
  planId: [{
            required: true,
            message: '此项必填',
            trigger: ['blur', 'change']
          }]
});

// 计算属性
const title = computed(() => (isEdit.value ? '编辑' : '新增'));
const size = computed(() => 'X-large');

// 用户信息
const userStore = useUserInfo();
const emits = defineEmits(['getData']);

// 为每个API组件创建响应式变量


// 为每个API组件创建获取数据的方法


// 初始化时获取所有API数据


// 关闭处理
function closeHandler() {
  visible.value = false;
}

// 格式化表单数据
async function formatFrom(row: Record<string, any>) {
  for(let key in formData) {
    delete formData[key];
  }
  if (!row.id) return;
  const res = await getObj(row.id);
  Object.assign(formData, res.data);

}

// 格式化提交参数
function formatParams() {
  const baseParams = {};
  
  // 处理特殊字段类型


  return {
    ...formData,
    ...baseParams,
  };
}

// 确认处理
async function confirmHandler(done: () => void, loading: { value: boolean }) {
  try {
    await vForm.value?.validate();
    loading.value = true;
    const api = isEdit.value ? putObj : addObj;
    const params = formatParams();
    await api(params);
    done();
    emits('getData', formData);
  } catch (error) {
    ElMessage.error('表单验证或提交失败');
  } finally {
    loading.value = false;
  }
}

// 显示表单
function show(row?: Record<string, any>) {
  visible.value = true;
  isEdit.value = !!row;
  formatFrom(row || {});
  nextTick(() => {
    vForm.value?.clearValidate();
  });
}

// 日期时间范围默认值
const defaultTime = [new Date(2000, 1, 1, 0, 0, 0), new Date(2000, 2, 1, 23, 59, 59)];

// 字典数据
const getDict = computed(() => (str: string) => {
  if (!str) return [];
  const value = useDict(str)?.[str]?.value;
  return value;
});

// 生成明细表新行的初始数据
function getEnhancedTableRow(widget: Widget): Record<string, any> {
  const row: Record<string, any> = {};
  if (Array.isArray(widget.widgetList)) {
    for (const colWidget of widget.widgetList) {
      const name = colWidget.options?.name;
      if (!name) continue;
      let value = colWidget.options?.defaultValue;
      if (value === undefined) {
        if (colWidget.type === 'number') value = 0;
        else value = '';
      }
      row[name] = value;
    }
  }
  return row;
}

// 添加明细表行
function handleAddRow(tableName: string) {
  if (!formData[tableName]) {
    formData[tableName] = [];
  }
  
  formData[tableName].push({});
}

// 删除明细表行
function handleRemoveRow(tableName: string, index: number) {
  if (!formData[tableName]) return;
  formData[tableName].splice(index, 1);
}

// 暴露方法
defineExpose({
  show,
  formData,
  rules
});
</script>

<style lang="scss">
.form-drawer {
  .el-form .el-form-item:last-of-type {
    margin-bottom: 24px !important;
  }
}
</style>

<style lang="scss" scoped>
.el-form-item {
  margin-bottom: 18px;
}

.table-header {
  margin-bottom: 15px;
  .label {
    font-weight: bold;
    font-size: 18px;
    padding-left: 8px;
    border-left: 4px solid var(--el-color-primary);
    line-height: 1.2;
  }
}

:deep(.el-form-item__label) {
  font-weight: normal;
}

:deep(.el-table) {
  .el-table__header-wrapper {
    th {
      background-color: var(--el-fill-color-light);
    }
  }
}
</style>