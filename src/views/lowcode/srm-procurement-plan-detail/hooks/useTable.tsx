import { ref, computed } from 'vue';
import moment from 'moment';
import { useDict } from '@/hooks/dict';
import { ElImage } from 'element-plus';
import { Session } from '/@/utils/storage';

const getDict = computed(() => (str) => {
  if (!str) return [];
  const value = useDict(str)?.[str]?.value;
  return value;
});

export const useTable = () => {



  const searchFields = computed(() => [
  ]);

  const columns = computed(() => [
    {
      label: '序号',
      type: 'index',
    },
    {
      label: '物料编码',
      prop: 'materialCode',
    },
    {
      label: '物料名称',
      prop: 'materialName',
    },
    {
      label: '规格型号',
      prop: 'specModel',
    },
    {
      label: '单位',
      prop: 'unit',
    },
    {
      label: '需求数量',
      prop: 'requiredQuantity',
    },
    {
      label: '计划单价',
      prop: 'unitPrice',
    },
    {
      label: '计划总价',
      prop: 'totalPrice',
    },
    {
      label: '上单价',
      prop: 'listPrice',
    },
    {
      label: '预算价',
      prop: 'budgetPrice',
    },
    {
      label: '备注',
      prop: 'note',
    },
    {
      label: '创建人',
      prop: 'createBy',
    },
    {
      label: '创建时间',
      prop: 'createTime',
    },
    {
      label: '修改人',
      prop: 'updateBy',
    },
    {
      label: '修改时间',
      prop: 'updateTime',
    },
    {
      prop: "action",
      fixed: "right",
      width: 150,
      label: "操作",
    },
  ])
  return {
    columns,
    searchFields,
  };
};