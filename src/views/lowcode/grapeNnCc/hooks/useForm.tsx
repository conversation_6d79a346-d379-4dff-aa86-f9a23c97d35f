import { ref, computed } from 'vue';
import moment from 'moment';
import { rule } from '@/utils/validate';
import YunUpload from '@/components/YunUpload/index.vue';
import EditTable from '@/components/EditTable/index.vue';
import { useDict } from '@/hooks/dict';

const getDict = computed(() => (str) => {
  if (!str) return [];
  const value = useDict(str)?.[str]?.value;
  return value;
});  
export const useForm = () => {
  const columns = computed(() => [  
    {
      prop: 'updateById',
      label: '修改人id',
      type: 'input',
      colProps: { span: 12 },
      attrs: {
        placeholder: '请输入修改人id',
        clearable: true,
      },
    },
    {
      prop: 'createById',
      label: '创建人id',
      type: 'input',
      colProps: { span: 12 },
      attrs: {
        placeholder: '请输入创建人id',
        clearable: true,
      },
    },
    {
      prop: 'testDemo',
      label: '测试字段',
      type: 'input',
      colProps: { span: 12 },
      attrs: {
        placeholder: '请输入测试字段',
        clearable: true,
      },
    },
  ])
  return {
    columns
  }
}