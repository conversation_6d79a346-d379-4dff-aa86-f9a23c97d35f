import { ref, computed } from 'vue';
import moment from 'moment';
import { useDict } from '@/hooks/dict';

const getDict = computed(() => (str) => {
  if (!str) return [];
  const value = useDict(str)?.[str]?.value;
  return value;
});

export const useTable = () => {
  const searchFields = computed(() => [
  ]);
  const columns = computed(() => [
    {
      label: '序号',
      type: 'index',
    },
    {
      label: '修改人id',
      prop: 'updateById'
    },
    {
      label: '创建人id',
      prop: 'createById'
    },
    {
      label: '测试字段',
      prop: 'testDemo'
    },
    {
      prop: "action",
      fixed: "right",
      width: 150,
      label: "操作",
    },
  ])
  return {
    columns,
    searchFields,
  };
};
