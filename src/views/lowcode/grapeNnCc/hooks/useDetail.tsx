import { ref, computed } from 'vue';
import moment from 'moment';
import { useDict } from '@/hooks/dict';

const getDict = computed(() => (str) => {
  if (!str) return [];
  const value = useDict(str)?.[str]?.value;
  return value;
});  
export const useDetail = () => {
  const columns = computed(() => [  
    {
      prop: 'updateById',
      label: '修改人id',
      attrs: {
      },
      span: 1,
    },
    {
      prop: 'createById',
      label: '创建人id',
      attrs: {
      },
      span: 1,
    },
    {
      prop: 'testDemo',
      label: '测试字段',
      attrs: {
      },
      span: 1,
    },
  ])
  return {
    columns
  }
}