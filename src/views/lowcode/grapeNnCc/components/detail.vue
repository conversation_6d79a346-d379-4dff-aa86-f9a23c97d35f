<template>
  <yun-drawer
    v-model="visible"
    title="详情"
    size="medium"
    destroy-on-close
    cancel-button-text="取消"
    :show-cancel-button="false"
    @confirm="visible = false"
  >
    <yun-pro-detail :detail="form" :config="config" :columns="columns" />
  </yun-drawer>
</template>

<script setup lang="jsx">
import { ref, computed, nextTick } from 'vue';
import { rule } from '@/utils/validate';
import { ElMessage, ElMessageBox } from 'yun-design';
import { useDict } from '@/hooks/dict';
import { useDetail } from '../hooks/useDetail';

const form = ref({});
const config = ref({
  descriptions: {
    column: 2,
    labelPosition: 'top',
  }
});
const getDict = computed(() => (str) => {
  if (!str) return [];
  const value = useDict(str)?.[str]?.value;
  return value;
});
const visible = ref(false);
// js逻辑可封装为hook
const { columns } = useDetail();
function formatRow(row) {
  // 重置表单的相关逻辑
  return {
    ...row,
  }
}
// 打开弹窗
const show = (row) => {
  visible.value = true;
  form.value = row ? formatRow(row) : {}
};
// 暴露变量
defineExpose({
  show
});
</script>