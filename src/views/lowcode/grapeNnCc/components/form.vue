<template>
  <yun-drawer
    v-model="visible"
    :title="title"
    size="medium"
    destroy-on-close
    :close-on-click-modal="true"
    confirm-button-text="确定"
    cancel-button-text="取消"
    :confirm-button-disabled="loading"
    @confirm="handleConfirm"
  >
    <yun-pro-form
      ref="formRef"
      :form="form"
      :columns="columns"
      :form-props="{ labelPosition: 'top' }"
      :config="{
        colProps: { span: 12 },
      }"
    />
  </yun-drawer>
</template>

<script setup lang="jsx">
import { ref, computed, nextTick } from 'vue';
import { rule } from '@/utils/validate';
import { ElMessage, ElMessageBox } from 'yun-design';
import { getObj, addObj, putObj, delObjs } from '../api/index';
import { useDict } from '@/hooks/dict';
import { useForm } from '../hooks/useForm';

const form = ref({});
const visible = ref(false);
const isAdd = ref(false);
const loading = ref(false);
const title = ref('新增');
const formRef = ref();
const emit = defineEmits(['refresh']);
// js逻辑可封装为hook
const { columns } = useForm();
function formatRow(row) {
  // 重置表单的相关逻辑
  return {
    ...row,
  }
}
// 打开弹窗
const show = (row) => {
  visible.value = true;
  title.value = row ? '编辑' : '新增';
  isAdd.value = !row;
  form.value = row ? formatRow(row) : {}
  // 重置表单数据
  nextTick(() => {
    formRef.value?.elForm.resetFields();
  });
};
const handleConfirm = async () => {
  const valid = await formRef.value.elForm.validate().catch(() => {});
  if (!valid) return false;
  try {
    loading.value = true;
    isAdd.value ? await addObj(form.value) : await putObj(form.value);
    ElMessage.success(isAdd.value ? '新增成功' : '修改成功');
    visible.value = false;
    emit('refresh');
  } catch (err) {
    console.error(err);
  } finally {
    loading.value = false;
  }
};
// 暴露变量
defineExpose({
  show
});
</script>