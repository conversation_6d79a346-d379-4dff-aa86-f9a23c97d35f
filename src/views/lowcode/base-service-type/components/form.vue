<template>
	<yun-drawer
		v-model="visible"
		destroy-on-close
		modal
		custom-class="form-drawer"
		close-on-click-modal
		confirm-button-text="确定"
		cancel-button-text="取消"
		:confirm-button-disabled="loading"
		:title="title"
		:size="size"
		@confirm="confirmHandler"
		@close="closeHandler"
	>
		<el-form
			ref="vForm"
			:model="formData"
			:rules="rules"
			:label-width="120"
			label-position="top"
		>
			<el-row :gutter="12">
				<el-col :span="8">
					<el-form-item
						label="类型编码"
						prop="typeCode"
					>
						<el-input
							v-model="formData.typeCode"
							type="text"
							placeholder="请输入类型编码"
							clearable
							icon="custom-search"
						/>
					</el-form-item>
				</el-col>
				<el-col :span="8">
					<el-form-item
						label="类型名称"
						placeholder="请输入类型名称"
						prop="typeName"
					>
						<el-input
							v-model="formData.typeName"
							type="text"
							clearable
							icon="custom-search"
						/>
					</el-form-item>
				</el-col>

				<el-col :span="8">
					<el-form-item
						label="状态"
						prop="status"
					>
						<el-select
							v-model="formData.status"
							style="width: 100%"
							clearable
							placeholder="请选择状态"
							:multipleLimit="0"
						>
							<el-option
								v-for="option in getDict('enable_status')"
								:key="option.value"
								:label="option.label"
								:value="option.value"
							/>
						</el-select>
					</el-form-item>
				</el-col>

				<el-col :span="8">
					<el-form-item
						label="描述"
						prop="description"
					>
						<el-input
							v-model="formData.description"
							type="textarea"
							placeholder="请输入描述"
							clearable
							icon="custom-search"
						/>
					</el-form-item>
				</el-col>
			</el-row>
			<div class="table-header">
				<div class="label">服务类型字段</div>
			</div>
			<div class="w-full overflow-x-auto">
				<el-table
					:data="formData.baseServiceTypeFieldList || []"
					:border="true"
					stripe
					class="w-full dark:bg-gray-800"
					:header-cell-class-name="'!bg-gray-50 dark:!bg-gray-700'"
					style="min-width: 600px"
					:show-overflow-tooltip="false"
				>
					<el-table-column
						type="index"
						label="序号"
						width="60"
						align="center"
						fixed
					/>
					<el-table-column
						label="字段编码"
						prop="fieldCode"
						min-width="200px"
					>
						<template #default="{ row }">
							<el-input
								v-model="row.fieldCode"
								type="text"
								clearable
								icon="custom-search"
							/>
						</template>
					</el-table-column>
					<el-table-column
						label="字段名称"
						prop="fieldName"
						min-width="200px"
					>
						<template #default="{ row }">
							<el-input
								v-model="row.fieldName"
								type="text"
								clearable
								icon="custom-search"
							/>
						</template>
					</el-table-column>
					<el-table-column
						label="字段类型（string-字符串、int-整数、decimal-小数、date-日期、datetime-日期时间、enum-枚举）"
						prop="fieldType"
						min-width="200px"
					>
						<template #default="{ row }">
							<el-select
								v-model="row.fieldType"
								style="width: 100%"
								clearable
								:multipleLimit="0"
							>
								<el-option
									label="select 1"
									value="1"
								/>
								<el-option
									label="select 2"
									value="2"
								/>
								<el-option
									label="select 3"
									value="3"
								/>
							</el-select>
						</template>
					</el-table-column>
					<el-table-column
						label="字段长度"
						prop="fieldLength"
						min-width="200px"
					>
						<template #default="{ row }">
							<el-input-number
								v-model="row.fieldLength"
								:defaultValue="0"
								:min="-100000000000"
								:max="100000000000"
								:precision="0"
								:step="1"
								controlsPosition="right"
							/>
						</template>
					</el-table-column>
					<el-table-column
						label="是否必填（0-否、1-是）"
						prop="isRequired"
						min-width="200px"
					>
						<template #default="{ row }">
							<el-select
								v-model="row.isRequired"
								style="width: 100%"
								clearable
								:multipleLimit="0"
							>
								<el-option
									v-for="option in getDict('yes_no_type')"
									:key="option.value"
									:label="option.label"
									:value="option.value"
								/>
							</el-select>
						</template>
					</el-table-column>
					<el-table-column
						label="默认值"
						prop="defaultValue"
						min-width="200px"
					>
						<template #default="{ row }">
							<el-input
								v-model="row.defaultValue"
								type="text"
								clearable
								icon="custom-search"
							/>
						</template>
					</el-table-column>
					<el-table-column
						label="验证规则（JSON格式）"
						prop="validationRule"
						min-width="200px"
					>
						<template #default="{ row }">
							<el-input
								v-model="row.validationRule"
								type="text"
								clearable
								icon="custom-search"
							/>
						</template>
					</el-table-column>
					<el-table-column
						label="枚举值（JSON格式，当field_type为enum时使用）"
						prop="enumValues"
						min-width="200px"
					>
						<template #default="{ row }">
							<el-input
								v-model="row.enumValues"
								type="text"
								clearable
								icon="custom-search"
							/>
						</template>
					</el-table-column>
					<el-table-column
						label="占位提示文本"
						prop="placeholder"
						min-width="200px"
					>
						<template #default="{ row }">
							<el-input
								v-model="row.placeholder"
								type="text"
								clearable
								icon="custom-search"
							/>
						</template>
					</el-table-column>
					<el-table-column
						label="排序"
						prop="sort"
						min-width="200px"
					>
						<template #default="{ row }">
							<el-input-number
								v-model="row.sort"
								:defaultValue="0"
								:min="-100000000000"
								:max="100000000000"
								:precision="0"
								:step="1"
								controlsPosition="right"
							/>
						</template>
					</el-table-column>
					<el-table-column
						label="是否应用到采购计划子表"
						prop="applyToPlan"
						min-width="200px"
					>
						<template #default="{ row }">
							<el-select
								v-model="row.applyToPlan"
								style="width: 100%"
								clearable
								:multipleLimit="0"
							>
								<el-option
									v-for="option in getDict('yes_no')"
									:key="option.value"
									:label="option.label"
									:value="option.value"
								/>
							</el-select>
						</template>
					</el-table-column>
					<el-table-column
						label="是否应用到采购立项子表"
						prop="applyToProject"
						min-width="200px"
					>
						<template #default="{ row }">
							<el-select
								v-model="row.applyToProject"
								style="width: 100%"
								clearable
								:multipleLimit="0"
							>
								<el-option
									v-for="option in getDict('yes_no')"
									:key="option.value"
									:label="option.label"
									:value="option.value"
								/>
							</el-select>
						</template>
					</el-table-column>
					<el-table-column
						label="是否应用到采购报价子表"
						prop="applyToQuote"
						min-width="200px"
					>
						<template #default="{ row }">
							<el-select
								v-model="row.applyToQuote"
								style="width: 100%"
								clearable
								:multipleLimit="0"
							>
								<el-option
									v-for="option in getDict('yes_no')"
									:key="option.value"
									:label="option.label"
									:value="option.value"
								/>
							</el-select>
						</template>
					</el-table-column>
					<el-table-column
						label="状态"
						prop="status"
						min-width="200px"
					>
						<template #default="{ row }">
							<el-select
								v-model="row.status"
								style="width: 100%"
								clearable
								:multipleLimit="0"
							>
								<el-option
									v-for="option in getDict('enable_status')"
									:key="option.value"
									:label="option.label"
									:value="option.value"
								/>
							</el-select>
						</template>
					</el-table-column>
					<el-table-column
						label="操作"
						fixed="right"
						width="100"
						align="center"
					>
						<template #default="{ $index }">
							<el-button
								type="danger"
								size="small"
								icon="Delete"
								@click="handleRemoveRow('baseServiceTypeFieldList', $index)"
								>删除</el-button
							>
						</template>
					</el-table-column>
				</el-table>
			</div>
			<el-button
				style="width: 100%; margin-top: 5px"
				:icon="Plus"
				@click="handleAddRow('baseServiceTypeFieldList')"
			>
				添加
			</el-button>
			<el-row :gutter="12">
				<el-col :span="8"> </el-col>
			</el-row>
		</el-form>
	</yun-drawer>
</template>

<script setup lang="ts">
import { ref, reactive, computed, nextTick } from 'vue';
import { ElMessage } from 'yun-design';
import { Plus, Delete } from '@element-plus/icons-vue';
import { useDict } from '@/hooks/dict';
import { addObj, putObj, getObj } from '@/api/lowcode/base-service-type/index';
import { useUserInfo } from '@/stores/userInfo';
import { rule } from '@/utils/validate';
import { Session } from '@/utils/storage';
import moment from 'moment';

// 状态管理
const visible = ref(false);
const loading = ref(false);
const isEdit = ref(false);
const vForm = ref();
const formData = reactive({});
const rules = reactive({
	typeCode: [
		{
			required: true,
			message: '此项必填',
			trigger: ['blur', 'change'],
		},
	],
	typeName: [
		{
			required: true,
			message: '此项必填',
			trigger: ['blur', 'change'],
		},
	],
});

// 计算属性
const title = computed(() => (isEdit.value ? '编辑' : '新增'));
const size = computed(() => 'X-large');

// 用户信息
const userStore = useUserInfo();
const emits = defineEmits(['getData']);

// 为每个API组件创建响应式变量

// 为每个API组件创建获取数据的方法

// 初始化时获取所有API数据

// 关闭处理
function closeHandler() {
	visible.value = false;
}

// 格式化表单数据
async function formatFrom(row: Record<string, any>) {
	for (let key in formData) {
		delete formData[key];
	}
	if (!row.id) return;
	const res = await getObj(row.id);
	Object.assign(formData, res.data);
}

// 格式化提交参数
function formatParams() {
	const baseParams = {};

	// 处理特殊字段类型

	return {
		...formData,
		...baseParams,
	};
}

// 确认处理
async function confirmHandler(done: () => void, loading: { value: boolean }) {
	try {
		await vForm.value?.validate();
		loading.value = true;
		const api = isEdit.value ? putObj : addObj;
		const params = formatParams();
		await api(params);
		done();
		emits('getData', formData);
	} catch (error) {
		ElMessage.error('表单验证或提交失败');
	} finally {
		loading.value = false;
	}
}

// 显示表单
function show(row?: Record<string, any>) {
	visible.value = true;
	isEdit.value = !!row;
	formatFrom(row || {});
	nextTick(() => {
		vForm.value?.clearValidate();
	});
}

// 日期时间范围默认值
const defaultTime = [new Date(2000, 1, 1, 0, 0, 0), new Date(2000, 2, 1, 23, 59, 59)];

// 字典数据
const getDict = computed(() => (str: string) => {
	if (!str) return [];
	const value = useDict(str)?.[str]?.value;
	return value;
});

// 生成明细表新行的初始数据
function getEnhancedTableRow(widget: Widget): Record<string, any> {
	const row: Record<string, any> = {};
	if (Array.isArray(widget.widgetList)) {
		for (const colWidget of widget.widgetList) {
			const name = colWidget.options?.name;
			if (!name) continue;
			let value = colWidget.options?.defaultValue;
			if (value === undefined) {
				if (colWidget.type === 'number') value = 0;
				else value = '';
			}
			row[name] = value;
		}
	}
	return row;
}

// 添加明细表行
function handleAddRow(tableName: string) {
	if (!formData[tableName]) {
		formData[tableName] = [];
	}

	formData[tableName].push({});
}

// 删除明细表行
function handleRemoveRow(tableName: string, index: number) {
	if (!formData[tableName]) return;
	formData[tableName].splice(index, 1);
}

// 暴露方法
defineExpose({
	show,
	formData,
	rules,
});
</script>

<style lang="scss">
.form-drawer {
	.el-form .el-form-item:last-of-type {
		margin-bottom: 24px !important;
	}
}
</style>

<style lang="scss" scoped>
.el-form-item {
	margin-bottom: 18px;
}

.table-header {
	margin-bottom: 15px;
	.label {
		font-weight: bold;
		font-size: 18px;
		padding-left: 8px;
		border-left: 4px solid var(--el-color-primary);
		line-height: 1.2;
	}
}

:deep(.el-form-item__label) {
	font-weight: normal;
}

:deep(.el-table) {
	.el-table__header-wrapper {
		th {
			background-color: var(--el-fill-color-light);
		}
	}
}
</style>
