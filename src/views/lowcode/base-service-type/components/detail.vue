<template>
  <yun-drawer
    v-model="visible"
    :show-cancel-button="false"
    destroy-on-close
    title="详情"
    size="X-large"
    @confirm="handleConfirm"
  >
    <Detail :groups="processedGroups" :data="form" />
  </yun-drawer>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import Detail from '@/components/Detail/index.vue';
import { getObj } from '@/api/lowcode/base-service-type/index';

interface FormData {
  [key: string]: any;
}

// 静态生成的分组配置
const processedGroups = [
  {
    title: '',
    columns: [
      {
        prop: 'typeCode',
        label: '类型编码',
        type: 'text',
      },
      {
        prop: 'typeName',
        label: '类型名称',
        type: 'text',
      },
      {
        prop: 'description',
        label: '描述',
        type: 'text',
      },
      {
        prop: 'status',
        label: '状态',
        type: 'enums',
        attrs: {
          options: [],
          dataSourceType: 'dict',
          dictType: 'enable_status',
        },
      },
    ],
  },
  {
    title: '服务类型字段',
    columns: [
      {
        prop: 'baseServiceTypeField',
        label: '',
        type: 'table',
        attrs: {
          showIndex: true,
          columns: [
            {
              prop: 'fieldCode',
              label: '字段编码',
              type: 'text',
            },
            {
              prop: 'fieldName',
              label: '字段名称',
              type: 'text',
            },
            {
              prop: 'fieldType',
              label:
                '字段类型（string-字符串、int-整数、decimal-小数、date-日期、datetime-日期时间、enum-枚举）',
              type: 'enums',
              attrs: {
                options: [
                  {
                    label: 'select 1',
                    value: 1,
                  },
                  {
                    label: 'select 2',
                    value: 2,
                  },
                  {
                    label: 'select 3',
                    value: 3,
                  },
                ],
                dataSourceType: 'custom',
                dictType: '',
              },
            },
            {
              prop: 'fieldLength',
              label: '字段长度',
              type: 'text',
            },
            {
              prop: 'isRequired',
              label: '是否必填（0-否、1-是）',
              type: 'enums',
              attrs: {
                options: [
                  {
                    label: 'select 1',
                    value: 1,
                  },
                  {
                    label: 'select 2',
                    value: 2,
                  },
                  {
                    label: 'select 3',
                    value: 3,
                  },
                ],
                dataSourceType: 'dict',
                dictType: 'yes_no_type',
              },
            },
            {
              prop: 'defaultValue',
              label: '默认值',
              type: 'text',
            },
            {
              prop: 'validationRule',
              label: '验证规则（JSON格式）',
              type: 'text',
            },
            {
              prop: 'enumValues',
              label: '枚举值（JSON格式，当field_type为enum时使用）',
              type: 'text',
            },
            {
              prop: 'placeholder',
              label: '占位提示文本',
              type: 'text',
            },
            {
              prop: 'sort',
              label: '排序',
              type: 'text',
            },
            {
              prop: 'applyToPlan',
              label: '是否应用到采购计划子表',
              type: 'enums',
              attrs: {
                options: [
                  {
                    label: 'select 1',
                    value: 1,
                  },
                  {
                    label: 'select 2',
                    value: 2,
                  },
                  {
                    label: 'select 3',
                    value: 3,
                  },
                ],
                dataSourceType: 'dict',
                dictType: 'yes_no',
              },
            },
            {
              prop: 'applyToProject',
              label: '是否应用到采购立项子表',
              type: 'enums',
              attrs: {
                options: [
                  {
                    label: 'select 1',
                    value: 1,
                  },
                  {
                    label: 'select 2',
                    value: 2,
                  },
                  {
                    label: 'select 3',
                    value: 3,
                  },
                ],
                dataSourceType: 'dict',
                dictType: 'yes_no',
              },
            },
            {
              prop: 'applyToQuote',
              label: '是否应用到采购报价子表',
              type: 'enums',
              attrs: {
                options: [
                  {
                    label: 'select 1',
                    value: 1,
                  },
                  {
                    label: 'select 2',
                    value: 2,
                  },
                  {
                    label: 'select 3',
                    value: 3,
                  },
                ],
                dataSourceType: 'dict',
                dictType: 'yes_no',
              },
            },
            {
              prop: 'status',
              label: '状态',
              type: 'enums',
              attrs: {
                options: [
                  {
                    label: 'select 1',
                    value: 1,
                  },
                  {
                    label: 'select 2',
                    value: 2,
                  },
                  {
                    label: 'select 3',
                    value: 3,
                  },
                ],
                dataSourceType: 'dict',
                dictType: 'enable_status',
              },
            },
          ],
        },
      },
    ],
  },
  {
    title: '',
    columns: [],
  },
];

const visible = ref(false);
const form = ref<FormData>({});

async function handleConfirm(done: () => void) {
  done();
}

async function show(row: FormData) {
  const res = await getObj(row.id);
  form.value = res.data;
  visible.value = true;
}

defineExpose({
  show,
});
</script>

<style scoped lang="scss"></style>
