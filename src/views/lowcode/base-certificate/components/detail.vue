<template>
  <yun-drawer
    v-model="visible"
    :show-cancel-button="false"
    destroy-on-close
    title="详情"
    size="small"
    @confirm="handleConfirm"
  >
    <Detail :groups="processedGroups" :data="form"/>
  </yun-drawer>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import Detail from '@/components/Detail/index.vue';
import { getObj } from '@/api/lowcode/base-certificate/index';

interface FormData {
  [key: string]: any;
}

// 静态生成的分组配置
const processedGroups = [
  {
    title: "",
    columns: [
      {
        prop: 'certName',
        label: '证件名称',
        type: 'text',
        span: 3,
      },
      {
        prop: 'description',
        label: '补充说明',
        type: 'text',
        span: 3,
      },
      // {
      //   prop: 'status',
      //   label: '状态',
      //   type: 'enums',
      //   attrs: {
      //     options: [],
      //     dataSourceType: 'dict',
      //     dictType: 'enable_status'
      //   }
      // }
    ]
  }
];

const visible = ref(false);
const form = ref<FormData>({});

async function handleConfirm(done: () => void) {
  done();
}

async function show(row: FormData) {
  const res = await getObj(row.id);
  form.value = res.data;
  visible.value = true;
}

defineExpose({
  show,
});
</script>

<style scoped lang="scss">
</style>