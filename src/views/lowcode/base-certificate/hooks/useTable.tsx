import { ref, computed } from 'vue';
import moment from 'moment';
import { useDict } from '@/hooks/dict';
import { ElImage } from 'element-plus';
import { Session } from '/@/utils/storage';

const getDict = computed(() => (str) => {
  if (!str) return [];
  const value = useDict(str)?.[str]?.value;
  return value;
});

export const useTable = () => {



  const searchFields = computed(() => [
    {
      prop: 'certName',
      label: '证件名称',
      component: 'el-input',
      componentAttrs: {
        placeholder: '请选择证件名称',
        clearable: true,
      }
    },
    // {
    //   label: '状态（DISABLED-禁用、ENABLED-启用）',
    //   prop: 'status',
    //   component: 'el-select',
    //   enums: getDict.value('enable_status') || [],
    //   componentAttrs: {
    //     placeholder: '请选择状态（DISABLED-禁用、ENABLED-启用）',
    //     clearable: true,
    //     filterable: true,
    //     multiple: true,
    //     collapseTags: true,
    //   },
    // },
  ]);

  const columns = computed(() => [
    {
      label: '序号',
      type: 'index',
    },
    {
      label: '证件名称',
      prop: 'certName',
    },
    {
      label: '补充说明',
      prop: 'description',
    },
    // {
    //   label: '状态（DISABLED-禁用、ENABLED-启用）',
    //   prop: 'status',
    //   formatter(row){
    //     const value = row['status'];
    //     const options = getDict.value('enable_status') || [];;
    //     if (Array.isArray(value)) {
    //       return value.map((item) => options?.find((opt) => opt.value == item)?.label).join('、');
    //     }
    //     return options?.find((item) => item.value == value)?.label || '--';
    //   }
    // },
    {
      label: '创建人',
      prop: 'createBy',
    },
    {
      label: '创建时间',
      prop: 'createTime',
      formatter(row) {
        return row.createTime?.split(' ')[0];
      }
    },
    {
      label: '修改人',
      prop: 'updateBy',
    },
    {
      label: '修改时间',
      prop: 'updateTime',
      formatter(row) {
        return row.updateTime?.split(' ')[0];
      }
    },
    {
      prop: "action",
      fixed: "right",
      width: 150,
      label: "操作",
    },
  ])
  return {
    columns,
    searchFields,
  };
};