<template>
  <div class="table-content">
    <div class="content-layout">
      <!-- 左侧物料分类树 -->
      <div class="category-tree-container">
        <div class="tree-header">
          <h3>物料分类</h3>
          <el-button
            size="small"
            @click="refreshCategoryTree"
            :icon="RefreshRight"
          >
            刷新
          </el-button>
        </div>
        <div class="tree-content">
          <!-- 全部选项 -->
          <div
            class="tree-all-option"
            :class="{ 'is-active': selectedCategoryCode === '' }"
            @click="handleShowAll"
          >
            <span>全部物料</span>
          </div>

          <el-tree
            ref="categoryTreeRef"
            :data="categoryTreeData"
            :props="treeProps"
            node-key="id"
            :default-expand-all="false"
            :expand-on-click-node="false"
            :highlight-current="true"
            @node-click="handleCategoryClick"
            class="category-tree"
          >
            <template #default="{ node, data }">
              <span class="tree-node">
                <span>{{ data.categoryName }}</span>
              </span>
            </template>
          </el-tree>
        </div>
      </div>

      <!-- 右侧物料信息表格 -->
      <div class="table-container">
        <yun-pro-table
          ref="proTableRef"
          v-model:pagination="pagination"
          v-model:filter-data="filterTableData"
          v-model:searchData="searchData"
          :table-columns="columns"
          :search-fields="searchFields"
          :auto-height="true"
          :remote-method="remoteMethod"
          :table-props="tablePropsObj"
          layout="whole"
        >
          <template #tableHeaderLeft>
            <el-button :icon="Plus" type="primary" @click="handleAdd()"> 新增 </el-button>
            <el-button @click="handleExport()"> 导出 </el-button>
            <el-button @click="handleImport()"> 导入 </el-button>
          </template>
          <template #t_action="{ row }">
						<yun-rest limit="3">
							<el-button
								type="text"
								size="small"
								@click.prevent="handleEdit(row)"
							>
								编辑
							</el-button>
							<el-button
								type="text"
								size="small"
								@click.prevent="handleView(row)"
							>
								查看
							</el-button>
							<el-button
								type="text"
								size="small"
								@click.prevent="handleSwitchStatus(row)"
							>
								{{ row.status === 'ENABLED' ? '禁用' : '启用' }}
							</el-button>
							<!-- <el-button
								type="text"
								size="small"
								class="text-danger"
								@click.prevent="handleDelete(row)"
							>
								删除
							</el-button> -->
						</yun-rest>
          </template>
        </yun-pro-table>
      </div>
    </div>

    <Form
      ref="formRef"
      @getData="getData"
    />
    <Detail ref="detailRef" />
    <yun-import
      v-model="importVisible"
      dialog-tips="温馨提示:单次最多只能导入1000条数据"
      upload-tips="只能上传.xls,.xlsx文件"
      :download-function="handleDownTemplate"
      :upload-function="importFn"
      :template-urls="templateUrls"
    />
  </div>
</template>

<script setup lang="ts">
import {
  ref, reactive, defineAsyncComponent, computed, onMounted
} from 'vue';
import { Plus, RefreshRight } from '@yun-design/icons-vue';
import { useProTable } from '@ylz-use/core';
import moment from 'moment';
import { ElMessage, ElMessageBox } from 'yun-design';
import { fetchList, delObjs, exportData, downloadTemp, importData, getMaterialCategoryTree, switchMaterialStatus } from '@/api/lowcode/base-material-info/index';
import { useTable } from './hooks/useTable';
import Form from './components/form.vue';
import Detail from './components/detail.vue';

const formRef = ref();
const detailRef = ref();
const categoryTreeRef = ref();
const searchData = reactive({
  status: ['ENABLED']
});

// 物料分类树相关数据
const categoryTreeData = ref([]);
const selectedCategoryCode = ref('');
const treeProps = {
  children: 'children',
  label: 'categoryName',
  value: 'id'
};
const { columns, searchFields } = useTable();
const dateFieldKeys = computed(() => {
  return [];
});
const datetimeFieldKeys = computed(() => {
  return [];
});
const selectFieldKeys = computed(() => {
  return ['status'];
});
const {
  pagination, remoteMethod, tableProps, proTableRef, filterTableData, reLoad,
} = useProTable({
  apiFn: fetchList,
  paramsHandler(params){
    Object.keys(params).forEach((key) => {
      if (dateFieldKeys.value.includes(key)) {
        if (params[key]?.length) {
          params[`${key}Start`] = moment(params[key][0]).startOf('day').format('YYYY-MM-DD');
          params[`${key}End`] = moment(params[key][1]).endOf('day').format('YYYY-MM-DD');
          delete params[key];
        }
      }
      if (datetimeFieldKeys.value.includes(key)) {
        if (params[key]?.length) {
          params[`${key}Start`] = moment(params[key][0]).format('YYYY-MM-DD HH:mm:ss');
          params[`${key}End`] = moment(params[key][1]).format('YYYY-MM-DD HH:mm:ss');
          delete params[key];
        }
      }
      if (selectFieldKeys.value.includes(key)) {
        if (Array.isArray(params[key])) {
          params[`${key}List`] = params[key];
          delete params[key];
        }
      }
    });

    // 添加物料分类过滤
    if (selectedCategoryCode.value) {
      params.materialCategoryId = selectedCategoryCode.value;
    }

    return {
      ...params
    }
  },
  querysHandler(query){
    return {
      ...query,
      current: pagination.value.page,
      size: pagination.value.size,
    }
  },
  responseHandler(result){
    return result.data;
  },
  plugins: { // 挂载插件
    config: {
      columns: columns.value, // 开启搜索增强需要传入列配置
      searchFields: searchFields.value, // 开启搜索增强需要传入搜索配置
    },
    list: ['SEARCH_PLUS'], // 开启插件列表
  },
});

const tablePropsObj = computed(() => {
  return {
    ...tableProps,
    // 去除斑马纹
    stripe: false,
    // 设置表头样式
    headerCellStyle: {
      backgroundColor: '#f5f7fa',
      color: '#303133',
      fontWeight: 'bold',
      height: '40px',
    },
    // 设置单元格样式
    cellStyle: {
      padding: '0',
      height: '40px',
      'vertical-align': 'middle',
    },
    // 设置行样式
    rowStyle: {
      height: '40px',
    },
    // 设置表格行高
    rowHeight: 40,
  };
});

function getData(){
  proTableRef.value?.getData()
}
function handleEdit(row){
  formRef.value?.show(row)
}
function handleAdd(){
  formRef.value?.show()
}
function paramsHandler() {
  const params = { ...searchData.value };
  Object.keys(params).forEach((key) => {
    if (dateFieldKeys.value.includes(key)) {
      if (params[key]?.length) {
        params[`${key}Start`] = moment(params[key][0]).startOf('day').format('YYYY-MM-DD HH:mm:ss');
        params[`${key}End`] = moment(params[key][1]).endOf('day').format('YYYY-MM-DD HH:mm:ss');
        delete params[key];
      }
    }
    if (datetimeFieldKeys.value.includes(key)) {
      if (params[key]?.length) {
        params[`${key}Start`] = moment(params[key][0]).format('YYYY-MM-DD HH:mm:ss');
        params[`${key}End`] = moment(params[key][1]).format('YYYY-MM-DD HH:mm:ss');
        delete params[key];
      }
    }
    if (selectFieldKeys.value.includes(key)) {
      if (Array.isArray(params[key])) {
        params[`${key}List`] = params[key];
        delete params[key];
      }
    }
  });
  return {
    ...params
  }
}
const importVisible = ref(false);
const templateUrls = ref([
  {
    label: '模板下载',
    value: '',
  },
]);

const handleImport = () => {
  importVisible.value = true;
};

async function handleDownTemplate() {
  const data = await downloadTemp();
  // 创建下载链接
  const url = window.URL.createObjectURL(data);
  const a = document.createElement('a');
  a.href = url;
  a.download = `物料管理_导入模板_${moment().format('YYYYMMDDHHmmss')}.xlsx`; // 设置下载文件名
  document.body.appendChild(a);
  a.click();

  // 释放URL对象
  window.URL.revokeObjectURL(url);
  document.body.removeChild(a);
}

// eslint-disable-next-line
function beforeUploadCheckHandler (file) {
  if (!/.(xls|xlsx)$/.test(file.name)) {
    ElMessage({ type: 'warning', message: '文件格式不正确!' });
    return false;
  }
  return true;
}

async function importFn (files) {
  if (files.length === 0) {
    ElMessage({ type: 'warning', message: "请先上传文件" });
    return;
  }
  if (files.some((i) => !beforeUploadCheckHandler(i.raw))) {
    return;
  }
  const sendData = new FormData();
  sendData.append('file', files[0].raw);
  importVisible.value = false;
  await importData(sendData);
  ElMessage.success('导入成功');
  getData();
}
async function handleExport() {
  const data = await exportData(paramsHandler(), {});

  // 创建下载链接
  const url = window.URL.createObjectURL(data);
  const a = document.createElement('a');
  a.href = url;
  a.download = '物料管理.xlsx'; // 设置下载文件名
  document.body.appendChild(a);
  a.click();

  // 释放URL对象
  window.URL.revokeObjectURL(url);
  document.body.removeChild(a);
  ElMessage.success('导出成功');
}
function handleView(row){
  detailRef.value.show(row)
}
function handleDelete(row){
  ElMessageBox.confirm('此操作将删除该项, 是否继续?', '删除确认', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'error',
  }).then(async () => {
    await delObjs({ids: row.id});
    getData();
    ElMessage.success('删除成功!');
  });
}

// 处理启用禁用状态切换
function handleSwitchStatus(row) {
  const action = row.status === 'ENABLED' ? '禁用' : '启用';
  const confirmText = `确定要${action}该物料吗？`;

  ElMessageBox.confirm(confirmText, `${action}确认`, {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  }).then(async () => {
    try {
      await switchMaterialStatus(row.id);
      getData(); // 刷新列表
      ElMessage.success(`${action}成功！`);
    } catch (error) {
      ElMessage.error(`${action}失败，请重试`);
    }
  }).catch(() => {
    // 用户取消操作
  });
}

// 获取物料分类树数据
async function fetchCategoryTree() {
  try {
    const response = await getMaterialCategoryTree('ENABLED');
    categoryTreeData.value = response.data || [];
  } catch (error) {
    console.error('获取物料分类树失败:', error);
    ElMessage.error('获取物料分类树失败');
  }
}

// 刷新分类树
function refreshCategoryTree() {
  fetchCategoryTree();
}

// 处理分类树节点点击
function handleCategoryClick(data: any) {
  selectedCategoryCode.value = data.id;
  // 清除树的当前选中状态，因为我们有自定义的"全部"选项
  categoryTreeRef.value?.setCurrentKey(data.id);
  // 重新加载表格数据
  getData();
}

// 显示全部物料
function handleShowAll() {
  selectedCategoryCode.value = '';
  // 清除树的选中状态
  categoryTreeRef.value?.setCurrentKey(null);
  // 重新加载表格数据
  getData();
}

// 页面初始化
onMounted(() => {
  fetchCategoryTree();
});
</script>

<style lang="scss" scoped>
.table-content {
  width: 100%;
  height: calc(100vh - 88px);
  :deep(.el-form .el-form-item:last-of-type) {
    margin-bottom: 24px !important;
  }
}

.content-layout {
  display: flex;
  height: 100%;
  gap: 16px;
}

.category-tree-container {
  width: 280px;
  min-width: 280px;
  background: #fff;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  display: flex;
  flex-direction: column;
}

.tree-header {
  padding: 16px;
  border-bottom: 1px solid #e4e7ed;
  display: flex;
  justify-content: space-between;
  align-items: center;

  h3 {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
    color: #303133;
  }
}

.tree-content {
  flex: 1;
  padding: 8px;
  overflow-y: auto;
}

.tree-all-option {
  height: 36px;
  padding: 0 12px;
  display: flex;
  align-items: center;
  cursor: pointer;
  border-radius: 4px;
  margin-bottom: 8px;
  font-size: 14px;
  color: #606266;

  &:hover {
    background-color: #f5f7fa;
  }

  &.is-active {
    background-color: #e6f7ff;
    color: #1890ff;
    font-weight: 500;
  }
}

.category-tree {
  :deep(.el-tree-node__content) {
    height: 36px;

    &:hover {
      background-color: #f5f7fa;
    }
  }

  :deep(.el-tree-node.is-current > .el-tree-node__content) {
    background-color: #e6f7ff;
    color: #1890ff;
  }
}

.tree-node {
  display: flex;
  align-items: center;
  width: 100%;

  span {
    font-size: 14px;
  }
}

.table-container {
  flex: 1;
  min-width: 0;
}

// 操作按钮样式
:deep(.el-button.is-text) {
  &.text-success {
    color: #67c23a;

    &:hover {
      color: #85ce61;
    }
  }

  &.text-warning {
    color: #e6a23c;

    &:hover {
      color: #ebb563;
    }
  }

  &.text-danger {
    color: #f56c6c;

    &:hover {
      color: #f78989;
    }
  }
}
</style>
