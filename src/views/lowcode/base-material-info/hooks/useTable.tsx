import { ref, computed } from 'vue';
import moment from 'moment';
import { useDict } from '@/hooks/dict';
import { ElImage } from 'element-plus';
import { Session } from '/@/utils/storage';
import { DATA_SOURCE_LIST, STATUS_LIST } from '@/views/lowcode/base-material-info/hooks/const';

const getDict = computed(() => (str) => {
  if (!str) return [];
  const value = useDict(str)?.[str]?.value;
  return value;
});

export const useTable = () => {



  const searchFields = computed(() => [
		{
			prop: 'materialName',
			label: '物料名称',
			component: 'el-input',
			componentAttrs: {
				placeholder: '请选择物料名称',
				clearable: true,
			}
		},
    {
      prop: 'materialCode',
      label: '物料编码',
      component: 'el-input',
      componentAttrs: {
        placeholder: '请选择物料编码',
        clearable: true,
      }
    },
    {
      label: '状态',
      prop: 'status',
      component: 'el-select',
      enums: STATUS_LIST,
      componentAttrs: {
        placeholder: '请选择',
        clearable: true,
        filterable: true,
        multiple: true,
        collapseTags: true,
      },
    },
  ]);

  const columns = computed(() => [
    {
      label: '序号',
      type: 'index',
    },
    {
      label: '物料编码',
      prop: 'materialCode',
    },
    {
      label: '物料名称',
      prop: 'materialName',
    },
    // {
    //   label: '物料简称',
    //   prop: 'materialSimpleName',
    // },
    {
      label: '规格/型号',
      prop: 'spec',
    },
    {
      label: '物料所属分类',
      prop: 'materialCategoryName',
    },
    {
      label: '物料一级分类',
      prop: 'materialCategoryRoot',
    },
    {
      label: '物料所属组织',
      prop: 'deptIds',
			formatter(row){
				return row.deptList.map((item) => item.deptName).join('、');
			}
    },
    {
      label: '计量单位',
      prop: 'unit',
    },
    {
      label: '来源标识',
      prop: 'dataSource',
			enums: DATA_SOURCE_LIST
    },
    {
      label: '创建人',
      prop: 'createBy',
    },
    {
      label: '创建时间',
      prop: 'createTime',
      formatter(row) {
        return row.createTime?.split(' ')[0];
      }
    },
    {
      label: '修改人',
      prop: 'updateBy',
    },
    {
      label: '修改时间',
      prop: 'updateTime',
      formatter(row) {
        return row.updateTime?.split(' ')[0];
      }
    },
		{
			label: '状态',
			prop: 'status',
			enums: STATUS_LIST
		},
    {
      prop: "action",
      fixed: "right",
      width: 150,
      label: "操作",
    },
  ])
  return {
    columns,
    searchFields,
  };
};
