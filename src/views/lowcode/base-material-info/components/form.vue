<template>
  <yun-drawer
    v-model="visible"
    destroy-on-close
    modal
    custom-class="form-drawer"
    close-on-click-modal
    confirm-button-text="确定"
    cancel-button-text="取消"
    :confirm-button-disabled="loading"
    :title="title"
    :size="size"
    @confirm="confirmHandler"
    @close="closeHandler"
  >
    <el-form
      ref="vForm"
      :model="formData"
      :rules="rules"
      :label-width="120"
      label-position="top"
    >
      <el-row :gutter="12">
        <el-col :span="8">
          <el-form-item label="物料编码" prop="materialCode">
            <el-input
              v-model="formData.materialCode"
              placeholder="请输入物料编码"
              type="text"
              clearable
              maxlength="30"
              show-word-limit
            />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="物料名称" prop="materialName">
            <el-input
              v-model="formData.materialName"
              placeholder="请输入物料名称"
              type="text"
              clearable
              maxlength="30"
              show-word-limit
            />
          </el-form-item>
        </el-col>
<!--        <el-col :span="8">-->
<!--          <el-form-item label="状态" prop="status">-->
<!--            <el-select v-model="formData.status" style="width: 100%;" clearable>-->
<!--              <el-option label="启用" value="ENABLED" />-->
<!--              <el-option label="禁用" value="DISABLED" />-->
<!--            </el-select>-->
<!--          </el-form-item>-->
<!--        </el-col>-->
<!--        <el-col :span="8">-->
<!--          <el-form-item label="物料简称" prop="materialSimpleName">-->
<!--            <el-input v-model="formData.materialSimpleName" type="text" clearable icon="custom-search" />-->
<!--          </el-form-item>-->
<!--        </el-col>-->
        <el-col :span="8">
          <el-form-item label="规格/型号" prop="spec">
            <el-input v-model="formData.spec" placeholder="请输入" type="text" clearable icon="custom-search" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="物料所属分类" prop="materialCategoryId">
            <el-cascader
              v-model="formData.materialCategoryId"
              style="width: 100%;"
              clearable
              filterable
              showAllLevels
              :options="categoryTreeData"
              :props="{
                multiple: false,
                value: 'id',
                label: 'categoryName',
                children: 'children',
                checkStrictly: true,
                emitPath: false,
                expandTrigger: 'hover'
              }"
              placeholder="请选择物料所属分类"
              @change="handleCategoryChange"
						>
							<template #default="{node, data}">
								<span>{{ data.categoryName }}-{{ data.categoryCode }}</span>
							</template>
						</el-cascader>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="物料一级分类" prop="materialCategoryRoot">
            <el-input
              v-model="formData.materialCategoryRoot"
              placeholder="根据物料所属分类自动带出"
              type="text"
              readonly
              disabled
            />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="计量单位" prop="unit">
            <el-input
              v-model="formData.unit"
              placeholder="请输入计量单位"
              type="text"
              clearable
              maxlength="10"
              show-word-limit
            />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="物料所属组织" prop="deptIds">
            <el-cascader
              v-model="formData.deptIds"
              style="width: 100%;"
              clearable
              :filterable="false"
              :options="deptTreeData"
              :props="{
                multiple: true,
                value: 'id',
                label: 'name',
                children: 'children',
                checkStrictly: true,
                emitPath: false,
                expandTrigger: 'hover'
              }"
              placeholder="请选择"
              collapse-tags
              collapse-tags-tooltip
            />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="备注" prop="remark">
            <el-input
              v-model="formData.remark"
              placeholder="请输入"
              type="textarea"
              :rows="3"
              clearable
              maxlength="200"
              show-word-limit
            />
          </el-form-item>
        </el-col>
<!--        <el-col :span="8">-->
<!--          <el-form-item label="来源标识" prop="dataSource">-->
<!--            <el-select v-model="formData.dataSource" style="width: 100%;" clearable :multipleLimit="0">-->
<!--                <el-option-->
<!--                  v-for="option in getDict('data_source')"-->
<!--                  :key="option.value"-->
<!--                  :label="option.label"-->
<!--                  :value="option.value"-->
<!--                />-->
<!--            </el-select>-->
<!--          </el-form-item>-->
<!--        </el-col>-->
<!--        <el-col :span="8">-->
<!--          <el-form-item label="状态" prop="status">-->
<!--            <el-select v-model="formData.status" style="width: 100%;" clearable :multipleLimit="0">-->
<!--                <el-option-->
<!--                  v-for="option in getDict('enable_status')"-->
<!--                  :key="option.value"-->
<!--                  :label="option.label"-->
<!--                  :value="option.value"-->
<!--                />-->
<!--            </el-select>-->
<!--          </el-form-item>-->
<!--        </el-col>-->
      </el-row>
    </el-form>
  </yun-drawer>
</template>

<script setup lang="ts">
import { ref, reactive, computed, nextTick, watch } from 'vue';
import { ElMessage } from 'yun-design';
import { Plus, Delete } from '@element-plus/icons-vue';
import { useDict } from '@/hooks/dict';
import { addObj, putObj, getObj, getMaterialCategoryTree } from '@/api/lowcode/base-material-info/index';
import { deptTree } from '@/api/admin/dept';
import { useUserInfo } from '@/stores/userInfo';
import { rule } from '@/utils/validate';
import { Session } from '@/utils/storage';
import moment from 'moment';

// 状态管理
const visible = ref(false);
const loading = ref(false);
const isEdit = ref(false);
const vForm = ref();
const categoryTreeRef = ref();
const formData = reactive({});

// 物料分类相关
const categoryTreeData = ref([]);

// 部门组织相关
const deptTreeData = ref([]);
// 字符长度校验函数
const validateMaxLength = (maxLength: number, fieldName: string) => {
  return (rule: any, value: any, callback: any) => {
    if (!value) {
      return callback();
    }
    if (value.length > maxLength) {
      return callback(new Error(`${fieldName}最多输入${maxLength}个字符`));
    }
    callback();
  };
};

const rules = reactive({
  materialCode: [
    {
      required: true,
      message: '请输入物料编码',
      trigger: ['blur', 'change']
    },
    {
      validator: validateMaxLength(30, '物料编码'),
      trigger: ['blur', 'change']
    }
  ],
  materialName: [
    {
      required: true,
      message: '请输入物料名称',
      trigger: ['blur', 'change']
    },
    {
      validator: validateMaxLength(30, '物料名称'),
      trigger: ['blur', 'change']
    }
  ],
  status: [
    {
      required: true,
      message: '请选择状态',
      trigger: ['blur', 'change']
    }
  ],
  materialCategoryId: [
    {
      required: true,
      message: '请选择物料所属分类',
      trigger: ['blur', 'change']
    }
  ],
  deptIds: [
    {
      required: true,
      message: '请选择物料所属组织',
      trigger: ['blur', 'change']
    }
  ],
  unit: [
    {
      validator: validateMaxLength(10, '计量单位'),
      trigger: ['blur', 'change']
    }
  ],
  description: [
    {
      validator: validateMaxLength(200, '物料描述'),
      trigger: ['blur', 'change']
    }
  ]
});



// 获取物料分类树
const getCategoryTree = async () => {
  try {
    const response = await getMaterialCategoryTree('ENABLED');
    categoryTreeData.value = response.data || [];
  } catch (error) {
    console.error('获取物料分类树失败:', error);
  }
};

// 获取部门树
const getDeptTree = async () => {
  try {
    const response = await deptTree();
    deptTreeData.value = response.data || [];
  } catch (error) {
    console.error('获取部门树失败:', error);
  }
};

// 分类搜索过滤方法（支持编码和名称搜索）
const filterCategoryMethod = (node: any, keyword: string) => {
  // if (!keyword) return true;
  // const searchText = keyword.toLowerCase();
  // const categoryName = (node.categoryName || '').toLowerCase();
  // const categoryCode = (node.categoryCode || '').toLowerCase();
  // return categoryName.includes(searchText) || categoryCode.includes(searchText);
};

// 获取分类树中的一级分类
const findRootCategory = (id: string, treeData: any[]): string => {
  for (const item of treeData) {
    if (item.id === id) {
      // 如果当前节点就是一级分类（没有父级），返回自己
      return item.materialCategoryRoot || item.categoryName;
    }
    if (item.children && item.children.length > 0) {
      const result = findRootCategory(id, item.children);
      if (result) {
        // 如果在子级找到了，返回当前一级分类
        return item.materialCategoryRoot || item.categoryName;
      }
    }
  }
  return '';
};

// 处理分类变化，自动带出一级分类
const handleCategoryChange = (value: string) => {
  if (value && categoryTreeData.value.length > 0) {
    const rootCategoryName = findRootCategory(value, categoryTreeData.value);
    formData.materialCategoryRoot = rootCategoryName;
  } else {
    formData.materialCategoryRoot = '';
  }
};



// 计算属性
const title = computed(() => (isEdit.value ? '编辑' : '新增'));
const size = computed(() => 'X-large');

// 用户信息
const userStore = useUserInfo();
const emits = defineEmits(['getData']);

// 关闭处理
function closeHandler() {
  visible.value = false;
}

// 格式化表单数据
async function formatFrom(row: Record<string, any>) {
  for(let key in formData) {
    delete formData[key];
  }

  // 初始化默认值
  formData.deptIds = [];
  formData.status = 'ENABLED'; // 状态默认启用

  if (!row.id) return;
  const res = await getObj(row.id);
  Object.assign(formData, res.data);

  // 处理物料分类数据（单选）
  if (res.data.materialCategoryId) {
    formData.materialCategoryId = res.data.materialCategoryId;
    // 自动带出一级分类
    handleCategoryChange(res.data.materialCategoryId);
  }

  // 处理所属组织数据（多选）
  if (res.data.deptList) {
    formData.deptIds = res.data.deptList.map((item) => item.deptId);
  } else {
    formData.deptIds = [];
  }
}

// 格式化提交参数
function formatParams() {
  const baseParams = {};

  // 处理所属组织字段（多选转字符串）
  // if (Array.isArray(formData.deptIds)) {
  //   baseParams.deptIds = formData.deptIds.join(',');
  // }

  return {
    ...formData,
    ...baseParams,
  };
}

// 确认处理
async function confirmHandler(done: () => void, loading: { value: boolean }) {
  try {
    await vForm.value?.validate();
    loading.value = true;
    const api = isEdit.value ? putObj : addObj;
    const params = formatParams();
    await api(params);
    done();
    emits('getData', formData);
  } catch (error) {
    ElMessage.error('表单验证或提交失败');
  } finally {
    loading.value = false;
  }
}

// 显示表单
async function show(row?: Record<string, any>) {
  visible.value = true;
  isEdit.value = !!row;
  await formatFrom(row || {});
  await getCategoryTree(); // 获取分类树数据
  await getDeptTree(); // 获取部门树数据
  nextTick(() => {
    vForm.value?.clearValidate();
  });
}

// 日期时间范围默认值
const defaultTime = [new Date(2000, 1, 1, 0, 0, 0), new Date(2000, 2, 1, 23, 59, 59)];

// 字典数据
const getDict = computed(() => (str: string) => {
  if (!str) return [];
  const value = useDict(str)?.[str]?.value;
  return value;
});

// 生成明细表新行的初始数据
function getEnhancedTableRow(widget: Widget): Record<string, any> {
  const row: Record<string, any> = {};
  if (Array.isArray(widget.widgetList)) {
    for (const colWidget of widget.widgetList) {
      const name = colWidget.options?.name;
      if (!name) continue;
      let value = colWidget.options?.defaultValue;
      if (value === undefined) {
        if (colWidget.type === 'number') value = 0;
        else value = '';
      }
      row[name] = value;
    }
  }
  return row;
}

// 添加明细表行
function handleAddRow(tableName: string) {
  if (!formData[tableName]) {
    formData[tableName] = [];
  }

  formData[tableName].push({});
}

// 删除明细表行
function handleRemoveRow(tableName: string, index: number) {
  if (!formData[tableName]) return;
  formData[tableName].splice(index, 1);
}

// 暴露方法
defineExpose({
  show,
  formData,
  rules
});
</script>

<style lang="scss">
.form-drawer {
  .el-form .el-form-item:last-of-type {
    margin-bottom: 24px !important;
  }
}
</style>

<style lang="scss" scoped>
.el-form-item {
  margin-bottom: 18px;
}

.table-header {
  margin-bottom: 15px;
  .label {
    font-weight: bold;
    font-size: 18px;
    padding-left: 8px;
    border-left: 4px solid var(--el-color-primary);
    line-height: 1.2;
  }
}

:deep(.el-form-item__label) {
  font-weight: normal;
}

:deep(.el-table) {
  .el-table__header-wrapper {
    th {
      background-color: var(--el-fill-color-light);
    }
  }
}

// 统一级联选择器的 placeholder 颜色与普通 input 保持一致
:deep(.el-cascader) {
  .el-input .el-input__inner {
    &::placeholder {
      color: var(--el-text-color-placeholder) !important;
      opacity: 1 !important;
    }
  }

  // 确保在不同状态下 placeholder 颜色一致
  &:hover .el-input .el-input__inner::placeholder {
    color: var(--el-text-color-placeholder) !important;
    opacity: 1 !important;
  }

  &.is-focus .el-input .el-input__inner::placeholder {
    color: var(--el-text-color-placeholder) !important;
    opacity: 1 !important;
  }

  // 禁用状态下的 placeholder 颜色
  &.is-disabled .el-input .el-input__inner::placeholder {
    color: var(--el-disabled-text-color) !important;
    opacity: 1 !important;
  }
}

// 确保普通 input 和级联选择器的 placeholder 颜色完全一致
:deep(.el-input .el-input__inner::placeholder) {
  color: var(--el-text-color-placeholder) !important;
  opacity: 1 !important;
}

</style>
