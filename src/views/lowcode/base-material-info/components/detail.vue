<template>
  <yun-drawer
    v-model="visible"
    :show-cancel-button="false"
    destroy-on-close
    title="详情"
    size="X-large"
    @confirm="handleConfirm"
  >
    <Detail :groups="processedGroups" :data="form"/>
  </yun-drawer>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import Detail from '@/components/Detail/index.vue';
import { getObj } from '@/api/lowcode/base-material-info/index';
import { DATA_SOURCE_LIST, STATUS_LIST } from '@/views/lowcode/base-material-info/hooks/const';

interface FormData {
  [key: string]: any;
}

// 静态生成的分组配置
const processedGroups = [
  {
    title: "",
    columns: [
      {
        prop: 'materialCode',
        label: '物料编码',
        type: 'text'
      },
      {
        prop: 'materialName',
        label: '物料名称',
        type: 'text'
      },
      // {
      //   prop: 'materialSimpleName',
      //   label: '物料简称',
      //   type: 'text'
      // },
      {
        prop: 'spec',
        label: '规格/型号',
        type: 'text'
      },
      {
        prop: 'materialCategoryName',
        label: '物料所属分类',
      },
			{
				prop: 'materialCategoryRoot',
				label: '物料一级分类',
				type: 'text'
			},
			{
				prop: 'deptId',
				label: '物料所属组织',
				formatter(row){
					return row.deptList.map((item) => item.deptName).join('、');
				},
				type: 'text',
			},
			{
				label: '来源标识',
				prop: 'dataSource',
				type: 'enums',
				attrs: {
					options: DATA_SOURCE_LIST,
				}
			},

			{
				prop: 'unit',
				label: '计量单位',
				type: 'text'
			},
      {
        prop: 'status',
        label: '状态',
        type: 'enums',
        attrs: {
          options: STATUS_LIST,
          // dataSourceType: 'dict',
          // dictType: 'enable_status'
        }
      },
			{
				prop: 'remark',
				label: '备注',
				type: 'text'
			},
    ]
  }
];

const visible = ref(false);
const form = ref<FormData>({});

async function handleConfirm(done: () => void) {
  done();
}

async function show(row: FormData) {
  const res = await getObj(row.id);
  form.value = res.data;
  visible.value = true;
}

defineExpose({
  show,
});
</script>

<style scoped lang="scss">
</style>
