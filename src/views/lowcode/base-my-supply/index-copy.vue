<template>
	<div class="table-content my-custom-table-wrapper">
		<yun-pro-table
			ref="proTableRef"
			v-model:pagination="pagination"
			v-model:filter-data="filterTableData"
			:table-columns="columns"
			:search-fields="searchFields"
			:auto-height="true"
			:remote-method="remoteMethod"
			:table-props="tablePropsObj"
			layout="whole"
		>
			<template #tableHeaderLeft>
				<el-button
					:icon="Plus"
					type="primary"
					@click="handleAdd()"
				>
					新增
				</el-button>
			</template>
			<template #t_action="{ row }">
				<el-button
					type="text"
					size="small"
					@click.prevent="handleView(row)"
				>
					查看
				</el-button>
				<el-button
					type="text"
					size="small"
					@click.prevent="handleEdit(row)"
				>
					编辑
				</el-button>
				<el-button
					type="text"
					size="small"
					@click.prevent="handleSwitch(row)"
				>
					{{ row.status === 'ENABLED' ? '禁用' : '启用' }}
				</el-button>
			</template>
			<!-- <template #t_status="{ row }">
        {{ row.status === 'ENABLED' ? '启用' : '禁用' }}
      </template> -->
		</yun-pro-table>
		<Form
			ref="formRef"
			@get-data="getData"
		/>
		<Detail ref="detailRef" />
	</div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import { Plus } from '@yun-design/icons-vue';
import { useProTable } from '@ylz-use/core';
import { ElMessage, ElMessageBox } from 'yun-design';
import { fetchList, switchStatus } from '@/api/lowcode/base-my-supply/index';
import { useTable } from './hooks/useTable';
import Form from './components/form.vue';
import Detail from './components/detail.vue';

const formRef = ref();
const detailRef = ref();
const { columns, searchFields } = useTable();

const { pagination, remoteMethod, tableProps, proTableRef, filterTableData } = useProTable({
	apiFn: fetchList,
	responseHandler(result: any) {
		return result.data?.records || [];
	},
	paramsHandler(params: any) {
		return {
			...params,
			// ...searchData.value,
			// current: pagination.value.page,
			// size: pagination.value.size,
		};
	},
	querysHandler(querys: any) {
		return {
			...querys,
			// ...searchData,
			current: pagination.value.page,
			size: pagination.value.size,
		};
	},
	plugins: {
		config: {
			columns: columns.value,
			searchFields: searchFields.value,
		},
		list: ['SEARCH_PLUS'],
	},
});

const tablePropsObj = computed(() => ({
	...tableProps,
	stripe: false,
	headerCellStyle: {
		backgroundColor: '#f5f7fa',
		color: '#303133',
		fontWeight: 'bold',
		height: '40px',
	},
	cellStyle: {
		padding: '0',
		height: '40px',
		'vertical-align': 'middle',
	},
	rowStyle: {
		height: '40px',
	},
	rowHeight: 40,
}));

function getData() {
	proTableRef.value?.getData();
}
function handleEdit(row: any) {
	formRef.value?.show(row);
}
function handleAdd() {
	formRef.value?.show();
}

function handleView(row: any) {
	detailRef.value.show(row);
}
function handleSwitch(row: any) {
	const action = row.status === 'ENABLED' ? '禁用' : '启用';
	ElMessageBox.confirm(`此操作将${action}该供应商, 是否继续?`, `${action}确认`, {
		confirmButtonText: '确定',
		cancelButtonText: '取消',
		type: 'warning',
	}).then(async () => {
		await switchStatus(row.id);
		getData();
		ElMessage.success(`${action}成功!`);
	});
}
</script>

<style lang="scss" scoped>
.table-content {
	width: 100%;
	height: calc(100vh - 88px);
	padding: 16px;
	background-color: #fff;
}
</style>
<style>
.my-custom-table-wrapper .el-form .el-form-item {
	margin-bottom: 24px !important;
}
</style>
