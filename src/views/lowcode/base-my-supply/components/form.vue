<template>
	<yun-drawer
		v-model="visible"
		destroy-on-close
		modal
		custom-class="form-drawer"
		close-on-click-modal
		confirm-button-text="确定"
		cancel-button-text="取消"
		:confirm-button-disabled="loading"
		:title="isEdit ? '编辑供应商' : '新增供应商'"
		size="X-large"
		@confirm="confirmHandler"
		@close="closeHandler"
	>
		<el-form
			ref="vForm"
			:model="formData"
			:rules="rules"
			label-width="120px"
			label-position="top"
		>
			<!-- 基础信息 -->
			<div class="table-header">
				<div class="label">基础信息</div>
			</div>
			<el-row :gutter="20">
				<el-col :span="8">
					<el-form-item label="供应商名称" prop="supplierName">
						<el-input v-model="formData.supplierName" placeholder="请输入" maxlength="30" show-word-limit />
					</el-form-item>
				</el-col>
				<el-col :span="8">
					<el-form-item label="供应商简称" prop="abbreviation">
						<el-input v-model="formData.abbreviation" placeholder="请输入" maxlength="30" show-word-limit />
					</el-form-item>
				</el-col>
				<el-col :span="8">
					<el-form-item label="统一社会信用代码" prop="unifiedSocialCreditCode">
						<el-input v-model="formData.unifiedSocialCreditCode" placeholder="请输入" maxlength="30" show-word-limit />
					</el-form-item>
				</el-col>
			</el-row>
			<el-row :gutter="20">
				<el-col :span="8">
					<el-form-item label="供应商类型" prop="supplierType">
						<el-select v-model="formData.supplierType" placeholder="生产型供应商" style="width: 100%">
							<el-option label="其他" value="其他" />
							<el-option label="租赁服务供应商" value="租赁服务供应商" />
							<el-option label="劳务外包服务商" value="劳务外包服务商" />
							<el-option label="物流承运商" value="物流承运商" />
							<el-option label="工程建筑承包商" value="工程建筑承包商" />
							<el-option label="服务行供应商" value="服务行供应商" />
							<el-option label="贸易型供应商" value="贸易型供应商" />
							<el-option label="生产型供应商" value="生产型供应商" />
						</el-select>
					</el-form-item>
				</el-col>
				<el-col :span="8">
					<el-form-item label="供应商状态" prop="supplierStatus">
						<el-select v-model="formData.supplierStatus" placeholder="合格供应商" style="width: 100%">
							<el-option label="陪标供应商" value="陪标供应商" />
							<el-option label="潜在供应商" value="潜在供应商" />
							<el-option label="合格供应商" value="合格供应商" />
							<el-option label="淘汰供应商" value="淘汰供应商" />
							<el-option label="黑名单" value="黑名单" />
						</el-select>
					</el-form-item>
				</el-col>
				<el-col :span="8">
					<el-form-item label="企业性质" prop="businessNature">
						<el-select v-model="formData.businessNature" placeholder="有限责任公司" style="width: 100%">
							<el-option label="有限责任公司" value="有限责任公司" />
							<el-option label="股份有限公司" value="股份有限公司" />
							<el-option label="个人独资" value="个人独资" />
							<el-option label="有限合伙" value="有限合伙" />
							<el-option label="普通合作" value="普通合作" />
							<el-option label="国有企业" value="国有企业" />
							<el-option label="外商投资" value="外商投资" />
							<el-option label="港澳台投资" value="港澳台投资" />
							<el-option label="集体所有制" value="集体所有制" />
							<el-option label="联营" value="联营" />
							<el-option label="私营" value="私营" />
							<el-option label="个体工商户" value="个体工商户" />
						</el-select>
					</el-form-item>
				</el-col>
			</el-row>
			<el-row :gutter="20">
				<el-col :span="8">
					<el-form-item label="供应商分类" prop="supplierCategory">
						<el-select v-model="formData.supplierCategory" placeholder="营造供应商" style="width: 100%">
							<el-option label="营造供应商" value="营造供应商" />
							<el-option label="内部供应商" value="内部供应商" />
							<el-option label="结算供应商" value="结算供应商" />
							<el-option label="一次性供应商" value="一次性供应商" />
							<el-option label="临时供应商" value="临时供应商" />
						</el-select>
					</el-form-item>
				</el-col>
				<el-col :span="8">
					<el-form-item label="注册资本" prop="registeredCapital">
						<el-input v-model="formData.registeredCapital" placeholder="请输入" />
					</el-form-item>
				</el-col>
				<el-col :span="8">
					<el-form-item label="成立日期" prop="establishmentDate">
						<el-date-picker
							v-model="formData.establishmentDate"
							type="date"
							placeholder="请输入"
							value-format="YYYY-MM-DD"
							style="width: 100%"
						/>
					</el-form-item>
				</el-col>
			</el-row>
			<el-row :gutter="20">
				<el-col :span="8">
					<el-form-item label="法人代表" prop="legalRepresentative">
						<el-input v-model="formData.legalRepresentative" placeholder="请输入" />
					</el-form-item>
				</el-col>
				<el-col :span="8">
					<el-form-item label="法人身份证号" prop="legalIdCard">
						<el-input v-model="formData.legalIdCard" placeholder="请输入" />
					</el-form-item>
				</el-col>
				<el-col :span="8">
					<el-form-item label="法人电话" prop="legalPhone">
						<el-input v-model="formData.legalPhone" placeholder="请输入" />
					</el-form-item>
				</el-col>
			</el-row>
			<el-row :gutter="20">
				<el-col :span="8">
					<el-form-item label="服务开始时间" prop="serviceStartDate">
						<el-date-picker
							v-model="formData.serviceStartDate"
							type="date"
							placeholder="请输入"
							value-format="YYYY-MM-DD"
							style="width: 100%"
						/>
					</el-form-item>
				</el-col>
				<el-col :span="8">
					<el-form-item label="服务结束时间" prop="serviceEndDate">
						<el-date-picker
							v-model="formData.serviceEndDate"
							type="date"
							placeholder="请输入"
							value-format="YYYY-MM-DD"
							style="width: 100%"
						/>
					</el-form-item>
				</el-col>
				<el-col :span="8">
					<el-form-item label="注册地址" prop="registeredAddress">
						<el-input v-model="formData.registeredAddress" placeholder="请输入" />
					</el-form-item>
				</el-col>
			</el-row>
			<el-row :gutter="20">
				<el-col :span="8">
					<el-form-item label="邮政编码" prop="postalCode">
						<el-input v-model="formData.postalCode" placeholder="请输入" />
					</el-form-item>
				</el-col>
				<el-col :span="8">
					<el-form-item label="传真" prop="fax">
						<el-input v-model="formData.fax" placeholder="请输入" />
					</el-form-item>
				</el-col>
				<el-col :span="8">
					<el-form-item label="年平均营业额" prop="annualRevenue">
						<el-input v-model="formData.annualRevenue" placeholder="请输入" />
					</el-form-item>
				</el-col>
			</el-row>
			<el-row :gutter="20">
				<el-col :span="24">
					<el-form-item label="公司简介" prop="companyProfile">
						<el-input v-model="formData.companyProfile" type="textarea" :rows="3" placeholder="请输入" maxlength="300" show-word-limit />
					</el-form-item>
				</el-col>
			</el-row>
			<el-row :gutter="20">
				<el-col :span="24">
					<el-form-item label="经营范围" prop="businessScope">
						<el-input v-model="formData.businessScope" type="textarea" :rows="3" placeholder="请输入" maxlength="300" show-word-limit />
					</el-form-item>
				</el-col>
			</el-row>
			<el-row :gutter="20">
				<el-col :span="24">
					<el-form-item label="备注" prop="basicRemark">
						<el-input v-model="formData.basicRemark" type="textarea" :rows="3" placeholder="请输入" maxlength="300" show-word-limit />
					</el-form-item>
				</el-col>
			</el-row>

			<!-- 联系人信息 -->
			<div class="table-header">
				<div class="label">联系人信息</div>
			</div>
			<div class="w-full overflow-x-auto">
				<el-table
					:data="formData.contactList || []"
					:border="true"
					stripe
					class="w-full dark:bg-gray-800"
					:header-cell-class-name="'!bg-gray-50 dark:!bg-gray-700'"
					style="min-width: 800px"
				>
					<el-table-column type="index" label="序号" width="60" align="center" fixed />
					<el-table-column label="联系人姓名" prop="contactName" min-width="150px">
						<template #default="{ row }">
							<el-input
								v-model="row.contactName"
								placeholder="请输入联系人姓名"
								maxlength="10"
								show-word-limit
							/>
						</template>
					</el-table-column>
					<el-table-column label="联系人电话" prop="contactPhone" min-width="150px">
						<template #default="{ row }">
							<el-input
								v-model="row.contactPhone"
								placeholder="请输入联系人电话"
							/>
						</template>
					</el-table-column>
					<el-table-column label="身份证" prop="idCard" min-width="150px">
						<template #default="{ row }">
							<el-input
								v-model="row.idCard"
								placeholder="请输入身份证"
								maxlength="20"
								show-word-limit
							/>
						</template>
					</el-table-column>
					<el-table-column label="是否生成登录账号" prop="generateAccount" min-width="150px">
						<template #default="{ row }">
							<el-switch
								v-model="row.generateAccount"
								active-value="YES"
								inactive-value="NO"
								active-text="是"
								inactive-text="否"
							/>
						</template>
					</el-table-column>
					<el-table-column label="备注" prop="contactRemark" min-width="200px">
						<template #default="{ row }">
							<el-input
								v-model="row.contactRemark"
								placeholder="请输入备注"
								maxlength="50"
								show-word-limit
							/>
						</template>
					</el-table-column>
					<el-table-column label="操作" fixed="right" width="100" align="center">
						<template #default="{ $index }">
							<el-button
								type="danger"
								size="small"
								icon="Delete"
								@click="handleRemoveRow('contactList', $index)"
							>
								删除
							</el-button>
						</template>
					</el-table-column>
				</el-table>
			</div>
			<el-button
				style="width: 100%; margin-top: 5px"
				:icon="Plus"
				@click="handleAddRow('contactList')"
			>
				添加联系人
			</el-button>

			<!-- 资质信息 -->
			<div class="table-header">
				<div class="label">资质信息</div>
			</div>
			<div class="w-full overflow-x-auto">
				<el-table
					:data="formData.qualificationList || []"
					:border="true"
					stripe
					class="w-full dark:bg-gray-800"
					:header-cell-class-name="'!bg-gray-50 dark:!bg-gray-700'"
					style="min-width: 900px"
				>
					<el-table-column type="index" label="序号" width="60" align="center" fixed />
					<el-table-column label="证书类型" prop="certificateType" min-width="150px">
						<template #default="{ row }">
							<el-input
								v-model="row.certificateType"
								placeholder="请输入证书类型"
								maxlength="30"
								show-word-limit
							/>
						</template>
					</el-table-column>
					<el-table-column label="证书名称" prop="certificateName" min-width="150px">
						<template #default="{ row }">
							<el-input
								v-model="row.certificateName"
								placeholder="请输入证书名称"
								maxlength="30"
								show-word-limit
							/>
						</template>
					</el-table-column>
					<el-table-column label="证书号" prop="certificateNumber" min-width="150px">
						<template #default="{ row }">
							<el-input
								v-model="row.certificateNumber"
								placeholder="请输入证书号"
								maxlength="30"
								show-word-limit
							/>
						</template>
					</el-table-column>
					<el-table-column label="证书生效日期" prop="effectiveDate" min-width="150px">
						<template #default="{ row }">
							<el-date-picker
								v-model="row.effectiveDate"
								type="date"
								placeholder="选择生效日期"
								value-format="YYYY-MM-DD"
								style="width: 100%"
							/>
						</template>
					</el-table-column>
					<el-table-column label="失效日期" prop="expiryDate" min-width="150px">
						<template #default="{ row }">
							<el-date-picker
								v-model="row.expiryDate"
								type="date"
								placeholder="选择失效日期"
								value-format="YYYY-MM-DD"
								style="width: 100%"
							/>
						</template>
					</el-table-column>
					<el-table-column label="备注" prop="qualificationRemark" min-width="200px">
						<template #default="{ row }">
							<el-input
								v-model="row.qualificationRemark"
								placeholder="请输入备注"
								maxlength="50"
								show-word-limit
							/>
						</template>
					</el-table-column>
					<el-table-column label="操作" fixed="right" width="100" align="center">
						<template #default="{ $index }">
							<el-button
								type="danger"
								size="small"
								icon="Delete"
								@click="handleRemoveRow('qualificationList', $index)"
							>
								删除
							</el-button>
						</template>
					</el-table-column>
				</el-table>
			</div>
			<el-button
				style="width: 100%; margin-top: 5px"
				:icon="Plus"
				@click="handleAddRow('qualificationList')"
			>
				添加资质
			</el-button>

			<!-- 银行信息 -->
			<div class="table-header">
				<div class="label">银行信息</div>
			</div>
			<div class="w-full overflow-x-auto">
				<el-table
					:data="formData.bankList || []"
					:border="true"
					stripe
					class="w-full dark:bg-gray-800"
					:header-cell-class-name="'!bg-gray-50 dark:!bg-gray-700'"
					style="min-width: 900px"
				>
					<el-table-column type="index" label="序号" width="60" align="center" fixed />
					<el-table-column label="开户行" prop="bankName" min-width="150px">
						<template #default="{ row }">
							<el-input
								v-model="row.bankName"
								placeholder="请输入开户行"
								maxlength="30"
								show-word-limit
							/>
						</template>
					</el-table-column>
					<el-table-column label="开户行地址" prop="bankAddress" min-width="200px">
						<template #default="{ row }">
							<el-input
								v-model="row.bankAddress"
								placeholder="请输入开户行地址"
								maxlength="30"
								show-word-limit
							/>
						</template>
					</el-table-column>
					<el-table-column label="开户行联行号" prop="bankCode" min-width="150px">
						<template #default="{ row }">
							<el-input
								v-model="row.bankCode"
								placeholder="请输入开户行联行号"
								maxlength="30"
								show-word-limit
							/>
						</template>
					</el-table-column>
					<el-table-column label="开户名" prop="accountName" min-width="150px">
						<template #default="{ row }">
							<el-input
								v-model="row.accountName"
								placeholder="请输入开户名"
								maxlength="30"
								show-word-limit
							/>
						</template>
					</el-table-column>
					<el-table-column label="开户行账号" prop="accountNumber" min-width="200px">
						<template #default="{ row }">
							<el-input
								v-model="row.accountNumber"
								placeholder="请输入开户行账号"
								maxlength="30"
								show-word-limit
							/>
						</template>
					</el-table-column>
					<el-table-column label="备注" prop="bankRemark" min-width="200px">
						<template #default="{ row }">
							<el-input
								v-model="row.bankRemark"
								placeholder="请输入备注"
								maxlength="50"
								show-word-limit
							/>
						</template>
					</el-table-column>
					<el-table-column label="操作" fixed="right" width="100" align="center">
						<template #default="{ $index }">
							<el-button
								type="danger"
								size="small"
								icon="Delete"
								@click="handleRemoveRow('bankList', $index)"
							>
								删除
							</el-button>
						</template>
					</el-table-column>
				</el-table>
			</div>
			<el-button
				style="width: 100%; margin-top: 5px"
				:icon="Plus"
				@click="handleAddRow('bankList')"
			>
				添加银行信息
			</el-button>

			<!-- 主营物料 -->
			<div class="table-header">
				<div class="label">主营物料</div>
			</div>
			<div class="w-full overflow-x-auto">
				<el-table
					:data="formData.materialList || []"
					:border="true"
					stripe
					class="w-full dark:bg-gray-800"
					:header-cell-class-name="'!bg-gray-50 dark:!bg-gray-700'"
					style="min-width: 800px"
				>
					<el-table-column type="index" label="序号" width="60" align="center" fixed />
					<el-table-column label="物料编码" prop="materialCode" min-width="150px" />
					<el-table-column label="物料名称" prop="materialName" min-width="200px" />
					<el-table-column label="规格型号" prop="spec" min-width="150px" />
					<el-table-column label="单位" prop="unit" min-width="100px" />
					<el-table-column label="备注" prop="materialRemark" min-width="200px">
						<template #default="{ row }">
							<el-input
								v-model="row.materialRemark"
								placeholder="请输入备注"
								maxlength="50"
								show-word-limit
							/>
						</template>
					</el-table-column>
					<el-table-column label="操作" fixed="right" width="100" align="center">
						<template #default="{ $index }">
							<el-button
								type="danger"
								size="small"
								icon="Delete"
								@click="handleRemoveRow('materialList', $index)"
							>
								删除
							</el-button>
						</template>
					</el-table-column>
				</el-table>
			</div>
			<el-button
				style="width: 100%; margin-top: 5px"
				:icon="Plus"
				@click="showMaterialSelector"
			>
				添加物料
			</el-button>
		</el-form>
	</yun-drawer>

	<!-- 物料选择抽屉 -->
	<MaterialSelector
		ref="materialSelectorRef"
		@confirm="handleMaterialConfirm"
	/>
</template>

<script setup lang="ts">
import { ref, reactive, computed, nextTick } from 'vue';
import { ElMessage } from 'yun-design';
import { Plus, Delete } from '@element-plus/icons-vue';
import { addObj, putObj } from '@/api/lowcode/base-my-supply/index';
import type { FormInstance, FormRules } from 'element-plus';
import MaterialSelector from './MaterialSelector.vue';

const emit = defineEmits(['getData']);

const visible = ref(false);
const loading = ref(false);
const isEdit = ref(false);
const vForm = ref<FormInstance>();
const materialSelectorRef = ref();

const initFormData = () => ({
	id: '',
	supplierCode: `SC${Date.now()}`,
	supplierName: '',
	abbreviation: '',
	unifiedSocialCreditCode: '',
	registeredCapital: '',
	legalRepresentative: '',
	postalCode: '',
	fax: '',
	annualRevenue: '',
	supplierType: '',
	supplierStatus: '合格供应商', // 默认值
	businessNature: '',
	supplierCategory: '',
	establishmentDate: '',
	serviceStartDate: '',
	serviceEndDate: '',
	companyProfile: '',
	businessScope: '',
	basicRemark: '',
	contactList: [],
	qualificationList: [],
	bankList: [],
	materialList: [],
	legalIdCard: '',
	legalPhone: '',
	registeredAddress: '',
});

const formData = reactive(initFormData());

const rules = reactive<FormRules>({
	supplierName: [{ required: true, message: '请输入供应商名称', trigger: 'blur' }],
	unifiedSocialCreditCode: [{ required: true, message: '请输入统一信用代码', trigger: 'blur' }],
	supplierType: [{ required: true, message: '请选择供应商类型', trigger: 'change' }],
	supplierStatus: [{ required: true, message: '请选择供应商状态', trigger: 'change' }],
	businessNature: [{ required: true, message: '请选择企业性质', trigger: 'change' }],
});

// 关闭处理
function closeHandler() {
	visible.value = false;
}

// 格式化表单数据
function formatFrom(row: any) {
	Object.assign(formData, initFormData());
	if (row && row.id) {
		Object.assign(formData, row);
	}
}

// 确认处理
async function confirmHandler(done: () => void, loadingRef: { value: boolean }) {
	try {
		await vForm.value?.validate();
		
		loadingRef.value = true;
		const api = isEdit.value ? putObj : addObj;
		await api(formData);
		ElMessage.success(isEdit.value ? '编辑成功' : '新增成功');
		done();
		emit('getData');
	} catch (error) {
		ElMessage.error('表单验证或提交失败');
	} finally {
		loadingRef.value = false;
	}
}

// 显示表单
const show = (row?: any) => {
	isEdit.value = !!row;
	formatFrom(row);
	visible.value = true;
	nextTick(() => {
		vForm.value?.clearValidate();
	});
};

// 添加表格行
function handleAddRow(tableName: string) {
	if (!(formData as any)[tableName]) {
		(formData as any)[tableName] = [];
	}
	
	const defaultRows: { [key: string]: any } = {
		contactList: { contactName: '', contactPhone: '', idCard: '', generateAccount: 'NO', contactRemark: '' },
		qualificationList: { certificateType: '', certificateName: '', certificateNumber: '', effectiveDate: '', expiryDate: '', qualificationRemark: '' },
		bankList: { bankName: '', bankAddress: '', bankCode: '', accountName: '', accountNumber: '', bankRemark: '' },
		materialList: { materialCode: '', materialName: '', specification: '', unit: '', materialRemark: '' },
	};
	
	(formData as any)[tableName].push(defaultRows[tableName] || {});
}

// 删除表格行
function handleRemoveRow(tableName: string, index: number) {
	if (!(formData as any)[tableName]) return;
	(formData as any)[tableName].splice(index, 1);
}

// 显示物料选择器
function showMaterialSelector() {
	materialSelectorRef.value?.show(formData.materialList || []);
}

// 处理物料选择确认
function handleMaterialConfirm(selectedMaterials: any[]) {
	formData.materialList = selectedMaterials.map(item => ({
		materialCode: item.materialCode,
		materialName: item.materialName,
		spec: item.spec,
		unit: item.unit,
		materialRemark: ''
	}));
}

defineExpose({
	show,
});
</script>

<style lang="scss">
.form-drawer {
	.el-form .el-form-item:last-of-type {
		margin-bottom: 24px !important;
	}
}
</style>

<style lang="scss" scoped>
:deep(.el-form-item) {
	margin-bottom: 18px !important;
}

.table-header {
	margin-bottom: 15px;
	margin-top: 30px;
	.label {
		font-weight: bold;
		font-size: 18px;
		padding-left: 8px;
		border-left: 4px solid var(--el-color-primary);
		line-height: 1.2;
	}
}

.table-header:first-of-type {
	margin-top: 0;
}

:deep(.el-form-item__label) {
	font-weight: normal;
}

:deep(.el-table) {
	.el-table__header-wrapper {
		th {
			background-color: var(--el-fill-color-light);
		}
	}
}
</style>
