<template>
	<yun-drawer
		v-model="visible"
		title="选择物料"
		size="65%"
		:before-close="handleClose"
	>
		<div class="material-selector-content">
			<div class="content-layout">
				<!-- 左侧物料分类树 -->
				<div class="category-tree-container">
					<div class="tree-header">
						<h3>物料分类</h3>
						<el-button
							size="small"
							@click="refreshCategoryTree"
							:icon="RefreshRight"
						>
							刷新
						</el-button>
					</div>
					<div class="tree-content">
						<!-- 全部选项 -->
						<div
							class="tree-all-option"
							:class="{ 'is-active': selectedCategoryCode === '' }"
							@click="handleShowAll"
						>
							<span>全部物料</span>
						</div>

						<el-tree
							ref="categoryTreeRef"
							:data="categoryTreeData"
							:props="treeProps"
							node-key="id"
							:default-expand-all="false"
							:expand-on-click-node="false"
							:highlight-current="true"
							@node-click="handleCategoryClick"
							class="category-tree"
						>
							<template #default="{ node, data }">
								<span class="tree-node">
									<span>{{ data.categoryName }}</span>
								</span>
							</template>
						</el-tree>
					</div>
				</div>

				<!-- 右侧物料信息表格 -->
				<div class="table-container">
					<yun-pro-table
						ref="proTableRef"
						v-model:pagination="pagination"
						v-model:filter-data="filterTableData"
						v-model:searchData="searchData"
						v-model:selected="selectedMaterials"
						:table-columns="columns"
						:search-fields="searchFields"
						:auto-height="true"
						:remote-method="remoteMethod"
						:table-props="tablePropsObj"
						:global-selection="true"
						layout="whole"
					>
					</yun-pro-table>
				</div>
			</div>
		</div>

		<template #footer>
			<div class="dialog-footer">
				<span class="selected-count">已选择 {{ selectedMaterials.length }} 项</span>
				<div>
					<el-button @click="handleClose">取消</el-button>
					<el-button
						type="primary"
						@click="handleConfirm"
						>确定</el-button
					>
				</div>
			</div>
		</template>
	</yun-drawer>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, watch } from 'vue';
import { RefreshRight } from '@yun-design/icons-vue';
import { useProTable } from '@ylz-use/core';
import moment from 'moment';
import { ElMessage } from 'yun-design';
import { fetchList, getMaterialCategoryTree } from '@/api/lowcode/base-material-info/index';

const emit = defineEmits(['confirm']);

const visible = ref(false);
const categoryTreeRef = ref();
const selectedMaterials = ref([]);
const existingMaterials = ref([]);

const searchData = reactive({});

// 物料分类树相关数据
const categoryTreeData = ref([]);
const selectedCategoryCode = ref('');
const treeProps = {
	children: 'children',
	label: 'categoryName',
	value: 'id',
};

const dateFieldKeys = computed(() => {
	return [];
});
const datetimeFieldKeys = computed(() => {
	return [];
});
const selectFieldKeys = computed(() => {
	return ['status'];
});

// 搜索字段配置
const searchFields = computed(() => [
	{
		prop: 'materialName',
		label: '物料名称',
		component: 'el-input',
		componentAttrs: {
			placeholder: '请选择物料名称',
			clearable: true,
		},
	},
	{
		prop: 'materialCode',
		label: '物料编码',
		component: 'el-input',
		componentAttrs: {
			placeholder: '请选择物料编码',
			clearable: true,
		},
	},
]);

// 表格列配置
const columns = computed(() => [
	{
		type: 'selection',
		width: 55,
		align: 'center',
	},
	{
		label: '序号',
		type: 'index',
		width: 60,
	},
	{
		label: '物料编码',
		prop: 'materialCode',
		minWidth: 120,
	},
	{
		label: '物料名称',
		prop: 'materialName',
		minWidth: 150,
	},
	{
		label: '规格/型号',
		prop: 'spec',
		minWidth: 120,
	},
	{
		label: '单位',
		prop: 'unit',
		width: 80,
	},
]);

const { pagination, remoteMethod, tableProps, proTableRef, filterTableData } = useProTable({
	apiFn: fetchList,
	paramsHandler(params: any) {
		Object.keys(params).forEach((key) => {
			if (dateFieldKeys.value.includes(key)) {
				if (params[key]?.length) {
					params[`${key}Start`] = moment(params[key][0]).startOf('day').format('YYYY-MM-DD');
					params[`${key}End`] = moment(params[key][1]).endOf('day').format('YYYY-MM-DD');
					delete params[key];
				}
			}
			if (datetimeFieldKeys.value.includes(key)) {
				if (params[key]?.length) {
					params[`${key}Start`] = moment(params[key][0]).format('YYYY-MM-DD HH:mm:ss');
					params[`${key}End`] = moment(params[key][1]).format('YYYY-MM-DD HH:mm:ss');
					delete params[key];
				}
			}
			if (selectFieldKeys.value.includes(key)) {
				if (Array.isArray(params[key])) {
					params[`${key}List`] = params[key];
					delete params[key];
				}
			}
		});

		// 添加物料分类过滤
		if (selectedCategoryCode.value) {
			params.materialCategoryId = selectedCategoryCode.value;
		}

		// 只查询启用状态的物料
		params.status = 'ENABLED';

		return {
			...params,
		};
	},
	querysHandler(query: any) {
		return {
			...query,
			current: pagination.value.page,
			size: pagination.value.size,
		};
	},
	responseHandler(result: any) {
		return result.data;
	},
	plugins: {
		config: {
			columns: columns.value,
			searchFields: searchFields.value,
		},
		list: ['SEARCH_PLUS'],
	},
});

const tablePropsObj = computed(() => {
	return {
		...tableProps,
		stripe: true,
		border: true,
		headerCellStyle: {
			backgroundColor: '#f5f7fa',
			color: '#303133',
			fontWeight: 'bold',
			height: '40px',
		},
		cellStyle: {
			padding: '0',
			height: '40px',
			'vertical-align': 'middle',
		},
		rowStyle: {
			height: '40px',
		},
		rowHeight: 40,
	};
});

// 获取物料分类树
async function getCategoryTree() {
	try {
		const response = await getMaterialCategoryTree();
		categoryTreeData.value = response.data || [];
	} catch (error) {
		console.error('获取物料分类树失败:', error);
		ElMessage.error('获取物料分类失败');
	}
}

// 刷新分类树
function refreshCategoryTree() {
	getCategoryTree();
}

// 显示全部物料
function handleShowAll() {
	selectedCategoryCode.value = '';
	// 清除树的选中状态
	categoryTreeRef.value?.setCurrentKey(null);
	// 重新加载表格数据
	proTableRef.value?.getData();
}

// 分类点击事件
function handleCategoryClick(data: any) {
	selectedCategoryCode.value = data.id;
	// 清除树的当前选中状态，因为我们有自定义的"全部"选项
	categoryTreeRef.value?.setCurrentKey(data.id);
	// 重新加载表格数据
	proTableRef.value?.getData();
}

// 显示选择器
function show(currentMaterials: any[] = []) {
	visible.value = true;
	existingMaterials.value = currentMaterials;

	// 设置已选中的物料
	setSelectedRows();

	// 初始化数据
	getCategoryTree();

	// 延迟获取表格数据，确保组件已渲染
	setTimeout(() => {
		proTableRef.value?.getData();
	}, 100);
}

// 关闭选择器
function handleClose() {
	visible.value = false;
	selectedMaterials.value = [];
	selectedCategoryCode.value = '';
}

// 确认选择
function handleConfirm() {
	emit('confirm', selectedMaterials.value);
	handleClose();
}

// 设置已选中的物料
function setSelectedRows() {
	if (!existingMaterials.value.length) return;

	// 使用 v-model:selected 直接设置选中的数据
	selectedMaterials.value = existingMaterials.value.slice();
}

onMounted(() => {
	getCategoryTree();
});

defineExpose({
	show,
});
</script>

<style lang="scss" scoped>
.material-selector-content {
	height: calc(100vh - 120px);
}

.content-layout {
	display: flex;
	height: 100%;
	gap: 16px;
}

.category-tree-container {
	width: 280px;
	min-width: 280px;
	background: #fff;
	border: 1px solid #e4e7ed;
	border-radius: 4px;
	display: flex;
	flex-direction: column;
}

.tree-header {
	padding: 16px;
	border-bottom: 1px solid #e4e7ed;
	display: flex;
	justify-content: space-between;
	align-items: center;

	h3 {
		margin: 0;
		font-size: 16px;
		font-weight: 600;
		color: #303133;
	}
}

.tree-content {
	flex: 1;
	padding: 8px;
	overflow-y: auto;
}

.tree-all-option {
	height: 36px;
	padding: 0 12px;
	display: flex;
	align-items: center;
	cursor: pointer;
	border-radius: 4px;
	margin-bottom: 8px;
	font-size: 14px;
	color: #606266;

	&:hover {
		background-color: #f5f7fa;
	}

	&.is-active {
		background-color: #e6f7ff;
		color: #1890ff;
		font-weight: 500;
	}
}

.category-tree {
	:deep(.el-tree-node__content) {
		height: 36px;

		&:hover {
			background-color: #f5f7fa;
		}
	}

	:deep(.el-tree-node.is-current > .el-tree-node__content) {
		background-color: #e6f7ff;
		color: #1890ff;
	}
}

.tree-node {
	display: flex;
	align-items: center;
	width: 100%;

	span {
		font-size: 14px;
	}
}

.table-container {
	flex: 1;
	min-width: 0;
}

.dialog-footer {
	display: flex;
	justify-content: space-between;
	align-items: center;

	.selected-count {
		color: #409eff;
		font-size: 14px;
	}
}
</style>
