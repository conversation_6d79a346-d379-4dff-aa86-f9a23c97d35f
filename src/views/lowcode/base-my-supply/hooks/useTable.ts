import { ref } from 'vue';

export function useTable() {
  const columns = ref([
    {
      label: '供应商编码',
      prop: 'supplierCode',
    },
    {
      label: '供应商名称',
      prop: 'supplierName',
    },
    {
      label: '统一信用代码',
      prop: 'unifiedSocialCreditCode',
      minWidth: '180px',
    },
    {
      label: '简称',
      prop: 'abbreviation',
    },
    {
      label: '注册资本',
      prop: 'registeredCapital',
    },
    {
      label: '成立日期',
      prop: 'establishmentDate',
    },
    {
      label: '法人代表',
      prop: 'legalRepresentative',
    },
    {
      label: '企业性质',
      prop: 'businessNature',
    },
    {
      label: '联系人',
      prop: 'contactPerson',
    },
    {
      label: '联系电话',
      prop: 'contactPhone',
    },
    {
      label: '供应商类型',
      prop: 'supplierType',
    },
    {
      label: '状态',
      prop: 'status',
      isSlot: true,
      enums: {
        ENABLED: { text: '启用', status: 'success' },
        DISABLED: { text: '禁用', status: 'danger' },
      },
    },
    {
      label: '供应商来源',
      prop: 'supplierSource',
    },
    {
      label: '操作',
      prop: 'action',
      isSlot: true,
      minWidth: '180px',
      fixed: 'right',
    },
  ]);

  const searchFields = ref([
    {
      label: '供应商名称',
      prop: 'supplierName',
      type: 'text',
    },
    {
      label: '供应商类型',
      prop: 'supplierType',
      type: 'select',
      options: [
        { label: '全部', value: '' },
        { label: '原材料', value: '原材料' },
        { label: '设备', value: '设备' },
        { label: '服务', value: '服务' },
      ],
    },
    {
      label: '企业性质',
      prop: 'businessNature',
      type: 'select',
      options: [
        { label: '全部', value: '' },
        { label: '私营企业', value: '私营企业' },
        { label: '国有企业', value: '国有企业' },
        { label: '合资企业', value: '合资企业' },
        { label: '外资企业', value: '外资企业' },
      ],
    },
    {
      label: '状态',
      prop: 'status',
      type: 'select',
      options: [
        { label: '全部', value: '' },
        { label: '启用', value: 'ENABLED' },
        { label: '禁用', value: 'DISABLED' },
      ],
    },
  ]);

  return {
    columns,
    searchFields,
  };
} 