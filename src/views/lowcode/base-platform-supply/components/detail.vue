<template>
  <yun-drawer
    v-model="visible"
    :show-cancel-button="false"
    destroy-on-close
    title="平台供应商详情"
    size="X-large"
    @confirm="handleConfirm"
  >
    <Detail :groups="processedGroups" :data="form"/>
  </yun-drawer>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import Detail from '@/components/Detail/index.vue';
import { getObj } from '@/api/lowcode/base-platform-supply/index';

interface FormData {
  [key: string]: any;
}

// 静态生成的分组配置
const processedGroups = [
  {
    title: "基础信息",
    columns: [
      {
        prop: 'supplierName',
        label: '供应商名称',
        type: 'text'
      },
      {
        prop: 'abbreviation',
        label: '供应商简称',
        type: 'text'
      },
      {
        prop: 'unifiedSocialCreditCode',
        label: '统一社会信用代码',
        type: 'text'
      },
      {
        prop: 'supplierType',
        label: '供应商类型',
        type: 'text'
      },
      {
        prop: 'supplierStatus',
        label: '供应商状态',
        type: 'text'
      },
      {
        prop: 'businessNature',
        label: '企业性质',
        type: 'text'
      },
      {
        prop: 'supplierCategory',
        label: '供应商分类',
        type: 'text'
      },
      {
        prop: 'registeredCapital',
        label: '注册资本',
        type: 'text'
      },
      {
        prop: 'establishmentDate',
        label: '成立日期',
        type: 'text'
      },
      {
        prop: 'legalRepresentative',
        label: '法人代表',
        type: 'text'
      },
      {
        prop: 'legalIdCard',
        label: '法人身份证号',
        type: 'text'
      },
      {
        prop: 'legalPhone',
        label: '法人电话',
        type: 'text'
      },
      {
        prop: 'serviceStartDate',
        label: '服务开始时间',
        type: 'text'
      },
      {
        prop: 'serviceEndDate',
        label: '服务结束时间',
        type: 'text'
      },
      {
        prop: 'registeredAddress',
        label: '注册地址',
        type: 'text'
      },
      {
        prop: 'postalCode',
        label: '邮政编码',
        type: 'text'
      },
      {
        prop: 'fax',
        label: '传真',
        type: 'text'
      },
      {
        prop: 'annualRevenue',
        label: '年平均营业额',
        type: 'text'
      },
      {
        prop: 'companyProfile',
        label: '公司简介',
        type: 'textarea'
      },
      {
        prop: 'businessScope',
        label: '经营范围',
        type: 'textarea'
      },
      {
        prop: 'basicRemark',
        label: '备注',
        type: 'textarea'
      }
    ]
  },
  {
    title: '联系人信息',
    columns: [
      {
        prop: 'contactList',
        label: '',
        type: 'table',
        attrs: {
          showIndex: true,
          columns: [
            {
              prop: 'contactName',
              label: '姓名',
              type: 'text'
            },
            {
              prop: 'contactPhone',
              label: '电话',
              type: 'text'
            },
            {
              prop: 'idCard',
              label: '身份证',
              type: 'text'
            },
            {
              prop: 'generateAccount',
              label: '是否生成登录账号',
              type: 'enums',
              attrs: {
                options: [
                  { label: '是', value: 'YES' },
                  { label: '否', value: 'NO' }
                ]
              }
            },
            {
              prop: 'contactRemark',
              label: '备注',
              type: 'text'
            }
          ]
        }
      }
    ]
  },
  {
    title: '资质信息',
    columns: [
      {
        prop: 'qualificationList',
        label: '',
        type: 'table',
        attrs: {
          showIndex: true,
          columns: [
            {
              prop: 'certificateType',
              label: '证书类型',
              type: 'text'
            },
            {
              prop: 'certificateName',
              label: '证书名称',
              type: 'text'
            },
            {
              prop: 'certificateNumber',
              label: '证书号码',
              type: 'text'
            },
            {
              prop: 'effectiveDate',
              label: '生效日期',
              type: 'text'
            },
            {
              prop: 'expiryDate',
              label: '失效日期',
              type: 'text'
            },
            {
              prop: 'qualificationRemark',
              label: '备注',
              type: 'text'
            }
          ]
        }
      }
    ]
  },
  {
    title: '银行信息',
    columns: [
      {
        prop: 'bankList',
        label: '',
        type: 'table',
        attrs: {
          showIndex: true,
          columns: [
            {
              prop: 'bankName',
              label: '开户行',
              type: 'text'
            },
            {
              prop: 'bankAddress',
              label: '开户行地址',
              type: 'text'
            },
            {
              prop: 'bankCode',
              label: '联行号',
              type: 'text'
            },
            {
              prop: 'accountName',
              label: '开户名',
              type: 'text'
            },
            {
              prop: 'accountNumber',
              label: '银行账号',
              type: 'text'
            },
            {
              prop: 'bankRemark',
              label: '备注',
              type: 'text'
            }
          ]
        }
      }
    ]
  },
  {
    title: '主营物料',
    columns: [
      {
        prop: 'materialList',
        label: '',
        type: 'table',
        attrs: {
          showIndex: true,
          columns: [
            {
              prop: 'materialCode',
              label: '物料编码',
              type: 'text'
            },
            {
              prop: 'materialName',
              label: '物料名称',
              type: 'text'
            },
            {
              prop: 'spec',
              label: '规格型号',
              type: 'text'
            },
            {
              prop: 'unit',
              label: '单位',
              type: 'text'
            },
            {
              prop: 'materialRemark',
              label: '备注',
              type: 'text'
            }
          ]
        }
      }
    ]
  }
];

const visible = ref(false);
const form = ref<FormData>({});

async function handleConfirm(done: () => void) {
  done();
}

async function show(row: FormData) {
  const res: any = await getObj(row.id);
  form.value = res.data;
  visible.value = true;
}

defineExpose({
  show,
});
</script>

<style scoped lang="scss">
</style> 