import { ref, computed } from 'vue';
import moment from 'moment';
import { useDict } from '@/hooks/dict';
import { ElImage } from 'element-plus';
import { Session } from '/@/utils/storage';
import { STATUS_LIST } from '@/views/lowcode/base-agent-org/hooks/const';

const getDict = computed(() => (str) => {
  if (!str) return [];
  const value = useDict(str)?.[str]?.value;
  return value;
});
function fetchDistrictOptions(options) {
  let requestUrl = 'https://oss-public.yunlizhi.cn/frontend/yun-design/area.json';
  const method = 'GET';

  // 构建请求参数
  const requestParams = {

  };

  const token = Session.getToken();
  const tenantId = Session.getTenant();
  // 构建请求配置
  const requestConfig = {
    method,
    headers: {
      'Content-Type': 'application/json',
      'TENANT-ID': tenantId,
      AUTHORIZATION: `Bearer ${token}`,
    },
  };

  // 如果是GET请求，将参数附加到URL
  if (method === 'GET') {
    const queryParams = new URLSearchParams();
    Object.entries(requestParams).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        queryParams.append(key, String(value));
      }
    });
    const queryString = queryParams.toString();
    if (queryString) {
      requestUrl += (requestUrl.includes('?') ? '&' : '?') + queryString;
    }
  } else {
    // 非GET请求，将参数放在body中
    requestConfig.body = JSON.stringify(requestParams);
  }

  // 发送请求并处理响应
  fetch(requestUrl, requestConfig)
    .then(response => {
      if (!response.ok) {
        throw new Error('API request failed');
      }
      return response.json();
    })
    .then(data => {
      // 根据配置的路径获取数据
      let result = data;


      // 处理数据
      const processData = (items) => {
        return items.map(item => ({
          label: item['areaName'],
          value: item['areaCode'],

          children: item['children'] ?
            processData(item['children']) : undefined
        }));
      };

      options.value = Array.isArray(result) ? processData(result) : [];
    })
    .catch(error => {
      console.error('Failed to fetch API options:', error);
      options.value = [];
    });
}
export const useTable = () => {
  const districtOptions = ref([]);
  fetchDistrictOptions(districtOptions);

  const searchFields = computed(() => [
    // {
    //   prop: 'agentCode',
    //   label: '代理机构编码',
    //   component: 'el-input',
    //   componentAttrs: {
    //     placeholder: '请选择代理机构编码',
    //     clearable: true,
    //   }
    // },
    {
      prop: 'agentName',
      label: '代理机构名称',
      component: 'el-input',
      componentAttrs: {
        placeholder: '请选择代理机构名称',
        clearable: true,
      }
    },
    {
      prop: 'socialCreditCode',
      label: '统一信用代码',
      component: 'el-input',
      componentAttrs: {
        placeholder: '请输入统一信用代码',
        clearable: true,
      }
    },
    {
      label: '状态',
      prop: 'status',
      component: 'el-select',
      enums: STATUS_LIST,
      componentAttrs: {
        placeholder: '请选择状态',
        clearable: true,
        filterable: true,
        multiple: true,
        collapseTags: true,
      },
    },
  ]);

  const columns = computed(() => [
    {
      label: '序号',
      type: 'index',
    },
    {
      label: '代理机构编码',
      prop: 'agentCode',
    },
    {
      label: '代理机构名称',
      prop: 'agentName',
    },
    {
      label: '统一信用代码',
      prop: 'socialCreditCode',
    },
    {
      label: '公司所在地区',
      prop: 'district',
			formatter(row){
        const value = row['district'];
        const options = districtOptions.value;
        if (!value) return '--';
				const findPath = (options, targetValue, path = []) => {
          for (const option of options) {
            // 如果找到目标值，返回完整路径
            if (option.value == targetValue) {
              return [...path, option.label];
            }
            // 如果有子节点，递归查找
            if (option.children && option.children.length > 0) {
              const result = findPath(option.children, targetValue, [...path, option.label]);
              if (result) return result;
            }
          }
          return null;
        };

        // 查找完整路径
        const path = findPath(options, value);
        row.districtName = path ? path.join(' / ') : '--';
        return path ? path.join(' / ') : '--';
			}
    },
    {
      label: '详细地址',
      prop: 'address',
    },
    {
      label: '状态',
      prop: 'status',
      enums: STATUS_LIST
    },
    {
      label: '创建人',
      prop: 'createBy',
    },
    {
      label: '创建时间',
      prop: 'createTime',
      formatter(row) {
        return row.createTime?.split(' ')[0];
      }
    },
    {
      label: '修改人',
      prop: 'updateBy',
    },
    {
      label: '修改时间',
      prop: 'updateTime',
      formatter(row) {
        return row.updateTime?.split(' ')[0];
      }
    },
    {
      prop: "action",
      fixed: "right",
      width: 150,
      label: "操作",
    },
  ])
  return {
    columns,
    searchFields,
  };
};
