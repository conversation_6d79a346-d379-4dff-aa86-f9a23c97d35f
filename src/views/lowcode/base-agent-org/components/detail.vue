<template>
  <yun-drawer
    v-model="visible"
    :show-cancel-button="false"
    destroy-on-close
    title="详情"
    size="X-large"
    @confirm="handleConfirm"
  >
    <Detail :groups="processedGroups" :data="form"/>
  </yun-drawer>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import Detail from '@/components/Detail/index.vue';
import { getObj } from '@/api/lowcode/base-agent-org/index';
import { STATUS_LIST } from '@/views/lowcode/base-agent-org/hooks/const';
import { Session } from '@/utils/storage';

interface FormData {
  [key: string]: any;
}

function fetchDistrictOptions(options) {
	let requestUrl = 'https://oss-public.yunlizhi.cn/frontend/yun-design/area.json';
	const method = 'GET';

	// 构建请求参数
	const requestParams = {

	};

	const token = Session.getToken();
	const tenantId = Session.getTenant();
	// 构建请求配置
	const requestConfig = {
		method,
		headers: {
			'Content-Type': 'application/json',
			'TENANT-ID': tenantId,
			AUTHORIZATION: `Bearer ${token}`,
		},
	};

	// 如果是GET请求，将参数附加到URL
	if (method === 'GET') {
		const queryParams = new URLSearchParams();
		Object.entries(requestParams).forEach(([key, value]) => {
			if (value !== undefined && value !== null) {
				queryParams.append(key, String(value));
			}
		});
		const queryString = queryParams.toString();
		if (queryString) {
			requestUrl += (requestUrl.includes('?') ? '&' : '?') + queryString;
		}
	} else {
		// 非GET请求，将参数放在body中
		requestConfig.body = JSON.stringify(requestParams);
	}

	// 发送请求并处理响应
	fetch(requestUrl, requestConfig)
		.then(response => {
			if (!response.ok) {
				throw new Error('API request failed');
			}
			return response.json();
		})
		.then(data => {
			// 根据配置的路径获取数据
			let result = data;


			// 处理数据
			const processData = (items) => {
				return items.map(item => ({
					label: item['areaName'],
					value: item['areaCode'],

					children: item['children'] ?
						processData(item['children']) : undefined
				}));
			};

			options.value = Array.isArray(result) ? processData(result) : [];
		})
		.catch(error => {
			console.error('Failed to fetch API options:', error);
			options.value = [];
		});
}

const districtOptions = ref([]);
fetchDistrictOptions(districtOptions);

// 静态生成的分组配置
const processedGroups = [
  {
    title: "",
    columns: [
      {
        prop: 'agentCode',
        label: '代理机构编码',
        type: 'text'
      },
      {
        prop: 'agentName',
        label: '代理机构名称',
        type: 'text'
      },
      {
        prop: 'socialCreditCode',
        label: '统一信用代码',
        type: 'text'
      },
      {
        prop: 'district',
        label: '公司所在地区',
        type: 'text',
      },
      {
        prop: 'address',
        label: '详细地址',
        type: 'text'
      },
      {
        prop: 'status',
        label: '状态',
        type: 'enums',
        attrs: {
          options: STATUS_LIST,
        }
      }
    ]
  },
  {
    title: '账号信息',
    columns: [
      {
        prop: 'baseAgentUser',
        label: '',
        type: 'table',
        attrs: {
          showIndex: true,
          columns: [
            {
              prop: 'userName',
              label: '姓名',
              type: 'text'
            },
            {
              prop: 'contactPhone',
              label: '联系电话',
              type: 'text'
            },
            {
              prop: 'role',
              label: '角色',
              type: 'text'
            },
            {
              prop: 'loginAccount',
              label: '登录账号',
              type: 'text'
            }
          ]
        }
      }
    ]
  }
];

const visible = ref(false);
const form = ref<FormData>({});

async function handleConfirm(done: () => void) {
  done();
}

async function show(row: FormData) {
  const res = await getObj(row.id);
  form.value = res.data;
  form.value.district = row.districtName;
  form.value.baseAgentUserList.forEach(i => i.role = '招标代理');
  visible.value = true;
}

defineExpose({
  show,
});
</script>

<style scoped lang="scss">
</style>
