<template>
  <yun-drawer
    v-model="visible"
    destroy-on-close
    modal
    custom-class="form-drawer"
    close-on-click-modal
    confirm-button-text="确定"
    cancel-button-text="取消"
    :confirm-button-disabled="loading"
    :title="title"
    :size="size"
    @confirm="confirmHandler"
    @close="closeHandler"
  >
    <el-form
      ref="vForm"
      :model="formData"
      :rules="rules"
      label-width="120"
      label-position="top"
    >
      <el-row :gutter="12">
        <el-col :span="8">
          <el-form-item label="代理机构名称" prop="agentName">
            <el-input
              v-model="formData.agentName"
              type="text"
              clearable
              maxlength="30"
              show-word-limit
              placeholder="请输入代理机构名称"
            />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="统一信用代码" prop="socialCreditCode">
            <el-input
              v-model="formData.socialCreditCode"
              type="text"
              clearable
              placeholder="请输入统一信用代码"
            />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="公司所在地区" prop="district">
            <el-cascader
              ref="cascader"
              v-model="formData.district"
              style="width: 100%;"
              clearable
              showAllLevels
              :options="districtOptions"
              :props="{
                multiple: false,
                value: 'value',
                label: 'label',
                children: 'children',
                checkStrictly: false,
                emitPath: false,
                expandTrigger: 'hover'
              }"
              placeholder="请选择公司所在地区"
            />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="详细地址" prop="address">
            <el-input
              v-model="formData.address"
              type="text"
              clearable
              maxlength="50"
              show-word-limit
              placeholder="请输入详细地址"
            />
          </el-form-item>
        </el-col>

        <el-col :span="8">
          <el-form-item
            label="部门"
            prop="deptTypeId"
          >
            <el-select
              v-model="formData.deptTypeId"
              filterable
              style="width: 100%"
              clearable
            >
              <el-option
                v-for="option in outsideDeptList"
                :key="option.id"
                :label="option.name"
                :value="option.id"
              >
                <span>{{ option.name || '-' }}</span>
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>

<!--        <el-col :span="8">-->
<!--          <el-form-item label="状态" prop="status">-->
<!--            <el-select v-model="formData.status" style="width: 100%;" clearable placeholder="请选择状态">-->
<!--                <el-option-->
<!--                  v-for="option in getDict('enable_status')"-->
<!--                  :key="option.value"-->
<!--                  :label="option.label"-->
<!--                  :value="option.value"-->
<!--                />-->
<!--            </el-select>-->
<!--          </el-form-item>-->
<!--        </el-col>-->
      </el-row>
      <div class="table-header">
        <div class="label">账号信息</div>
      </div>
      <div class="w-full overflow-x-auto">
        <el-table
          :data="formData.baseAgentUserList || []"
          :border="true"
          stripe
          class="w-full dark:bg-gray-800"
          :header-cell-class-name="'!bg-gray-50 dark:!bg-gray-700'"
          style="min-width: 600px"
          :show-overflow-tooltip="false"
        >
          <el-table-column
            type="index"
            label="序号"
            width="60"
            align="center"
            fixed
          />
          <el-table-column
            label="姓名"
            prop="userName"
            min-width="150px"
          >
            <template #header>
              <span class="required-label">姓名</span>
            </template>
            <template #default="{ row, $index }">
              <el-form
                :model="formData"
                :rules="rules"
                :ref="(el) => addTableForm(el)"
                class="table-form"
              >
                <el-form-item
                  :prop="'baseAgentUserList.' + $index + '.userName'"
                  :rules="[
                    { required: true, message: '请输入姓名', trigger: 'blur' }
                  ]"
                >
                  <el-input
                    v-model="row.userName"
                    type="text"
                    clearable
                    maxlength="10"
                    show-word-limit
                    placeholder="请输入姓名"
                  />
                </el-form-item>
              </el-form>
            </template>
          </el-table-column>
          <el-table-column
            label="联系电话"
            prop="contactPhone"
            min-width="180px"
          >
            <template #header>
              <span class="required-label">联系电话</span>
            </template>
            <template #default="{ row, $index }">
              <el-form
                :model="formData"
                :rules="rules"
                :ref="(el) => addTableForm(el)"
                class="table-form"
              >
                <el-form-item
                  :prop="'baseAgentUserList.' + $index + '.contactPhone'"
                  :rules="[
                    { required: true, message: '请输入联系电话', trigger: 'blur' }
                  ]"
                >
                  <el-input
                    v-model="row.contactPhone"
                    type="text"
                    clearable
                    placeholder="请输入联系电话"
                    @input="handlePhoneChange(row)"
                  />
                </el-form-item>
              </el-form>
            </template>
          </el-table-column>
          <el-table-column
            label="角色"
            prop="loginAccount"
            min-width="180px"
          >
            <span>招标代理</span>
          </el-table-column>
          <el-table-column
            label="登录账号"
            prop="loginAccount"
            min-width="180px"
          >
            <template #default="{ row }">
              <el-input
                v-model="row.loginAccount"
                type="text"
                clearable
                placeholder="自动带入联系电话"
                :disabled='row.disabled'
              />
            </template>
          </el-table-column>
          <el-table-column
            label="密码"
            prop="password"
            min-width="120px"
          >
            <template #default="{ row }">
              <el-input
                v-model="row.password"
                type="password"
                clearable
                placeholder="默认123456"
                show-password
              />
            </template>
          </el-table-column>
          <el-table-column
            label="操作"
            fixed="right"
            width="100"
            align="center"
          >
            <template #default="{ $index }">
              <el-button
                type="danger"
                size="small"
                icon="Delete"
                @click="handleRemoveRow('baseAgentUserList', $index)"
              >删除</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <el-button
        style="width: 100%; margin-top: 5px"
        :icon="Plus"
        @click="handleAddRow('baseAgentUserList')"
      >
        添加
      </el-button>
    </el-form>
  </yun-drawer>
</template>

<script setup lang="ts">
import { ref, reactive, computed, nextTick } from 'vue';
import { ElMessage } from 'yun-design';
import { Plus, Delete } from '@element-plus/icons-vue';
import { useDict } from '@/hooks/dict';
import { addObj, putObj, getObj } from '@/api/lowcode/base-agent-org/index';
import { useUserInfo } from '@/stores/userInfo';
import { rule } from '@/utils/validate';
import { Session } from '@/utils/storage';
import moment from 'moment';
import { deptTree } from '/@/api/admin/dept';

// 状态管理
const visible = ref(false);
const loading = ref(false);
const isEdit = ref(false);
const vForm = ref();
const formData = reactive({});
const tableForms = ref([]);
const cascader = ref();

// 字符长度校验函数
const validateMaxLength = (maxLength: number, fieldName: string) => {
  return (rule: any, value: any, callback: any) => {
    if (!value) {
      return callback();
    }
    if (value.length > maxLength) {
      return callback(new Error(`${fieldName}最多输入${maxLength}个字符`));
    }
    callback();
  };
};

// 统一信用代码校验
const validateSocialCreditCode = (rule: any, value: any, callback: any) => {
  if (!value) {
    return callback(new Error('请输入统一信用代码'));
  }
  // 统一信用代码格式：18位，第1位为数字或大写字母，第2-9位为数字或大写字母，第10-17位为数字或大写字母，第18位为数字或大写字母
  const socialCreditCodeRegex = /^[0-9A-HJ-NPQRTUWXY]{2}\d{6}[0-9A-HJ-NPQRTUWXY]{10}$/;
  if (!socialCreditCodeRegex.test(value)) {
    return callback(new Error('统一信用代码格式不正确'));
  }
  callback();
};

const rules = reactive({
  agentName: [
    {
      required: true,
      message: '请输入代理机构名称',
      trigger: ['blur', 'change']
    },
    {
      validator: validateMaxLength(30, '代理机构名称'),
      trigger: ['blur', 'change']
    }
  ],
  socialCreditCode: [
    {
      required: true,
      message: '请输入统一信用代码',
      trigger: ['blur']
    },
    {
      validator: validateSocialCreditCode,
      trigger: ['blur', 'change']
    }
  ],
	district: [
    {
			required: true,
			message: '请选择公司所在地区',
			trigger: ['blur', 'change']
    }
  ],
  address: [
		{
			required: true,
			message: '请输入详细地址',
			trigger: ['blur', 'change']
		},
    {
      validator: validateMaxLength(50, '详细地址'),
      trigger: ['blur', 'change']
    },
    {
      required: true,
      message: '请输入详细地址',
      trigger: ['blur']
    }
  ],
  district: [
    {
      required: true,
      message: '请选择公司所在地区',
      trigger: ['change']
    }
  ],
  deptTypeId: [
    {
      required: true,
      message: '此项必填',
      trigger: ['blur', 'change'],
    },
  ],
});

const outsideDeptList = ref([]);

// 计算属性
const title = computed(() => (isEdit.value ? '编辑' : '新增'));
const size = computed(() => 'X-large');

// 用户信息
const userStore = useUserInfo();
const emits = defineEmits(['getData']);

// 为每个API组件创建响应式变量
const districtOptions = ref([]);

// 为每个API组件创建获取数据的方法
function fetchDistrictOptions(options, query, formData) {
  let requestUrl = 'https://oss-public.yunlizhi.cn/frontend/yun-design/area.json';
  const method = 'GET';

  // 构建请求参数
  const requestParams = {

  };

  const token = Session.getToken();
  const tenantId = Session.getTenant();
  // 构建请求配置
  const requestConfig = {
    method,
    headers: {
      'Content-Type': 'application/json',
      'TENANT-ID': tenantId,
      AUTHORIZATION: `Bearer ${token}`,
    },
  };

  // 如果是GET请求，将参数附加到URL
  if (method === 'GET') {
    const queryParams = new URLSearchParams();
    Object.entries(requestParams).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        queryParams.append(key, String(value));
      }
    });
    const queryString = queryParams.toString();
    if (queryString) {
      requestUrl += (requestUrl.includes('?') ? '&' : '?') + queryString;
    }
  } else {
    // 非GET请求，将参数放在body中
    requestConfig.body = JSON.stringify(requestParams);
  }

  // 发送请求并处理响应
  fetch(requestUrl, requestConfig)
    .then(response => {
      if (!response.ok) {
        throw new Error('API request failed');
      }
      return response.json();
    })
    .then(data => {
      // 根据配置的路径获取数据
      let result = data;


      // 处理数据
      const processData = (items) => {
        return items.map(item => ({
          label: item['areaName'],
          value: item['areaCode'], // 使用中文名称作为value

          children: item['children'] ?
            processData(item['children']) : undefined
        }));
      };

      options.value = Array.isArray(result) ? processData(result) : [];
    })
    .catch(error => {
      console.error('Failed to fetch API options:', error);
      options.value = [];
    });
}

async function getDeptList(name: string = '') {
  const res = await deptTree({ deptType: 'EXTERNAL' });
  outsideDeptList.value = res.data;
}

getDeptList();

// 初始化时获取所有API数据
fetchDistrictOptions(districtOptions);

// 关闭处理
function closeHandler() {
  visible.value = false;
}

// 格式化表单数据
async function formatFrom(row: Record<string, any>) {
  for(let key in formData) {
    delete formData[key];
  }
  if (!row.id) return;
  const res = await getObj(row.id);
  res.data.baseAgentUserList.forEach(i => i.disabled = true);
  Object.assign(formData, res.data);
	if (Array.isArray(row.district)) {
		formData.district = row.district.map((item) => {
			if (Array.isArray(item)) {
				return item;
			}
			return item.split(',');
		});
	}

  // // 处理地区数据回显
  // if (res.data.province && res.data.city && res.data.district) {
  //   formData.regionPath = [res.data.province, res.data.city, res.data.district];
  // } else if (res.data.province && res.data.city) {
  //   formData.regionPath = [res.data.province, res.data.city];
  // } else if (res.data.province) {
  //   formData.regionPath = [res.data.province];
  // }
}

// 格式化提交参数
function formatParams() {
  const baseParams = {};

	// 处理特殊字段类型
	if (Array.isArray(formData.district)) {
		baseParams.district = formData.district.map((item) => {
			if (Array.isArray(item)) {
				return item.join(',');
			}
			return item;
		});
	}

	return {
		...formData,
		...baseParams,
	};
}

// 确认处理
const addTableForm = (form) => {
  if (form) {
    tableForms.value.push(form);
  }
};

const confirmHandler = async () => {
  try {
    // 验证所有表格中的表单
    const formPromises = tableForms.value.map(form => {
      return new Promise((resolve, reject) => {
        form.validate((valid) => {
          if (valid) {
            resolve(true);
          } else {
            reject(new Error('表单验证失败'));
          }
        });
      });
    });

    // 等待所有表单验证完成
    await Promise.all(formPromises);

    // 原有的提交逻辑
    loading.value = true;
    if (isEdit.value) {
      await putObj(formData);
    } else {
      await addObj(formData);
    }
    ElMessage.success('保存成功');
    visible.value = false;
    emits('getData', formData);
  } catch (error) {
    if (error.message === '表单验证失败') {
      ElMessage.error('请填写必填项');
    } else {
      console.error('保存失败:', error);
    }
  } finally {
    loading.value = false;
  }
};

// 显示表单
function show(row?: Record<string, any>) {
  visible.value = true;
  isEdit.value = !!row;
  formatFrom(row || {});
  nextTick(() => {
    vForm.value?.clearValidate();
  });
}

// 日期时间范围默认值
const defaultTime = [new Date(2000, 1, 1, 0, 0, 0), new Date(2000, 2, 1, 23, 59, 59)];

// 字典数据
const getDict = computed(() => (str: string) => {
  if (!str) return [];
  const value = useDict(str)?.[str]?.value;
  return value;
});

// 生成明细表新行的初始数据
function getEnhancedTableRow(widget: Widget): Record<string, any> {
  const row: Record<string, any> = {};
  if (Array.isArray(widget.widgetList)) {
    for (const colWidget of widget.widgetList) {
      const name = colWidget.options?.name;
      if (!name) continue;
      let value = colWidget.options?.defaultValue;
      if (value === undefined) {
        if (colWidget.type === 'number') value = 0;
        else value = '';
      }
      row[name] = value;
    }
  }
  return row;
}

// 添加明细表行
function handleAddRow(tableName: string) {
  if (!formData[tableName]) {
    formData[tableName] = [];
  }

  formData[tableName].push({
    userName: '',
    contactPhone: '',
    loginAccount: '',
    password: '123456' // 默认密码
  });
}

// 处理地区选择变化
// function handleRegionChange(value: string[]) {
//   if (value && value.length >= 3) {
//     formData.province = value[0];
//     formData.city = value[1];
//     formData.district = value[2];
//   } else if (value && value.length === 2) {
//     formData.province = value[0];
//     formData.city = value[1];
//     formData.district = '';
//   } else if (value && value.length === 1) {
//     formData.province = value[0];
//     formData.city = '';
//     formData.district = '';
//   } else {
//     formData.province = '';
//     formData.city = '';
//     formData.district = '';
//   }
// }

// 处理联系电话变化，自动带入登录账号
function handlePhoneChange(row: any) {
  if (row.contactPhone) {
    // 验证手机号格式
    const phoneRegex = /^1[3-9]\d{9}$/;
    if (phoneRegex.test(row.contactPhone) && !row.disabled) {
      row.loginAccount = row.contactPhone;
    }
  }
}

// 删除明细表行
function handleRemoveRow(tableName: string, index: number) {
  if (!formData[tableName]) return;
  formData[tableName].splice(index, 1);
}

// 暴露方法
defineExpose({
  show,
  formData,
  rules
});
</script>

<style lang="scss">
.form-drawer {
  .el-form .el-form-item:last-of-type {
    margin-bottom: 24px !important;
  }
  .el-table {
    .el-form .el-form-item:last-of-type {
      margin-bottom: 0 !important;
    }
  }
}
</style>

<style lang="scss" scoped>
.el-form-item {
  margin-bottom: 18px;
}

.table-header {
  margin-bottom: 15px;
  .label {
    font-weight: bold;
    font-size: 18px;
    padding-left: 8px;
    border-left: 4px solid var(--el-color-primary);
    line-height: 1.2;
  }
}

:deep(.el-form-item__label) {
  font-weight: normal;
}

:deep(.el-table) {
  .el-table__header-wrapper {
    th {
      background-color: var(--el-fill-color-light);
    }
  }
}

.required-label {
  &::before {
    content: '*';
    color: var(--el-color-danger);
    margin-right: 4px;
  }
}

.is-error {
  :deep(.el-input__wrapper) {
    box-shadow: 0 0 0 1px var(--el-color-danger) inset;
  }
}

.table-form {
  :deep(.el-form-item) {
    margin-bottom: 0;
  }
  :deep(.el-form-item__error) {
    position: absolute;
    top: 100%;
    left: 0;
    padding-top: 2px;
  }
}
</style>
