<template>
  <yun-drawer
    v-model="visible"
    :show-cancel-button="false"
    destroy-on-close
    title="详情"
    size="X-large"
    @confirm="handleConfirm"
  >
    <Detail :groups="processedGroups" :data="form" v-if="visible"/>
  </yun-drawer>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import Detail from '@/components/Detail/index.vue';
import { getObj } from '@/api/lowcode/base-payment-method/index';
import { cloneDeep } from 'lodash';

interface FormData {
  [key: string]: any;
}

// 静态生成的分组配置
let processedGroups = ref([]);
let processedGroupsPeriod = [
  {
    title: "",
    columns: [
      {
        prop: 'methodName',
        label: '支付方式名称',
        type: 'text'
      },
      {
        prop: 'period',
        label: '是否有账期',
        type: 'text',
      },
      {
        prop: 'status',
        label: '状态',
        type: 'text',
      }
    ]
  },
  {
    title: '账期时间',
    columns: [
      {
        prop: 'basePaymentMethodPeriod',
        label: '',
        type: 'table',
        attrs: {
          showIndex: true,
          columns: [
            {
              prop: 'periodName',
              label: '账期名称',
              type: 'text'
            },
            {
              prop: 'status',
              label: '状态',
              type: 'text',
            }
          ]
        }
      }
    ]
  }
];
let processedGroupsNoPeriod = [
  {
    title: "",
    columns: [
      {
        prop: 'methodName',
        label: '支付方式名称',
        type: 'text'
      },
      {
        prop: 'period',
        label: '是否有账期',
        type: 'text',
      },
      {
        prop: 'status',
        label: '状态',
        type: 'text',
      }
    ]
  }
];

const visible = ref(false);
const form = ref<FormData>({});

async function handleConfirm(done: () => void) {
  done();
}

async function show(row: FormData) {
  const { data } = await getObj(row.id);
  form.value = cloneDeep(data);
  form.value.period = form.value.period === 'YES' ? '是' : '否';
  form.value.status = form.value.status === 'ENABLED' ? '启用' : '禁用';
  form.value.basePaymentMethodPeriodList?.forEach(i => {
    i.status = i.status === 'ENABLED' ? '启用' : '禁用';
  })
  if(data.period === 'YES') {
    processedGroups.value = processedGroupsPeriod;
  } else {
    processedGroups.value = processedGroupsNoPeriod;
  }
  visible.value = true;
}

defineExpose({
  show,
});
</script>

<style scoped lang="scss">
</style>