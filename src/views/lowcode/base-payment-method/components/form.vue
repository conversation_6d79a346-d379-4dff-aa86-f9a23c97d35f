<template>
	<yun-drawer
		v-model="visible"
		destroy-on-close
		modal
		custom-class="form-drawer"
		close-on-click-modal
		confirm-button-text="确定"
		cancel-button-text="取消"
		:confirm-button-disabled="loading"
		:title="title"
		:size="size"
		@confirm="confirmHandler"
		@close="closeHandler"
	>
		<el-form
			ref="vForm"
			:model="formData"
			:rules="rules"
			label-width="120"
			label-position="top"
		>
			<el-row :gutter="12">
				<!-- <el-col :span="8">
          <el-form-item label="支付方式编码" prop="methodCode">
            <el-input v-model="formData.methodCode" type="text" clearable icon="custom-search" />
          </el-form-item>
        </el-col> -->
				<el-col :span="8">
					<el-form-item
						label="支付方式"
						prop="methodName"
					>
						<el-input
							v-model="formData.methodName"
							placeholder="请输入支付方式"
							type="text"
							clearable
							icon="custom-search"
						/>
					</el-form-item>
				</el-col>
				<!-- <el-col :span="8">
          <el-form-item label="应用业务类型" prop="applicationType">
            <el-input v-model="formData.applicationType" type="text" clearable icon="custom-search" />
          </el-form-item>
        </el-col> -->
				<el-col :span="8">
					<el-form-item
						label="是否有账期"
						prop="period"
					>
						<el-radio-group v-model="formData.period">
							<el-radio
								v-for="option in periodTypes"
								:key="option.value"
								:label="option.value"
							>
								{{ option.label }}
							</el-radio>
						</el-radio-group>
					</el-form-item>
				</el-col>
				<!-- <el-col :span="8">
          <el-form-item label="状态" prop="status">
            <el-select v-model="formData.status" style="width: 100%;" clearable :multipleLimit="0">
                <el-option
                  v-for="option in statusTypes"
                  :key="option.value"
                  :label="option.label"
                  :value="option.value"
                />
            </el-select>
          </el-form-item>
        </el-col> -->
			</el-row>
			<div class="table-header" v-if="formData.period === 'YES'">
				<div class="label">账期时间</div>
			</div>
			<div class="w-full overflow-x-auto" v-if="formData.period === 'YES'">
				<el-table
					:data="formData.basePaymentMethodPeriodList || []"
					:border="true"
					stripe
					class="w-full dark:bg-gray-800"
					:header-cell-class-name="'!bg-gray-50 dark:!bg-gray-700'"
					style="min-width: 600px"
					:show-overflow-tooltip="false"
				>
					<el-table-column
						type="index"
						label="序号"
						width="60"
						align="center"
						fixed
					/>
					<!-- <el-table-column
						label="账期天数"
						prop="periodDays"
						min-width="200px"
					>
						<template #default="{ row }">
							<el-input-number
								v-model="row.periodDays"
								:defaultValue="0"
								:min="-100000000000"
								:max="100000000000"
								:precision="0"
								:step="1"
								controlsPosition="right"
							/>
						</template>
					</el-table-column> -->
					<el-table-column
						label="账期名称"
						prop="periodName"
						min-width="200px"
					>
						<template #default="{ row }">
							<el-input
								placeholder="请输入账期名称"
								v-model="row.periodName"
								type="text"
								clearable
								icon="custom-search"
								maxlength="30"
							/>
						</template>
					</el-table-column>
					<!-- <el-table-column
						label="账期描述"
						prop="description"
						min-width="200px"
					>
						<template #default="{ row }">
							<el-input
								v-model="row.description"
								type="textarea"
								:rows="1"
							/>
						</template>
					</el-table-column> -->
					<el-table-column
						label="状态"
						prop="status"
						min-width="200px"
					>
						<template #default="{ row }">
							<el-switch
								v-model="row.status"
								active-value="ENABLED"
								inactive-value="DISABLED"
								active-text="启用"
								inactive-text="禁用"
							/>
						</template>
					</el-table-column>
					<el-table-column
						label="操作"
						fixed="right"
						width="100"
						align="center"
					>
						<template #default="{ $index }">
							<el-button
								type="danger"
								size="small"
								icon="Delete"
								@click="handleRemoveRow('basePaymentMethodPeriodList', $index)"
								>删除</el-button
							>
						</template>
					</el-table-column>
				</el-table>
			</div>
			<el-button
				style="width: 100%; margin-top: 5px"
				:icon="Plus"
				@click="handleAddRow('basePaymentMethodPeriodList')"
				v-if="formData.period === 'YES'"
			>
				添加
			</el-button>
		</el-form>
	</yun-drawer>
</template>

<script setup lang="ts">
import { ref, reactive, computed, nextTick } from 'vue';
import { ElMessage } from 'yun-design';
import { Plus, Delete } from '@element-plus/icons-vue';
import { useDict } from '@/hooks/dict';
import { addObj, putObj, getObj } from '@/api/lowcode/base-payment-method/index';
import { useUserInfo } from '@/stores/userInfo';
import { rule } from '@/utils/validate';
import { Session } from '@/utils/storage';
import moment from 'moment';

const statusTypes = [
	{ label: '禁用', value: 'DISABLED' },
	{ label: '启用', value: 'ENABLED' },
];

const periodTypes = [
	{ label: '有账期', value: 'YES' },
	{ label: '无账期', value: 'NO' },
];

// 状态管理
const visible = ref(false);
const loading = ref(false);
const isEdit = ref(false);
const vForm = ref();
const formData = ref({ period: 'NO', basePaymentMethodPeriodList: [] });
const rules = reactive({
	methodName: [
		{
			required: true,
			message: '此项必填',
			trigger: ['blur', 'change'],
		},
		{ max: 30, message: '长度在30个字符内' },
	],
	period: [
		{
			required: true,
			message: '请选择是否有账期',
			trigger: ['change'],
		},
	],
});

// 计算属性
const title = computed(() => (isEdit.value ? '编辑' : '新增'));
const size = computed(() => 'X-large');

// 用户信息
const userStore = useUserInfo();
const emits = defineEmits(['getData']);

// 为每个API组件创建响应式变量

// 为每个API组件创建获取数据的方法

// 初始化时获取所有API数据

// 关闭处理
function closeHandler() {
	visible.value = false;
}

// 格式化表单数据
async function formatFrom(row: Record<string, any>) {
	if (!row.id) {
		formData.value = { period: 'NO', basePaymentMethodPeriodList: [] };
		return;
	}
	const res = await getObj(row.id);
	Object.assign(formData.value, res.data);
}

// 格式化提交参数
function formatParams() {
	const baseParams = {};

	// 处理特殊字段类型

	return {
		...formData.value,
		...baseParams,
	};
}

// 确认处理
async function confirmHandler(done: () => void, loading: { value: boolean }) {
	try {
		await vForm.value?.validate();
		
		// 验证账期时间至少有一条数据
		if (formData.value.period === 'YES' && (!formData.value?.basePaymentMethodPeriodList || formData.value?.basePaymentMethodPeriodList?.length === 0)) {
			ElMessage.error('账期时间至少需要添加一条数据');
			return;
		}
		if (formData.value.period === 'NO') {
			formData.value.basePaymentMethodPeriodList = [];
		}
		
		loading.value = true;
		const api = isEdit.value ? putObj : addObj;
		const params = formatParams();
		await api(params);
		ElMessage.success(isEdit.value ? '修改成功' : '新增成功');
		done();
		emits('getData', formData.value);
	} catch (error) {
		ElMessage.error('表单验证或提交失败');
	} finally {
		loading.value = false;
	}
}

// 显示表单
function show(row?: Record<string, any>) {
	visible.value = true;
	isEdit.value = !!row;
	formatFrom(row || { period: 'NO' });
	nextTick(() => {
		vForm.value?.clearValidate();
	});
}

// 日期时间范围默认值
const defaultTime = [new Date(2000, 1, 1, 0, 0, 0), new Date(2000, 2, 1, 23, 59, 59)];

// 字典数据
const getDict = computed(() => (str: string) => {
	if (!str) return [];
	const value = useDict(str)?.[str]?.value;
	return value;
});

// 生成明细表新行的初始数据
function getEnhancedTableRow(widget: Widget): Record<string, any> {
	const row: Record<string, any> = {};
	if (Array.isArray(widget.widgetList)) {
		for (const colWidget of widget.widgetList) {
			const name = colWidget.options?.name;
			if (!name) continue;
			let value = colWidget.options?.defaultValue;
			if (value === undefined) {
				if (colWidget.type === 'number') value = 0;
				else value = '';
			}
			row[name] = value;
		}
	}
	return row;
}

// 添加明细表行
function handleAddRow(tableName: string) {
	if (!formData.value[tableName]) {
		formData.value[tableName] = [];
	}

	formData.value[tableName].push({ status: 'ENABLED' });
}

// 删除明细表行
function handleRemoveRow(tableName: string, index: number) {
	if (!formData.value[tableName]) return;
	formData.value[tableName].splice(index, 1);
}

// 暴露方法
defineExpose({
	show,
	formData,
	rules,
});
</script>

<style lang="scss">
.form-drawer {
	.el-form .el-form-item:last-of-type {
		margin-bottom: 24px !important;
	}
}
</style>

<style lang="scss" scoped>
.el-form-item {
	margin-bottom: 18px;
}

.table-header {
	margin-bottom: 15px;
	.label {
		font-weight: bold;
		font-size: 18px;
		padding-left: 8px;
		border-left: 4px solid var(--el-color-primary);
		line-height: 1.2;
	}
}

:deep(.el-form-item__label) {
	font-weight: normal;
}

:deep(.el-table) {
	.el-table__header-wrapper {
		th {
			background-color: var(--el-fill-color-light);
		}
	}
}
</style>
