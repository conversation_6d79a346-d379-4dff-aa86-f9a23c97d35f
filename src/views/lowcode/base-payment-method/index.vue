<template>
  <div class="table-content">
    <yun-pro-table
      ref="proTableRef"
      v-model:pagination="pagination"
      v-model:filter-data="filterTableData"
      v-model:searchData="searchData"
      :table-columns="columns"
      :search-fields="searchFields"
      :auto-height="true"
      :remote-method="remoteMethod"
      :table-props="tablePropsObj"
      layout="whole"
    >
      <template #tableHeaderLeft>
        <el-button :icon="Plus" type="primary" @click="handleAdd()"> 新增 </el-button>
        <!-- <el-button @click="handleExport()"> 导出 </el-button> -->
        <el-button @click="handleImport()"> 导入 </el-button>
      </template>
      <template #t_action="{ row }">
        <el-button
          type="text"
          size="small"
          @click.prevent="handleEdit(row)"
        >
          编辑
        </el-button>
        <el-button
          type="text"
          size="small"
          @click.prevent="handleView(row)"
        >
          查看
        </el-button>
        <el-button
          type="text"
          size="small"
          @click.prevent="handleSwitch(row)"
        >
          {{ row.status === 'ENABLED' ? '禁用' : '启用' }}
        </el-button>
        <!-- <el-button
          type="text"
          size="small"
          @click.prevent="handleDelete(row)"
        >
          删除
        </el-button> -->
      </template>
    </yun-pro-table>
    <Form
      ref="formRef"
      @getData="getData"
    />
    <Detail ref="detailRef" />
    <yun-import
      v-model="importVisible"
      dialog-tips="温馨提示:单次最多只能导入1000条数据"
      upload-tips="只能上传.xls,.xlsx文件"
      :download-function="handleDownTemplate"
      :upload-function="importFn"
      :template-urls="templateUrls"
    />
  </div>
</template>

<script setup lang="ts">
import {
  ref, reactive, defineAsyncComponent, computed
} from 'vue';
import { Plus } from '@yun-design/icons-vue';
import { useProTable } from '@ylz-use/core';
import moment from 'moment';
import { ElMessage, ElMessageBox } from 'yun-design';
import { fetchList, delObjs, exportData, downloadTemp, importData, switchStatus } from '@/api/lowcode/base-payment-method/index';
import { useTable } from './hooks/useTable';
import Form from './components/form.vue';
import Detail from './components/detail.vue';

const formRef = ref();
const detailRef = ref();
const searchData = reactive({
});
const { columns, searchFields } = useTable();
const dateFieldKeys = computed(() => {
  return [];
});
const datetimeFieldKeys = computed(() => {
  return [];
});
const selectFieldKeys = computed(() => {
  return ['period','status'];
});
const {
  pagination, remoteMethod, tableProps, proTableRef, filterTableData, reLoad,
} = useProTable({
  apiFn: fetchList,
  paramsHandler(params){
    Object.keys(params).forEach((key) => {
      if (dateFieldKeys.value.includes(key)) {
        if (params[key]?.length) {
          params[`${key}Start`] = moment(params[key][0]).startOf('day').format('YYYY-MM-DD');
          params[`${key}End`] = moment(params[key][1]).endOf('day').format('YYYY-MM-DD');
          delete params[key];
        }
      }
      if (datetimeFieldKeys.value.includes(key)) {
        if (params[key]?.length) {
          params[`${key}Start`] = moment(params[key][0]).format('YYYY-MM-DD HH:mm:ss');
          params[`${key}End`] = moment(params[key][1]).format('YYYY-MM-DD HH:mm:ss');
          delete params[key];
        }
      }
      if (selectFieldKeys.value.includes(key)) {
        if (Array.isArray(params[key])) {
          params[`${key}List`] = params[key];
          delete params[key];
        }
      }
    });
    return {
      ...params
    }
  },
  querysHandler(query){
    return {
      ...query,
      current: pagination.value.page,
      size: pagination.value.size,
    }
  },
  responseHandler(result){
    return result.data;
  },
  plugins: { // 挂载插件
    config: {
      columns: columns.value, // 开启搜索增强需要传入列配置
      searchFields: searchFields.value, // 开启搜索增强需要传入搜索配置
    },
    list: ['SEARCH_PLUS'], // 开启插件列表
  },
});

const tablePropsObj = computed(() => {
  return {
    ...tableProps,
    // 去除斑马纹
    stripe: false,
    // 设置表头样式
    headerCellStyle: {
      backgroundColor: '#f5f7fa',
      color: '#303133',
      fontWeight: 'bold',
      height: '40px',
    },
    // 设置单元格样式
    cellStyle: {
      padding: '0',
      height: '40px',
      'vertical-align': 'middle',
    },
    // 设置行样式
    rowStyle: {
      height: '40px',
    },
    // 设置表格行高
    rowHeight: 40,
  };
});

function getData(){
  proTableRef.value?.getData()
}
function handleEdit(row){
  formRef.value?.show(row)
}
function handleAdd(){
  formRef.value?.show()
}
function paramsHandler() {
  const params = { ...searchData.value };
  Object.keys(params).forEach((key) => {
    if (dateFieldKeys.value.includes(key)) {
      if (params[key]?.length) {
        params[`${key}Start`] = moment(params[key][0]).startOf('day').format('YYYY-MM-DD HH:mm:ss');
        params[`${key}End`] = moment(params[key][1]).endOf('day').format('YYYY-MM-DD HH:mm:ss');
        delete params[key];
      }
    }
    if (datetimeFieldKeys.value.includes(key)) {
      if (params[key]?.length) {
        params[`${key}Start`] = moment(params[key][0]).format('YYYY-MM-DD HH:mm:ss');
        params[`${key}End`] = moment(params[key][1]).format('YYYY-MM-DD HH:mm:ss');
        delete params[key];
      }
    }
    if (selectFieldKeys.value.includes(key)) {
      if (Array.isArray(params[key])) {
        params[`${key}List`] = params[key];
        delete params[key];
      }
    }
  });
  return {
    ...params
  }
}
const importVisible = ref(false);
const templateUrls = ref([
  {
    label: '模板下载',
    value: '',
  },
]);

const handleImport = () => {
  importVisible.value = true;
};

async function handleDownTemplate() {
  const data = await downloadTemp();
  // 创建下载链接
  const url = window.URL.createObjectURL(data);
  const a = document.createElement('a');
  a.href = url;
  a.download = `支付方式_导入模板_${moment().format('YYYYMMDDHHmmss')}.xlsx`; // 设置下载文件名
  document.body.appendChild(a);
  a.click();

  // 释放URL对象
  window.URL.revokeObjectURL(url);
  document.body.removeChild(a);
}

// eslint-disable-next-line
function beforeUploadCheckHandler (file) {
  if (!/.(xls|xlsx)$/.test(file.name)) {
    ElMessage({ type: 'warning', message: '文件格式不正确!' });
    return false;
  }
  return true;
}

async function importFn (files) {
  if (files.length === 0) {
    ElMessage({ type: 'warning', message: "请先上传文件" });
    return;
  }
  if (files.some((i) => !beforeUploadCheckHandler(i.raw))) {
    return;
  }
  const sendData = new FormData();
  sendData.append('file', files[0].raw);
  importVisible.value = false;
  await importData(sendData);
  ElMessage.success('导入成功');
  getData();
}
async function handleExport() {
  const data = await exportData(paramsHandler(), {});

  // 创建下载链接
  const url = window.URL.createObjectURL(data);
  const a = document.createElement('a');
  a.href = url;
  a.download = 'basePaymentMethod.xlsx'; // 设置下载文件名
  document.body.appendChild(a);
  a.click();

  // 释放URL对象
  window.URL.revokeObjectURL(url);
  document.body.removeChild(a);
  ElMessage.success('导出成功');
}
function handleView(row){
  detailRef.value.show(row)
}
function handleSwitch(row: any){
  const action = row.status === 'ENABLED' ? '禁用' : '启用';
  ElMessageBox.confirm(`此操作将${action}该项, 是否继续?`, `${action}确认`, {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  }).then(async () => {
    await switchStatus(row.id);
    getData();
    ElMessage.success(`${action}成功!`);
  });
}

function handleDelete(row: any){
  ElMessageBox.confirm('此操作将删除该项, 是否继续?', '删除确认', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'error',
  }).then(async () => {
    await delObjs({ids: row.id});
    getData();
    ElMessage.success('删除成功!');
  });
} 
</script>

<style lang="scss" scoped>
.table-content {
  width: 100%;
  height: calc(100vh - 88px);
  :deep(.el-form .el-form-item:last-of-type) {
    margin-bottom: 24px !important;
  }
}
</style>