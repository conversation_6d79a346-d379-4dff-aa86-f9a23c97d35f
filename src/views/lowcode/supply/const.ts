// 供应商类型
// (MANUFACTURER-生产型供应商、TRADER-贸易型供应商、SERVICE-服务型供应商、LOGISTICS-物流承运商、PROJECT-工程建筑供应商、LABOR-劳务外包供应商、RENTAL-租赁服务供应商、OTHER-其他)

// 企业性质
// LIMITED_LIABILITY_COMPANY("有限责任公司"),
// COMPANY_LIMITED("股份有限公司"),
// SOLE_PROPRIETORSHIP_COMPANY("个人独资"),
// LIMITED_PARTNERSHIP("有限合伙"),
// PARTNERSHIP("合伙企业"),
// ORDINARY_COOPERATION("普通合作"),
// STATE_OWNED("国有企业"),
// FOREIGN_INVESTED("外商投资"),
// HONGKONG_MACAO_TAIWAN("港澳台投资"),
// COLLECTIVE("集体所有制"),
// JOINT_VENTURE("联营"),
// PRIVATE("私营"),
// INDIVIDUAL("个体工商户");


// 供应商来源
// (PLATFORM_APPLY-平台申请、INVITE_JOIN-邀请加入)

// 审批状态
// (DRAFT-草稿、APPROVING-审批中、APPROVED-已审批、REJECTED-已驳回)

// 供应商分类
// (COMMON-普通供应商、STRATEGIC-战略供应商)（必填）

// 供应商状态
// (QUALIFIED-合格供应商、POTENTIAL-潜在供应商、UNQUALIFIED-不合格供应商、BLACKLIST-黑名单供应商、TERMINATED-终止合作)（必填）

// 审核状态
// WAIT_APPROVAL("待审核"),
// APPROVING("审核中"),
// APPROVED("审核通过"),
// REJECTED("审核不通过"),
// INVITE_APPROVING("邀请审核中"),
// INVITE_REJECTED("邀请审核不通过"),
// INVITE_APPROVED("邀请审核通过"),

/**供应商专用枚举 */
// 对象转数组
function objToArr(obj: any) {
  return Object.entries(obj).map(([value, label]) => ({
    label,
    value,
  }));
}
// 供应商类型枚举
export const SUPPLY_TYPE_ENUM = {
  MANUFACTURER: "MANUFACTURER",
  TRADER: "TRADER",
  SERVICE: "SERVICE",
  LOGISTICS: "LOGISTICS",
  PROJECT: "PROJECT",
  LABOR: "LABOR",
  RENTAL: "RENTAL",
  OTHER: "OTHER",
}
export const SUPPLY_TYPE_OBJ = {
  [SUPPLY_TYPE_ENUM.MANUFACTURER]: '生产型供应商',
  [SUPPLY_TYPE_ENUM.TRADER]: '贸易型供应商',
  [SUPPLY_TYPE_ENUM.SERVICE]: '服务型供应商',
  [SUPPLY_TYPE_ENUM.LOGISTICS]: '物流承运商',
  [SUPPLY_TYPE_ENUM.PROJECT]: '工程建筑供应商',
  [SUPPLY_TYPE_ENUM.LABOR]: '劳务外包供应商',
  [SUPPLY_TYPE_ENUM.RENTAL]: '租赁服务供应商',
  [SUPPLY_TYPE_ENUM.OTHER]: '其他',
}
export const SUPPLY_TYPE_OPTIONS = objToArr(SUPPLY_TYPE_OBJ);

// 企业性质枚举
export const ENTERPRISE_NATURE_ENUM = {
  LIMITED_LIABILITY_COMPANY: "LIMITED_LIABILITY_COMPANY",
  COMPANY_LIMITED: "COMPANY_LIMITED",
  SOLE_PROPRIETORSHIP_COMPANY: "SOLE_PROPRIETORSHIP_COMPANY",
  LIMITED_PARTNERSHIP: "LIMITED_PARTNERSHIP",
  PARTNERSHIP: "PARTNERSHIP",
  ORDINARY_COOPERATION: "ORDINARY_COOPERATION",
  STATE_OWNED: "STATE_OWNED",
  FOREIGN_INVESTED: "FOREIGN_INVESTED",
  HONGKONG_MACAO_TAIWAN: "HONGKONG_MACAO_TAIWAN",
  COLLECTIVE: "COLLECTIVE",
  JOINT_VENTURE: "JOINT_VENTURE",
  PRIVATE: "PRIVATE",
  INDIVIDUAL: "INDIVIDUAL",
}
export const ENTERPRISE_NATURE_OBJ = {
  [ENTERPRISE_NATURE_ENUM.LIMITED_LIABILITY_COMPANY]: '有限责任公司',
  [ENTERPRISE_NATURE_ENUM.COMPANY_LIMITED]: '股份有限公司',
  [ENTERPRISE_NATURE_ENUM.SOLE_PROPRIETORSHIP_COMPANY]: '个人独资',
  [ENTERPRISE_NATURE_ENUM.LIMITED_PARTNERSHIP]: '有限合伙',
  [ENTERPRISE_NATURE_ENUM.PARTNERSHIP]: '合伙企业',
  [ENTERPRISE_NATURE_ENUM.ORDINARY_COOPERATION]: '普通合作',
  [ENTERPRISE_NATURE_ENUM.STATE_OWNED]: '国有企业',
  [ENTERPRISE_NATURE_ENUM.FOREIGN_INVESTED]: '外资企业',
  [ENTERPRISE_NATURE_ENUM.HONGKONG_MACAO_TAIWAN]: '港澳台投资',
  [ENTERPRISE_NATURE_ENUM.COLLECTIVE]: '集体所有制',
  [ENTERPRISE_NATURE_ENUM.JOINT_VENTURE]: '联营',
  [ENTERPRISE_NATURE_ENUM.PRIVATE]: '私营',
  [ENTERPRISE_NATURE_ENUM.INDIVIDUAL]: '个体工商户',
}
export const ENTERPRISE_NATURE_OPTIONS = objToArr(ENTERPRISE_NATURE_OBJ);

// 供应商来源枚举
export const SUPPLY_SOURCE_ENUM = {
  SYSTEM_CREATE: "SYSTEM_CREATE",
  SRM_SYSTEM: "SRM_SYSTEM",
  SUPPLIER_REGISTER: "SUPPLIER_REGISTER",
  INVITE_JOIN: "INVITE_JOIN",
}
export const SUPPLY_SOURCE_OBJ = {
  [SUPPLY_SOURCE_ENUM.SYSTEM_CREATE]: '系统创建',
  [SUPPLY_SOURCE_ENUM.SRM_SYSTEM]: 'SRM系统',
  [SUPPLY_SOURCE_ENUM.SUPPLIER_REGISTER]: '供应商注册',
  [SUPPLY_SOURCE_ENUM.INVITE_JOIN]: '邀请加入',
}
export const SUPPLY_SOURCE_OPTIONS = objToArr(SUPPLY_SOURCE_OBJ);

// 审批状态枚举
// export const APPROVAL_STATUS_ENUM = {
//   DRAFT: "DRAFT",
//   APPROVING: "APPROVING",
//   APPROVED: "APPROVED",
//   REJECTED: "REJECTED",
// }
// export const APPROVAL_STATUS_OBJ = {
//   [APPROVAL_STATUS_ENUM.DRAFT]: '草稿',
//   [APPROVAL_STATUS_ENUM.APPROVING]: '审批中',
//   [APPROVAL_STATUS_ENUM.APPROVED]: '已审批',
//   [APPROVAL_STATUS_ENUM.REJECTED]: '已驳回',
// }
// export const APPROVAL_STATUS_OPTIONS = objToArr(APPROVAL_STATUS_OBJ);

// 供应商分类枚举
export const SUPPLY_CATEGORY_ENUM = {
  COMMON: "COMMON",
  SETTLEMENT: "SETTLEMENT",
  INTERNAL: "INTERNAL",
  INE_TIME: "INE_TIME",
  TEMPORARY: "TEMPORARY",
}
export const SUPPLY_CATEGORY_OBJ = {
  [SUPPLY_CATEGORY_ENUM.COMMON]: '普通供应商',
  [SUPPLY_CATEGORY_ENUM.SETTLEMENT]: '结算供应商',
  [SUPPLY_CATEGORY_ENUM.INTERNAL]: '内部供应商',
  [SUPPLY_CATEGORY_ENUM.INE_TIME]: '一次性供应商',
  [SUPPLY_CATEGORY_ENUM.TEMPORARY]: '临时供应商',
}
export const SUPPLY_CATEGORY_OPTIONS = objToArr(SUPPLY_CATEGORY_OBJ);

// 供应商状态枚举
export const SUPPLY_STATUS_ENUM = {
  // QUALIFIED: "QUALIFIED",
  // POTENTIAL: "POTENTIAL",
  // UNQUALIFIED: "UNQUALIFIED",
  // BLACKLIST: "BLACKLIST",
  // TERMINATED: "TERMINATED",
  UNFAMILIAR: "UNFAMILIAR",
  POTENTIAL: "POTENTIAL",
  QUALIFIED: "QUALIFIED",
  TERMINATED: "TERMINATED",
  BLACKLIST: "BLACKLIST",
}
export const SUPPLY_STATUS_OBJ = {
  // [SUPPLY_STATUS_ENUM.QUALIFIED]: '合格供应商',
  // [SUPPLY_STATUS_ENUM.POTENTIAL]: '潜在供应商',
  // [SUPPLY_STATUS_ENUM.UNQUALIFIED]: '不合格供应商',
  // [SUPPLY_STATUS_ENUM.BLACKLIST]: '黑名单供应商',
  // [SUPPLY_STATUS_ENUM.TERMINATED]: '终止合作',
  [SUPPLY_STATUS_ENUM.UNFAMILIAR]: '陌生供应商',
  [SUPPLY_STATUS_ENUM.POTENTIAL]: '潜在供应商',
  [SUPPLY_STATUS_ENUM.QUALIFIED]: '合格供应商',
  [SUPPLY_STATUS_ENUM.TERMINATED]: '淘汰供应商',
  [SUPPLY_STATUS_ENUM.BLACKLIST]: '黑名单',
}
export const SUPPLY_STATUS_OPTIONS = objToArr(SUPPLY_STATUS_OBJ);

export const STATUS_ENUM = {
  ENABLED: "ENABLED",
  DISABLED: "DISABLED",
}
export const STATUS_OBJ = {
  [STATUS_ENUM.ENABLED]: '启用',
  [STATUS_ENUM.DISABLED]: '禁用',
}
export const STATUS_OPTIONS = objToArr(STATUS_OBJ);

// 审核状态枚举
export const APPROVAL_STATUS_ENUM = {
  WAIT_APPROVAL: "WAIT_APPROVAL",
  APPROVING: "APPROVING",
  APPROVED: "APPROVED",
  REJECTED: "REJECTED",
  INVITE_APPROVING: "INVITE_APPROVING",
  INVITE_REJECTED: "INVITE_REJECTED",
  INVITE_APPROVED: "INVITE_APPROVED",
}
export const APPROVAL_STATUS_OBJ = {
  [APPROVAL_STATUS_ENUM.WAIT_APPROVAL]: '待审核',
  [APPROVAL_STATUS_ENUM.APPROVING]: '审核中',
  [APPROVAL_STATUS_ENUM.APPROVED]: '审核通过',
  [APPROVAL_STATUS_ENUM.REJECTED]: '审核不通过',
  [APPROVAL_STATUS_ENUM.INVITE_APPROVING]: '邀请审核中',
  [APPROVAL_STATUS_ENUM.INVITE_REJECTED]: '邀请审核不通过',
  [APPROVAL_STATUS_ENUM.INVITE_APPROVED]: '邀请审核通过',
}
export const APPROVAL_STATUS_OPTIONS = objToArr(APPROVAL_STATUS_OBJ);

// 模块类型 我的 / 平台 / 注册
export const MODULE_TYPE_ENUM = {
  MY: "MY",
  PLATFORM: "PLATFORM",
  REGISTER: "REGISTER",
}

// 模块名称  联系人/资质/银行账户/主营物料
export const MODULE_NAME_ENUM = {
  CONTACT: "CONTACT",
  CERTIFICATE: "CERTIFICATE",
  BANK_ACCOUNT: "BANK_ACCOUNT",
  MATERIAL: "MATERIAL",
}

// tabs类型   基础信息/联系人信息/资质信息/银行信息/主营物料
export const TABS_TYPE_ENUM = {
  BASIC_INFO: "BASIC_INFO",
  CONTACT_INFO: "CONTACT_INFO",
  CERTIFICATE_INFO: "CERTIFICATE_INFO",
  BANK_INFO: "BANK_INFO",
  MATERIAL_INFO: "MATERIAL_INFO",
}
export const TABS_TYPE_OPTIONS = [
  {
    label: '基础信息',
    value: TABS_TYPE_ENUM.BASIC_INFO,
  },
  {
    label: '联系人信息',
    value: TABS_TYPE_ENUM.CONTACT_INFO,
  },
  {
    label: '资质信息',
    value: TABS_TYPE_ENUM.CERTIFICATE_INFO,
  },
  {
    label: '银行信息',
    value: TABS_TYPE_ENUM.BANK_INFO,
  },
  {
    label: '主营物料',
    value: TABS_TYPE_ENUM.MATERIAL_INFO,
  }
];

// 是否枚举
export const YES_OR_NO_ENUM = {
  YES: "1",
  NO: "0",
}
export const YES_OR_NO_OBJ = {
  [YES_OR_NO_ENUM.YES]: '是',
  [YES_OR_NO_ENUM.NO]: '否',
}
export const YES_OR_NO_OPTIONS = objToArr(YES_OR_NO_OBJ);

// 操作类型
export const OPERATION_TYPE_ENUM = {
  EDIT: "EDIT",
  ADD: "ADD",
  AUDIT: "AUDIT",
  INVITE: "INVITE",
  VIEW: "VIEW",
}
export const OPERATION_TYPE_OBJ = {
  [OPERATION_TYPE_ENUM.EDIT]: '编辑',
  [OPERATION_TYPE_ENUM.ADD]: '新增',
  [OPERATION_TYPE_ENUM.AUDIT]: '审核',
  [OPERATION_TYPE_ENUM.INVITE]: '邀请',
  [OPERATION_TYPE_ENUM.VIEW]: '查看',
}
export const OPERATION_TYPE_OPTIONS = objToArr(OPERATION_TYPE_OBJ);
/**供应商专用枚举 */
