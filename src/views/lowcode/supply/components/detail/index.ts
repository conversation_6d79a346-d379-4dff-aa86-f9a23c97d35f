import { computed } from 'vue';
import { useHandler } from '@/views/lowcode/supply/hooks/useHandler';
import {
	ENTERPRISE_NATURE_OPTIONS,
	SUPPLY_TYPE_OPTIONS,
	SUPPLY_STATUS_OPTIONS,
	SUPPLY_CATEGORY_OPTIONS,
	YES_OR_NO_OPTIONS,
	MODULE_TYPE_ENUM,
} from '@/views/lowcode/supply/const';

export default (props: any) => {
	const { collections, initData, loading } = useHandler();

	// 静态生成的分组配置
	const processedGroups = computed(() => {
		return [
			{
				title: '基础信息',
				columns: [
					{
						prop: 'supplierName',
						label: '供应商名称',
						type: 'text',
					},
					{
						prop: 'supplierShortName',
						label: '供应商简称',
						type: 'text',
					},
					{
						prop: 'socialCreditCode',
						label: '统一社会信用代码',
						type: 'text',
					},
					{
						prop: 'supplierType',
						label: '供应商类型',
						type: 'enums',
						attrs: {
							options: SUPPLY_TYPE_OPTIONS,
						},
					},
					...(props.moduleType === MODULE_TYPE_ENUM.MY
						? [
								{
									prop: 'supplierStatus',
									label: '供应商状态',
									type: 'enums',
									attrs: {
										options: SUPPLY_STATUS_OPTIONS,
									},
								},
						  ]
						: []),
					{
						prop: 'enterpriseNature',
						label: '企业性质',
						type: 'enums',
						attrs: {
							options: ENTERPRISE_NATURE_OPTIONS,
						},
					},
					...(props.moduleType === MODULE_TYPE_ENUM.MY
						? [
								{
									prop: 'deptNames',
									label: '适用组织',
									type: 'enums',
									formatter: (row: any) => {
										return row.isAllDept ? '全部组织' : row.deptNames;
									},
								},
						  ]
						: []),
					...(props.moduleType === MODULE_TYPE_ENUM.MY
						? [
								{
									prop: 'supplierCategory',
									label: '供应商分类',
									type: 'enums',
									attrs: {
										options: SUPPLY_CATEGORY_OPTIONS,
									},
								},
						  ]
						: []),
					{
						prop: 'registeredCapital',
						label: '注册资本(万)',
						type: 'text',
					},
					{
						prop: 'establishDate',
						label: '成立日期',
						type: 'text',
					},
					...(props.moduleType === MODULE_TYPE_ENUM.MY
						? [
								{
									prop: 'legalPerson',
									label: '法人代表',
									type: 'text',
								},
								{
									prop: 'legalPersonId',
									label: '法人身份证号',
									type: 'text',
								},
								{
									prop: 'legalPersonPhone',
									label: '法人电话',
									type: 'text',
								},
								{
									prop: 'serviceStartDate',
									label: '服务开始时间',
									type: 'text',
								},
								{
									prop: 'serviceEndDate',
									label: '服务结束时间',
									type: 'text',
								},
						  ]
						: []),
					{
						prop: 'registeredAddress',
						label: '注册地址',
						type: 'text',
					},
					{
						prop: 'postalCode',
						label: '邮政编码',
						type: 'text',
					},
					{
						prop: 'fax',
						label: '传真',
						type: 'text',
					},
					{
						prop: 'annualRevenue',
						label: '年平均营业额(万)',
						type: 'text',
					},
					{
						prop: 'letterOfAuthorization',
						label: '法人委托书',
						type: 'files',
						// span: 3,
					},
					{
						prop: 'companyProfile',
						label: '公司简介',
						type: 'textarea',
					},
					{
						prop: 'businessScope',
						label: '经营范围',
						type: 'textarea',
					},
					...(props.moduleType === MODULE_TYPE_ENUM.MY
						? [
								{
									prop: 'remark',
									label: '备注',
									type: 'textarea',
								},
						  ]
						: []),
				],
			},
			{
				title: '联系人信息',
				columns: [
					{
						prop: 'contactList',
						label: '',
						type: 'table',
						attrs: {
							showIndex: true,
							columns: [
								{
									prop: 'contactName',
									label: '联系人姓名',
									type: 'text',
								},
								{
									prop: 'contactPhone',
									label: '联系人电话',
									type: 'text',
								},
								{
									prop: 'idNumber',
									label: '身份证',
									type: 'text',
								},
								{
									prop: 'idAttachment',
									label: '身份证附件',
									type: 'files',
								},
								{
									prop: 'generateAccount',
									label: '是否生成登录账号',
									type: 'enums',
									attrs: {
										options: YES_OR_NO_OPTIONS,
									},
								},
								{
									prop: 'loginAccount',
									label: '登录账号',
									type: 'text',
								},
								{
									prop: 'password',
									label: '登录密码',
									type: 'text',
								},
								{
									prop: 'remark',
									label: '备注',
									type: 'text',
								},
							],
						},
					},
				],
			},
			{
				title: '资质信息',
				columns: [
					{
						prop: 'certificateList',
						label: '',
						type: 'table',
						attrs: {
							showIndex: true,
							columns: [
								{
									prop: 'certificateType',
									label: '证书类型',
									type: 'text',
								},
								{
									prop: 'certificateName',
									label: '证书名称',
									type: 'text',
								},
								{
									prop: 'certificateNo',
									label: '证书号码',
									type: 'text',
								},
								{
									prop: 'validStartDate',
									label: '证书生效日期',
									type: 'text',
								},
								{
									prop: 'validEndDate',
									label: '证书失效日期',
									type: 'text',
								},
								{
									prop: 'attachmentUrl',
									label: '附件',
									type: 'files',
								},
								{
									prop: 'remark',
									label: '备注',
									type: 'text',
								},
							],
						},
					},
				],
			},
			{
				title: '银行信息',
				columns: [
					{
						prop: 'bankAccountList',
						label: '',
						type: 'table',
						attrs: {
							showIndex: true,
							columns: [
								{
									prop: 'bankName',
									label: '开户行名称',
									type: 'text',
								},
								{
									prop: 'bankCode',
									label: '开户行联行号',
									type: 'text',
								},
								{
									prop: 'bankAddress',
									label: '开户行地址',
									type: 'text',
								},
								{
									prop: 'accountName',
									label: '开户名',
									type: 'text',
								},
								{
									prop: 'accountNo',
									label: '开户行账号',
									type: 'text',
								},
								{
									prop: 'remark',
									label: '备注',
									type: 'text',
								},
							],
						},
					},
				],
			},
			{
				title: '主营物料',
				columns: [
					{
						prop: 'materialList',
						label: '',
						type: 'table',
						attrs: {
							showIndex: true,
							columns: [
								{
									prop: 'materialCode',
									label: '物料编码',
									type: 'text',
								},
								{
									prop: 'materialName',
									label: '物料名称',
									type: 'text',
								},
								{
									prop: 'spec',
									label: '规格型号',
									type: 'text',
								},
								{
									prop: 'materialCategoryName',
									label: '物流所属分类',
									type: 'text',
								},
								{
									prop: 'materialCategoryRoot',
									label: '物流一级分类',
									type: 'text',
								},
								{
									prop: 'unit',
									label: '计量单位',
									type: 'text',
								},
							],
						},
					},
				],
			},
		];
	});

	return {
		loading,
		processedGroups,
		collections,
		initData,
	};
};
