<template>
	<yun-drawer
		v-model="visible"
		:show-confirm-button="false"
		:show-cancel-button="true"
		cancel-button-text="关闭"
		confirm-button-text="确定"
		destroy-on-close
		title="详情"
		size="X-large"
	>
		<div v-loading="loading">
			<Detail
				:groups="processedGroups"
				:data="form"
			/>
		</div>
	</yun-drawer>
</template>

<script setup lang="ts">
import Detail from '@/components/Detail/index.vue';
import { MODULE_TYPE_ENUM } from '@/views/lowcode/supply/const';
import useIndex from './index';

const props = defineProps({
	moduleType: {
		type: String,
		default: MODULE_TYPE_ENUM.REGISTER,
	},
});

interface FormData {
	[key: string]: any;
}

const visible = ref(false);
const form = ref<FormData>({});
const { loading, processedGroups, collections, initData } = useIndex(props);

async function show(row: FormData) {
	visible.value = true;
	await initData(row);
	form.value = collections.value || {};
}

defineExpose({
	show,
});
</script>

<style scoped lang="scss"></style>
