<template>
  <yun-drawer
    v-model="visible"
    destroy-on-close
    append-to-body
    modal
    custom-class="form-drawer"
    :close-on-click-modal="false"
    confirm-button-text="确定"
    cancel-button-text="取消"
    :confirm-button-disabled="loading"
    :title="`${OPERATION_TYPE_OBJ[operationType]}供应商`"
    size="X-large"
  >
    <template #footer>
      <div class="audit-footer">
        <el-button @click="closeHandler"> 取消 </el-button>
        <el-button
          type="primary"
          :loading="loading"
          @click="confirmHandler"
          v-if="[OPERATION_TYPE_ENUM.EDIT, OPERATION_TYPE_ENUM.ADD].includes(operationType)"
        >
          提交
        </el-button>
        <el-button
          type="primary"
          :loading="loading"
          @click="confirmHandler"
          v-if="operationType === OPERATION_TYPE_ENUM.AUDIT"
        >
          提交审核
        </el-button>
        <el-button
          type="primary"
          :loading="loading"
          @click="confirmHandler"
          v-if="operationType === OPERATION_TYPE_ENUM.INVITE"
        >
          邀请
        </el-button>
      </div>
    </template>
    <div v-loading="handlerLoading">
      <el-tabs
        v-model="currentTab"
        size="small"
        type="card"
        style=""
        class="demo-tabs"
      >
        <el-tab-pane
          v-for="item in TABS_TYPE_OPTIONS"
          :key="item.value"
          :name="item.value"
          :label="item.label"
        ></el-tab-pane>
      </el-tabs>
      <el-form
        ref="vForm"
        :model="formData"
        :rules="rules"
        label-width="120px"
        label-position="top"
      >
        <!-- 基础信息 -->
        <!-- <div class="table-header">
					<div class="label">基础信息</div>
				</div> -->
        <div v-show="currentTab === TABS_TYPE_ENUM.BASIC_INFO">
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item
                label="供应商名称"
                prop="supplierName"
              >
                <el-input
                  v-model="formData.supplierName"
                  placeholder="请输入"
                  maxlength="30"
                  show-word-limit
                />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item
                label="供应商简称"
                prop="supplierShortName"
              >
                <el-input
                  v-model="formData.supplierShortName"
                  placeholder="请输入"
                  maxlength="30"
                  show-word-limit
                />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item
                label="统一社会信用代码"
                prop="socialCreditCode"
              >
                <el-input
                  v-model="formData.socialCreditCode"
                  placeholder="请输入"
                  maxlength="30"
                  show-word-limit
                />
              </el-form-item>
            </el-col>
            <!-- </el-row>
					<el-row :gutter="20"> -->
            <el-col :span="8">
              <el-form-item
                label="供应商类型"
                prop="supplierType"
              >
                <el-select
                  v-model="formData.supplierType"
                  placeholder="请选择供应商"
                  style="width: 100%"
                >
                  <el-option
                    v-for="item in SUPPLY_TYPE_OPTIONS"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col
              :span="8"
              v-show="moduleType === MODULE_TYPE_ENUM.MY"
            >
              <el-form-item
                label="供应商状态"
                prop="supplierStatus"
              >
                <el-select
                  v-model="formData.supplierStatus"
                  placeholder="请选择供应商状态"
                  style="width: 100%"
                >
                  <el-option
                    v-for="item in SUPPLY_STATUS_OPTIONS"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item
                label="企业性质"
                prop="enterpriseNature"
              >
                <el-select
                  v-model="formData.enterpriseNature"
                  placeholder="请选择企业性质"
                  style="width: 100%"
                >
                  <el-option
                    v-for="item in ENTERPRISE_NATURE_OPTIONS"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <!-- </el-row>
					<el-row :gutter="20"> -->
            <el-col
              :span="8"
              v-show="moduleType === MODULE_TYPE_ENUM.MY"
            >
              <el-form-item
                label="适用组织"
                prop="deptIds"
              >
                <div class="flex items-center gap-2">
                  <el-radio-group
                    v-model="formData.isAllDept"
                    @change="handleDeptChange"
                  >
                    <el-radio :label="true">全部组织</el-radio>
                    <el-radio :label="false">指定组织</el-radio>
                  </el-radio-group>
                  <DeptSelector
                    v-model="formData.deptIds"
                    v-model:deptNames="formData.deptNames"
                    v-show="formData.isAllDept === false"
                    :multiple="true"
                    :check-strictly="false"
                    placeholder="请选择适用组织"
                    style="width: auto; flex: 1"
                    ref="deptSelectorRef"
                  />
                </div>
              </el-form-item>
            </el-col>
            <el-col
              :span="8"
              v-show="moduleType === MODULE_TYPE_ENUM.MY"
            >
              <el-form-item
                label="供应商分类"
                prop="supplierCategory"
              >
                <el-select
                  v-model="formData.supplierCategory"
                  placeholder="请选择供应商分类"
                  style="width: 100%"
                >
                  <el-option
                    v-for="item in SUPPLY_CATEGORY_OPTIONS"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item
                label="注册资本(万)"
                prop="registeredCapital"
              >
                <el-input
                  v-model="formData.registeredCapital"
                  placeholder="请输入"
                  maxlength="20"
                  show-word-limit
                >
                  <!-- <template #append>万</template> -->
                </el-input>
              </el-form-item>
            </el-col>
            <!-- </el-row>
					<el-row :gutter="20"> -->
            <el-col :span="8">
              <el-form-item
                label="成立日期"
                prop="establishDate"
              >
                <el-date-picker
                  v-model="formData.establishDate"
                  type="datetime"
                  placeholder="请输入"
                  value-format="YYYY-MM-DD"
                  format="YYYY-MM-DD"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
            <el-col
              :span="8"
              v-show="moduleType === MODULE_TYPE_ENUM.MY"
            >
              <el-form-item
                label="法人代表"
                prop="legalPerson"
              >
                <el-input
                  v-model="formData.legalPerson"
                  placeholder="请输入"
                  maxlength="30"
                  show-word-limit
                />
              </el-form-item>
            </el-col>
            <el-col
              :span="8"
              v-show="moduleType === MODULE_TYPE_ENUM.MY"
            >
              <el-form-item
                label="法人身份证号"
                prop="legalPersonId"
              >
                <el-input
                  v-model="formData.legalPersonId"
                  placeholder="请输入"
                />
              </el-form-item>
            </el-col>
            <!-- </el-row>
					<el-row :gutter="20"> -->
            <el-col
              :span="8"
              v-show="moduleType === MODULE_TYPE_ENUM.MY"
            >
              <el-form-item
                label="法人电话"
                prop="legalPersonPhone"
              >
                <el-input
                  v-model="formData.legalPersonPhone"
                  placeholder="请输入"
                />
              </el-form-item>
            </el-col>
            <el-col
              :span="8"
              v-show="moduleType === MODULE_TYPE_ENUM.MY"
            >
              <el-form-item
                label="服务开始时间"
                prop="serviceStartDate"
              >
                <el-date-picker
                  v-model="formData.serviceStartDate"
                  type="date"
                  placeholder="请输入"
                  value-format="YYYY-MM-DD"
                  format="YYYY-MM-DD"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
            <el-col
              :span="8"
              v-show="moduleType === MODULE_TYPE_ENUM.MY"
            >
              <el-form-item
                label="服务结束时间"
                prop="serviceEndDate"
              >
                <el-date-picker
                  v-model="formData.serviceEndDate"
                  type="date"
                  placeholder="请输入"
                  value-format="YYYY-MM-DD"
                  format="YYYY-MM-DD"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
            <!-- </el-row>
					<el-row :gutter="20"> -->
            <el-col :span="8">
              <el-form-item
                label="注册地址"
                prop="registeredAddress"
              >
                <el-input
                  v-model="formData.registeredAddress"
                  placeholder="请输入"
                />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item
                label="邮政编码"
                prop="postalCode"
              >
                <el-input
                  v-model="formData.postalCode"
                  placeholder="请输入"
                  maxlength="30"
                  show-word-limit
                />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item
                label="传真"
                prop="fax"
              >
                <el-input
                  v-model="formData.fax"
                  placeholder="请输入"
                  maxlength="30"
                  show-word-limit
                >
                </el-input>
              </el-form-item>
            </el-col>
            <!-- </el-row>
					<el-row :gutter="20"> -->
            <el-col :span="8">
              <el-form-item
                label="年平均营业额(万)"
                prop="annualRevenue"
              >
                <el-input
                  v-model="formData.annualRevenue"
                  placeholder="请输入"
                  maxlength="20"
                  show-word-limit
                >
                  <!-- <template #append>万</template> -->
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item
                label="法人委托书"
                prop="letterOfAuthorization"
              >
                <div style="width: 100%">
                  <YunUpload
                    v-model="formData.letterOfAuthorization"
                    :show-file-list="false"
                  >
                  </YunUpload>
                  <File
                    v-if="formData.letterOfAuthorization?.length"
                    v-model="formData.letterOfAuthorization"
                  />
                </div>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item
                label="公司简介"
                prop="companyProfile"
              >
                <el-input
                  v-model="formData.companyProfile"
                  type="textarea"
                  :rows="3"
                  placeholder="请输入"
                  maxlength="300"
                  show-word-limit
                />
              </el-form-item>
            </el-col>
            <!-- </el-row>
					<el-row :gutter="20"> -->
            <el-col :span="24">
              <el-form-item
                label="经营范围"
                prop="businessScope"
              >
                <el-input
                  v-model="formData.businessScope"
                  type="textarea"
                  :rows="3"
                  placeholder="请输入"
                  maxlength="300"
                  show-word-limit
                />
              </el-form-item>
            </el-col>
            <!-- </el-row>
					<el-row :gutter="20" > -->
            <el-col
              :span="24"
              v-show="moduleType === MODULE_TYPE_ENUM.MY"
            >
              <el-form-item
                label="备注"
                prop="remark"
              >
                <el-input
                  v-model="formData.remark"
                  type="textarea"
                  :rows="3"
                  placeholder="请输入"
                  maxlength="300"
                  show-word-limit
                />
              </el-form-item>
            </el-col>
          </el-row>
        </div>
        <!-- 联系人信息 -->
        <!-- <div class="table-header">
					<div class="label">联系人信息</div>
				</div> -->
        <div
          class="w-full overflow-x-auto"
          v-show="currentTab === TABS_TYPE_ENUM.CONTACT_INFO"
        >
          <el-button
            type="primary"
            style="margin-top: 0px; margin-bottom: 12px; float: right"
            :icon="Plus"
            @click="handleAddRow('contactList')"
          >
            添加联系人
          </el-button>
          <el-table
            :data="formData.contactList || []"
            :border="true"
            stripe
            class="w-full dark:bg-gray-800"
            :header-cell-class-name="'!bg-gray-50 dark:!bg-gray-700'"
            style="min-width: 800px"
          >
            <el-table-column
              type="index"
              label="序号"
              width="60"
              align="center"
              fixed
            />
            <el-table-column
              label="联系人姓名"
              prop="contactName"
              min-width="150px"
            >
              <template #default="{ row }">
                <el-input
                  v-model="row.contactName"
                  placeholder="请输入联系人姓名"
                  maxlength="10"
                  :show-word-limit="false"
                />
              </template>
            </el-table-column>
            <el-table-column
              label="联系人电话"
              maxlength="10"
              prop="contactPhone"
              min-width="150px"
            >
              <template #default="{ row, $index }">
                <el-form-item
                  :prop="`contactList.${$index}.contactPhone`"
                  :rules="contactPhoneRules"
                  style="margin: 0 !important"
                  inline-message
                >
                  <el-input
                    v-model="row.contactPhone"
                    placeholder="请输入联系人电话"
                    maxlength="11"
                    @change="handleContactPhoneChange(row)"
                    @blur="validateContactPhone(row, $index)"
                  />
                </el-form-item>
              </template>
            </el-table-column>
            <el-table-column
              label="身份证"
              prop="idNumber"
              min-width="150px"
            >
              <template #default="{ row }">
                <el-input
                  v-model="row.idNumber"
                  placeholder="请输入身份证"
                  maxlength="20"
                  :show-word-limit="false"
                />
              </template>
            </el-table-column>
            <el-table-column
              label="身份证附件"
              prop="idAttachment"
              min-width="150px"
            >
              <template #default="{ row }">
                <div style="width: 100%">
                  <YunUpload
                    v-model="row.idAttachment"
                    :show-file-list="false"
                  />
                  <File
                    v-if="row.idAttachment?.length"
                    v-model="row.idAttachment"
                  />
                </div>
              </template>
            </el-table-column>

            <el-table-column
              label="部门"
              prop="deptId"
              min-width="180px"
            >
              <template #default="{ row }">
                <el-select
                  v-model="row.deptId"
                  filterable
                  style="width: 100%"
                  clearable
                >
                  <el-option
                    v-for="option in outsideDeptList"
                    :key="option.id"
                    :label="option.name"
                    :value="option.id"
                  >
                    <span>{{ option.name || '-' }}</span>
                  </el-option>
                </el-select>
              </template>
            </el-table-column>

            <el-table-column
              label="是否生成登录账号"
              prop="generateAccount"
              min-width="150px"
            >
              <template #default="{ row }">
                <el-switch
                  v-model="row.generateAccount"
                  :active-value="1"
                  :inactive-value="0"
                  active-text="是"
                  inactive-text="否"
                  @change="handleGenerateAccountChange(row)"
                />
              </template>
            </el-table-column>
            <el-table-column
              label="登录账号"
              prop="loginAccount"
              min-width="150px"
            >
              <template #default="{ row }">
                <el-input
                  v-model="row.loginAccount"
                  placeholder="请输入登录账号"
                  maxlength="20"
                  :show-word-limit="false"
                  autocomplete="off"
                  :disabled="!!row?.id"
                  v-if="row.generateAccount === 1"
                />
                <span v-else>--</span>
              </template>
            </el-table-column>
            <el-table-column
              label="登录密码"
              prop="password"
              min-width="150px"
            >
              <template #default="{ row }">
                <el-input
                  v-model="row.password"
                  placeholder="请输入登录密码"
                  maxlength="20"
                  :show-word-limit="false"
                  type="password"
                  show-password
                  autocomplete="off"
                  v-if="showPassword(row)"
                />
                <span v-else>--</span>
              </template>
            </el-table-column>
            <el-table-column
              label="备注"
              prop="remark"
              min-width="200px"
            >
              <template #default="{ row }">
                <el-input
                  v-model="row.remark"
                  placeholder="请输入备注"
                  maxlength="50"
                  show-word-limit
                />
              </template>
            </el-table-column>
            <el-table-column
              label="操作"
              fixed="right"
              width="100"
              align="center"
            >
              <template #default="{ $index }">
                <el-button
                  type="danger"
                  size="small"
                  icon="Delete"
                  @click="handleRemoveRow('contactList', $index)"
                >
                  删除
                </el-button>
              </template>
            </el-table-column>
          </el-table>
          <!-- <Pagination
						v-model:current="pagination[MODULE_NAME_ENUM.CONTACT].current"
						v-model:size="pagination[MODULE_NAME_ENUM.CONTACT].size"
						v-model:total="pagination[MODULE_NAME_ENUM.CONTACT].total"
						@size-change="handleSizeChange($event, MODULE_NAME_ENUM.CONTACT)"
						@current-change="handleCurrentChange($event, MODULE_NAME_ENUM.CONTACT)" /> -->
        </div>

        <!-- 资质信息 -->
        <!-- <div class="table-header">
					<div class="label">资质信息</div>
				</div> -->
        <div
          class="w-full overflow-x-auto"
          v-show="currentTab === TABS_TYPE_ENUM.CERTIFICATE_INFO"
        >
          <el-button
            type="primary"
            style="margin-top: 0px; margin-bottom: 12px; float: right"
            :icon="Plus"
            @click="handleAddRow('certificateList')"
          >
            添加资质
          </el-button>
          <el-table
            :data="formData.certificateList || []"
            :border="true"
            stripe
            class="w-full dark:bg-gray-800"
            :header-cell-class-name="'!bg-gray-50 dark:!bg-gray-700'"
            style="min-width: 900px"
          >
            <el-table-column
              type="index"
              label="序号"
              width="60"
              align="center"
              fixed
            />
            <el-table-column
              label="证书类型"
              prop="certificateType"
              min-width="150px"
            >
              <template #default="{ row }">
                <el-input
                  v-model="row.certificateType"
                  placeholder="请输入证书类型"
                  maxlength="30"
                  show-word-limit
                />
              </template>
            </el-table-column>
            <el-table-column
              label="证书名称"
              prop="certificateName"
              min-width="150px"
            >
              <template #default="{ row }">
                <el-input
                  v-model="row.certificateName"
                  placeholder="请输入证书名称"
                  maxlength="30"
                  show-word-limit
                />
              </template>
            </el-table-column>
            <el-table-column
              label="证书号"
              prop="certificateNo"
              min-width="150px"
            >
              <template #default="{ row }">
                <el-input
                  v-model="row.certificateNo"
                  placeholder="请输入证书号"
                  maxlength="30"
                  show-word-limit
                />
              </template>
            </el-table-column>
            <el-table-column
              label="证书生效日期"
              prop="validStartDate"
              min-width="150px"
            >
              <template #default="{ row }">
                <el-date-picker
                  v-model="row.validStartDate"
                  type="date"
                  placeholder="选择生效日期"
                  value-format="YYYY-MM-DD"
                  format="YYYY-MM-DD"
                  style="width: 100%"
                />
              </template>
            </el-table-column>
            <el-table-column
              label="证书失效日期"
              prop="validEndDate"
              min-width="150px"
            >
              <template #default="{ row }">
                <el-date-picker
                  v-model="row.validEndDate"
                  type="date"
                  placeholder="选择证书失效日期"
                  value-format="YYYY-MM-DD"
                  format="YYYY-MM-DD"
                  style="width: 100%"
                />
              </template>
            </el-table-column>
            <el-table-column
              label="附件"
              prop="attachmentUrl"
              min-width="150px"
            >
              <template #default="{ row }">
                <div style="width: 100%">
                  <YunUpload
                    v-model="row.attachmentUrl"
                    :show-file-list="false"
                  />
                  <File
                    v-if="row.attachmentUrl?.length"
                    v-model="row.attachmentUrl"
                  />
                </div>
              </template>
            </el-table-column>
            <el-table-column
              label="备注"
              prop="remark"
              min-width="200px"
            >
              <template #default="{ row }">
                <el-input
                  v-model="row.remark"
                  placeholder="请输入备注"
                  maxlength="50"
                  show-word-limit
                />
              </template>
            </el-table-column>
            <el-table-column
              label="操作"
              fixed="right"
              width="100"
              align="center"
            >
              <template #default="{ $index }">
                <el-button
                  type="danger"
                  size="small"
                  icon="Delete"
                  @click="handleRemoveRow('certificateList', $index)"
                >
                  删除
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>

        <!-- 银行信息 -->
        <!-- <div class="table-header">
					<div class="label">银行信息</div>
				</div> -->
        <div
          class="w-full overflow-x-auto"
          v-show="currentTab === TABS_TYPE_ENUM.BANK_INFO"
        >
          <el-button
            type="primary"
            style="margin-top: 0px; margin-bottom: 12px; float: right"
            :icon="Plus"
            @click="handleAddRow('bankAccountList')"
          >
            添加银行信息
          </el-button>

          <el-table
            :data="formData.bankAccountList || []"
            :border="true"
            stripe
            class="w-full dark:bg-gray-800"
            :header-cell-class-name="'!bg-gray-50 dark:!bg-gray-700'"
            style="min-width: 900px"
          >
            <el-table-column
              type="index"
              label="序号"
              width="60"
              align="center"
              fixed
            />
            <el-table-column
              label="开户行名称"
              prop="bankName"
              min-width="150px"
            >
              <template #default="{ row }">
                <el-input
                  v-model="row.bankName"
                  placeholder="请输入开户行名称"
                  maxlength="30"
                  show-word-limit
                />
              </template>
            </el-table-column>
            <el-table-column
              label="开户行联行号"
              prop="bankCode"
              min-width="150px"
            >
              <template #default="{ row }">
                <el-input
                  v-model="row.bankCode"
                  placeholder="请输入开户行联行号"
                  maxlength="30"
                  show-word-limit
                />
              </template>
            </el-table-column>
            <el-table-column
              label="开户行地址"
              prop="bankAddress"
              min-width="200px"
            >
              <template #default="{ row }">
                <el-input
                  v-model="row.bankAddress"
                  placeholder="请输入开户行地址"
                  maxlength="30"
                  show-word-limit
                />
              </template>
            </el-table-column>
            <el-table-column
              label="开户名"
              prop="accountName"
              min-width="150px"
            >
              <template #default="{ row }">
                <el-input
                  v-model="row.accountName"
                  placeholder="请输入开户名"
                  maxlength="30"
                  show-word-limit
                />
              </template>
            </el-table-column>
            <el-table-column
              label="开户行账号"
              prop="accountNo"
              min-width="200px"
            >
              <template #default="{ row }">
                <el-input
                  v-model="row.accountNo"
                  placeholder="请输入开户行账号"
                  maxlength="30"
                  show-word-limit
                />
              </template>
            </el-table-column>
            <el-table-column
              label="备注"
              prop="remark"
              min-width="200px"
            >
              <template #default="{ row }">
                <el-input
                  v-model="row.remark"
                  placeholder="请输入备注"
                  maxlength="50"
                  show-word-limit
                />
              </template>
            </el-table-column>
            <el-table-column
              label="操作"
              fixed="right"
              width="100"
              align="center"
            >
              <template #default="{ $index }">
                <el-button
                  type="danger"
                  size="small"
                  icon="Delete"
                  @click="handleRemoveRow('bankAccountList', $index)"
                >
                  删除
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
        <!-- 主营物料 -->
        <!-- <div class="table-header">
					<div class="label">主营物料</div>
				</div> -->
        <div
          class="w-full overflow-x-auto"
          v-show="currentTab === TABS_TYPE_ENUM.MATERIAL_INFO"
        >
          <yun-filter
            v-model="searchFilter"
            :fields="fields"
            :showOperation="false"
            :showResetBtn="false"
            :showSubmitBtn="false"
            :collapse="false"
            :showCollapseBtn="false"
            :filterProps="{
              labellimit: 6,
              labelWidth: 70,
              inline: true,
              'label-position': 'left',
            }"
            @submit="handleSearch"
            @reset="handleReset"
            class="yun-filter-wrapper"
          />

          <el-button
            type="primary"
            style="margin-top: 0px; margin-bottom: 12px; float: right"
            :icon="Plus"
            @click="showMaterialSelector"
          >
            添加物料
          </el-button>
          <el-table
            :data="formData.materialList || []"
            :border="true"
            stripe
            class="w-full dark:bg-gray-800"
            :header-cell-class-name="'!bg-gray-50 dark:!bg-gray-700'"
            style="min-width: 800px"
          >
            <el-table-column
              type="index"
              label="序号"
              width="60"
              align="center"
              fixed
            />
            <el-table-column
              label="物料编码"
              prop="materialCode"
              min-width="150px"
            />
            <el-table-column
              label="物料名称"
              prop="materialName"
              min-width="200px"
            />
            <el-table-column
              label="规格型号"
              prop="spec"
              min-width="150px"
            />
            <el-table-column
              label="物流所属分类"
              prop="materialCategoryName"
              min-width="100px"
            />
            <el-table-column
              label="物流一级分类"
              prop="materialCategoryRoot"
              min-width="100px"
            />
            <el-table-column
              label="计量单位"
              prop="unit"
              min-width="100px"
            />
            <!-- <el-table-column
							label="备注"
							prop="materialRemark"
							min-width="200px"
						>
							<template #default="{ row }">
								<el-input
									v-model="row.materialRemark"
									placeholder="请输入备注"
									maxlength="50"
									show-word-limit
								/>
							</template>
						</el-table-column> -->
            <el-table-column
              label="操作"
              fixed="right"
              width="100"
              align="center"
            >
              <template #default="{ $index }">
                <el-button
                  type="danger"
                  size="small"
                  icon="Delete"
                  @click="handleRemoveRow('materialList', $index)"
                >
                  删除
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </el-form>
    </div>
  </yun-drawer>
  <!-- 物料选择抽屉 -->
  <MaterialSelector
    ref="materialSelectorRef"
    @confirm="handleMaterialConfirm"
  />
</template>

<script setup lang="ts">
import { ref, reactive, nextTick } from 'vue';
import { ElMessage } from 'yun-design';
import { Plus } from '@element-plus/icons-vue';
import { saveCompleteInfo, submitRegisterSupplier, submitSupplierInfo, inviteSupplier } from '@/api/lowcode/supply/index';
import type { FormInstance, FormRules } from 'element-plus';
import MaterialSelector from '@/views/lowcode/supply/components/material/index.vue';
import {
  ENTERPRISE_NATURE_ENUM,
  ENTERPRISE_NATURE_OPTIONS,
  SUPPLY_TYPE_OPTIONS,
  SUPPLY_TYPE_ENUM,
  SUPPLY_STATUS_OPTIONS,
  SUPPLY_STATUS_ENUM,
  SUPPLY_CATEGORY_OPTIONS,
  SUPPLY_CATEGORY_ENUM,
  TABS_TYPE_OPTIONS,
  TABS_TYPE_ENUM,
  MODULE_TYPE_ENUM,
  OPERATION_TYPE_ENUM,
  OPERATION_TYPE_OBJ,
} from '@/views/lowcode/supply/const';
import DeptSelector from '@/components/DeptSelector';
import YunUpload from '@/components/YunUpload/index.vue';
import File from '@/views/lowcode/supply/components/file/index.vue';
import { useHandler } from '@/views/lowcode/supply/hooks/useHandler';
import { deptTree } from '/@/api/admin/dept';

const operationType = ref();
const props = defineProps({
  moduleType: {
    type: String,
    default: MODULE_TYPE_ENUM.REGISTER,
  },
});
const { collections, initData, formatAttachment, loading: handlerLoading } = useHandler();
const submitApi = computed(() => {
  // 平台邀请
  if (props.moduleType === MODULE_TYPE_ENUM.PLATFORM && operationType.value === OPERATION_TYPE_ENUM.INVITE) {
    return inviteSupplier;
  }
  // 我的供应商审核
  if (props.moduleType === MODULE_TYPE_ENUM.MY && operationType.value === OPERATION_TYPE_ENUM.AUDIT) {
    return submitSupplierInfo;
  }
  // 注册供应商审核
  if (props.moduleType === MODULE_TYPE_ENUM.REGISTER && operationType.value === OPERATION_TYPE_ENUM.AUDIT) {
    return submitRegisterSupplier;
  }
  // 保存供应商完整信息
  return saveCompleteInfo;
});

// 联系人电话验证规则
const contactPhoneRules = [
  { required: true, message: '请输入联系人电话', trigger: 'blur' },
  {
    pattern: /^1[3-9]\d{9}$/,
    message: '请输入正确的手机号码',
    trigger: 'blur',
  },
];

// 账户手机变更
function handleContactPhoneChange(row: any) {
  // 验证手机号格式
  const phonePattern = /^1[3-9]\d{9}$/;
  if (row.contactPhone && !phonePattern.test(row.contactPhone)) {
    ElMessage.warning('请输入正确的手机号码格式');
    return;
  }

  // 只针对新增的场景
  if (row.generateAccount === 1 && !row?.id) {
    row.loginAccount = row.contactPhone;
  }
}

// 验证联系人电话
function validateContactPhone(row: any, index: number) {
  const fieldName = `contactList.${index}.contactPhone`;
  vForm.value?.validateField(fieldName, (valid) => {
    if (!valid) {
      // eslint-disable-next-line no-console
      console.warn(`联系人电话验证失败: ${row.contactPhone}`);
    }
  });
}

// 生成登录账号和密码
function handleGenerateAccountChange(row: any) {
  if (row.generateAccount === 1) {
    row.password = '123456';
    row.loginAccount = row.contactPhone;
  } else {
    row.password = '';
    row.loginAccount = '';
  }
}

// 密码特殊逻辑
function showPassword(row: any) {
  const { id, generateAccount, oldPassword } = row || {};
  if (!id && generateAccount === 1) {
    return true;
  }
  if (id && !oldPassword && generateAccount === 1) {
    return true;
  }
  return false;
}

const copyMaterialList = ref([]);
const deptSelectorRef = ref();
const emit = defineEmits(['refresh']);
const visible = ref(false);
const loading = ref(false);
const vForm = ref<FormInstance>();
const materialSelectorRef = ref();
const currentTab = ref(TABS_TYPE_ENUM.BASIC_INFO);

const initFormData = () => ({
  supplierName: '',
  supplierCode: '',
  supplierShortName: '',
  supplierType: SUPPLY_TYPE_ENUM.MANUFACTURER,
  supplierStatus: SUPPLY_STATUS_ENUM.QUALIFIED,
  enterpriseNature: ENTERPRISE_NATURE_ENUM.STATE_OWNED,
  supplierCategory: SUPPLY_CATEGORY_ENUM.COMMON,
  socialCreditCode: '',
  registeredCapital: '',
  establishDate: '',
  legalPerson: '',
  legalPersonId: '',
  legalPersonPhone: '',
  serviceStartDate: '',
  serviceEndDate: '',
  registeredAddress: '',
  postalCode: '',
  fax: '',
  annualRevenue: '',
  companyProfile: '',
  businessScope: '',
  remark: '',
  contactList: [],
  certificateList: [],
  bankAccountList: [],
  materialList: [],
  deptIds: undefined,
  letterOfAuthorization: undefined,
  deleteContactIds: [], // 需要删除的联系人ID列表
  deleteCertificateIds: [], // 需要删除的资质ID列表
  deleteBankAccountIds: [], // 需要删除的银行信息ID列表
  deleteMaterialIds: [], // 需要删除的物料ID列表
  // supplierCode: `SC${Date.now()}`,

  isAllDept: null,
  deptNames: '',

  id: '', // 供应商ID 有值代表是编辑状态
  platformSupplierId: '',
  tenantSupplierId: '',
});

const delMap = {
  contactList: 'deleteContactIds',
  certificateList: 'deleteCertificateIds',
  bankAccountList: 'deleteBankAccountIds',
  materialList: 'deleteMaterialIds',
};

const outsideDeptList = ref([]);

const searchFilter = ref({
  materialName: '',
  materialCategoryName: '',
});
const fields = ref([
  {
    label: '物料名称',
    prop: 'materialName',
    component: 'el-input',
    componentAttrs: {
      placeholder: '请输入物料名称',
      clearable: true,
    },
    trim: true,
  },
  {
    label: '物流分类',
    prop: 'materialCategoryName',
    component: 'el-input',
    componentAttrs: {
      placeholder: '请输入物流分类',
      clearable: true,
    },
    trim: true,
  },
]);
const handleSearch = (filter: any) => {
	// eslint-disable-next-line no-console
	console.log('handleSearch', filter)
	const materialName = filter.materialName;
	const materialCategoryName = filter.materialCategoryName;
	formData.materialList = (copyMaterialList.value || []).filter((item: any) => {
		if (materialName && materialCategoryName) {
			return String(item.materialName)?.indexOf(materialName) !== -1 && String(item.materialCategoryName)?.indexOf(materialCategoryName) !== -1;
		} else if (materialName && !materialCategoryName) {
			return String(item.materialName)?.indexOf(materialName) !== -1;
		} else if (!materialName && materialCategoryName) {
			return String(item.materialCategoryName)?.indexOf(materialCategoryName) !== -1;
		} else {
			return true;
		}
	})
}
const handleReset = (filter: any) => {
  // eslint-disable-next-line no-console
  console.log('handleReset', filter);
};

const formData = reactive<Record<string, any>>(initFormData());

const rules = reactive<FormRules>({
	supplierName: [{ required: true, message: '请输入供应商名称', trigger: 'blur' }],
	socialCreditCode: [{ required: true, message: '请输入统一社会信用代码', trigger: 'blur' }],
	supplierCategory: [{ required: !!(props.moduleType === MODULE_TYPE_ENUM.MY), message: '请选择供应商分类', trigger: 'change' }],
	supplierStatus: [{ required: !!(props.moduleType === MODULE_TYPE_ENUM.MY), message: '请选择供应商状态', trigger: 'change' }],
	deptIds: [{ required: !!(props.moduleType === MODULE_TYPE_ENUM.MY), message: '请选择适用组织', trigger: 'change' }],
});

// 关闭处理
function closeHandler() {
  visible.value = false;
}

// 适用组织改变
function handleDeptChange(mode: boolean) {
  if (mode === false) {
    const currentDeptIds = (collections?.value as any)?.deptIds || [];
    deptSelectorRef.value?.setChecked('none', currentDeptIds?.length ? currentDeptIds : undefined);
  } else if (mode === true) {
    deptSelectorRef.value?.setChecked('all');
  }
}

// 回填数据 遍历formData 将data的值赋值给formData
function fillData(data: any) {
  for (const key in formData) {
    if (data[key] !== undefined && formData.hasOwnProperty(key)) {
      if (key === 'tenantSupplierId') {
        formData[key] = formData[key] || formData.id;
      } else {
        formData[key] = data[key];
      }
    }
  }
}


async function getDeptList(name: string = '') {
  const res = await deptTree({ deptType: 'EXTERNAL' });
  outsideDeptList.value = res.data;
}

getDeptList();

// 格式化表单数据
async function formatFrom(row: any) {
  // 初始化表单数据
  Object.assign(formData, initFormData());
  // 如果存在ID 则回填数据
  if (row?.id) {
    await initData(row);
    fillData(collections.value || {});
    copyMaterialList.value = collections.value?.materialList || [];
    handleDeptChange(row.isAllDept);
  }
}

// 处理提交的参数
function handleSubmitData() {
	const params = { ...formData };
	params.letterOfAuthorization = formatAttachment(formData.letterOfAuthorization);
	params.contactList = (formData?.contactList || []).map((item: any) => ({
		...item,
		idAttachment: formatAttachment(item.idAttachment),
	}));
	params.certificateList = (formData?.certificateList || []).map((item: any) => ({
		...item,
		attachmentUrl: formatAttachment(item.attachmentUrl),
	}));
	return params;
}

// 确认处理
async function confirmHandler() {
  try {
    await vForm.value?.validate();
    loading.value = true;
    const params = handleSubmitData();
    await submitApi.value(params);
    ElMessage.success('操作成功');
    emit('refresh');
    closeHandler();
  } catch (error) {
    // eslint-disable-next-line no-console
    console.log('error', error);
    const e: any = typeof error === 'object' && error ? error : {};
    const eKeys = Object.keys(e);
    const eContactList = eKeys.filter((key: string) => key.includes('contactList'));

    const eDepList = eKeys.filter((key: string) => key.includes('deptId'));

    const eLength = eKeys.length;
    if (eLength > 0) {
      const eKey = eKeys[0];
      vForm.value?.scrollToField(eKey);
      if (eContactList?.length === eLength) {
        ElMessage.error('联系人信息标签页存在必填项未填写');
        if (currentTab.value !== TABS_TYPE_ENUM.CONTACT_INFO) {
          currentTab.value = TABS_TYPE_ENUM.CONTACT_INFO;
        }
      } else if (eDepList?.length === eLength) {
        ElMessage.error('部门信息标签页存在必填项未填写');
        if (currentTab.value !== TABS_TYPE_ENUM.CONTACT_INFO) {
          currentTab.value = TABS_TYPE_ENUM.CONTACT_INFO;
        }
      } else {
        if (currentTab.value !== TABS_TYPE_ENUM.BASIC_INFO) {
          currentTab.value = TABS_TYPE_ENUM.BASIC_INFO;
        }
      }
    }
  } finally {
    loading.value = false;
  }
}

// 显示表单
const show = (row?: any) => {
  visible.value = true;
  currentTab.value = TABS_TYPE_ENUM.BASIC_INFO;
  operationType.value = row?.operationType;
  formatFrom(row);
  nextTick(() => {
    vForm.value?.clearValidate();
  });
};

// 添加表格行
function handleAddRow(tableName: string) {
  if (!(formData as any)[tableName]) {
    (formData as any)[tableName] = [];
  }

  const defaultRows: { [key: string]: any } = {
    contactList: { id: '', supplierId: '', contactName: '', contactPhone: '', idCard: '', generateAccount: 0, contactRemark: '' },
    certificateList: {
      id: '',
      supplierId: '',
      certificateType: '',
      certificateName: '',
      certificateNumber: '',
      effectiveDate: '',
      expiryDate: '',
      qualificationRemark: '',
      attachmentUrl: '',
    },
    bankAccountList: { id: '', supplierId: '', bankName: '', bankAddress: '', bankCode: '', accountName: '', accountNumber: '', bankRemark: '' },
    materialList: {
      id: '',
      supplierId: '',
      materialCode: '',
      materialName: '',
      materialCategoryRoot: '',
      unit: '',
      materialCategoryName: '',
      materialId: '',
      spec: '',
    },
  };

  (formData as any)[tableName].push(defaultRows[tableName] || {});
}

// 删除表格行
function handleRemoveRow(tableName: string, index: number) {
  if (!(formData as any)[tableName]) return;

  // 删除的记录 需要添加到删除列表中 只需要ID集合
  const delRecord = (formData as any)[tableName][index];
  const delId = delRecord?.id;
  const delKey = delMap[tableName as keyof typeof delMap];
  if (delId && delKey) {
    if (Array.isArray(formData[delKey])) {
      formData[delKey].push(delId);
    } else {
      formData[delKey] = [delId];
    }
  }

  // 删除表格行
  (formData as any)[tableName].splice(index, 1);

  if (tableName === 'materialList') {
    copyMaterialList.value = formData.materialList;
  }
}

// 显示物料选择器
function showMaterialSelector() {
  materialSelectorRef.value?.show(formData.materialList || []);
}

// 处理物料选择确认
function handleMaterialConfirm(selectedMaterials: any[]) {
  formData.materialList = selectedMaterials.map((item) => ({
    materialCode: item.materialCode,
    materialName: item.materialName,
    spec: item.spec,
    unit: item.unit,
    materialId: item.id,
    materialCategoryRoot: item.materialCategoryRoot,
    materialCategoryName: item.materialCategoryName,
  }));

  copyMaterialList.value = formData.materialList;
}

defineExpose({
  show,
});
</script>

<style lang="scss">
.form-drawer {
  .el-form .el-form-item:last-of-type {
    margin-bottom: 24px !important;
  }
}
</style>

<style lang="scss" scoped>
:deep(.el-form-item) {
  margin-bottom: 18px !important;
}
.yun-filter-wrapper {
  float: left !important;
  margin: 0 !important;
  :deep(.el-form-item__label) {
    margin-bottom: 0px !important;
    line-height: 32px !important;
  }
}
.table-header {
  margin-bottom: 15px;
  margin-top: 30px;

  .label {
    font-weight: bold;
    font-size: 18px;
    padding-left: 8px;
    border-left: 4px solid var(--el-color-primary);
    line-height: 1.2;
  }
}

.table-header:first-of-type {
  margin-top: 0;
}

:deep(.el-form-item__label) {
  font-weight: normal;
}

:deep(.el-table) {
  .el-table__header-wrapper {
    th {
      background-color: var(--el-fill-color-light);
    }
  }
}
</style>
