import { ref, computed } from 'vue';
import moment from 'moment';
import { useDict } from '@/hooks/dict';
import { ElImage } from 'element-plus';
import { Session } from '/@/utils/storage';

const getDict = computed(() => (str) => {
  if (!str) return [];
  const value = useDict(str)?.[str]?.value;
  return value;
});

export const useTable = () => {



  const searchFields = computed(() => [
    {
      prop: 'planCode',
      label: '采购计划编号',
      component: 'el-input',
      componentAttrs: {
        placeholder: '请选择采购计划编号',
        clearable: true,
      }
    },
    {
      prop: 'planName',
      label: '采购计划名称',
      component: 'el-input',
      componentAttrs: {
        placeholder: '请选择采购计划名称',
        clearable: true,
      }
    },
    {
      prop: 'applyOrgId',
      label: '申请部门',
      component: 'el-input',
      componentAttrs: {
        placeholder: '请选择申请部门',
        clearable: true,
      }
    },
    {
      prop: 'applicant',
      label: '申请人',
      component: 'el-input',
      componentAttrs: {
        placeholder: '请选择申请人',
        clearable: true,
      }
    },
    {
      label: '计划招标方式',
      prop: 'planRecruitMethod',
      component: 'el-select',
      enums: getDict.value('plan_recruit_method') || [],
      componentAttrs: {
        placeholder: '请选择计划招标方式',
        clearable: true,
        filterable: true,
        multiple: true,
        collapseTags: true,
      },
    },
    {
      label: '采购方式',
      prop: 'procurementMethod',
      component: 'el-select',
      enums: getDict.value('procurement_method') || [],
      componentAttrs: {
        placeholder: '请选择采购方式',
        clearable: true,
        filterable: true,
        multiple: true,
        collapseTags: true,
      },
    },
    {
      label: '单据状态',
      prop: 'status',
      component: 'el-select',
      enums: getDict.value('plan_status') || [],
      componentAttrs: {
        placeholder: '请选择单据状态',
        clearable: true,
        filterable: true,
        multiple: true,
        collapseTags: true,
      },
    },
  ]);

  const columns = computed(() => [
    {
      label: '序号',
      type: 'index',
    },
    {
      label: '采购计划编号',
      prop: 'planCode',
    },
    {
      label: '采购计划名称',
      prop: 'planName',
    },
    {
      label: '服务类型ID',
      prop: 'serviceTypeId',
      formatter(row){
        const value = row['serviceTypeId'];
        const options = [{'label':'select 1','value':'1'},{'label':'select 2','value':'2'},{'label':'select 3','value':'3'}];
        if (Array.isArray(value)) {
          return value.map((item) => options?.find((opt) => opt.value == item)?.label).join('、');
        }
        return options?.find((item) => item.value == value)?.label || '--';
      }
    },
    {
      label: '申请日期',
      prop: 'applyDate',
    },
    {
      label: '申请部门',
      prop: 'applyOrgId',
      formatter(row){
        const value = row['applyOrgId'];
        const options = [{'label':'select 1','value':'1','children':[{'label':'child 1','value':'11'}]},{'label':'select 2','value':'2'},{'label':'select 3','value':'3'}];
        if (!value) return '--';

        // 如果是数组，说明是多选
        if (Array.isArray(value)) {
          return value
            .map((v) => {
              // 如果是字符串，说明是逗号分隔的路径
              if (typeof v === 'string') {
                const path = v.split(',');
                let currentOptions = options;
                const labels = [];

                // 遍历路径找到对应的标签
                for (const val of path) {
                  const option = currentOptions.find((opt) => opt.value == val);
                  if (option) {
                    labels.push(option.label);
                    currentOptions = option.children || [];
                  }
                }
                return labels.join(' / ');
              }
              return v;
            })
            .join('、');
        }

        // 如果是字符串，说明是单选
        if (typeof value === 'string') {
          // 递归查找函数
          const findPath = (options, targetValue, path = []) => {
            for (const option of options) {
              // 如果找到目标值，返回完整路径
              if (option.value == targetValue) {
                return [...path, option.label];
              }
              // 如果有子节点，递归查找
              if (option.children && option.children.length > 0) {
                const result = findPath(option.children, targetValue, [...path, option.label]);
                if (result) return result;
              }
            }
            return null;
          };

          // 查找完整路径
          const path = findPath(options, value);
          return path ? path.join(' / ') : '--';
        }

        return '--';
      }
    },
    {
      label: '申请人',
      prop: 'applicant',
    },
    {
      label: '申请人电话',
      prop: 'applicantPhone',
    },
    {
      label: '采购部门',
      prop: 'procurementOrgId',
      formatter(row){
        const value = row['procurementOrgId'];
        const options = [{'label':'select 1','value':'1','children':[{'label':'child 1','value':'11'}]},{'label':'select 2','value':'2'},{'label':'select 3','value':'3'}];
        if (!value) return '--';

        // 如果是数组，说明是多选
        if (Array.isArray(value)) {
          return value
            .map((v) => {
              // 如果是字符串，说明是逗号分隔的路径
              if (typeof v === 'string') {
                const path = v.split(',');
                let currentOptions = options;
                const labels = [];

                // 遍历路径找到对应的标签
                for (const val of path) {
                  const option = currentOptions.find((opt) => opt.value == val);
                  if (option) {
                    labels.push(option.label);
                    currentOptions = option.children || [];
                  }
                }
                return labels.join(' / ');
              }
              return v;
            })
            .join('、');
        }

        // 如果是字符串，说明是单选
        if (typeof value === 'string') {
          // 递归查找函数
          const findPath = (options, targetValue, path = []) => {
            for (const option of options) {
              // 如果找到目标值，返回完整路径
              if (option.value == targetValue) {
                return [...path, option.label];
              }
              // 如果有子节点，递归查找
              if (option.children && option.children.length > 0) {
                const result = findPath(option.children, targetValue, [...path, option.label]);
                if (result) return result;
              }
            }
            return null;
          };

          // 查找完整路径
          const path = findPath(options, value);
          return path ? path.join(' / ') : '--';
        }

        return '--';
      }
    },
    {
      label: '计划招标方式',
      prop: 'planRecruitMethod',
      formatter(row){
        const value = row['planRecruitMethod'];
        const options = getDict.value('plan_recruit_method') || [];;
        if (Array.isArray(value)) {
          return value.map((item) => options?.find((opt) => opt.value == item)?.label).join('、');
        }
        return options?.find((item) => item.value == value)?.label || '--';
      }
    },
    {
      label: '期限到货日期',
      prop: 'periodStartDate',
    },
    {
      label: '采购方式',
      prop: 'procurementMethod',
      formatter(row){
        const value = row['procurementMethod'];
        const options = getDict.value('procurement_method') || [];;
        if (Array.isArray(value)) {
          return value.map((item) => options?.find((opt) => opt.value == item)?.label).join('、');
        }
        return options?.find((item) => item.value == value)?.label || '--';
      }
    },
    {
      label: '资金来源',
      prop: 'fundSource',
    },
    {
      label: '预算金额',
      prop: 'budgetAmount',
    },
    {
      label: '计划来源方式',
      prop: 'planSource',
    },
    {
      label: '单据状态',
      prop: 'status',
      formatter(row){
        const value = row['status'];
        const options = getDict.value('plan_status') || [];;
        if (Array.isArray(value)) {
          return value.map((item) => options?.find((opt) => opt.value == item)?.label).join('、');
        }
        return options?.find((item) => item.value == value)?.label || '--';
      }
    },
    {
      label: '创建人名称',
      prop: 'createByName',
    },
    {
      label: '修改人名称',
      prop: 'updateByName',
    },
    {
      label: '当前审批人',
      prop: 'currentApprover',
    },
    {
      label: '代理机构表ID',
      prop: 'baseAgentOrgId',
    },
    {
      prop: "action",
      fixed: "right",
      width: 150,
      label: "操作",
    },
  ])
  return {
    columns,
    searchFields,
  };
};