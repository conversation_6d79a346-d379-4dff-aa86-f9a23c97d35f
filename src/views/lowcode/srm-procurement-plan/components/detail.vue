<template>
  <yun-drawer
    v-model="visible"
    :show-cancel-button="false"
    destroy-on-close
    title="详情"
    size="X-large"
    @confirm="handleConfirm"
  >
    <Detail :groups="processedGroups" :data="form"/>
  </yun-drawer>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import Detail from '@/components/Detail/index.vue';
import { getObj } from '@/api/lowcode/srm-procurement-plan/index';

interface FormData {
  [key: string]: any;
}

// 静态生成的分组配置
const processedGroups = [
  {
    title: "",
    columns: [
      {
        prop: 'planCode',
        label: '采购计划编号',
        type: 'text'
      },
      {
        prop: 'planName',
        label: '采购计划名称',
        type: 'text'
      },
      {
        prop: 'serviceTypeId',
        label: '服务类型ID',
        type: 'enums',
        attrs: {
          options: [
            {
              label: 'select 1',
              value: 1
            },
            {
              label: 'select 2',
              value: 2
            },
            {
              label: 'select 3',
              value: 3
            }
          ],
          dataSourceType: 'custom',
          dictType: ''
        }
      },
      {
        prop: 'applyDate',
        label: '申请日期',
        type: 'text'
      },
      {
        prop: 'applyOrgId',
        label: '申请部门',
        type: 'enums',
        attrs: {
          options: [
            {
              label: 'select 1',
              value: 1,
              children: [
                {
                  label: 'child 1',
                  value: 11
                }
              ]
            },
            {
              label: 'select 2',
              value: 2
            },
            {
              label: 'select 3',
              value: 3
            }
          ],
          dataSourceType: 'custom',
          dictType: ''
        }
      },
      {
        prop: 'applicant',
        label: '申请人',
        type: 'text'
      },
      {
        prop: 'applicantPhone',
        label: '申请人电话',
        type: 'text'
      },
      {
        prop: 'procurementOrgId',
        label: '采购部门',
        type: 'enums',
        attrs: {
          options: [
            {
              label: 'select 1',
              value: 1,
              children: [
                {
                  label: 'child 1',
                  value: 11
                }
              ]
            },
            {
              label: 'select 2',
              value: 2
            },
            {
              label: 'select 3',
              value: 3
            }
          ],
          dataSourceType: 'custom',
          dictType: ''
        }
      },
      {
        prop: 'planRecruitMethod',
        label: '计划招标方式',
        type: 'enums',
        attrs: {
          options: [],
          dataSourceType: 'dict',
          dictType: 'plan_recruit_method'
        }
      },
      {
        prop: 'periodStartDate',
        label: '期限到货日期',
        type: 'text'
      },
      {
        prop: 'procurementMethod',
        label: '采购方式',
        type: 'enums',
        attrs: {
          options: [],
          dataSourceType: 'dict',
          dictType: 'procurement_method'
        }
      },
      {
        prop: 'fundSource',
        label: '资金来源',
        type: 'text'
      },
      {
        prop: 'budgetAmount',
        label: '预算金额',
        type: 'text'
      },
      {
        prop: 'planSource',
        label: '计划来源方式',
        type: 'text'
      },
      {
        prop: 'status',
        label: '单据状态',
        type: 'enums',
        attrs: {
          options: [],
          dataSourceType: 'dict',
          dictType: 'plan_status'
        }
      }
    ]
  },
  {
    title: '物料计划明细',
    columns: [
      {
        prop: 'srmProcurementPlanDetail',
        label: '',
        type: 'table',
        attrs: {
          showIndex: true,
          columns: [
            {
              prop: 'materialCode',
              label: '物料编码',
              type: 'text'
            },
            {
              prop: 'materialName',
              label: '物料名称',
              type: 'text'
            },
            {
              prop: 'specModel',
              label: '规格型号',
              type: 'text'
            },
            {
              prop: 'unit',
              label: '单位',
              type: 'text'
            },
            {
              prop: 'serviceTypeId',
              label: '服务类型ID',
              type: 'enums',
              attrs: {
                options: [
                  {
                    label: 'select 1',
                    value: 1
                  },
                  {
                    label: 'select 2',
                    value: 2
                  },
                  {
                    label: 'select 3',
                    value: 3
                  }
                ],
                dataSourceType: 'custom',
                dictType: ''
              }
            },
            {
              prop: 'requiredQuantity',
              label: '需求数量',
              type: 'text'
            },
            {
              prop: 'unitPrice',
              label: '计划单价',
              type: 'text'
            },
            {
              prop: 'totalPrice',
              label: '计划总价',
              type: 'text'
            },
            {
              prop: 'listPrice',
              label: '上单价',
              type: 'text'
            },
            {
              prop: 'budgetPrice',
              label: '预算价',
              type: 'text'
            },
            {
              prop: 'note',
              label: '备注',
              type: 'text'
            }
          ]
        }
      }
    ]
  }
];

const visible = ref(false);
const form = ref<FormData>({});

async function handleConfirm(done: () => void) {
  done();
}

async function show(row: FormData) {
  const res = await getObj(row.id);
  form.value = res.data;
  visible.value = true;
}

defineExpose({
  show,
});
</script>

<style scoped lang="scss">
</style>