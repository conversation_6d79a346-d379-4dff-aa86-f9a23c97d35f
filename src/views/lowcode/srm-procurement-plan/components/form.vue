<template>
  <yun-drawer
    v-model="visible"
    destroy-on-close
    modal
    custom-class="form-drawer"
    close-on-click-modal
    confirm-button-text="确定"
    cancel-button-text="取消"
    :confirm-button-disabled="loading"
    :title="title"
    :size="size"
    @confirm="confirmHandler"
    @close="closeHandler"
  >
    <el-form
      ref="vForm"
      :model="formData"
      :rules="rules"
      :label-width="120"
      label-position="top"
    >
      <el-row :gutter="12">
        <el-col :span="8">
          <el-form-item label="采购计划编号" prop="planCode">
            <el-input
              v-model="formData.planCode"
              type="text"
              clearable
              icon="custom-search"
            />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="采购计划名称" prop="planName">
            <el-input
              v-model="formData.planName"
              type="text"
              clearable
              icon="custom-search"
            />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="服务类型ID" prop="serviceTypeId">
            <el-select
              v-model="formData.serviceTypeId"
              style="width: 100%"
              clearable
              :multipleLimit="0"
            >
              <el-option label="select 1" value="1" />
              <el-option label="select 2" value="2" />
              <el-option label="select 3" value="3" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="申请日期" prop="applyDate">
            <el-date-picker
              v-model="formData.applyDate"
              style="width: 100%"
              type="date"
              autoFullWidth
              clearable
              format="YYYY-MM-DD"
              valueFormat="YYYY-MM-DD"
            />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="申请部门" prop="applyOrgId">
            <el-cascader
              v-model="formData.applyOrgId"
              style="width: 100%"
              clearable
              showAllLevels
              :options="[
                {
                  label: 'select 1',
                  value: '1',
                  children: [{ label: 'child 1', value: '11' }],
                },
                { label: 'select 2', value: '2' },
                { label: 'select 3', value: '3' },
              ]"
              :props="{
                multiple: false,
                value: 'value',
                label: 'label',
                children: 'children',
                checkStrictly: false,
                emitPath: false,
                expandTrigger: 'hover',
              }"
            />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="申请人" prop="applicant">
            <el-input
              v-model="formData.applicant"
              type="text"
              clearable
              icon="custom-search"
            />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="申请人电话" prop="applicantPhone">
            <el-input
              v-model="formData.applicantPhone"
              type="text"
              clearable
              icon="custom-search"
            />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="采购部门" prop="procurementOrgId">
            <el-cascader
              v-model="formData.procurementOrgId"
              style="width: 100%"
              clearable
              showAllLevels
              :options="[
                {
                  label: 'select 1',
                  value: '1',
                  children: [{ label: 'child 1', value: '11' }],
                },
                { label: 'select 2', value: '2' },
                { label: 'select 3', value: '3' },
              ]"
              :props="{
                multiple: false,
                value: 'value',
                label: 'label',
                children: 'children',
                checkStrictly: false,
                emitPath: false,
                expandTrigger: 'hover',
              }"
            />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="计划招标方式" prop="planRecruitMethod">
            <el-select
              v-model="formData.planRecruitMethod"
              style="width: 100%"
              clearable
              :multipleLimit="0"
            >
              <el-option
                v-for="option in getDict('plan_recruit_method')"
                :key="option.value"
                :label="option.label"
                :value="option.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="期限到货日期" prop="periodStartDate">
            <el-date-picker
              v-model="formData.periodStartDate"
              style="width: 100%"
              type="date"
              autoFullWidth
              clearable
              format="YYYY-MM-DD"
              valueFormat="YYYY-MM-DD"
            />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="采购方式" prop="procurementMethod">
            <el-select
              v-model="formData.procurementMethod"
              style="width: 100%"
              clearable
              :multipleLimit="0"
            >
              <el-option
                v-for="option in getDict('procurement_method')"
                :key="option.value"
                :label="option.label"
                :value="option.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="资金来源" prop="fundSource">
            <el-input
              v-model="formData.fundSource"
              type="text"
              clearable
              icon="custom-search"
            />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="预算金额" prop="budgetAmount">
            <el-input-number
              v-model="formData.budgetAmount"
              :defaultValue="0"
              :min="-100000000000"
              :max="100000000000"
              :precision="0"
              :step="1"
              controlsPosition="right"
            />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="计划来源方式" prop="planSource">
            <el-input
              v-model="formData.planSource"
              type="text"
              clearable
              icon="custom-search"
            />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="单据状态" prop="status">
            <el-select
              v-model="formData.status"
              style="width: 100%"
              clearable
              :multipleLimit="0"
            >
              <el-option
                v-for="option in getDict('plan_status')"
                :key="option.value"
                :label="option.label"
                :value="option.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <div class="table-header">
        <div class="label">物料计划明细</div>
      </div>
      <div class="w-full overflow-x-auto">
        <el-table
          :data="formData.srmProcurementPlanDetailList || []"
          :border="true"
          stripe
          class="w-full dark:bg-gray-800"
          :header-cell-class-name="'!bg-gray-50 dark:!bg-gray-700'"
          style="min-width: 600px"
          :show-overflow-tooltip="false"
        >
          <el-table-column type="index" label="序号" width="60" align="center" fixed />
          <el-table-column label="物料编码" prop="materialCode" min-width="200px">
            <template #default="{ row }">
              <el-input
                v-model="row.materialCode"
                type="text"
                clearable
                icon="custom-search"
              />
            </template>
          </el-table-column>
          <el-table-column label="物料名称" prop="materialName" min-width="200px">
            <template #default="{ row }">
              <el-input
                v-model="row.materialName"
                type="text"
                clearable
                icon="custom-search"
              />
            </template>
          </el-table-column>
          <el-table-column label="规格型号" prop="specModel" min-width="200px">
            <template #default="{ row }">
              <el-input
                v-model="row.specModel"
                type="text"
                clearable
                icon="custom-search"
              />
            </template>
          </el-table-column>
          <el-table-column label="单位" prop="unit" min-width="200px">
            <template #default="{ row }">
              <el-input v-model="row.unit" type="text" clearable icon="custom-search" />
            </template>
          </el-table-column>
          <el-table-column label="服务类型ID" prop="serviceTypeId" min-width="200px">
            <template #default="{ row }">
              <el-select
                v-model="row.serviceTypeId"
                style="width: 100%"
                clearable
                :multipleLimit="0"
              >
                <el-option label="select 1" value="1" />
                <el-option label="select 2" value="2" />
                <el-option label="select 3" value="3" />
              </el-select>
            </template>
          </el-table-column>
          <el-table-column label="需求数量" prop="requiredQuantity" min-width="200px">
            <template #default="{ row }">
              <el-input-number
                v-model="row.requiredQuantity"
                :defaultValue="0"
                :min="-100000000000"
                :max="100000000000"
                :precision="0"
                :step="1"
                controlsPosition="right"
              />
            </template>
          </el-table-column>
          <el-table-column label="计划单价" prop="unitPrice" min-width="200px">
            <template #default="{ row }">
              <el-input-number
                v-model="row.unitPrice"
                :defaultValue="0"
                :min="-100000000000"
                :max="100000000000"
                :precision="0"
                :step="1"
                controlsPosition="right"
              />
            </template>
          </el-table-column>
          <el-table-column label="计划总价" prop="totalPrice" min-width="200px">
            <template #default="{ row }">
              <el-input-number
                v-model="row.totalPrice"
                :defaultValue="0"
                :min="-100000000000"
                :max="100000000000"
                :precision="0"
                :step="1"
                controlsPosition="right"
              />
            </template>
          </el-table-column>
          <el-table-column label="上单价" prop="listPrice" min-width="200px">
            <template #default="{ row }">
              <el-input-number
                v-model="row.listPrice"
                :defaultValue="0"
                :min="-100000000000"
                :max="100000000000"
                :precision="0"
                :step="1"
                controlsPosition="right"
              />
            </template>
          </el-table-column>
          <el-table-column label="预算价" prop="budgetPrice" min-width="200px">
            <template #default="{ row }">
              <el-input-number
                v-model="row.budgetPrice"
                :defaultValue="0"
                :min="-100000000000"
                :max="100000000000"
                :precision="0"
                :step="1"
                controlsPosition="right"
              />
            </template>
          </el-table-column>
          <el-table-column label="备注" prop="note" min-width="200px">
            <template #default="{ row }">
              <el-input v-model="row.note" type="textarea" :rows="3" />
            </template>
          </el-table-column>
          <el-table-column label="操作" fixed="right" width="100" align="center">
            <template #default="{ $index }">
              <el-button
                type="danger"
                size="small"
                icon="Delete"
                @click="handleRemoveRow('srmProcurementPlanDetailList', $index)"
                >删除</el-button
              >
            </template>
          </el-table-column>
        </el-table>
      </div>
      <el-button
        style="width: 100%; margin-top: 5px"
        :icon="Plus"
        @click="handleAddRow('srmProcurementPlanDetailList')"
      >
        添加
      </el-button>
    </el-form>
  </yun-drawer>
</template>

<script setup lang="ts">
import { ref, reactive, computed, nextTick } from 'vue';
import { ElMessage } from 'yun-design';
import { Plus, Delete } from '@element-plus/icons-vue';
import { useDict } from '@/hooks/dict';
import { addObj, putObj, getObj } from '@/api/lowcode/srm-procurement-plan/index';
import { useUserInfo } from '@/stores/userInfo';
import { rule } from '@/utils/validate';
import { Session } from '@/utils/storage';
import moment from 'moment';

// 状态管理
const visible = ref(false);
const loading = ref(false);
const isEdit = ref(false);
const vForm = ref();
const formData = reactive({});
const rules = reactive({
  planName: [
    {
      required: true,
      message: '此项必填',
      trigger: ['blur', 'change'],
    },
  ],
  serviceTypeId: [
    {
      required: true,
      message: '此项必填',
      trigger: ['blur', 'change'],
    },
  ],
  applyDate: [
    {
      required: true,
      message: '此项必填',
      trigger: ['blur', 'change'],
    },
  ],
  applyOrgId: [
    {
      required: true,
      message: '此项必填',
      trigger: ['blur', 'change'],
    },
  ],
  applicant: [
    {
      required: true,
      message: '此项必填',
      trigger: ['blur', 'change'],
    },
  ],
  applicantPhone: [
    {
      required: true,
      message: '此项必填',
      trigger: ['blur', 'change'],
    },
    {
      validator: rule['mobilePhone'],
      trigger: ['blur', 'change'],
    },
  ],
  procurementOrgId: [
    {
      required: true,
      message: '此项必填',
      trigger: ['blur', 'change'],
    },
  ],
});

// 计算属性
const title = computed(() => (isEdit.value ? '编辑' : '新增'));
const size = computed(() => 'X-large');

// 用户信息
const userStore = useUserInfo();
const emits = defineEmits(['getData']);

// 为每个API组件创建响应式变量

// 为每个API组件创建获取数据的方法

// 初始化时获取所有API数据

// 关闭处理
function closeHandler() {
  visible.value = false;
}

// 格式化表单数据
async function formatFrom(row: Record<string, any>) {
  for (let key in formData) {
    delete formData[key];
  }
  if (!row.id) return;
  const res = await getObj(row.id);
  Object.assign(formData, res.data);
  if (Array.isArray(row.applyOrgId)) {
    formData.applyOrgId = row.applyOrgId.map((item) => {
      if (Array.isArray(item)) {
        return item;
      }
      return item.split(',');
    });
  }
  if (Array.isArray(row.procurementOrgId)) {
    formData.procurementOrgId = row.procurementOrgId.map((item) => {
      if (Array.isArray(item)) {
        return item;
      }
      return item.split(',');
    });
  }
}

// 格式化提交参数
function formatParams() {
  const baseParams = {};

  // 处理特殊字段类型
  if (Array.isArray(formData.applyOrgId)) {
    baseParams.applyOrgId = formData.applyOrgId.map((item) => {
      if (Array.isArray(item)) {
        return item.join(',');
      }
      return item;
    });
  }
  if (Array.isArray(formData.procurementOrgId)) {
    baseParams.procurementOrgId = formData.procurementOrgId.map((item) => {
      if (Array.isArray(item)) {
        return item.join(',');
      }
      return item;
    });
  }

  return {
    ...formData,
    ...baseParams,
  };
}

// 确认处理
async function confirmHandler(done: () => void, loading: { value: boolean }) {
  try {
    await vForm.value?.validate();
    loading.value = true;
    const api = isEdit.value ? putObj : addObj;
    const params = formatParams();
    await api(params);
    done();
    emits('getData', formData);
  } catch (error) {
    ElMessage.error('表单验证或提交失败');
  } finally {
    loading.value = false;
  }
}

// 显示表单
function show(row?: Record<string, any>) {
  visible.value = true;
  isEdit.value = !!row;
  formatFrom(row || {});
  nextTick(() => {
    vForm.value?.clearValidate();
  });
}

// 日期时间范围默认值
const defaultTime = [new Date(2000, 1, 1, 0, 0, 0), new Date(2000, 2, 1, 23, 59, 59)];

// 字典数据
const getDict = computed(() => (str: string) => {
  if (!str) return [];
  const value = useDict(str)?.[str]?.value;
  return value;
});

// 生成明细表新行的初始数据
function getEnhancedTableRow(widget: Widget): Record<string, any> {
  const row: Record<string, any> = {};
  if (Array.isArray(widget.widgetList)) {
    for (const colWidget of widget.widgetList) {
      const name = colWidget.options?.name;
      if (!name) continue;
      let value = colWidget.options?.defaultValue;
      if (value === undefined) {
        if (colWidget.type === 'number') value = 0;
        else value = '';
      }
      row[name] = value;
    }
  }
  return row;
}

// 添加明细表行
function handleAddRow(tableName: string) {
  if (!formData[tableName]) {
    formData[tableName] = [];
  }

  formData[tableName].push({});
}

// 删除明细表行
function handleRemoveRow(tableName: string, index: number) {
  if (!formData[tableName]) return;
  formData[tableName].splice(index, 1);
}

// 暴露方法
defineExpose({
  show,
  formData,
  rules,
});
</script>

<style lang="scss">
.form-drawer {
  .el-form .el-form-item:last-of-type {
    margin-bottom: 24px !important;
  }
}
</style>

<style lang="scss" scoped>
.el-form-item {
  margin-bottom: 18px;
}

.table-header {
  margin-bottom: 15px;
  .label {
    font-weight: bold;
    font-size: 18px;
    padding-left: 8px;
    border-left: 4px solid var(--el-color-primary);
    line-height: 1.2;
  }
}

:deep(.el-form-item__label) {
  font-weight: normal;
}

:deep(.el-table) {
  .el-table__header-wrapper {
    th {
      background-color: var(--el-fill-color-light);
    }
  }
}
</style>
