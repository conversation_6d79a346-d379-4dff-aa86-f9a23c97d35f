import { ref, computed } from 'vue';
import moment from 'moment';
import { useDict } from '@/hooks/dict';
import { ElImage } from 'element-plus';
import { Session } from '/@/utils/storage';
import { EXPERT_CLASSIFIER, EXPERT_TYPE, STATUS_LIST } from './const.js';

const getDict = computed(() => (str) => {
  if (!str) return [];
  const value = useDict(str)?.[str]?.value;
  return value;
});
function fetchAddressOptions(options) {
  let requestUrl = 'https://oss-public.yunlizhi.cn/frontend/yun-design/area.json';
  const method = 'GET';

  // 构建请求参数
  const requestParams = {

  };

  const token = Session.getToken();
  const tenantId = Session.getTenant();
  // 构建请求配置
  const requestConfig = {
    method,
    headers: {
      'Content-Type': 'application/json',
      'TENANT-ID': tenantId,
      AUTHORIZATION: `Bearer ${token}`,
    },
  };

  // 如果是GET请求，将参数附加到URL
  if (method === 'GET') {
    const queryParams = new URLSearchParams();
    Object.entries(requestParams).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        queryParams.append(key, String(value));
      }
    });
    const queryString = queryParams.toString();
    if (queryString) {
      requestUrl += (requestUrl.includes('?') ? '&' : '?') + queryString;
    }
  } else {
    // 非GET请求，将参数放在body中
    requestConfig.body = JSON.stringify(requestParams);
  }

  // 发送请求并处理响应
  fetch(requestUrl, requestConfig)
    .then(response => {
      if (!response.ok) {
        throw new Error('API request failed');
      }
      return response.json();
    })
    .then(data => {
      // 根据配置的路径获取数据
      let result = data;


      // 处理数据
      const processData = (items) => {
        return items.map(item => ({
          label: item['areaName'],
          value: item['areaCode'],

          children: item['children'] ?
            processData(item['children']) : undefined
        }));
      };

      options.value = Array.isArray(result) ? processData(result) : [];
    })
    .catch(error => {
      console.error('Failed to fetch API options:', error);
      options.value = [];
    });
}
export const useTable = () => {
  const addressOptions = ref([]);
  fetchAddressOptions(addressOptions);

  const searchFields = computed(() => [
    {
      prop: 'name',
      label: '姓名',
      component: 'el-input',
      componentAttrs: {
        placeholder: '请选择姓名',
        clearable: true,
      }
    },
    {
      prop: 'phone',
      label: '手机号',
      component: 'el-input',
      componentAttrs: {
        placeholder: '请选择手机号',
        clearable: true,
      }
    },
    {
      label: '专家分类',
      prop: 'expertCategory',
      component: 'el-select',
      enums: EXPERT_CLASSIFIER,
      componentAttrs: {
        placeholder: '请选择专家分类',
        clearable: true,
        filterable: true,
        multiple: true,
        collapseTags: true,
      },
    },
    {
      label: '状态',
      prop: 'status',
      component: 'el-select',
      enums: STATUS_LIST,
      componentAttrs: {
        placeholder: '请选择状态',
        clearable: true,
        filterable: true,
        multiple: true,
        collapseTags: true,
      },
    },
  ]);

  const columns = computed(() => [
    {
      label: '序号',
      type: 'index',
    },
    {
      label: '专家编码',
      prop: 'expertCode',
    },
    {
      label: '姓名',
      prop: 'name',
    },
    {
      label: '手机号',
      prop: 'phone',
    },
    {
      label: '专家分类',
      prop: 'expertCategory',
			enums: EXPERT_CLASSIFIER
    },
    {
      label: '登录账号',
      prop: 'loginAccount',
    },
    {
      label: '所在地',
      prop: 'address',
      formatter(row){
        const value = row['address'];
        const options = addressOptions.value;
        if (!value) return '-';

        // 如果是数组，说明是多选
        if (Array.isArray(value)) {
          return value
            .map((v) => {
              // 如果是字符串，说明是逗号分隔的路径
              if (typeof v === 'string') {
                const path = v.split(',');
                let currentOptions = options;
                const labels = [];

                // 遍历路径找到对应的标签
                for (const val of path) {
                  const option = currentOptions.find((opt) => opt.value == val);
                  if (option) {
                    labels.push(option.label);
                    currentOptions = option.children || [];
                  }
                }
                return labels.join(' / ');
              }
              return v;
            })
            .join('、');
        }

        // 如果是字符串，说明是单选
        if (typeof value === 'string') {
          // 递归查找函数
          const findPath = (options, targetValue, path = []) => {
            for (const option of options) {
              // 如果找到目标值，返回完整路径
              if (option.value == targetValue) {
                return [...path, option.label];
              }
              // 如果有子节点，递归查找
              if (option.children && option.children.length > 0) {
                const result = findPath(option.children, targetValue, [...path, option.label]);
                if (result) return result;
              }
            }
            return null;
          };

          // 查找完整路径
          const path = findPath(options, value);
          return path ? path.join(' / ') : '-';
        }

        return '-';
      }
    },
    {
      label: '身份证号码',
      prop: 'idNumber',
    },
    {
      label: '专家类型',
      prop: 'expertType',
      formatter(row){
        const value = row['expertType']?.split(',');
        const options = EXPERT_TYPE;
        if (Array.isArray(value)) {
          return value.map((item) => options?.find((opt) => opt.value == item)?.label).join('、');
        }
        return options?.find((item) => item.value == value)?.label || '-';
      }
    },
    {
      label: '关联用户ID',
      prop: 'userId',
    },
    {
      label: '邮箱',
      prop: 'email',
    },
    {
      label: '状态',
      prop: 'status',
      enums: STATUS_LIST
    },
    {
      label: '创建人',
      prop: 'createBy',
    },
    {
      label: '创建时间',
      prop: 'createTime',
      formatter(row) {
        return row.createTime?.split(' ')[0];
      }
    },
    {
      label: '修改人',
      prop: 'updateBy',
    },
    {
      label: '修改时间',
      prop: 'updateTime',
      formatter(row) {
        return row.updateTime?.split(' ')[0];
      }
    },
    {
      prop: "action",
      fixed: "right",
      width: 150,
      label: "操作",
    },
  ])
  return {
    columns,
    searchFields,
  };
};
