<template>
  <yun-drawer
    v-model="visible"
    destroy-on-close
    modal
    custom-class="form-drawer"
    close-on-click-modal
    confirm-button-text="确定"
    cancel-button-text="取消"
    :confirm-button-disabled="loading"
    :title="title"
    :size="size"
    @confirm="confirmHandler"
    @close="closeHandler"
  >
    <el-form
      ref="vForm"
      :model="formData"
      :rules="rules"
      :label-width="120"
      label-position="top"
    >
      <el-row :gutter="12">
        <el-col :span="8">
          <el-form-item
            label="专家分类"
            prop="expertCategory"
          >
            <el-radio-group v-model="formData.expertCategory">
              <el-radio
                v-for="option in EXPERT_CLASSIFIER"
                :key="option.value"
                :label="option.value"
                >{{ option.label }}</el-radio
              >
            </el-radio-group>
          </el-form-item>
        </el-col>
        <el-col
          :span="8"
          v-if="formData.expertCategory !== 'INTERNAL' || isEdit"
        >
          <el-form-item
            label="姓名"
            prop="name"
          >
            <el-input
              placeholder="请输入"
              v-model="formData.name"
              type="text"
              clearable
              icon="custom-search"
            />
          </el-form-item>
        </el-col>
        <el-col
          :span="8"
          v-else
        >
          <el-form-item
            label="姓名"
            prop="user"
          >
            <el-select
              v-model="formData.user"
              filterable
              style="width: 100%"
              @change="changeUser"
              clearable
              :multipleLimit="0"
              value-key="userId"
            >
              <el-option
                v-for="option in userList"
                :key="option.value"
                :label="option.name"
                :value="option"
              >
                <span>{{ option.name || '-' }}/{{ option.username }}</span>
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <!--				<el-col :span="8">-->
        <!--					<el-form-item-->
        <!--						label="专家编码"-->
        <!--						prop="expertCode"-->
        <!--					>-->
        <!--						<el-input-->
        <!--							v-model="formData.expertCode"-->
        <!--							placeholder="请输入"-->
        <!--							type="text"-->
        <!--							clearable-->
        <!--							icon="custom-search"-->
        <!--						/>-->
        <!--					</el-form-item>-->
        <!--				</el-col>-->
        <el-col :span="8">
          <el-form-item
            label="手机号"
            prop="phone"
          >
            <el-input
              v-model="formData.phone"
              :disabled="formData.expertCategory === 'INTERNAL' && !isEdit"
              placeholder="请输入"
              type="text"
              clearable
              icon="custom-search"
            />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item
            label="登录账号"
            prop="loginAccount"
          >
            <el-input
              v-model="formData.loginAccount"
              placeholder="请输入"
              :disabled="isEdit || formData.expertCategory === 'INTERNAL'"
              type="text"
              clearable
              icon="custom-search"
            />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item
            label="所在地"
            prop="address"
          >
            <el-cascader
              v-model="formData.address"
              style="width: 100%"
              clearable
              showAllLevels
              :options="addressOptions"
              :props="{
                multiple: false,
                value: 'value',
                label: 'label',
                children: 'children',
                checkStrictly: false,
                emitPath: false,
                expandTrigger: 'hover',
              }"
            />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item
            label="身份证号码"
            prop="idNumber"
          >
            <el-input
              v-model="formData.idNumber"
              placeholder="请输入"
              type="text"
              clearable
              icon="custom-search"
            />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item
            label="专家类型"
            prop="expertType"
          >
            <el-select
              v-model="formData.expertType"
              style="width: 100%"
              clearable
              multiple
              :multipleLimit="0"
            >
              <el-option
                v-for="option in EXPERT_TYPE"
                :key="option.value"
                :label="option.label"
                :value="option.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item
            label="邮箱"
            prop="email"
          >
            <el-input
              v-model="formData.email"
              placeholder="请输入"
              type="text"
              clearable
              icon="custom-search"
            />
          </el-form-item>
        </el-col>

        <el-col :span="8">
          <el-form-item
            label="部门"
            prop="deptTypeId"
          >
            <el-select
              v-model="formData.deptTypeId"
              filterable
              style="width: 100%"
              clearable
            >
              <el-option
                v-for="option in outsideDeptList"
                :key="option.id"
                :label="option.name"
                :value="option.id"
              >
                <span>{{ option.name || '-' }}</span>
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <div class="table-header">
        <div class="label">专家成果表</div>
      </div>
      <div class="w-full overflow-x-auto">
        <el-table
          :data="formData.baseExpertAchievementList || []"
          :border="true"
          stripe
          class="w-full dark:bg-gray-800"
          :header-cell-class-name="'!bg-gray-50 dark:!bg-gray-700'"
          style="min-width: 600px"
          :show-overflow-tooltip="false"
        >
          <el-table-column
            type="index"
            label="序号"
            width="60"
            align="center"
            fixed
          />
          <el-table-column
            label="专业领域"
            prop="professionalField"
            min-width="200px"
          >
            <template #default="{ row }">
              <el-input
                v-model="row.professionalField"
                type="textarea"
                clearable
                icon="custom-search"
                maxlength="30"
                show-word-limit
                placeholder="最多输入30个字符"
              />
            </template>
          </el-table-column>
          <el-table-column
            label="专业成果"
            prop="achievement"
            min-width="200px"
          >
            <template #default="{ row }">
              <el-input
                v-model="row.achievement"
                type="textarea"
                clearable
                icon="custom-search"
                maxlength="30"
                show-word-limit
                placeholder="最多输入30个字符"
              />
            </template>
          </el-table-column>
          <el-table-column
            label="附件"
            prop="attachment"
            min-width="200px"
          >
            <template #default="{ row }">
              <upload
                v-model="row.attachment"
                type="button"
                listType="text"
                show-file-list
                :limit="3"
                :fileMaxSize="5"
                :fileTypes="['doc', 'docx', 'xls', 'xlsx']"
              />
            </template>
          </el-table-column>
          <el-table-column
            label="备注"
            prop="remarks"
            min-width="200px"
          >
            <template #default="{ row }">
              <el-input
                v-model="row.remarks"
                type="textarea"
                :rows="3"
                maxlength="30"
                show-word-limit
                placeholder="最多输入30个字符"
              />
            </template>
          </el-table-column>
          <el-table-column
            label="操作"
            fixed="right"
            width="100"
            align="center"
          >
            <template #default="{ $index }">
              <el-button
                type="danger"
                size="small"
                icon="Delete"
                @click="handleRemoveRow('baseExpertAchievementList', $index)"
                >删除</el-button
              >
            </template>
          </el-table-column>
        </el-table>
      </div>
      <el-button
        style="width: 100%; margin-top: 5px"
        :icon="Plus"
        @click="handleAddRow('baseExpertAchievementList')"
      >
        添加
      </el-button>
      <div class="table-header">
        <div class="label">专家教育经历表</div>
      </div>
      <div class="w-full overflow-x-auto">
        <el-table
          :data="formData.baseExpertEducationList || []"
          :border="true"
          stripe
          class="w-full dark:bg-gray-800"
          :header-cell-class-name="'!bg-gray-50 dark:!bg-gray-700'"
          style="min-width: 600px"
          :show-overflow-tooltip="false"
        >
          <el-table-column
            type="index"
            label="序号"
            width="60"
            align="center"
            fixed
          />
          <el-table-column
            label="开始时间"
            prop="startDate"
            min-width="200px"
          >
            <template #default="{ row, $index }">
              <el-date-picker
                v-model="row.startDate"
                type="date"
                autoFullWidth
                clearable
                format="YYYY-MM-DD"
                valueFormat="YYYY-MM-DD"
                @change="validateEducationDateRange(row, $index)"
              />
            </template>
          </el-table-column>
          <el-table-column
            label="截止时间"
            prop="endDate"
            min-width="200px"
          >
            <template #default="{ row, $index }">
              <el-date-picker
                v-model="row.endDate"
                type="date"
                autoFullWidth
                clearable
                format="YYYY-MM-DD"
                valueFormat="YYYY-MM-DD"
                @change="validateEducationDateRange(row, $index)"
              />
            </template>
          </el-table-column>
          <el-table-column
            label="毕业院校"
            prop="school"
            min-width="200px"
          >
            <template #default="{ row }">
              <el-input
                v-model="row.school"
                type="text"
                clearable
                icon="custom-search"
                maxlength="30"
                show-word-limit
                placeholder="最多输入30个字符"
              />
            </template>
          </el-table-column>
          <el-table-column
            label="专业"
            prop="major"
            min-width="200px"
          >
            <template #default="{ row }">
              <el-input
                v-model="row.major"
                type="text"
                clearable
                icon="custom-search"
                maxlength="30"
                show-word-limit
                placeholder="最多输入30个字符"
              />
            </template>
          </el-table-column>
          <el-table-column
            label="学位"
            prop="degree"
            min-width="200px"
          >
            <template #default="{ row }">
              <el-input
                v-model="row.degree"
                type="text"
                clearable
                icon="custom-search"
                maxlength="30"
                show-word-limit
                placeholder="最多输入30个字符"
              />
            </template>
          </el-table-column>
          <el-table-column
            label="证书附件"
            prop="certificate"
            min-width="200px"
          >
            <template #default="{ row }">
              <upload
                v-model="row.certificate"
                type="button"
                listType="text"
                show-file-list
                :limit="3"
                :fileMaxSize="5"
                :fileTypes="['doc', 'docx', 'xls', 'xlsx']"
              />
            </template>
          </el-table-column>
          <el-table-column
            label="操作"
            fixed="right"
            width="100"
            align="center"
          >
            <template #default="{ $index }">
              <el-button
                type="danger"
                size="small"
                icon="Delete"
                @click="handleRemoveRow('baseExpertEducationList', $index)"
                >删除</el-button
              >
            </template>
          </el-table-column>
        </el-table>
      </div>
      <el-button
        style="width: 100%; margin-top: 5px"
        :icon="Plus"
        @click="handleAddRow('baseExpertEducationList')"
      >
        添加
      </el-button>
      <div class="table-header">
        <div class="label">专家职业履历表</div>
      </div>
      <div class="w-full overflow-x-auto">
        <el-table
          :data="formData.baseExpertWorkExperienceList || []"
          :border="true"
          stripe
          class="w-full dark:bg-gray-800"
          :header-cell-class-name="'!bg-gray-50 dark:!bg-gray-700'"
          style="min-width: 600px"
          :show-overflow-tooltip="false"
        >
          <el-table-column
            type="index"
            label="序号"
            width="60"
            align="center"
            fixed
          />
          <el-table-column
            label="开始时间"
            prop="startDate"
            min-width="200px"
          >
            <template #default="{ row, $index }">
              <el-date-picker
                v-model="row.startDate"
                type="date"
                autoFullWidth
                clearable
                format="YYYY-MM-DD"
                valueFormat="YYYY-MM-DD"
                @change="validateWorkDateRange(row, $index)"
              />
            </template>
          </el-table-column>
          <el-table-column
            label="截止时间"
            prop="endDate"
            min-width="200px"
          >
            <template #default="{ row, $index }">
              <el-date-picker
                v-model="row.endDate"
                type="date"
                autoFullWidth
                clearable
                format="YYYY-MM-DD"
                valueFormat="YYYY-MM-DD"
                @change="validateWorkDateRange(row, $index)"
              />
            </template>
          </el-table-column>
          <el-table-column
            label="工作单位"
            prop="company"
            min-width="200px"
          >
            <template #default="{ row }">
              <el-input
                v-model="row.company"
                type="text"
                clearable
                icon="custom-search"
                maxlength="30"
                show-word-limit
                placeholder="最多输入30个字符"
              />
            </template>
          </el-table-column>
          <el-table-column
            label="职位"
            prop="position"
            min-width="200px"
          >
            <template #default="{ row }">
              <el-input
                v-model="row.position"
                type="text"
                clearable
                icon="custom-search"
                maxlength="30"
                show-word-limit
                placeholder="最多输入30个字符"
              />
            </template>
          </el-table-column>
          <el-table-column
            label="附件"
            prop="attachment"
            min-width="200px"
          >
            <template #default="{ row }">
              <upload
                v-model="row.attachment"
                type="button"
                listType="text"
                show-file-list
                :limit="3"
                :fileMaxSize="5"
                :fileTypes="['doc', 'docx', 'xls', 'xlsx']"
              />
            </template>
          </el-table-column>
          <el-table-column
            label="备注"
            prop="remarks"
            min-width="200px"
          >
            <template #default="{ row }">
              <el-input
                v-model="row.remarks"
                type="textarea"
                :rows="3"
              />
            </template>
          </el-table-column>
          <el-table-column
            label="操作"
            fixed="right"
            width="100"
            align="center"
          >
            <template #default="{ $index }">
              <el-button
                type="danger"
                size="small"
                icon="Delete"
                @click="handleRemoveRow('baseExpertWorkExperienceList', $index)"
                >删除</el-button
              >
            </template>
          </el-table-column>
        </el-table>
      </div>
      <el-button
        style="width: 100%; margin-top: 5px"
        :icon="Plus"
        @click="handleAddRow('baseExpertWorkExperienceList')"
      >
        添加
      </el-button>
    </el-form>
  </yun-drawer>
</template>

<script setup lang="ts">
import { ref, reactive, computed, nextTick } from 'vue';
import { ElMessage } from 'yun-design';
import { Plus, Delete } from '@element-plus/icons-vue';
import { useDict } from '@/hooks/dict';
import { addObj, putObj, getObj, getInnerAccountApi } from '@/api/lowcode/base-expert/index';
import { useUserInfo } from '@/stores/userInfo';
import { rule } from '@/utils/validate';
import { Session } from '@/utils/storage';
import moment from 'moment';
import { EXPERT_CLASSIFIER, EXPERT_TYPE, STATUS_LIST } from '@/views/lowcode/base-expert/hooks/const';
import { deptTree } from '/@/api/admin/dept';

// 状态管理
const visible = ref(false);
const loading = ref(false);
const isEdit = ref(false);
const userList = ref([]);
const vForm = ref();
const formData = reactive({
  expertCategory: 'INTERNAL',
  status: 'ENABLED',
});
const outsideDeptList = ref([]);

// 身份证校验函数
const validateIdCard = (rule: any, value: any, callback: any) => {
  if (!value) {
    return callback();
  }
  const idCardRegex = /^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/;
  const factor = [7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2];
  const parity = [1, 0, 'X', 9, 8, 7, 6, 5, 4, 3, 2];
  const code = value.substring(17);

  if (idCardRegex.test(value)) {
    let sum = 0;
    for (let i = 0; i < 17; i++) {
      sum += value[i] * factor[i];
    }
    if (parity[sum % 11] == code.toUpperCase()) {
      return callback();
    }
  }
  return callback(new Error('身份证号不合法'));
};

// 字符长度校验函数
const validateMaxLength = (maxLength: number) => {
  return (rule: any, value: any, callback: any) => {
    if (!value) {
      return callback();
    }
    if (value.length > maxLength) {
      return callback(new Error(`最多输入${maxLength}个字符`));
    }
    callback();
  };
};

// 日期范围校验函数
const validateDateRange = (startDateField: string, endDateField: string, rowIndex: number) => {
  return (rule: any, value: any, callback: any) => {
    if (!value) {
      return callback();
    }

    const currentRow = formData[startDateField.includes('Education') ? 'baseExpertEducationList' : 'baseExpertWorkExperienceList'][rowIndex];
    if (!currentRow) {
      return callback();
    }

    const startDate = currentRow.startDate;
    const endDate = currentRow.endDate;

    if (startDate && endDate && new Date(endDate) <= new Date(startDate)) {
      return callback(new Error('结束时间必须大于开始时间'));
    }
    callback();
  };
};

async function getDeptList(name: string = '') {
  const res = await deptTree({ deptType: 'EXTERNAL' });
  outsideDeptList.value = res.data;
}

const rules = reactive({
  name: [
    {
      required: true,
      message: '此项必填',
      trigger: ['blur', 'change'],
    },
  ],
  // expertCode: [//1932720727367094273
  // 	{
  // 		required: true,
  // 		message: '此项必填',
  // 		trigger: ['blur', 'change'],
  // 	},
  // ],
  phone: [
    {
      required: true,
      message: '此项必填',
      trigger: ['blur', 'change'],
    },
    {
      validator: rule['mobilePhone'],
      trigger: ['blur', 'change'],
    },
  ],
  expertCategory: [
    {
      required: true,
      message: '此项必填',
      trigger: ['blur', 'change'],
    },
  ],
  loginAccount: [
    {
      required: true,
      message: '此项必填',
      trigger: ['blur', 'change'],
    },
  ],
  address: [
    {
      required: true,
      message: '此项必填',
      trigger: ['blur', 'change'],
    },
  ],
  // 身份证号校验
  idNumber: [
    {
      validator: validateIdCard,
      trigger: ['blur', 'change'],
    },
  ],
  // 邮箱校验 - 限制20字符
  email: [
    {
      validator: rule['email'],
      trigger: ['blur', 'change'],
    },
    {
      validator: validateMaxLength(20),
      trigger: ['blur', 'change'],
    },
  ],
  deptTypeId: [
    {
      required: true,
      message: '此项必填',
      trigger: ['blur', 'change'],
    },
  ],
});

// 计算属性
const title = computed(() => (isEdit.value ? '编辑' : '新增'));
const size = computed(() => 'X-large');

// 用户信息
const userStore = useUserInfo();
const emits = defineEmits(['getData']);

// 为每个API组件创建响应式变量
const addressOptions = ref([]);

// 为每个API组件创建获取数据的方法
function fetchAddressOptions(options, query, formData) {
  let requestUrl = 'https://oss-public.yunlizhi.cn/frontend/yun-design/area.json';
  const method = 'GET';

  // 构建请求参数
  const requestParams = {};

  const token = Session.getToken();
  const tenantId = Session.getTenant();
  // 构建请求配置
  const requestConfig = {
    method,
    headers: {
      'Content-Type': 'application/json',
      'TENANT-ID': tenantId,
      AUTHORIZATION: `Bearer ${token}`,
    },
  };

  // 如果是GET请求，将参数附加到URL
  if (method === 'GET') {
    const queryParams = new URLSearchParams();
    Object.entries(requestParams).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        queryParams.append(key, String(value));
      }
    });
    const queryString = queryParams.toString();
    if (queryString) {
      requestUrl += (requestUrl.includes('?') ? '&' : '?') + queryString;
    }
  } else {
    // 非GET请求，将参数放在body中
    requestConfig.body = JSON.stringify(requestParams);
  }

  // 发送请求并处理响应
  fetch(requestUrl, requestConfig)
    .then((response) => {
      if (!response.ok) {
        throw new Error('API request failed');
      }
      return response.json();
    })
    .then((data) => {
      // 根据配置的路径获取数据
      let result = data;

      // 处理数据
      const processData = (items) => {
        return items.map((item) => ({
          label: item['areaName'],
          value: item['areaCode'],

          children: item['children'] ? processData(item['children']) : undefined,
        }));
      };

      options.value = Array.isArray(result) ? processData(result) : [];
    })
    .catch((error) => {
      console.error('Failed to fetch API options:', error);
      options.value = [];
    });
}

getDeptList();

// 初始化时获取所有API数据
fetchAddressOptions(addressOptions);

// 关闭处理
function closeHandler() {
  visible.value = false;
}

// 格式化表单数据
async function formatFrom(row: Record<string, any>) {
  for (let key in formData) {
    delete formData[key];
  }
  if (!row.id) {
    formData.expertCategory = 'INTERNAL';
    formData.status = 'ENABLED';
    return;
  }
  const res = await getObj(row.id);
  res.data.expertType = res.data?.expertType?.split(',')?.filter((i: String) => i);
  Object.assign(formData, res.data);
  if (Array.isArray(row.address)) {
    formData.address = row.address.map((item) => {
      if (Array.isArray(item)) {
        return item;
      }
      return item.split(',');
    });
  }
  console.log(formData);
}

// 格式化提交参数
function formatParams() {
  const baseParams = {};

  // 处理特殊字段类型
  if (Array.isArray(formData.address)) {
    baseParams.address = formData.address.map((item) => {
      if (Array.isArray(item)) {
        return item.join(',');
      }
      return item;
    });
  }

  const expertType = formData?.expertType?.join();

  return {
    ...formData,
    ...baseParams,
    expertType,
  };
}

// 确认处理
async function confirmHandler(done: () => void, loading: { value: boolean }) {
  try {
    await vForm.value?.validate();
    loading.value = true;
    const api = isEdit.value ? putObj : addObj;
    const params = formatParams();
    await api(params);
    done();
    emits('getData', formData);
  } catch (error) {
    ElMessage.error('表单验证或提交失败');
  } finally {
    loading.value = false;
  }
}

// 显示表单
function show(row?: Record<string, any>) {
  visible.value = true;
  isEdit.value = !!row;
  formatFrom(row || {});
  nextTick(() => {
    vForm.value?.clearValidate();
  });
  getInnerAccount();
}

// 日期时间范围默认值
const defaultTime = [new Date(2000, 1, 1, 0, 0, 0), new Date(2000, 2, 1, 23, 59, 59)];

// 字典数据
const getDict = computed(() => (str: string) => {
  if (!str) return [];
  const value = useDict(str)?.[str]?.value;
  return value;
});

// 生成明细表新行的初始数据
function getEnhancedTableRow(widget: Widget): Record<string, any> {
  const row: Record<string, any> = {};
  if (Array.isArray(widget.widgetList)) {
    for (const colWidget of widget.widgetList) {
      const name = colWidget.options?.name;
      if (!name) continue;
      let value = colWidget.options?.defaultValue;
      if (value === undefined) {
        if (colWidget.type === 'number') value = 0;
        else value = '';
      }
      row[name] = value;
    }
  }
  return row;
}

// 添加明细表行
function handleAddRow(tableName: string) {
  if (!formData[tableName]) {
    formData[tableName] = [];
  }

  formData[tableName].push({});
}

// 删除明细表行
function handleRemoveRow(tableName: string, index: number) {
  if (!formData[tableName]) return;
  formData[tableName].splice(index, 1);
}

// 教育经历日期范围校验
function validateEducationDateRange(row: any, index: number) {
  if (row.startDate && row.endDate) {
    if (new Date(row.endDate) <= new Date(row.startDate)) {
      ElMessage.warning('结束时间必须大于开始时间');
      // 清空结束时间
      row.endDate = '';
    }
  }
}

// 职业履历日期范围校验
function validateWorkDateRange(row: any, index: number) {
  if (row.startDate && row.endDate) {
    if (new Date(row.endDate) <= new Date(row.startDate)) {
      ElMessage.warning('结束时间必须大于开始时间');
      // 清空结束时间
      row.endDate = '';
    }
  }
}

const changeUser = (data) => {
  formData.name = data.name;
  formData.phone = data.phone;
  formData.userId = data.userId;
  if (!isEdit.value) {
    formData.loginAccount = data.username;
  }
};

const getInnerAccount = async () => {
  try {
    const { data } = await getInnerAccountApi({});
    userList.value = data;
  } catch (e) {
    console.log(e);
  }
};

// 暴露方法
defineExpose({
  show,
  formData,
  rules,
});
</script>

<style lang="scss">
.form-drawer {
  .el-form .el-form-item:last-of-type {
    margin-bottom: 24px !important;
  }
}
</style>

<style lang="scss" scoped>
.el-form-item {
  margin-bottom: 18px;
}

.table-header {
  margin-bottom: 15px;
  .label {
    font-weight: bold;
    font-size: 18px;
    padding-left: 8px;
    border-left: 4px solid var(--el-color-primary);
    line-height: 1.2;
  }
}

:deep(.el-form-item__label) {
  font-weight: normal;
}

:deep(.el-table) {
  .el-table__header-wrapper {
    th {
      background-color: var(--el-fill-color-light);
    }
  }
}
</style>
