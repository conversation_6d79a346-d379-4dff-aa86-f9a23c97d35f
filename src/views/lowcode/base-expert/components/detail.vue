<template>
  <yun-drawer
    v-model="visible"
    :show-cancel-button="false"
    destroy-on-close
    title="详情"
    size="X-large"
    @confirm="handleConfirm"
  >
    <Detail :groups="processedGroups" :data="form"/>
  </yun-drawer>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import Detail from '@/components/Detail/index.vue';
import { getObj } from '@/api/lowcode/base-expert/index';
import { EXPERT_CLASSIFIER, EXPERT_TYPE, STATUS_LIST } from '@/views/lowcode/base-expert/hooks/const';
import { Session } from '@/utils/storage';

function fetchDistrictOptions(options) {
	let requestUrl = 'https://oss-public.yunlizhi.cn/frontend/yun-design/area.json';
	const method = 'GET';

	// 构建请求参数
	const requestParams = {

	};

	const token = Session.getToken();
	const tenantId = Session.getTenant();
	// 构建请求配置
	const requestConfig = {
		method,
		headers: {
			'Content-Type': 'application/json',
			'TENANT-ID': tenantId,
			AUTHORIZATION: `Bearer ${token}`,
		},
	};

	// 如果是GET请求，将参数附加到URL
	if (method === 'GET') {
		const queryParams = new URLSearchParams();
		Object.entries(requestParams).forEach(([key, value]) => {
			if (value !== undefined && value !== null) {
				queryParams.append(key, String(value));
			}
		});
		const queryString = queryParams.toString();
		if (queryString) {
			requestUrl += (requestUrl.includes('?') ? '&' : '?') + queryString;
		}
	} else {
		// 非GET请求，将参数放在body中
		requestConfig.body = JSON.stringify(requestParams);
	}

	// 发送请求并处理响应
	fetch(requestUrl, requestConfig)
		.then(response => {
			if (!response.ok) {
				throw new Error('API request failed');
			}
			return response.json();
		})
		.then(data => {
			// 根据配置的路径获取数据
			let result = data;


			// 处理数据
			const processData = (items) => {
				return items.map(item => ({
					label: item['areaName'],
					value: item['areaCode'],

					children: item['children'] ?
						processData(item['children']) : undefined
				}));
			};

			options.value = Array.isArray(result) ? processData(result) : [];
		})
		.catch(error => {
			console.error('Failed to fetch API options:', error);
			options.value = [];
		});
}

const districtOptions = ref([]);
fetchDistrictOptions(districtOptions);

interface FormData {
  [key: string]: any;
}

// 静态生成的分组配置
const processedGroups = [
  {
    title: "",
    columns: [
      {
        prop: 'expertCode',
        label: '专家编码',
        type: 'text'
      },
      {
        prop: 'name',
        label: '姓名',
        type: 'text'
      },
      {
        prop: 'phone',
        label: '手机号',
        type: 'text'
      },
      {
        prop: 'expertCategory',
        label: '专家分类',
        type: 'enums',
        attrs: {
          options: EXPERT_CLASSIFIER,
        }
      },
      {
        prop: 'loginAccount',
        label: '登录账号',
        type: 'text'
      },
      {
        prop: 'address',
        label: '所在地',
        type: 'enums',
				formatter(row){
					const value = row['address'];
					const options = districtOptions.value;
					if (!value) return '--';

					// 如果是数组，说明是多选
					if (Array.isArray(value)) {
						return value
							.map((v) => {
								// 如果是字符串，说明是逗号分隔的路径
								if (typeof v === 'string') {
									const path = v.split(',');
									let currentOptions = options;
									const labels = [];

									// 遍历路径找到对应的标签
									for (const val of path) {
										const option = currentOptions.find((opt) => opt.value == val);
										if (option) {
											labels.push(option.label);
											currentOptions = option.children || [];
										}
									}
									return labels.join(' / ');
								}
								return v;
							})
							.join('、');
					}

					// 如果是字符串，说明是单选
					if (typeof value === 'string') {
						// 递归查找函数
						const findPath = (options, targetValue, path = []) => {
							for (const option of options) {
								// 如果找到目标值，返回完整路径
								if (option.value == targetValue) {
									return [...path, option.label];
								}
								// 如果有子节点，递归查找
								if (option.children && option.children.length > 0) {
									const result = findPath(option.children, targetValue, [...path, option.label]);
									if (result) return result;
								}
							}
							return null;
						};

						// 查找完整路径
						const path = findPath(options, value);
						return path ? path.join(' / ') : '--';
					}
					return '--';
				}
      },
      {
        prop: 'idNumber',
        label: '身份证号码',
        type: 'text'
      },
      {
        prop: 'expertType',
        label: '专家类型',
        type: 'enums',
        attrs: {
          options: EXPERT_TYPE,
        }
      },
      {
        prop: 'email',
        label: '邮箱',
        type: 'text'
      },
      {
        prop: 'status',
        label: '状态',
        type: 'enums',
        attrs: {
          options: STATUS_LIST,
        }
      }
    ]
  },
  {
    title: '专家成果表',
    columns: [
      {
        prop: 'baseExpertAchievement',
        label: '',
        type: 'table',
        attrs: {
          showIndex: true,
          columns: [
            {
              prop: 'professionalField',
              label: '专业领域',
              type: 'text'
            },
            {
              prop: 'achievement',
              label: '专业成果',
              type: 'text'
            },
            {
              prop: 'attachment',
              label: '附件',
              type: 'files'
            },
            {
              prop: 'remarks',
              label: '备注',
              type: 'text'
            }
          ]
        }
      }
    ]
  },
  {
    title: '专家教育经历表',
    columns: [
      {
        prop: 'baseExpertEducation',
        label: '',
        type: 'table',
        attrs: {
          showIndex: true,
          columns: [
            {
              prop: 'startDate',
              label: '开始时间',
              type: 'text'
            },
            {
              prop: 'endDate',
              label: '截止时间',
              type: 'text'
            },
            {
              prop: 'school',
              label: '毕业院校',
              type: 'text'
            },
            {
              prop: 'major',
              label: '专业',
              type: 'text'
            },
            {
              prop: 'degree',
              label: '学位',
              type: 'text'
            },
            {
              prop: 'certificate',
              label: '证书附件',
              type: 'files'
            }
          ]
        }
      }
    ]
  },
  {
    title: '专家职业履历表',
    columns: [
      {
        prop: 'baseExpertWorkExperience',
        label: '',
        type: 'table',
        attrs: {
          showIndex: true,
          columns: [
            {
              prop: 'startDate',
              label: '开始时间',
              type: 'text'
            },
            {
              prop: 'endDate',
              label: '截止时间',
              type: 'text'
            },
            {
              prop: 'company',
              label: '工作单位',
              type: 'text'
            },
            {
              prop: 'position',
              label: '职位',
              type: 'text'
            },
            {
              prop: 'attachment',
              label: '附件',
              type: 'files'
            },
            {
              prop: 'remarks',
              label: '备注',
              type: 'text'
            }
          ]
        }
      }
    ]
  }
];

const visible = ref(false);
const form = ref<FormData>({});

async function handleConfirm(done: () => void) {
  done();
}

async function show(row: FormData) {
  const res = await getObj(row.id);
	res.data.expertType = res.data?.expertType?.split(',')
  form.value = res.data;
  visible.value = true;
}

defineExpose({
  show,
});
</script>

<style scoped lang="scss">
</style>
