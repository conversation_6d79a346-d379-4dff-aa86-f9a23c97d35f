<template>
	<yun-drawer
		v-model="visible"
		destroy-on-close
		modal
		custom-class="form-drawer"
		close-on-click-modal
		confirm-button-text="确定"
		cancel-button-text="取消"
		:confirm-button-disabled="loading"
		:title="title"
		:size="size"
		@confirm="confirmHandler"
		@close="closeHandler"
	>
		<el-form
			ref="vForm"
			:model="formData"
			:rules="rules"
			label-width="120"
			label-position="top"
		>
			<el-row :gutter="12">
				<el-col :span="8">
					<el-form-item
						label="物料名称"
						prop="materialCode"
					>
						<el-select
							v-model="formData.materialCode"
							style="width: 100%"
							clearable
							filterable
							placeholder="请选择物料名称"
							:multipleLimit="0"
							@change="handleMaterialChange"
						>
							<el-option
								v-for="option in materialCodeOptions"
								:key="option.value"
								:label="option.label"
								:value="option.value"
							/>
						</el-select>
					</el-form-item>
				</el-col>
				<el-col :span="8">
					<el-form-item
						label="指标名称"
						prop="indicatorName"
					>
						<el-input
							v-model="formData.indicatorName"
							type="text"
							clearable
							placeholder="请输入指标名称"
							icon="custom-search"
						/>
					</el-form-item>
				</el-col>
				<el-col :span="8">
					<el-form-item
						label="适用部门"
						prop="applyAllLocations"
					>
						<el-radio-group
							v-model="formData.applyAllLocations"
							@change="handleApplyOrgChange"
						>
							<el-radio :label="'1'">全部部门</el-radio>
							<el-radio :label="'0'">指定部门</el-radio>
						</el-radio-group>
					</el-form-item>
				</el-col>
				<el-col
					:span="8"
					v-if="formData.applyAllLocations === '0'"
				>
					<el-form-item
						label="负责部门"
						prop="locationIds"
					>
						<el-cascader
              v-model="formData.locationIds"
              style="width: 100%;"
              clearable
              :filterable="false"
              :options="deptTreeData"
              :props="{
                multiple: true,
                value: 'id',
                label: 'name',
                children: 'children',
                checkStrictly: true,
                emitPath: false,
                expandTrigger: 'hover'
              }"
              placeholder="请选择"
              collapse-tags
              collapse-tags-tooltip
            />
					</el-form-item>
				</el-col>
				<el-col :span="8">
					<el-form-item
						label="生效日期"
						prop="releaseDate"
					>
						<el-date-picker
							v-model="formData.releaseDate"
							type="date"
							autoFullWidth
							clearable
							placeholder="请选择生效日期"
							format="YYYY-MM-DD"
							valueFormat="YYYY-MM-DD"
							:disabledDate="disabledReleaseDate"
							@change="handleReleaseDateChange"
						/>
					</el-form-item>
				</el-col>
				<el-col :span="8">
					<el-form-item
						label="失效日期"
						prop="expiryDate"
					>
						<el-date-picker
							v-model="formData.expiryDate"
							type="date"
							placeholder="请选择失效日期"
							autoFullWidth
							clearable
							format="YYYY-MM-DD"
							valueFormat="YYYY-MM-DD"
							:disabledDate="disabledExpiryDate"
							@change="handleExpiryDateChange"
						/>
					</el-form-item>
				</el-col>
			</el-row>
			<div class="table-header">
				<div class="label">质量指标</div>
			</div>
			<div class="w-full overflow-x-auto">
				<el-table
					:data="formData.baseQualityIndicatorAttributeValueList || []"
					:border="true"
					stripe
					class="w-full dark:bg-gray-800"
					:header-cell-class-name="'!bg-gray-50 dark:!bg-gray-700'"
					style="min-width: 600px"
					:show-overflow-tooltip="false"
				>
					<el-table-column
						type="index"
						label="序号"
						width="60"
						align="center"
						fixed
					/>
					<el-table-column
						label="质量属性"
						prop="attributeId"
						min-width="200px"
					>
						<template #default="{ row }">
							<el-select
								v-model="row.attributeId"
								style="width: 100%"
								filterable
								placeholder="请选择质量属性"
								:multipleLimit="0"
								@change="(value) => handleAttributeChange(value, row)"
							>
								<el-option
									v-for="option in attributeIdOptions"
									:key="option.value"
									:label="option.label"
									:value="option.value"
								/>
							</el-select>
						</template>
					</el-table-column>
					<el-table-column
						label="质量标准"
						prop="valueContent"
						min-width="200px"
					>
						<template #default="{ row }">
							<el-autocomplete
								v-model="row.valueContent"
								:fetch-suggestions="(queryString, cb) => querySearch(queryString, cb, row)"
								popper-class="my-autocomplete"
								placeholder="请输入质量标准"
								:debounce="300"
								:trigger-on-focus="true"
								:disabled="row.ifDetail || !row.attributeId || row.autocompleteDisabled"
								@select="(item) => handleSelect(item, row)"
								clearable
								maxlength="200"
							>
								<template #default="{ item }">
									<div class="suggestion-item">
										<div class="suggestion-content">
											<div class="suggestion-row">
												<span class="label-badge quality-standard">质量标准</span>
												<span class="content-text">{{ item.valueContent }}</span>
											</div>
											<div class="suggestion-row">
												<span class="label-badge rejection-standard">拒收标准</span>
												<span class="content-text">{{ item.rejectionStandard }}</span>
											</div>
											<div class="suggestion-row">
												<span class="label-badge penalty-standard">扣款标准</span>
												<span class="content-text">{{ item.penaltyStandard }}</span>
											</div>
										</div>
									</div>
								</template>
							</el-autocomplete>
						</template>
					</el-table-column>
					<el-table-column
						label="拒收标准"
						prop="rejectionStandard"
						min-width="200px"
					>
						<template #default="{ row }">
							<el-input
								v-model="row.rejectionStandard"
								placeholder="请输入拒收标准"
								type="text"
								clearable
								icon="custom-search"
								:disabled="row.ifDetail || !row.attributeId || row.autocompleteDisabled"
								maxlength="200"
							/>
						</template>
					</el-table-column>
					<el-table-column
						label="扣款标准"
						prop="penaltyStandard"
						min-width="200px"
					>
						<template #default="{ row }">
							<el-input
								v-model="row.penaltyStandard"
								placeholder="请输入扣款标准"
								type="text"
								clearable
								icon="custom-search"
								:disabled="row.ifDetail || !row.attributeId || row.autocompleteDisabled"
								maxlength="200"
							/>
						</template>
					</el-table-column>
					<el-table-column
						label="操作"
						fixed="right"
						width="100"
						align="center"
					>
						<template #default="{ $index }">
							<el-button
								type="danger"
								size="small"
								icon="Delete"
								@click="handleRemoveRow('baseQualityIndicatorAttributeValueList', $index)"
								>删除</el-button
							>
						</template>
					</el-table-column>
				</el-table>
			</div>
			<el-button
				style="width: 100%; margin-top: 5px"
				:icon="Plus"
				@click="handleAddRow('baseQualityIndicatorAttributeValueList')"
			>
				添加
			</el-button>
		</el-form>
	</yun-drawer>
</template>

<script setup lang="ts">
import { ref, reactive, computed, nextTick } from 'vue';
import { ElMessage } from 'yun-design';
import { Plus, Delete } from '@element-plus/icons-vue';
import { useDict } from '@/hooks/dict';
import { addObj, putObj, getObj, getStandard } from '@/api/lowcode/base-quality-indicator/index';
import { useUserInfo } from '@/stores/userInfo';
import { rule } from '@/utils/validate';
import { Session } from '@/utils/storage';
import moment from 'moment';
import DeptSelector from '@/components/DeptSelector';
import { Row } from 'element-plus/es/components/table-v2/src/components';
import { deptTree } from '@/api/admin/dept';

// 状态管理
const visible = ref(false);
const loading = ref(false);
const isEdit = ref(false);
const vForm = ref();
const formData = reactive({});
const rules = reactive({
	indicatorName: [
		{
			required: true,
			message: '请填写指标名称',
			trigger: ['blur', 'change'],
		},
	],
	materialCode: [
		{
			required: true,
			message: '请选择物料名称',
			trigger: ['blur', 'change'],
		},
	],
	applyAllLocations: [
		{
			required: true,
			message: '请选择适用部门',
			trigger: ['blur', 'change'],
		},
	],
	locationIds: [
		{
			required: true,
			message: '请选择负责部门',
			trigger: ['blur', 'change'],
		},
	],
});
const autocompleteDisabled = ref(false);
// 部门组织相关
const deptTreeData = ref([]);

// 计算属性
const title = computed(() => (isEdit.value ? '编辑' : '新增'));
const size = computed(() => 'X-large');

// 用户信息
const userStore = useUserInfo();
const emits = defineEmits(['getData']);

// 为每个API组件创建响应式变量
const materialCodeOptions = ref([]);
const attributeIdOptions = ref([]);
const attributeValueIdOptions = ref([]);
const attributeIdLoading = ref(false);
// 保存完整的质量属性数据，用于获取详细信息
const attributeFullData = ref([]);

// 为每个API组件创建获取数据的方法
function fetchMaterialCodeOptions(options, query, formData) {
	let requestUrl = '/api/admin/baseMaterialInfo/page?size=1000&current=1';
	const method = 'POST';

	// 构建请求参数
	const requestParams = {
		statusList: ['ENABLED']
	};

	const token = Session.getToken();
	const tenantId = Session.getTenant();
	// 构建请求配置
	const requestConfig = {
		method,
		headers: {
			'Content-Type': 'application/json',
			'TENANT-ID': tenantId,
			AUTHORIZATION: `Bearer ${token}`,
		},
	};

	// 如果是GET请求，将参数附加到URL
	if (method === 'GET') {
		const queryParams = new URLSearchParams();
		Object.entries(requestParams).forEach(([key, value]) => {
			if (value !== undefined && value !== null) {
				queryParams.append(key, String(value));
			}
		});
		const queryString = queryParams.toString();
		if (queryString) {
			requestUrl += (requestUrl.includes('?') ? '&' : '?') + queryString;
		}
	} else {
		// 非GET请求，将参数放在body中
		requestConfig.body = JSON.stringify(requestParams);
	}

	// 发送请求并处理响应
	fetch(requestUrl, requestConfig)
		.then((response) => {
			if (!response.ok) {
				throw new Error('API request failed');
			}
			return response.json();
		})
		.then((data) => {
			// 根据配置的路径获取数据
			let result = data;

			const paths = 'data.records'.split('.');
			for (const path of paths) {
				result = result[path];
				if (result === undefined) break;
			}

			// 处理数据
			const processData = (items) => {
				return items.map((item) => ({
					label: item['materialName'],
					value: item['materialCode'],
				}));
			};

			options.value = Array.isArray(result) ? processData(result) : [];
		})
		.catch((error) => {
			console.error('Failed to fetch API options:', error);
			options.value = [];
		});
}

function fetchAttributeIdOptions(options, query, formData) {
	// 如果没有选择物料，不请求数据
	// if (!formData?.materialCode) {
	// 	options.value = [];
	// 	return;
	// }

	let requestUrl = '/api/admin/baseQualityAttribute/list';
	const method = 'POST';

	// 构建请求参数
	const requestParams = {
		materialCode: '',
		attributeName: query || ''
	};

	const token = Session.getToken();
	const tenantId = Session.getTenant();
	// 构建请求配置
	const requestConfig = {
		method,
		headers: {
			'Content-Type': 'application/json',
			'TENANT-ID': tenantId,
			AUTHORIZATION: `Bearer ${token}`,
		},
	};

	// 非GET请求，将参数放在body中
	requestConfig.body = JSON.stringify(requestParams);

	// 发送请求并处理响应
	fetch(requestUrl, requestConfig)
		.then((response) => {
			if (!response.ok) {
				throw new Error('API request failed');
			}
			return response.json();
		})
		.then((data) => {
			// 根据配置的路径获取数据
			let result = data;
			if (data?.data) {
				result = data.data;
			}

			// 保存完整数据
			attributeFullData.value = Array.isArray(result) ? result : [];

			// 处理数据
			const processData = (items) => {
				return items.map((item) => ({
					label: item['attributeName'] || item['name'],
					value: item['id'],
					attributeId: item['attributeId'],
				}));
			};

			options.value = Array.isArray(result) ? processData(result) : [];
		})
		.catch((error) => {
			console.error('Failed to fetch API options:', error);
			options.value = [];
		});
}
function fetchAttributeValueIdOptions(options, query, formData) {
	let requestUrl = '/api/admin/baseQualityAttribute/1';
	const method = 'GET';

	// 构建请求参数
	const requestParams = {};

	const token = Session.getToken();
	const tenantId = Session.getTenant();
	// 构建请求配置
	const requestConfig = {
		method,
		headers: {
			'Content-Type': 'application/json',
			'TENANT-ID': tenantId,
			AUTHORIZATION: `Bearer ${token}`,
		},
	};

	// 如果是GET请求，将参数附加到URL
	if (method === 'GET') {
		const queryParams = new URLSearchParams();
		Object.entries(requestParams).forEach(([key, value]) => {
			if (value !== undefined && value !== null) {
				queryParams.append(key, String(value));
			}
		});
		const queryString = queryParams.toString();
		if (queryString) {
			requestUrl += (requestUrl.includes('?') ? '&' : '?') + queryString;
		}
	} else {
		// 非GET请求，将参数放在body中
		requestConfig.body = JSON.stringify(requestParams);
	}

	// 发送请求并处理响应
	fetch(requestUrl, requestConfig)
		.then((response) => {
			if (!response.ok) {
				throw new Error('API request failed');
			}
			return response.json();
		})
		.then((data) => {
			// 根据配置的路径获取数据
			let result = data;

			const paths = 'data.baseQualityAttributeValueList'.split('.');
			for (const path of paths) {
				result = result[path];
				if (result === undefined) break;
			}

			// 处理数据
			const processData = (items) => {
				return items.map((item) => ({
					label: item['valueContent'],
					value: item['id'],
				}));
			};

			options.value = Array.isArray(result) ? processData(result) : [];
		})
		.catch((error) => {
			console.error('Failed to fetch API options:', error);
			options.value = [];
		});
}

// 初始化时获取所有API数据
fetchMaterialCodeOptions(materialCodeOptions);
// 质量属性需要等待物料选择后再加载
fetchAttributeValueIdOptions(attributeValueIdOptions);
fetchAttributeIdOptionsWithSearch();

// 关闭处理
function closeHandler() {
	visible.value = false;
}

// 格式化表单数据
async function formatFrom(row: Record<string, any>) {
	for (let key in formData) {
		delete formData[key];
	}
	if (!row.id) return;
	const res = await getObj(row.id);
	res.data.baseQualityIndicatorAttributeValueList.forEach(i => i.ifDetail = true);
	Object.assign(formData, res.data);
	
	fetchAttributeIdOptionsWithSearch('');
}

// 格式化提交参数
function formatParams() {
	const baseParams = {};

	// 处理特殊字段类型

	return {
		...formData,
		...baseParams,
	};
}

// 确认处理
async function confirmHandler(done: () => void, loading: { value: boolean }) {
	try {
		await vForm.value?.validate();
		
		// 验证质量指标是否填写
		if (!formData.baseQualityIndicatorAttributeValueList || formData.baseQualityIndicatorAttributeValueList.length === 0) {
			ElMessage.error('请至少添加一个质量指标');
			return;
		}
		
		// 验证每个质量指标的必要字段
		for (let i = 0; i < formData.baseQualityIndicatorAttributeValueList.length; i++) {
			const item = formData.baseQualityIndicatorAttributeValueList[i];
			if (!item.attributeId) {
				ElMessage.error(`请选择第${i + 1}行的质量属性`);
				return;
			}
			// 可以根据需要添加其他字段的验证
			// if (!item.valueContent) {
			//   ElMessage.error(`请填写第${i + 1}行的质量标准`);
			//   return;
			// }
		}
		
		loading.value = true;
		const api = isEdit.value ? putObj : addObj;
		const params = formatParams();
		await api(params);
		ElMessage.success(isEdit.value ? '编辑成功' : '新增成功');
		done();
		emits('getData', formData);
	} catch (error) {
		ElMessage.error('表单验证或提交失败');
	} finally {
		loading.value = false;
	}
}

// 显示表单
async function show(row?: Record<string, any>) {
	await getDeptTree(); // 获取部门树数据
	visible.value = true;
	isEdit.value = !!row;
	formatFrom(row || {});
	nextTick(() => {
		vForm.value?.clearValidate();
	});
}

// 日期时间范围默认值
const defaultTime = [new Date(2000, 1, 1, 0, 0, 0), new Date(2000, 2, 1, 23, 59, 59)];

// 字典数据
const getDict = computed(() => (str: string) => {
	if (!str) return [];
	const value = useDict(str)?.[str]?.value;
	return value;
});

// 生成明细表新行的初始数据
function getEnhancedTableRow(widget: Widget): Record<string, any> {
	const row: Record<string, any> = {};
	if (Array.isArray(widget.widgetList)) {
		for (const colWidget of widget.widgetList) {
			const name = colWidget.options?.name;
			if (!name) continue;
			let value = colWidget.options?.defaultValue;
			if (value === undefined) {
				if (colWidget.type === 'number') value = 0;
				else value = '';
			}
			row[name] = value;
		}
	}
	return row;
}

// 添加明细表行
function handleAddRow(tableName: string) {
	// 验证物料名称是否选择
	// if (!formData.materialCode) {
	// 	ElMessage.warning('请先选择物料名称');
	// 	return;
	// }

	if (!formData[tableName]) {
		formData[tableName] = [];
	}

	formData[tableName].push({});
}

// 删除明细表行
function handleRemoveRow(tableName: string, index: number) {
	if (!formData[tableName]) return;
	formData[tableName].splice(index, 1);
}

// 处理适用组织变化
function handleApplyOrgChange(value: string) {
	if (value === '1') {
		// 选择全部组织时，清空部门相关数据
		formData.locationIds = [];
	}
	// 重新验证负责部门字段
	nextTick(() => {
		vForm.value?.validateField('locationIds');
	});
}

// 带搜索的获取质量属性选项
function fetchAttributeIdOptionsWithSearch(query: string = '') {
	// if (!formData.materialCode) {
	// 	ElMessage.warning('请先选择物料名称');
	// 	return;
	// }
	
	attributeIdLoading.value = true;
	
	// 使用Promise处理异步请求
	return new Promise((resolve) => {
		fetchAttributeIdOptions(attributeIdOptions, query, formData);
		// 模拟异步完成
		setTimeout(() => {
			attributeIdLoading.value = false;
			resolve(undefined);
		}, 100);
	});
}

// 处理物料名称选择变化
function handleMaterialChange(value: string) {
	// 自动设置指标名称
	if (value) {
		const selectedOption = materialCodeOptions.value.find((option: any) => option.value === value);
		if (selectedOption) {
			formData.indicatorName = selectedOption.label + '的质量指标';
		}
	} else {
		// 清空物料时也清空质量属性
		attributeIdOptions.value = [];
	}
}

// 处理部门选择变化
function handleDeptChange(value: string, item: any) {
	formData.deptId = value;
	formData.deptName = item.name;
}

// 处理生效日期变化
function handleReleaseDateChange(value: string) {
	// 如果失效日期小于生效日期，清空失效日期
	if (formData.expiryDate && value && formData.expiryDate < value) {
		formData.expiryDate = '';
	}
}

// 处理失效日期变化
function handleExpiryDateChange(value: string) {
	// 如果生效日期大于失效日期，清空生效日期
	if (formData.releaseDate && value && formData.releaseDate > value) {
		formData.releaseDate = '';
	}
}

// 禁用生效日期的函数
function disabledReleaseDate(time: Date) {
	// 如果没有设置失效日期，不禁用任何日期
	if (!formData.expiryDate) {
		return false;
	}
	
	// 将失效日期转换为 Date 对象
	const expiryDate = new Date(formData.expiryDate);
	expiryDate.setHours(0, 0, 0, 0);
	
	// 禁用失效日期之后的日期
	return time.getTime() > expiryDate.getTime();
}

// 禁用失效日期的函数
function disabledExpiryDate(time: Date) {
	// 如果没有设置生效日期，不禁用任何日期
	if (!formData.releaseDate) {
		return false;
	}
	
	// 将生效日期转换为 Date 对象
	const releaseDate = new Date(formData.releaseDate);
	releaseDate.setHours(0, 0, 0, 0);
	
	// 禁用生效日期之前的日期
	return time.getTime() < releaseDate.getTime();
}

// 处理质量属性选择变化
function handleAttributeChange(value: string, row: any) {
	row.autocompleteDisabled = false;
	row.attributeValueId = '';
	getStandard({attributeId: value}).then(res => {
		row.content = res.data;
	}).catch(error => {
		console.error('getStandard 接口调用失败:', error);
	});
}

// 自动补全搜索函数
async function querySearch(queryString: string, cb: (suggestions: any[]) => void, row: any) {
	try {
		// 从当前行的 content 中获取数据
		if (!row || !row.content) {
			cb([]);
			return;
		}
		// 解析 content 数据
		let contentData;
		try {
			contentData = typeof row.content === 'string' ? JSON.parse(row.content) : row.content;
		} catch (error) {
			contentData = [{ valueContent: row.content, rejectionStandard: '', penaltyStandard: '' }];
		}
		// 确保 contentData 是数组格式
		const dataArray = Array.isArray(contentData) ? contentData : [contentData];
		// 如果没有输入内容，显示全部数据；否则根据输入内容过滤数据
		let filteredData;
		if (!queryString) {
			// 不输入内容时显示全部数据
			filteredData = dataArray;
		} else {
			// 根据输入内容过滤数据
			filteredData = dataArray.filter(item => 
				item.valueContent && item.valueContent.toLowerCase().includes(queryString.toLowerCase()) ||
				item.rejectionStandard && item.rejectionStandard.toLowerCase().includes(queryString.toLowerCase()) ||
				item.penaltyStandard && item.penaltyStandard.toLowerCase().includes(queryString.toLowerCase())
			);
		}
		// 格式化建议数据
		const formattedSuggestions = filteredData.map((item: any) => ({
			...item // 保留原始数据
		}));
		cb(formattedSuggestions);
	} catch (error) {
		console.error('获取质量标准建议失败:', error);
		cb([]);
	}
}

// 处理自动补全选择
function handleSelect(item: any, row: any) {
	// 填充选中的质量标准到当前行
	if (row) {
		row.content = item.valueContent;
		row.valueContent = item.valueContent;
		row.rejectionStandard = item.rejectionStandard;
		row.penaltyStandard = item.penaltyStandard;
		row.autocompleteDisabled = true;
		row.attributeValueId = item.id;
	}
}

// 获取部门树
const getDeptTree = async () => {
  try {
    const response = await deptTree();
    deptTreeData.value = response.data || [];
  } catch (error) {
    console.error('获取部门树失败:', error);
  }
};

// 暴露方法
defineExpose({
	show,
	formData,
	rules,
});
</script>

<style lang="scss">
.form-drawer {
	.el-form .el-form-item:last-of-type {
		margin-bottom: 24px !important;
	}
}

.my-autocomplete {
	li {
		padding: 0 !important;
		border-bottom: 1px solid #E5E6EB;
	}
	li:last-of-type {
		border-bottom: none;
	}
	.el-autocomplete-suggestion__wrap {
    max-height: 400px !important;
	}
}
</style>

<style lang="scss" scoped>
.el-form-item {
	margin-bottom: 18px;
}

.table-header {
	margin-bottom: 15px;
	.label {
		font-weight: bold;
		font-size: 18px;
		padding-left: 8px;
		border-left: 4px solid var(--el-color-primary);
		line-height: 1.2;
	}
}

:deep(.el-form-item__label) {
	font-weight: normal;
}

:deep(.el-table) {
	.el-table__header-wrapper {
		th {
			background-color: var(--el-fill-color-light);
		}
	}
}

:deep(.el-date-editor) {
	width: 100%;
}

// 自动补全样式
.el-autocomplete-suggestion {
	.suggestion-item {
		padding: 5px 16px;
		
		.suggestion-content {
			.suggestion-row {
				display: flex;
				align-items: flex-start;
				margin-bottom: 8px;
				flex-wrap: wrap;
				
				&:last-child {
					margin-bottom: 0;
				}
				
				.label-badge {
					display: flex;
					padding: 4px 8px;
					justify-content: center;
					align-items: center;
					border-radius: 2px;
					font-size: 12px;
					color: #1D2129;
					font-weight: 400;
					margin-right: 4px;
					line-height: 20px;
					
					&.quality-standard {
						background: #E8F5FF; // 质量标准
					}
					
					&.rejection-standard {
						background: #FFF9E8; // 拒收标准
					}
					
					&.penalty-standard {
						background: #FFEDE8; // 扣款标准
					}
				}
				
				.content-text {
					flex: 1;
					word-wrap: break-word;
					word-break: break-all;
					line-height: 29px;
					color: #4E5969;
					font-size: 14px;
					width: 380px;
					white-space: normal;
					overflow-wrap: break-word;
				}
			}
		}
	}
}
</style>
