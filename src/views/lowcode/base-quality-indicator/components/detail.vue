<template>
  <yun-drawer
    v-model="visible"
    :show-cancel-button="false"
    destroy-on-close
    title="详情"
    size="X-large"
    @confirm="handleConfirm"
  >
    <Detail :groups="processedGroups" :data="form"/>
  </yun-drawer>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import Detail from '@/components/Detail/index.vue';
import { getObj, getAttribute } from '@/api/lowcode/base-quality-indicator/index';

interface FormData {
  [key: string]: any;
}

// 静态生成的分组配置
const processedGroups = [
  {
    title: "",
    columns: [
      {
        prop: 'materialCode',
        label: '适用物料编码',
        type: 'text',
      },
      {
        prop: 'indicatorName',
        label: '指标名称',
        type: 'text',
      },
      {
        prop: 'applyAllLocations',
        label: '适用部门',
        type: 'text',
      },
      {
        prop: 'releaseDate',
        label: '生效日期',
        type: 'text'
      },
      {
        prop: 'expiryDate',
        label: '失效日期',
        type: 'text'
      },
    ]
  },
  {
    title: '质量指标',
    columns: [
      {
        prop: 'baseQualityIndicatorAttributeValue',
        label: '',
        type: 'table',
        attrs: {
          showIndex: true,
          columns: [
            {
              prop: 'attributeName',
              label: '质量属性',
              type: 'text',
            },
            {
              prop: 'valueContent',
              label: '属性值内容',
              type: 'text'
            },
            {
              prop: 'rejectionStandard',
              label: '拒收标准',
              type: 'text'
            },
            {
              prop: 'penaltyStandard',
              label: '扣款标准',
              type: 'text'
            }
          ]
        }
      }
    ]
  }
];

const visible = ref(false);
const form = ref<FormData>({});

async function handleConfirm(done: () => void) {
  done();
}

async function show(row: FormData) {
  const res = await getObj(row.id);
  const { data } = await getAttribute({});
  res.data.baseQualityIndicatorAttributeValueList.forEach(i => {
    i.attributeName = data.find(j => j.id === i.attributeId)?.attributeName;
  })
  res.data.applyAllLocations = res.data.applyAllLocations === '1' ? '指定部门' : '全部部门';
  form.value = res.data;
  visible.value = true;
}

defineExpose({
  show,
});
</script>

<style scoped lang="scss">
</style>