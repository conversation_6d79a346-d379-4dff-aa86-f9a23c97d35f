import { ref, computed } from 'vue';
import moment from 'moment';
import { useDict } from '@/hooks/dict';
import { ElImage } from 'element-plus';
import { Session } from '/@/utils/storage';

const getDict = computed(() => (str) => {
  if (!str) return [];
  const value = useDict(str)?.[str]?.value;
  return value;
});
function fetchMaterialCodeOptions(options) {
  let requestUrl = '/api/admin/baseMaterialInfo/page?size=1000&current=1';
  const method = 'POST';
  
  // 构建请求参数
  const requestParams = {
    
  };

  const token = Session.getToken();
  const tenantId = Session.getTenant();
  // 构建请求配置
  const requestConfig = {
    method,
    headers: {
      'Content-Type': 'application/json',
      'TENANT-ID': tenantId,
      AUTHORIZATION: `Bearer ${token}`,
    },
  };

  // 如果是GET请求，将参数附加到URL
  if (method === 'GET') {
    const queryParams = new URLSearchParams();
    Object.entries(requestParams).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        queryParams.append(key, String(value));
      }
    });
    const queryString = queryParams.toString();
    if (queryString) {
      requestUrl += (requestUrl.includes('?') ? '&' : '?') + queryString;
    }
  } else {
    // 非GET请求，将参数放在body中
    requestConfig.body = JSON.stringify(requestParams);
  }

  // 发送请求并处理响应
  fetch(requestUrl, requestConfig)
    .then(response => {
      if (!response.ok) {
        throw new Error('API request failed');
      }
      return response.json();
    })
    .then(data => {
      // 根据配置的路径获取数据
      let result = data;
      
      const paths = 'data.records'.split('.');
      for (const path of paths) {
        result = result[path];
        if (result === undefined) break;
      }

      // 处理数据
      const processData = (items) => {
        return items.map(item => ({
          label: item['materialName'],
          value: item['materialCode'],
          
        }));
      };

      options.value = Array.isArray(result) ? processData(result) : [];
    })
    .catch(error => {
      console.error('Failed to fetch API options:', error);
      options.value = [];
    });
}
export const useTable = () => {
  const materialCodeOptions = ref([]);
  fetchMaterialCodeOptions(materialCodeOptions);

  const searchFields = computed(() => [
    {
      prop: 'materialName',
      label: '物料名称',
      component: 'el-input',
      componentAttrs: {
        placeholder: '请输入',
        clearable: true,
      }
    },
    {
      prop: 'locationName',
      label: '适用部门',
      component: 'el-input',
      componentAttrs: {
        placeholder: '请输入',
        clearable: true,
      },
    },
    {
      prop: 'indicatorName',
      label: '指标名称',
      component: 'el-input',
      componentAttrs: {
        placeholder: '请输入',
        clearable: true,
      }
    },
    {
      label: '状态',
      prop: 'status',
      component: 'el-select',
      componentAttrs: {
        placeholder: '请选择状态',
        clearable: true,
        filterable: true,
        collapseTags: true,
        multiple: true,
      },
      options: [
        { label: '启用', value: 'ENABLED' },
        { label: '禁用', value: 'DISABLED' },
      ],
    },
  ]);

  const columns = computed(() => [
    {
      label: '序号',
      type: 'index',
    },
    {
      label: '适用物料编码',
      prop: 'materialCode'
    },
    {
      label: '物料名称',
      prop: 'materialName',
    },
    // {
    //   label: '指标编码',
    //   prop: 'indicatorCode',
    // },
    {
      label: '指标名称',
      prop: 'indicatorName',
    },
    // {
    //   label: '牧场',
    //   prop: 'locationName',
    // },
    // {
    //   label: '指标描述',
    //   prop: 'description',
    // },
    {
      label: '适用部门',
      prop: 'locationNameList',
      formatter(row) {
        return row.locationNameList?.join(',') || '全部部门'
      }
    },
    {
      label: '生效日期',
      prop: 'releaseDate',
    },
    {
      label: '失效日期',
      prop: 'expiryDate',
    },
    // {
    //   label: '是否适用于所有场地',
    //   prop: 'applyAllLocations',
    //   formatter(row){
    //     const value = row['applyAllLocations'];
    //     const options = getDict.value('yes_no_type') || [];;
    //     if (Array.isArray(value)) {
    //       return value.map((item) => options?.find((opt) => opt.value == item)?.label).join('、');
    //     }
    //     return options?.find((item) => item.value == value)?.label || '--';
    //   }
    // },
    {
      label: '状态',
      prop: 'status',
      formatter(row){
        const value = row['status'];
        return value === 'ENABLED' ? '启用' : '禁用';
      }
    },
    {
      label: '创建人',
      prop: 'createBy',
    },
    {
      label: '创建时间',
      prop: 'createTime',
      formatter(row) {
        return row.createTime?.split(' ')[0];
      }
    },
    {
      label: '修改人',
      prop: 'updateBy',
    },
    {
      label: '修改时间',
      prop: 'updateTime',
      formatter(row) {
        return row.updateTime?.split(' ')[0];
      }
    },
    {
      prop: "action",
      fixed: "right",
      width: 150,
      label: "操作",
    },
  ])
  return {
    columns,
    searchFields,
  };
};