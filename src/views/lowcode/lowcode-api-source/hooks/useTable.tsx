import { ref, computed } from 'vue';
import moment from 'moment';
import { useDict } from '@/hooks/dict';
import { ElImage } from 'element-plus';
import { Session } from '/@/utils/storage';

const getDict = computed(() => (str) => {
	if (!str) return [];
	const value = useDict(str)?.[str]?.value;
	return value;
});

export const useTable = () => {
	const searchFields = computed(() => [
		{
			prop: 'name',
			label: '名称',
			component: 'el-input',
			componentAttrs: {
				placeholder: '请选择名称',
				clearable: true,
			},
		},
		{
			label: '接口分类',
			prop: 'type',
			component: 'el-select',
			enums: [
				{
					label: '树状接口',
					value: 'tree',
				},
				{
					label: '数组接口',
					value: 'arr',
				},
			],
			componentAttrs: {
				placeholder: '请选择接口分类',
				clearable: true,
				filterable: true,
				multiple: true,
				collapseTags: true,
			},
		},
		{
			prop: 'apiName',
			label: '接口名称',
			component: 'el-input',
			componentAttrs: {
				placeholder: '请选择接口名称',
				clearable: true,
			},
		},
	]);

	const columns = computed(() => [
		{
			label: '序号',
			type: 'index',
		},
		{
			label: '名称',
			prop: 'name',
		},
		{
			label: '接口分类',
			prop: 'type',
			formatter(row) {
				const value = row['type'];
				const options = [
					{ label: '树状接口', value: 'tree' },
					{ label: '数组接口', value: 'arr' },
				];
				if (Array.isArray(value)) {
					return value.map((item) => options?.find((opt) => opt.value == item)?.label).join('、');
				}
				return options?.find((item) => item.value == value)?.label || '--';
			},
		},
		{
			label: '接口名称',
			prop: 'apiName',
		},
		{
			label: '请求方法',
			prop: 'method',
		},
		{
			label: '数据路径',
			prop: 'dataPath',
		},
		{
			label: '子节点字段',
			prop: 'childrenField',
		},
		{
			label: '标签字段',
			prop: 'labelField',
		},
		{
			label: '值字段',
			prop: 'valueField',
		},
		{
			label: '备注',
			prop: 'remark',
		},
		{
			prop: 'action',
			fixed: 'right',
			width: 150,
			label: '操作',
		},
	]);
	return {
		columns,
		searchFields,
	};
};
