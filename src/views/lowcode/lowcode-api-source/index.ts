import request from "@/utils/request"

const projectName ='/admin'
export function fetchList(data={}, query?: Object) {
  return request({
    url: `${projectName}/lowcodeApiSource/page`,
    method: 'post',
    params: query,
    data
  })
}

export function addObj(obj?: Object) {
  return request({
    url: `${projectName}/lowcodeApiSource/save`,
    method: 'post',
    data: obj
  })
}

export function getObj(id?: string) {
  return request({
    url: `${projectName}/lowcodeApiSource/${id}`,
    method: 'get'
  })
}

export function delObjs(ids?: Object) {
  return request({
    url: `${projectName}/lowcodeApiSource/remove`,
    method: 'get',
    params: ids
  })
}

export function putObj(obj?: Object) {
  return request({
    url: `${projectName}/lowcodeApiSource/update`,
    method: 'post',
    data: obj
  })
}

export function exportData(data, params) {
  return request({
    url: `${projectName}/lowcodeApiSource/export`,
    method: 'post',
    responseType: 'blob',
    data,
    params,
  });
}

export function downloadTemp() {
  return request({
    url: `${projectName}/lowcodeApiSource/template`,
    method: 'get',
    responseType: 'blob',
  });
}

export function importData(data) {
  return request({
    url: `${projectName}/lowcodeApiSource/import`,
    method: 'post',
    data,
  });
}