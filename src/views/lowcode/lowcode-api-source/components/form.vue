<template>
  <yun-drawer
    v-model="visible"
    destroy-on-close
    modal
    custom-class="form-drawer"
    close-on-click-modal
    confirm-button-text="确定"
    cancel-button-text="取消"
    :confirm-button-disabled="loading"
    :title="title"
    :size="size"
    @confirm="confirmHandler"
    @close="closeHandler"
  >
    <el-form
      ref="vForm"
      :model="formData"
      :rules="rules"
      label-width="120"
      label-position="top"
    >
      <el-row :gutter="12">
        <el-col :span="12">
          <el-form-item label="名称" prop="name">
            <el-input v-model="formData.name" type="text" clearable icon="custom-search" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="接口分类" prop="type">
            <el-select v-model="formData.type" style="width: 100%;" clearable :multipleLimit="0">
                <el-option
                  label="树状接口"
                  value="tree"
                />
                <el-option
                  label="数组接口"
                  value="arr"
                />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="接口路径" prop="apiName">
            <el-input v-model="formData.apiName" type="text" clearable icon="custom-search" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="请求方法" prop="method">
            <el-select v-model="formData.method" style="width: 100%;" clearable>
              <el-option
                label="GET"
                value="get"
              />
              <el-option
                label="POST"
                value="post"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="入参配置" prop="params">
            <div class="mb-2">
              <el-button type="primary" size="small" @click="addParam">添加参数</el-button>
            </div>
            <el-table :data="formData.params" border>
              <el-table-column label="参数名" width="150">
                <template #default="{ row }">
                  <el-input v-model="row.key" placeholder="参数名" />
                </template>
              </el-table-column>
              <el-table-column label="参数类型" width="150">
                <template #default="{ row }">
                  <el-select v-model="row.type" class="w-full">
                    <el-option label="静态值" value="static" />
                    <el-option label="动态值" value="dynamic" />
                    <el-option label="搜索值" value="search" />
                  </el-select>
                </template>
              </el-table-column>
              <el-table-column label="参数值">
                <template #default="{ row }">
                  <el-input
                    v-model="row.value"
                    :placeholder="getParamPlaceholder(row.type)"
                    :disabled="row.type === 'search'"
                  >
                    <template #prefix v-if="row.type === 'dynamic'">form.</template>
                  </el-input>
                </template>
              </el-table-column>
              <el-table-column label="操作" width="80" align="center">
                <template #default="{ $index }">
                  <el-button type="text" @click="removeParam($index)"
                    >删除</el-button
                  >
                </template>
              </el-table-column>
            </el-table>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="返回值路径" prop="dataPath">
            <el-input
              v-model="formData.dataPath"
              placeholder="请输入返回值的筛选项路径"
            >
              <template #prefix>res.</template>
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="标签字段" prop="labelField">
            <el-input v-model="formData.labelField" type="text" clearable icon="custom-search" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="值字段" prop="valueField">
            <el-input v-model="formData.valueField" type="text" clearable icon="custom-search" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="禁用字段" prop="disabledField">
            <el-input v-model="formData.disabledField" type="text" clearable icon="custom-search" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="子节点字段" prop="childrenField">
            <el-input v-model="formData.childrenField" type="text" clearable icon="custom-search" />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="备注" prop="remark">
            <el-input v-model="formData.remark" type="textarea" :rows="3" />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </yun-drawer>
</template>

<script setup lang="ts">
import { ref, reactive, computed, nextTick } from 'vue';
import { ElMessage } from 'yun-design';
import { Plus, Delete } from '@element-plus/icons-vue';
import { useDict } from '@/hooks/dict';
import { addObj, putObj, getObj } from '@/api/lowcode/lowcode-api-source/index';
import { useUserInfo } from '@/stores/userInfo';
import { rule } from '@/utils/validate';
import StaticGroup from '@/components/StaticGroup/index.vue';
import { Session } from '@/utils/storage';
import moment from 'moment';

// 状态管理
const visible = ref(false);
const loading = ref(false);
const isEdit = ref(false);
const vForm = ref();
const formData = reactive({});
const rules = reactive({
  name: [
    {
      required: true,
      message: '请输入名称',
      trigger: 'blur',
    },
  ],
  type: [
    {
      required: true,
      message: '请选择接口分类',
      trigger: 'change',
    },
  ],
  apiName: [
    {
      required: true,
      message: '请输入接口路径',
      trigger: 'blur',
    },
  ],
  method: [
    {
      required: true,
      message: '请选择请求方法',
      trigger: 'change',
    },
  ]
});

// 计算属性
const title = computed(() => (isEdit.value ? '编辑' : '新增'));
const size = computed(() => 'large');

// 用户信息
const userStore = useUserInfo();
const emits = defineEmits(['getData']);
// 添加参数
function addParam() {
  if (!formData.params) {
    formData.params = [];
  }
  formData.params.push({
    key: '',
    type: 'static',
    value: '',
  });
}

// 删除参数
function removeParam(index: number) {
  formData.params.splice(index, 1);
}

// 获取参数输入框的placeholder
function getParamPlaceholder(type: string) {
  switch (type) {
    case 'static':
      return '请输入静态值';
    case 'dynamic':
      return '请输入表单字段名';
    case 'search':
      return '搜索值无需填写';
    default:
      return '';
  }
}
// 为每个API组件创建响应式变量


// 为每个API组件创建获取数据的方法


// 初始化时获取所有API数据


// 关闭处理
function closeHandler() {
  visible.value = false;
}

// 格式化表单数据
async function formatFrom(row: Record<string, any>) {
  for(let key in formData) {
    delete formData[key];
  }
  if (!row.id) return;
  const res = await getObj(row.id);
  Object.assign(formData, res.data);

}

// 格式化提交参数
function formatParams() {
  const baseParams = {};
  
  // 处理特殊字段类型


  return {
    ...formData,
    ...baseParams,
  };
}

// 确认处理
async function confirmHandler(done: () => void, loading: { value: boolean }) {
  try {
    await vForm.value?.validate();
    loading.value = true;
    const api = isEdit.value ? putObj : addObj;
    const params = formatParams();
    await api(params);
    done();
    emits('getData', formData);
  } finally {
    loading.value = false;
  }
}

// 显示表单
function show(row?: Record<string, any>) {
  visible.value = true;
  isEdit.value = !!row;
  formatFrom(row || {});
  nextTick(() => {
    vForm.value?.clearValidate();
  });
}

// 日期时间范围默认值
const defaultTime = [new Date(2000, 1, 1, 0, 0, 0), new Date(2000, 2, 1, 23, 59, 59)];

// 字典数据
const getDict = computed(() => (str: string) => {
  if (!str) return [];
  const value = useDict(str)?.[str]?.value;
  return value;
});

// 生成明细表新行的初始数据
function getEnhancedTableRow(widget: Widget): Record<string, any> {
  const row: Record<string, any> = {};
  if (Array.isArray(widget.widgetList)) {
    for (const colWidget of widget.widgetList) {
      const name = colWidget.options?.name;
      if (!name) continue;
      let value = colWidget.options?.defaultValue;
      if (value === undefined) {
        if (colWidget.type === 'number') value = 0;
        else value = '';
      }
      row[name] = value;
    }
  }
  return row;
}

// 添加明细表行
function handleAddRow(tableName: string) {
  if (!formData[tableName]) {
    formData[tableName] = [];
  }
  
  formData[tableName].push({});
}

// 删除明细表行
function handleRemoveRow(tableName: string, index: number) {
  if (!formData[tableName]) return;
  formData[tableName].splice(index, 1);
}

// 暴露方法
defineExpose({
  show,
  formData,
  rules
});
</script>

<style lang="scss">
.form-drawer {
  .el-form .el-form-item:last-of-type {
    margin-bottom: 24px !important;
  }
}
</style>

<style lang="scss" scoped>
.el-form-item {
  margin-bottom: 18px;
}

.table-header {
  margin-bottom: 15px;
  .label {
    font-weight: bold;
    font-size: 18px;
    padding-left: 8px;
    border-left: 4px solid var(--el-color-primary);
    line-height: 1.2;
  }
}

:deep(.el-form-item__label) {
  font-weight: normal;
}

:deep(.el-table) {
  .el-table__header-wrapper {
    th {
      background-color: var(--el-fill-color-light);
    }
  }
}
:deep(.el-input--prefix .el-input__inner) {
  padding-left: 42px !important;
}
</style>