<template>
  <yun-drawer
    v-model="visible"
    :show-cancel-button="false"
    destroy-on-close
    title="详情"
    size="X-large"
    @confirm="handleConfirm"
  >
    <Detail :groups="processedGroups" :data="form"/>
  </yun-drawer>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import Detail from '@/components/Detail/index.vue';
import { getObj } from '@/api/lowcode/lowcode-api-source/index';

interface FormData {
  [key: string]: any;
}

// 静态生成的分组配置
const processedGroups = [
  {
    title: "",
    columns: [
      {
        prop: 'name',
        label: '名称',
        type: 'text'
      },
      {
        prop: 'type',
        label: '接口分类',
        type: 'enums',
        attrs: {
          options: [
            {
              label: '树状接口',
              value: 'tree'
            },
            {
              label: '数组接口',
              value: 'arr'
            }
          ],
          dataSourceType: 'custom',
          dictType: ''
        }
      },
      {
        prop: 'apiName',
        label: '接口名称',
        type: 'text'
      },
      {
        prop: 'method',
        label: '请求方法',
        type: 'text'
      },
      {
        prop: 'params',
        label: '入参配置',
        type: 'images'
      },
      {
        prop: 'dataPath',
        label: '数据路径',
        type: 'text'
      },
      {
        prop: 'childrenField',
        label: '子节点字段',
        type: 'text'
      },
      {
        prop: 'labelField',
        label: '标签字段',
        type: 'text'
      },
      {
        prop: 'valueField',
        label: '值字段',
        type: 'text'
      },
      {
        prop: 'remark',
        label: '备注',
        type: 'text'
      }
    ]
  }
];

const visible = ref(false);
const form = ref<FormData>({});

async function handleConfirm(done: () => void) {
  done();
}

async function show(row: FormData) {
  const res = await getObj(row.id);
  form.value = res.data;
  visible.value = true;
}

defineExpose({
  show,
});
</script>

<style scoped lang="scss">
</style>