<template>
  <div class="proposal-container">
    <yun-pro-table
      ref="tableRef"
      v-model:pagination="state.page"
      v-model:searchData="state.searchData"
      :search-fields="searchFields"
      :layout="'whole'"
      :auto-height="true"
      :table-columns="tableColumns"
      :remote-method="remoteMethod"
      :table-props="{ showTableSetting: true }"
    >
      <template #tableHeaderLeft>
        <el-button
          type="primary"
          @click="toDetail()"
        >
          新建项目
        </el-button>
      </template>
      <template #t_bidOpenTime="{ row }">
        <TimeCountdown :time="row.bidOpenTime || ''" />
      </template>
      <template #t_relationPlanCodeList="{ row }">
        <div v-if="row.relationPlanCodeList && row.relationPlanCodeList.length">
          <template
            v-for="planCode in row.relationPlanCodeList"
            :key="planCode"
          >
            <a
              :href="`/purchasing/plan/detail/index?planCode=${planCode}&type=view`"
              style="color: #0069ff; text-decoration: none; margin-right: 8px; display: block"
              @click.prevent="toPlanDetail(planCode)"
            >
              {{ planCode }}
            </a>
          </template>
        </div>
        <span v-else>-</span>
      </template>
      <template #t_action="scope">
        <el-button
          type="text"
          @click="toDetail(scope.row, 'view')"
        >
          查看
        </el-button>
        <el-button
          @click="toDetail(scope.row)"
          type="text"
          v-if="(!['APPROVE', 'APPROVING'].includes(scope.row.approveStatus) || scope.row.status === 'EFFECT') && hasEditAuth(scope.row.createById)"
        >
          编辑
        </el-button>
        <el-button
          @click="handleDel(scope.row, refreshTable)"
          type="text"
          v-if="!['APPROVE', 'APPROVING'].includes(scope.row.approveStatus)"
        >
          删除
        </el-button>
        <el-button
          @click="handleCancelApproval(scope.row, refreshTable)"
          type="text"
          v-if="['APPROVING'].includes(scope.row.approveStatus)"
        >
          撤销审批
        </el-button>
        <el-button
          @click="toBidding(scope.row)"
          type="text"
          v-if="scope.row.approveStatus === 'APPROVE'"
        >
          进入项目进程
        </el-button>
        <el-button
          @click="handleTerminate(scope.row, refreshTable)"
          type="text"
          v-if="['EFFECT'].includes(scope.row.status) && scope.row.approveStatus !== 'APPROVING'"
        >
          终止
        </el-button>
      </template>
    </yun-pro-table>
  </div>
</template>

<script setup name="purchasingProjectProposal" lang="ts">
import { ref, reactive, computed } from 'vue';
import { proposalListApi } from '@/api/purchasing/proposal.ts';
import useOptions from './hooks/useOptions.jsx';
import useHandler from './hooks/useHandler.js';
import TimeCountdown from './components/timeCountdown.vue';
import { useUserInfo } from '/@/stores/userInfo';

// 用户信息
const stores = useUserInfo();
const { userInfos } = storeToRefs(stores);

const { searchFields, tableColumns } = useOptions();
const { toDetail, handleDel, toBidding, handleCancelApproval, toPlanDetail, handleTerminate } = useHandler();

const state = reactive({
  page: {
    total: 0, // 总页数
    page: 1, // 当前页数
    size: 20, // 每页显示多少条
  },
  searchData: {},
  loading: false,
});
const tableRef = ref();

const userId = computed(() => userInfos?.value?.user?.userId);

const hasEditAuth = computed(() => (createById: any) => {
  if (!userId.value) return false;
  return createById == userId.value;
});

const remoteMethod = async ({ searchData, pagination }) => {
  const { bidOpenTime, ...rest } = searchData;
  const { data } = await proposalListApi(
    {
      ...rest,
      bidOpenStartTime: bidOpenTime && bidOpenTime.length ? bidOpenTime[0] : '',
      bidOpenEndTime: bidOpenTime && bidOpenTime.length > 1 ? bidOpenTime[1] : '',
    },
    { current: pagination.page, size: pagination.size }
  );
  return data;
};
const refreshTable = () => {
  state.page.page = 1;
  tableRef.value?.getData?.();
};
</script>

<style lang="scss" scoped>
.proposal-container {
  flex: 1;
  display: flex;
  .pro_table.autoHeight {
    height: auto !important;
    flex: 1;
  }
}
</style>
