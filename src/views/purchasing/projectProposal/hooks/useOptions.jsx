import { computed } from 'vue';
import { useDict } from '@/hooks/dict';
const getDict = computed(() => (str) => {
  if (!str) return [];
  const value = useDict(str)?.[str]?.value;
  return value;
});

export default () => {
  const tableColumns = [
    {
      label: '序号',
      prop: 'index',
      type: 'index',
    },
    {
      label: '项目编号',
      prop: 'projectCode',
      width: 170
    },
    {
      label: '项目名称',
      prop: 'projectName',
    },
    {
      label: '采购业务类型',
      prop: 'serviceTypeName',
      minWidth: 150,
    },
    {
      label: '寻源方式',
      prop: 'sourcingType',
      formatter(row){
        const value = row['sourcingType'];
        const options = getDict.value('plan_recruit_method') || [];
        if (Array.isArray(value)) {
          return value.map((item) => options?.find((opt) => opt.value == item)?.label).join('、');
        }
        return options?.find((item) => item.value == value)?.label || '-';
      }
    },
    {
      label: '邀请方式',
      prop: 'inviteMethod',
      formatter(row){
        const value = row['inviteMethod'];
        return value === 'INVITE' ? '邀请' : '公开';
      }
    },
    {
      label: '需求部门',
      prop: 'purchaseDeptName',
    },
    {
      label: '采购部门',
      prop: 'buyerDeptName',
    },
    {
      label: '项目预算',
      prop: 'budgetAmount',
    },
    {
      label: '支付方式',
      prop: 'paymentName',
    },
    {
      label: '账期时间',
      prop: 'paymentPeriodName',
      formatter(row) {
        return row.paymentPeriodName === 'null' ? '-' : row.paymentPeriodName
      }
    },
    {
      label: '项目负责人',
      prop: 'projectLeaderName',
      minWidth: 150,
    },
    {
      label: '报名截止时间',
      prop: 'registerEndTime',
      width: 170,
    },
    {
      label: '报价开始时间',
      prop: 'quoteStartTime',
      width: 170,
    },
    {
      label: '报价截止时间',
      prop: 'quoteEndTime',
      width: 170,
    },
    {
      label: '开标时间',
      prop: 'bidOpenTime',
      width: 170,
    },
    {
      label: '单据状态',
      prop: 'status',
      formatter(row){
        const value = row['status'];
        const options = getDict.value('project_status') || [];
        return options?.find((item) => item.value == value)?.label || '-';
      }
    },
    {
      label: '审批状态',
      prop: 'approveStatus',
      formatter(row){
        const value = row['approveStatus'];
        const options = getDict.value('project_approve_status') || [];
        return options?.find((item) => item.value == value)?.label || '-';
      }
    },
    {
      label: '关联采购计划',
      prop: 'relationPlanCodeList',
      minWidth: 170,
    },
    {
      label: '操作',
      prop: 'action',
      width: 250,
      fixed: 'right',
    },
  ]
  const searchFields = computed(() => {
    return[
      {
        label: '项目编号/名称',
        prop: 'searchContent',
        component: 'el-input',
      },
      {
        label: '项目进度',
        prop: 'progressStatus',
        component: 'el-select',
        attrs: {
          multiple: true,
          filterable: true,
        },
        enums: getDict.value('project_process') || [],
      },
      {
        label: '项目负责人',
        prop: 'projectLeaderName',
        component: 'el-input',
      },
      {
        label: '寻源方式',
        prop: 'sourcingType',
        component: 'el-select',
        attrs: {
          multiple: true,
          filterable: true,
          clearable: true,
        },
        enums: getDict.value('plan_recruit_method') || [],
      },
      {
        label: '采购单位',
        prop: 'buyerDeptName',
        component: 'el-input',
      },
      {
        label: '开标起止时间',
        prop: 'bidOpenTime',
        component: 'el-date-picker',
        colAttrs: {
          span: 12,
        },
        componentAttrs: {
          startPlaceholder: '开始时间',
          endPlaceholder: '结束时间',
          type: 'datetimerange',
          format: 'YYYY-MM-DD HH:mm:ss',
          valueFormat: 'YYYY-MM-DD HH:mm:ss',
          clearable: true,
          defaultTime: [new Date(2000, 1, 1, 0, 0, 0), new Date(2000, 2, 1, 23, 59, 59)],
        },
      },
    ]
  })
  return {
    searchFields,
    tableColumns,
  };
}