import { useRouter } from 'vue-router';
import { ElMessage, ElMessageBox } from 'yun-design';
import { procurementPlanRemove, cancelApproveProposal, terminateProject } from '@/api/purchasing/proposal.ts';

export default () => {
  const router = useRouter();
  const toDetail = (row, pageType) => {
    let pageTypeName = '';
    if(pageType) {
      pageTypeName = '查看';
    } else {
      if(row?.id) {
        pageTypeName = '编辑';
      } else {
        pageTypeName = '新增';
      }
    }
    router.push({
      path: '/purchasing/projectProposal/detail/index',
      query: {
        projectCode: row?.projectCode,
        pageType: pageType || row?.status === 'EFFECT' ? 'view' : '', // 如果是生效状态，则只能查看
        partialEdit: row?.status === 'EFFECT' && !pageType, // 是否是局部编辑
        tagsViewName: `${pageTypeName}项目`
      }
    })
  };
  // 撤销审批
  const handleCancelApproval = (row, refreshTable) => {
    ElMessageBox.confirm('确定要撤销审批吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })
      .then(() => {
        cancelApproveProposal({ bizType: row.status === 'EFFECT' ? 'SRM_PROCUREMENT_PROJECT_CANCEL' : 'SRM_PROCUREMENT_PROJECT', bizKey: row.projectCode }).then(() => {
          refreshTable?.();
          ElMessage({
            type: 'success',
            message: '撤销审批成功！',
          });
        });
      })
      .catch(() => { });
  };
  const handleDel = (item, refreshTable) => {
    ElMessageBox.confirm('确定要删除采购项目', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })
      .then(() => {
        procurementPlanRemove(item.id).then(() => {
          refreshTable();
          ElMessage({
            type: 'success',
            message: '删除成功！',
          });
        });
      })
      .catch(() => {
        ElMessage({
          type: 'info',
          message: '删除失败',
        });
      });
  };
  // 进入项目流程
  const toBidding = (row) => {
    router.push({
      path: '/procurementSourcing/biddingProcess/index',
      query: {
        projectCode: row?.projectCode,
        pageType: 'biddingView',
      }
    })
  };
  // 跳转到采购计划详情
  const toPlanDetail = (planCode) => {
    router.push(`/purchasing/plan/detail/index?planCode=${planCode}&pageType=view`);
  };

  // 终止
  const handleTerminate = (row, refreshTable) => {
    ElMessageBox.confirm('确定要终止该采购项目吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })
      .then(async () => {
        await terminateProject({ bizType: 'SRM_PROCUREMENT_PROJECT_CANCEL', bizKey: row.projectCode, args: [row.projectCode, row.projectCode] });
        refreshTable?.();
        ElMessage({
          type: 'success',
          message: '终止成功！',
        });
      })
      .catch(() => {});
  };

  return {
    toDetail,
    handleDel,
    toBidding,
    handleCancelApproval,
    toPlanDetail,
    handleTerminate,
  }
}