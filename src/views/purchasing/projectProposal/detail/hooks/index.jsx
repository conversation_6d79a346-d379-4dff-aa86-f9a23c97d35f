import { ref, computed, nextTick, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { rule } from '/@/utils/validate';
import { deptTree } from '@/api/admin/dept';
import { getCityList, getAgentListApi, getPlanDetail } from '@/api/purchasing/plan';
import { getUserListApi, getProposalDetail } from '@/api/purchasing/proposal';
import FileList from '@/views/purchasing/plan/detail/components/useFileList.jsx';
import { anchorList, inviteMethodOptions, yesOrNo, memberOptions } from '../../constant';
import { useUserInfo } from '/@/stores/userInfo';
import { storeToRefs } from 'pinia';
import { ElMessage } from 'yun-design';
import { useDict } from '@/hooks/dict';
import { getBaseServiceType } from '@/api/purchasing/config';
import { useDynamicFields } from './useDynamicFields';
import { fetchList as getPaymentMethod<PERSON>ist<PERSON><PERSON>, getObj as getPaymentPeriodListApi } from '@/api/lowcode/base-payment-method';
import { deepClone } from '/@/utils/other';

const loading = ref(false);

const choosePlanRef = ref();
const chooseMaterialRef = ref();
const choosePlanMaterialRef = ref();

const typeOptions = ref([])
const agentOptions = ref([])
const paymentMethodOptions = ref([])
const userList = ref([])

// 计划带入的招标代理机构为空时，需要允许录入，录入后可修改；
// 计划带入的招标代理机构有值时，不允许修改；
const agencyDisabled = ref(false);

// 当前标段
const activeName = ref('0');

// 选择的数据
const selectedRow = ref([]);
const selectedPlans = ref([]);

// 字典数据
const getDict = computed(() => (str) => {
  if (!str) return [];
  const value = useDict(str)?.[str]?.value;
  return value;
});

// 用户信息
const stores = useUserInfo();
const { userInfos } = storeToRefs(stores);

// 当前活跃的锚点
const activeAnchor = ref('basic');
const isSliderReady = ref(false);
// 滑块位置
const sliderStyle = ref({
  transform: 'translateY(0px)',
  height: '16px',
  opacity: 1, // 改为1，确保初始可见
});
// 折叠面板状态
const activeCollapse = ref({
  basic: ['basic'],
  payment: ['payment'],
  timeLimit: ['timeLimit'],
  team: ['team'],
  target: ['target'],
  list: ['list'],
  attachments: ['attachments'],
});

// 初始化表单数据
const form = ref({
  lotType: 0,
  paymentInfoList: [{ paymentMethodId: '', paymentPeriodId: '', paymentRemark: '' }],
  projectMembers: [{ role: 'PROJECT_LEADER', userId: '', contactPhone: '' }],
  sections: [{ projectItems: [] }],
});

// 部门相关
const treeDeptData = ref([]);
const getTree = () => {
  deptTree().then((response) => {
    treeDeptData.value = response.data;
  });
};
getTree();

// 省市区相关
const cityList = ref([]);
const getCityTree = () => {
  return getCityList().then((response) => {
    cityList.value = response?.districts[0]?.districts;
  });
};
const getNames = (carrierLocation) => {
  const arr = [];
  if (carrierLocation.length) {
    const [provin, city, county] = carrierLocation;
    const item = cityList.value.find((ele) => ele.adcode === provin);
    arr.push(item.name);
    const cityItem = item?.districts?.find((ele) => ele.adcode === city);
    arr.push(cityItem?.name);
    const countyItem = cityItem?.districts?.find((ele) => ele.adcode === county);
    arr.push(countyItem?.name);
  }
  return arr;
};
const handleChangeCity = (val) => {
  console.log(val, '省市区')
  if (val && val.length) {
    const [provinceName, cityName, areaName] = getNames(val);
    form.value.provinceName = provinceName;
    form.value.province = val[0];
    form.value.cityName = cityName;
    form.value.city = val[1];
    form.value.countyName = areaName;
    form.value.district = val[2];
  } else {
    form.value.province = '';
    form.value.city = '';
    form.value.district = '';
  }

};

export const useTable = (props, indicatorDetailRef, formRef, projectTimeRef, attachRef, lotRef, formRefs) => {
  const route = useRoute();
  const router = useRouter();
  const pageType = computed(() => route?.query?.pageType || props.pageType); // 区分新增、编辑或详情
  const partialEdit = computed(() => route?.query?.partialEdit === 'true'); // 项目生效状态下部分可修改的数据
  // 动态表单相关逻辑全部交由useDynamicFields管理
  const {
    fetchDynamicFields,
    formFields,
    tableColumns,
    mergedTableFormConfig,
    dynamicFields,
    getDynamicFieldsByPlanId,
  } = useDynamicFields({ form, selectedRow, typeOptions, pageType, indicatorDetailRef, cityList });
  // 基础信息
  const basicColumns = computed(() => {
    return [
      {
        prop: 'planCode',
        label: '采购计划',
        type: 'input',
        colProps: { span: 12 },
        attrs: {
          onClick: () => {
            choosePlanRef.value.show();
          }
        },
        show: () => !pageType.value,
      },
      {
        prop: 'planCode',
        label: '采购计划',
        type: '',
        colProps: { span: 12 },
        render: (row) => {
          if (pageType.value && row.relationPlanCodeList && row.relationPlanCodeList.length > 0) {
            return (
              <div>
                {row.relationPlanCodeList.map((planCode, index) => (
                  <a
                    key={index}
                    href={`/purchasing/plan/detail/index?planCode=${planCode}&pageType=view`}
                    style={{ color: '#0069ff', textDecoration: 'none', display: 'block' }}
                    onClick={(e) => {
                      e.preventDefault();
                      router.push(`/purchasing/plan/detail/index?planCode=${planCode}&pageType=view`);
                    }}
                  >
                    {planCode}
                  </a>
                ))}
              </div>
            );
          }
        },
        show: () => pageType.value,
      },
      {
        prop: 'projectName',
        label: '项目名称',
        type: pageType.value ? '' : 'input',
        rules: [
          { required: true, message: '项目名称不能为空', trigger: 'blur' },
          { max: 30, message: '长度在30个字符内' },
        ],
        colProps: { span: 12 },
      },
      {
        prop: 'serviceTypeId',
        label: '采购业务类型',
        type: pageType.value ? '' : 'select',
        enums: typeOptions.value,
        rules: [
          { required: true, message: '请选择采购业务类型', trigger: 'change' },
        ],
        colProps: { span: 12 },
        attrs: {
          disabled: !!form.value.planCode,
          onChange: (val) => {
            fetchDynamicFields(val, form.value.sourcingType);
          },
        },
        show: () => !pageType.value
      },
      {
        prop: 'serviceTypeId',
        label: '采购业务类型',
        type: '',
        rules: [
          { required: true, message: '请选择采购业务类型', trigger: 'change' },
        ],
        colProps: { span: 12 },
        show: () => pageType.value,
        render: (row) => {
          let obj = typeOptions.value.find(i => i.value === row.serviceTypeId);
          return (<span>{obj?.label || '-'}</span>)
        },
      },
      {
        prop: 'sourcingType',
        label: '计划寻源方式',
        type: pageType.value ? '' : 'select',
        enums: getDict.value('plan_recruit_method'),
        colProps: { span: 12 },
        attrs: {
          clearable: true,
          onChange: (val) => {
            if (['XJCG', 'JJCG', 'ZB', 'JZTP'].includes(val)) {
              form.value.inviteMethod = 'PUBLICITY';
              form.value.preQualification = 1;
            } else {
              form.value.inviteMethod = 'INVITE';
              form.value.preQualification = 0;
            }
            form.value.bidOpenTime = null;
            form.value.quoteTime = null;
            form.value.registerTime = null;
            form.value.preReviewTime = null;
            form.value.tenderFeePayTime = null;
            form.value.tenderFileGainTime = null;
            form.value.tenderClarifyTime = null;
            nextTick(() => {
              projectTimeRef?.value?.elForm?.clearValidate();
            })
            form.value.serviceTypeId && fetchDynamicFields(form.value.serviceTypeId, val);
          },
          disabled: !!form.value.planCode,
        },
        rules: [
          { required: true, message: '请选择计划寻源方式', trigger: 'change' },
        ],
        show: () => !pageType.value
      },
      {
        prop: 'sourcingType',
        label: '计划寻源方式',
        type: '',
        colProps: { span: 12 },
        show: () => pageType.value,
        render: (row) => {
          let planRecruitMethod = getDict.value('plan_recruit_method');
          let obj = planRecruitMethod.find(i => i.value === row.sourcingType);
          return (<span>{obj?.label || '-'}</span>)
        },
        rules: [
          { required: true, message: '请选择计划寻源方式', trigger: 'change' },
        ]
      },
      {
        prop: 'inviteMethod',
        label: '邀请方式',
        type: 'radio',
        enums: inviteMethodOptions,
        rules: [
          { required: true, message: '请选择邀请方式', trigger: 'change' },
        ],
        attrs: {
          disabled: ['ZJWT', 'KSXJ'].includes(form.value.sourcingType),
          onChange: (val) => {
            if(val === 'INVITE') {
              form.value.inviteReceipt = 1;
              form.value.preQualification = 0;
            } else {
              form.value.inviteReceipt = 2;
              form.value.preQualification = 1;
            }
          }
        },
        colProps: { span: 12 },
        show: () => !pageType.value,
      },
      {
        prop: 'inviteMethod',
        label: '邀请方式',
        type: '',
        colProps: { span: 12 },
        show: () => pageType.value,
        render: (row) => {
          let obj = inviteMethodOptions.find(i => i.value === row.inviteMethod);
          return (<span>{obj?.label}</span>)
        }
      },
      {
        prop: 'inviteReceipt',
        label: '邀请是否回执',
        type: 'radio',
        enums: yesOrNo,
        rules: [
          { required: true, message: '请选择邀请是否回执', trigger: 'change' },
        ],
        show: () => !pageType.value && form.value.inviteMethod === 'INVITE',
        colProps: { span: 12 },
      },
      {
        prop: 'inviteReceipt',
        label: '邀请是否回执',
        type: '',
        show: () => pageType.value && form.value.inviteMethod === 'INVITE',
        colProps: { span: 12 },
        render: (row) => {
          let obj = yesOrNo.find(i => i.value === row.inviteReceipt);
          return (<span>{obj?.label}</span>)
        }
      },
      {
        prop: 'sourcingMethod',
        label: '采购方式',
        type: pageType.value ? '' : 'select',
        enums: getDict.value('procurement_method'),
        colProps: { span: 12 },
        rules: [
          { required: true, message: '请选择采购方式', trigger: 'change' },
        ],
        attrs: {
          multiple: false,
          clearable: true,
          disabled: !!form.value.planCode,
        },
        show: () => !pageType.value
      },
      {
        prop: 'sourcingMethod',
        label: '采购方式',
        type: '',
        colProps: { span: 12 },
        rules: [
          { required: true, message: '请选择采购方式', trigger: 'change' },
        ],
        show: () => pageType.value,
        render: (row) => {
          let sourcingMethod = getDict.value('procurement_method');
          let obj = sourcingMethod.find(i => i.value === row.sourcingMethod);
          return (<span>{obj?.label}</span>)
        }
      },
      {
        prop: 'agencyOrgId',
        label: '招标代理机构',
        type: pageType.value ? '' : 'select',
        enums: agentOptions.value,
        colProps: { span: 12 },
        attrs: {
          multiple: false,
          clearable: true,
          disabled: agencyDisabled.value,
          onChange: (val) => handleChangeAgency(val)
        },
        rules: [
          { required: true, message: '请选择招标代理机构', trigger: 'change' },
        ],
        show: () => !pageType.value && form.value.sourcingMethod?.includes('WTCG'),
      },
      {
        prop: 'agencyOrgId',
        label: '招标代理机构',
        type: '',
        colProps: { span: 12 },
        show: () => pageType.value && form.value.sourcingMethod?.includes('WTCG'),
        render: (row) => {
          let obj = agentOptions.value.find(i => i.value === row.agencyOrgId);
          return (<span>{obj?.label}</span>)
        }
      },
      {
        prop: 'preQualification',
        label: '资格预审',
        type: 'radio',
        enums: yesOrNo,
        rules: [
          { required: true, message: '请选择资格预审', trigger: 'change' },
        ],
        colProps: { span: 12 },
        show: () => !pageType.value && !['ZJWT', 'KSXJ'].includes(form.value.sourcingType) && form.value.inviteMethod === 'PUBLICITY'
      },
      {
        prop: 'preQualification',
        label: '资格预审',
        type: '',
        colProps: { span: 12 },
        show: () => pageType.value && !['ZJWT', 'KSXJ'].includes(form.value.sourcingType) && form.value.inviteMethod === 'PUBLICITY',
        render: (row) => {
          let obj = yesOrNo.find(i => i.value === row.preQualification);
          return (<span>{obj?.label}</span>)
        }
      },
      {
        prop: 'purchaseDeptId',
        label: '需求部门',
        render: (form) => {
          return pageType.value && !partialEdit.value  ? form.purchaseDeptName : (
            <el-cascader
              v-model={form.purchaseDeptId}
              style={{ width: '100%' }}
              clearable
              options={treeDeptData.value}
              props={{
                multiple: true,
                value: 'id',
                label: 'name',
                children: 'children',
                checkStrictly: true,
                emitPath: false,
                expandTrigger: 'hover',
              }}
              placeholder="请选择需求部门"
              collapse-tags
              collapse-tags-tooltip
              disabled={!!form.planCode && !partialEdit.value}
            />
          );
        },
        colProps: { span: 12 },
      },
      {
        prop: 'buyerDeptId',
        label: '采购部门',
        render: (form) => {
          return pageType.value ? form.buyerDeptName : (
            <el-cascader
              v-model={form.buyerDeptId}
              style={{ width: '100%' }}
              clearable
              options={treeDeptData.value}
              props={{
                multiple: false,
                value: 'id',
                label: 'name',
                children: 'children',
                checkStrictly: true,
                emitPath: false,
                expandTrigger: 'hover',
              }}
              placeholder="请选择采购部门"
              collapse-tags
              collapse-tags-tooltip
              disabled={!!form.planCode && !!form.buyerDeptId}
            />
          );
        },
        rules: [{ required: true, message: '采购部门不能为空', trigger: 'change' }],
        colProps: { span: 12 },
      },
      {
        label: '项目所在地区',
        prop: 'carrierLocation',
        render: (form) => {
          return pageType.value ? form.carrierLocation : (
            <el-cascader
              clearable
              style="width:100%"
              props={{ value: 'adcode', label: 'name', children: 'districts' }}
              v-model={form.carrierLocation}
              options={cityList.value}
              onChange={handleChangeCity}
            />
          );
        },
        colProps: { span: 12 },
      },
      {
        prop: 'address',
        label: '项目详细地址',
        type: pageType.value ? '' : 'input',
        rules: [
          { max: 50, message: '长度在50个字符内' },
        ],
        colProps: { span: 12 },
      },
      {
        prop: 'budgetAmount',
        label: '项目预算',
        type: pageType.value ? '' : 'input',
        rules: [
          { pattern: /^(?=\d+\.?\d*$)(.{1,30})$/, message: '请输入30位数字', trigger: 'blur' },
        ],
        colProps: { span: 12 },
      },
      {
        prop: 'purchaseReason',
        label: '采购意图',
        type: pageType.value && !partialEdit.value ? '' : 'input',
        rules: [
          { max: 100, message: '长度在100个字符内' },
        ],
        colProps: { span: 24 },
      },
      {
        prop: 'supplierRequirement',
        label: '供应商要求',
        type: pageType.value ? '' : 'input',
        attrs: { type: 'textarea' },
        rules: [
          { max: 100, message: '长度在100个字符内' },
        ],
        colProps: { span: 24 },
      },
      {
        prop: 'projectDesc',
        label: '项目概况',
        type: pageType.value && !partialEdit.value ? '' : 'input',
        attrs: { type: 'textarea' },
        rules: [
          { max: 300, message: '长度在300个字符内' },
        ],
        colProps: { span: 24 },
      }
    ];
  });

  // 支付信息
  const payColumns = computed(() => {
    const columns = [
      {
        prop: 'paymentMethodId',
        label: '支付方式',
        type: '',
        rules: [
          { required: true, message: '请选择支付方式', trigger: 'change' },
        ],
        colProps: { span: 24 },
        headerRender: ({ column }) => {
          return (
            <span>
              <i style="color: #FF3B30">*</i> {column.label}
            </span>
          );
        },
        render: (row) => {
          // 计算已被其它行选中的 paymentMethodId
          const selectedIds = form.value.paymentInfoList
            .filter(i => i !== row)
            .map(i => i.paymentMethodId)
            .filter(Boolean);

          return pageType.value
            ? paymentMethodOptions.value.find(i => i.value === row.paymentMethodId)?.label
            : (
              <el-select
                v-model={row.paymentMethodId}
                disabled={!!pageType.value}
                onChange={value => getPaymentPeriodOptions(value, row)}
                filterable
              >
                {paymentMethodOptions.value.map(item => (
                  <el-option
                    key={item.value}
                    label={item.label}
                    value={item.value}
                    disabled={selectedIds.includes(item.value)}
                  />
                ))}
              </el-select>
            );
        }
      },
      {
        prop: 'paymentPeriodId',
        label: '账期时间',
        type: '',
        rules: [
          // { required: true, message: '请选择账期时间', trigger: 'blur' },
        ],
        attrs: {
          disabled: !!pageType.value,
        },
        colProps: { span: 24 },
        headerRender: ({ column }) => {
          return (
            <span>
              <i style="color: #FF3B30">*</i> {column.label}
            </span>
          );
        },
        render: (row) => {
          return pageType.value ? (row.paymentPeriodOptions || []).find(i => i.value === row.paymentPeriodId)?.label : (
            <el-select
              v-model={row.paymentPeriodId}
              disabled={!!pageType.value}
            >
              {(row.paymentPeriodOptions || []).map(item => (
                <el-option key={item.value} label={item.label} value={item.value} />
              ))}
            </el-select>
          )
        }
      },
      {
        prop: 'paymentRemark',
        label: '账期备注',
        type: pageType.value && !partialEdit.value ? '' : 'input',
        rules: [
          { max: 100, message: '长度在100个字符内' },
        ],
        attrs: {
          disabled: !!pageType.value && !partialEdit.value,
        },
        colProps: { span: 24 },
      },
    ]
    if(!pageType.value) {
      columns.push (
        {
        label: '操作',
        prop: 'action',
        width: 100,
        fixed: 'right',
      })
    }
    return columns;
  });

  // 项目时间限制
  const projectTimeColumns = computed(() => {
    if (form.value.sourcingType == 'XJCG') {
      return projectTimeColumns1;
    } else if (['JZTP', 'ZB'].includes(form.value.sourcingType)) {
      return projectTimeColumns2;
    } else if (['KSXJ', 'ZJWT'].includes(form.value.sourcingType)) {
      return projectTimeColumns3;
    } else {
      return projectTimeColumns4;
    }
  })

  // 询价项目时间限制
  const projectTimeColumns1 = computed(() => {
    return [
      {
        label: '开标时间',
        prop: 'bidOpenTime',
        type: 'date-picker',
        attrs: {
          type: 'datetime',
          valueFormat: 'YYYY-MM-DD HH:mm:ss',
        },
        rules: [
          { required: true, message: '请选择开标时间' },
          {
            validator: (rule, value, callback) => {
              if (!form.value.quoteTime || Array.isArray(form.value) || form.value.quoteTime.length < 1) {
                return;
              }
              if (new Date(value).getTime() < new Date(form.value.quoteTime[1]).getTime()) {
                callback(new Error('开标时间不能小于报价截止时间'));
              } else {
                callback();
              }
            },
            trigger: 'change',
          },
        ],
        colProps: { span: 12 },
        show: () => !pageType.value,
      },
      {
        label: '开标时间',
        prop: 'bidOpenTime',
        type: '',
        rules: [
          { required: true, message: '请选择开标时间' },
        ],
        colProps: { span: 12 },
        show: () => !!pageType.value,
        render: (row) => row.bidOpenTime
      },
      {
        label: '报价起止时间',
        prop: 'quoteTime',
        type: 'date-picker',
        attrs: {
          startPlaceholder: '开始时间',
          endPlaceholder: '结束时间',
          type: 'datetimerange',
          format: 'YYYY-MM-DD HH:mm:ss',
          valueFormat: 'YYYY-MM-DD HH:mm:ss',
          clearable: true,
          defaultTime: [new Date(2000, 1, 1, 0, 0, 0), new Date(2000, 2, 1, 23, 59, 59)],
        },
        rules: [
          { required: true, message: '请选择报价起止时间', trigger: 'change' },
          {
            validator: (rule, value, callback) => {
              if (!value && value.length < 1) {
                callback();
              }
              if (new Date(value[1]).getTime() > new Date(form.value.bidOpenTime).getTime()) {
                callback(new Error('报价截止时间不能大于开标时间'));
              } else {
                callback();
              }
            },
            trigger: 'change',
          },
        ],
        colProps: { span: 12 },
        show: () => !pageType.value,
      },
      {
        label: '报价起止时间',
        prop: 'quoteTime',
        type: '',
        rules: [
          { required: true, message: '请选择报价起止时间' },
        ],
        colProps: { span: 12 },
        show: () => !!pageType.value,
        render: (row) => (
          <span>{row.quoteStartTime} 至 {row.quoteEndTime} </span>
        )
      },
      {
        label: '报名起止时间',
        prop: 'registerTime',
        type: 'date-picker',
        attrs: {
          startPlaceholder: '开始时间',
          endPlaceholder: '结束时间',
          type: 'datetimerange',
          format: 'YYYY-MM-DD HH:mm:ss',
          valueFormat: 'YYYY-MM-DD HH:mm:ss',
          clearable: true,
          defaultTime: [new Date(2000, 1, 1, 0, 0, 0), new Date(2000, 2, 1, 23, 59, 59)],
        },
        rules: [
          { required: true, message: '请选择报名起止时间', trigger: 'change' },
          {
            validator: (rule, value, callback) => {
              if (!value && value.length < 1) {
                callback();
              }
              if (form.value.preReviewTime && new Date(value[1]).getTime() > new Date(form.value.preReviewTime[1]).getTime()) {
                callback(new Error('报名截止时间不能大于资格预审截止时间'));
              } else {
                callback();
              }
            },
            trigger: 'change',
          },
        ],
        show: (form) => form.inviteMethod === 'PUBLICITY' && form.preQualification === 1 && !pageType.value,
        colProps: { span: 12 },
      },
      {
        label: '报名起止时间',
        prop: 'registerTime',
        type: '',
        rules: [
          { required: true, message: '请选择报名起止时间' },
        ],
        colProps: { span: 12 },
        show: (form) => form.inviteMethod === 'PUBLICITY' && form.preQualification === 1 && !!pageType.value,
        render: (row) => (
          <span>{row.registerStartTime} 至 {row.registerEndTime} </span>
        )
      },
      {
        label: '资格预审起止时间',
        prop: 'preReviewTime',
        type: 'date-picker',
        attrs: {
          startPlaceholder: '开始时间',
          endPlaceholder: '结束时间',
          type: 'datetimerange',
          format: 'YYYY-MM-DD HH:mm:ss',
          valueFormat: 'YYYY-MM-DD HH:mm:ss',
          clearable: true,
          defaultTime: [new Date(2000, 1, 1, 0, 0, 0), new Date(2000, 2, 1, 23, 59, 59)],
        },
        rules: [
          { required: true, message: '请选择资格预审起止时间', trigger: 'change' },
          {
            validator: (rule, value, callback) => {
              if (!value && value.length < 1) {
                callback();
              }
              if (form.value.registerTime && new Date(value[1]).getTime() < new Date(form.value.registerTime[1]).getTime()) {
                callback(new Error('资格预审截止时间不能小于报名截止时间'));
              } else {
                callback();
              }
            },
            trigger: 'change',
          },
        ],
        show: (form) => form.inviteMethod === 'PUBLICITY' && form.preQualification === 1 && !pageType.value,
        colProps: { span: 12 },
      },
      {
        label: '资格预审起止时间',
        prop: 'preReviewTime',
        type: '',
        rules: [
          { required: true, message: '请选择资格预审起止时间' },
        ],
        colProps: { span: 12 },
        show: (form) => form.inviteMethod === 'PUBLICITY' && form.preQualification === 1 && !!pageType.value,
        render: (row) => (
          <span>{row.preReviewStartTime} 至 {row.preReviewEndTime} </span>
        )
      },
    ]
  })

  // 招标、竞谈时间限制
  const projectTimeColumns2 = computed(() => {
    return [
      {
        label: '开标时间',
        prop: 'bidOpenTime',
        type: 'date-picker',
        attrs: {
          type: 'datetime',
          valueFormat: 'YYYY-MM-DD HH:mm:ss',
        },
        rules: [
          { required: true, message: '请选择开标时间' },
          {
            validator: (rule, value, callback) => {
              if (form.value.quoteTime && new Date(value).getTime() < new Date(form.value.quoteTime[1]).getTime()) {
                callback(new Error('开标时间不能小于投标截止时间'));
              } if (form.value.tenderClarifyTime && new Date(value).getTime() < new Date(form.value.tenderClarifyTime[1]).getTime()) {
                callback(new Error('开标时间不能小于澄清截止时间'));
              } if (form.value.tenderFileGainTime && new Date(value).getTime() < new Date(form.value.tenderFileGainTime[1]).getTime()) {
                callback(new Error('开标时间不能小于文件获取截止时间'));
              } else {
                callback();
              }
            },
            trigger: 'change',
          },
        ],
        colProps: { span: 12 },
        show: () => !pageType.value,
      },
      {
        label: '开标时间',
        prop: 'bidOpenTime',
        type: '',
        rules: [
          { required: true, message: '请选择开标时间' },
        ],
        colProps: { span: 12 },
        show: () => !!pageType.value,
        render: (row) => row.bidOpenTime
      },
      {
        label: '投标起止时间',
        prop: 'quoteTime',
        type: 'date-picker',
        attrs: {
          startPlaceholder: '开始时间',
          endPlaceholder: '结束时间',
          type: 'datetimerange',
          format: 'YYYY-MM-DD HH:mm:ss',
          valueFormat: 'YYYY-MM-DD HH:mm:ss',
          clearable: true,
          defaultTime: [new Date(2000, 1, 1, 0, 0, 0), new Date(2000, 2, 1, 23, 59, 59)],
        },
        rules: [
          { required: true, message: '请选择投标起止时间', trigger: 'change' },
          {
            validator: (rule, value, callback) => {
              if (!value && value.length < 1) {
                callback();
              }
              if (new Date(value[1]).getTime() > new Date(form.value.bidOpenTime).getTime()) {
                callback(new Error('投标截止时间不能大于开标时间'));
              } else {
                callback();
              }
            },
            trigger: 'change',
          },
        ],
        colProps: { span: 12 },
        show: () => !pageType.value,
      },
      {
        label: '投标起止时间',
        prop: 'quoteTime',
        type: '',
        rules: [
          { required: true, message: '请选择投标起止时间' },
        ],
        colProps: { span: 12 },
        show: () => !!pageType.value,
        render: (row) => (
          <span>{row.quoteStartTime} 至 {row.quoteEndTime} </span>
        )
      },
      {
        label: '标书费缴纳起止时间',
        prop: 'tenderFeePayTime',
        type: 'date-picker',
        attrs: {
          startPlaceholder: '开始时间',
          endPlaceholder: '结束时间',
          type: 'datetimerange',
          format: 'YYYY-MM-DD HH:mm:ss',
          valueFormat: 'YYYY-MM-DD HH:mm:ss',
          clearable: true,
          defaultTime: [new Date(2000, 1, 1, 0, 0, 0), new Date(2000, 2, 1, 23, 59, 59)],
        },
        rules: [
          { required: true, message: '请选择标书费缴纳起止时间', trigger: 'change' },
        ],
        colProps: { span: 12 },
        show: () => !pageType.value,
      },
      {
        label: '标书费缴纳起止时间',
        prop: 'tenderFeePayTime',
        type: '',
        rules: [
          { required: true, message: '请选择标书费缴纳起止时间' },
        ],
        colProps: { span: 12 },
        show: () => !!pageType.value,
        render: (row) => (
          <span>{row.tenderFeePayStartTime} 至 {row.tenderFeePayEndTime} </span>
        )
      },
      {
        label: '文件获取起止时间',
        prop: 'tenderFileGainTime',
        type: 'date-picker',
        attrs: {
          startPlaceholder: '开始时间',
          endPlaceholder: '结束时间',
          type: 'datetimerange',
          format: 'YYYY-MM-DD HH:mm:ss',
          valueFormat: 'YYYY-MM-DD HH:mm:ss',
          clearable: true,
          defaultTime: [new Date(2000, 1, 1, 0, 0, 0), new Date(2000, 2, 1, 23, 59, 59)],
        },
        rules: [
          { required: true, message: '请选择文件获取起止时间', trigger: 'change' },
          {
            validator: (rule, value, callback) => {
              if (!value && value.length < 1) {
                callback();
              }
              if (new Date(value[1]).getTime() > new Date(form.value.bidOpenTime).getTime()) {
                callback(new Error('文件获取截止时间不能大于开标时间'));
              } else {
                callback();
              }
            },
            trigger: 'change',
          },
        ],
        colProps: { span: 12 },
        show: () => !pageType.value,
      },
      {
        label: '文件获取起止时间',
        prop: 'tenderFileGainTime',
        type: '',
        rules: [
          { required: true, message: '请选择文件获取起止时间' },
        ],
        colProps: { span: 12 },
        show: () => !!pageType.value,
        render: (row) => (
          <span>{row.tenderFileGainStartTime} 至 {row.tenderFileGainEndTime} </span>
        )
      },
      {
        label: '澄清起止时间',
        prop: 'tenderClarifyTime',
        type: 'date-picker',
        attrs: {
          startPlaceholder: '开始时间',
          endPlaceholder: '结束时间',
          type: 'datetimerange',
          format: 'YYYY-MM-DD HH:mm:ss',
          valueFormat: 'YYYY-MM-DD HH:mm:ss',
          clearable: true,
          defaultTime: [new Date(2000, 1, 1, 0, 0, 0), new Date(2000, 2, 1, 23, 59, 59)],
        },
        rules: [
          { required: true, message: '请选择澄清起止时间', trigger: 'change' },
          {
            validator: (rule, value, callback) => {
              if (!value && value.length < 1) {
                callback();
              }
              if (new Date(value[1]).getTime() > new Date(form.value.bidOpenTime).getTime()) {
                callback(new Error('澄清截止时间不能大于开标时间'));
              } else {
                callback();
              }
            },
            trigger: 'change',
          },
        ],
        colProps: { span: 12 },
        show: () => !pageType.value,
      },
      {
        label: '澄清起止时间',
        prop: 'tenderClarifyTime',
        type: '',
        rules: [
          { required: true, message: '请选择澄清起止时间' },
        ],
        colProps: { span: 12 },
        show: () => !!pageType.value,
        render: (row) => (
          <span>{row.tenderClarifyStartTime} 至 {row.tenderClarifyEndTime} </span>
        )
      },
      {
        label: '报名起止时间',
        prop: 'registerTime',
        type: 'date-picker',
        attrs: {
          startPlaceholder: '开始时间',
          endPlaceholder: '结束时间',
          type: 'datetimerange',
          format: 'YYYY-MM-DD HH:mm:ss',
          valueFormat: 'YYYY-MM-DD HH:mm:ss',
          clearable: true,
          defaultTime: [new Date(2000, 1, 1, 0, 0, 0), new Date(2000, 2, 1, 23, 59, 59)],
        },
        rules: [
          { required: true, message: '请选择报名起止时间', trigger: 'change' },
          {
            validator: (rule, value, callback) => {
              if (!value && value.length < 1) {
                callback();
              }
              if (form.value.preReviewTime && new Date(value[1]).getTime() > new Date(form.value.preReviewTime[1]).getTime()) {
                callback(new Error('报名截止时间不能大于资格预审截止时间'));
              } else {
                callback();
              }
            },
            trigger: 'change',
          },
        ],
        show: (form) => form.inviteMethod === 'PUBLICITY' && form.preQualification === 1 && !pageType.value,
        colProps: { span: 12 },
      },
      {
        label: '报名起止时间',
        prop: 'registerTime',
        type: '',
        rules: [
          { required: true, message: '请选择报名起止时间' },
        ],
        colProps: { span: 12 },
        show: (form) => form.inviteMethod === 'PUBLICITY' && form.preQualification === 1 && !!pageType.value,
        render: (row) => (
          <span>{row.registerStartTime} 至 {row.registerEndTime} </span>
        )
      },
      {
        label: '资格预审起止时间',
        prop: 'preReviewTime',
        type: 'date-picker',
        attrs: {
          startPlaceholder: '开始时间',
          endPlaceholder: '结束时间',
          type: 'datetimerange',
          format: 'YYYY-MM-DD HH:mm:ss',
          valueFormat: 'YYYY-MM-DD HH:mm:ss',
          clearable: true,
          defaultTime: [new Date(2000, 1, 1, 0, 0, 0), new Date(2000, 2, 1, 23, 59, 59)],
        },
        rules: [
          { required: true, message: '请选择资格预审起止时间', trigger: 'change' },
          {
            validator: (rule, value, callback) => {
              if (!value && value.length < 1) {
                callback();
              }
              if (form.value.registerTime && new Date(value[1]).getTime() < new Date(form.value.registerTime[1]).getTime()) {
                callback(new Error('资格预审截止时间不能小于报名截止时间'));
              } else {
                callback();
              }
            },
            trigger: 'change',
          },
        ],
        show: (form) => form.inviteMethod === 'PUBLICITY' && form.preQualification === 1 && !pageType.value,
        colProps: { span: 12 },
      },
      {
        label: '资格预审起止时间',
        prop: 'preReviewTime',
        type: '',
        rules: [
          { required: true, message: '请选择资格预审起止时间' },
        ],
        colProps: { span: 12 },
        show: (form) => form.inviteMethod === 'PUBLICITY' && form.preQualification && !!pageType.value,
        render: (row) => (
          <span>{row.preReviewStartTime} 至 {row.preReviewEndTime} </span>
        )
      },
    ]
  })

  // 快速询价、直接委托时间限制
  const projectTimeColumns3 = computed(() => {
    return [
      {
        label: '开标时间',
        prop: 'bidOpenTime',
        type: 'date-picker',
        attrs: {
          type: 'datetime',
          valueFormat: 'YYYY-MM-DD HH:mm:ss',
        },
        rules: [
          { required: true, message: '请选择开标时间' },
          {
            validator: (rule, value, callback) => {
              if (!form.value.quoteTime || Array.isArray(form.value) || form.value.quoteTime.length < 1) {
                return;
              }
              if (form.value.quoteTime && new Date(value).getTime() < new Date(form.value.quoteTime[1]).getTime()) {
                callback(new Error('开标时间不能小于报价截止时间'));
              } else {
                callback();
              }
            },
            trigger: 'change',
          },
        ],
        colProps: { span: 12 },
        show: () => !pageType.value,
      },
      {
        label: '开标时间',
        prop: 'bidOpenTime',
        type: '',
        rules: [
          { required: true, message: '请选择开标时间' },
        ],
        colProps: { span: 12 },
        show: () => !!pageType.value,
        render: (row) => row.bidOpenTime
      },
      {
        label: '报价起止时间',
        prop: 'quoteTime',
        type: 'date-picker',
        attrs: {
          startPlaceholder: '开始时间',
          endPlaceholder: '结束时间',
          type: 'datetimerange',
          format: 'YYYY-MM-DD HH:mm:ss',
          valueFormat: 'YYYY-MM-DD HH:mm:ss',
          clearable: true,
          defaultTime: [new Date(2000, 1, 1, 0, 0, 0), new Date(2000, 2, 1, 23, 59, 59)],
        },
        rules: [
          { required: true, message: '请选择报价起止时间', trigger: 'change' },
          {
            validator: (rule, value, callback) => {
              if (!value && value.length < 1) {
                callback();
              }
              if (new Date(value[1]).getTime() > new Date(form.value.bidOpenTime).getTime()) {
                callback(new Error('报价截止时间不能大于开标时间'));
              } else {
                callback();
              }
            },
            trigger: 'change',
          },
        ],
        colProps: { span: 12 },
        show: () => !pageType.value,
      },
      {
        label: '报价起止时间',
        prop: 'quoteTime',
        type: '',
        rules: [
          { required: true, message: '请选择报价起止时间' },
        ],
        colProps: { span: 12 },
        show: () => !!pageType.value,
        render: (row) => (
          <span>{row.quoteStartTime} 至 {row.quoteEndTime} </span>
        )
      },
    ]
  })

  // 竞价时间限制
  const projectTimeColumns4 = computed(() => {
    return [
      {
        label: '竞价起止时间',
        prop: 'quoteTime',
        type: 'date-picker',
        attrs: {
          startPlaceholder: '开始时间',
          endPlaceholder: '结束时间',
          type: 'datetimerange',
          format: 'YYYY-MM-DD HH:mm:ss',
          valueFormat: 'YYYY-MM-DD HH:mm:ss',
          clearable: true,
          defaultTime: [new Date(2000, 1, 1, 0, 0, 0), new Date(2000, 2, 1, 23, 59, 59)],
        },
        rules: [
          { required: true, message: '请选择竞价起止时间', trigger: 'change' },
        ],
        colProps: { span: 12 },
        show: () => !pageType.value,
      },
      {
        label: '竞价起止时间',
        prop: 'quoteTime',
        type: '',
        rules: [
          { required: true, message: '请选择竞价起止时间' },
        ],
        colProps: { span: 12 },
        show: () => !!pageType.value,
        render: (row) => (
          <span>{row.quoteStartTime} 至 {row.quoteEndTime} </span>
        )
      },
      {
        label: '报名起止时间',
        prop: 'registerTime',
        type: 'date-picker',
        attrs: {
          startPlaceholder: '开始时间',
          endPlaceholder: '结束时间',
          type: 'datetimerange',
          format: 'YYYY-MM-DD HH:mm:ss',
          valueFormat: 'YYYY-MM-DD HH:mm:ss',
          clearable: true,
          defaultTime: [new Date(2000, 1, 1, 0, 0, 0), new Date(2000, 2, 1, 23, 59, 59)],
        },
        rules: [
          { required: true, message: '请选择报名起止时间', trigger: 'change' },
          {
            validator: (rule, value, callback) => {
              if (!value && value.length < 1) {
                callback();
              }
              if (form.value.preReviewTime && new Date(value[1]).getTime() > new Date(form.value.preReviewTime[1]).getTime()) {
                callback(new Error('报名截止时间不能大于资格预审截止时间'));
              } else {
                callback();
              }
            },
            trigger: 'change',
          },
        ],
        show: (form) => form.inviteMethod === 'PUBLICITY' && form.preQualification === 1 && !pageType.value,
        colProps: { span: 12 },
      },
      {
        label: '报名起止时间',
        prop: 'registerTime',
        type: '',
        rules: [
          { required: true, message: '请选择报名起止时间' },
        ],
        colProps: { span: 12 },
        show: (form) => form.inviteMethod === 'PUBLICITY' && !form.preQualification && !!pageType.value,
        render: (row) => (
          <span>{row.registerStartTime} 至 {row.registerEndTime} </span>
        )
      },
      {
        label: '资格预审起止时间',
        prop: 'preReviewTime',
        type: 'date-picker',
        attrs: {
          startPlaceholder: '开始时间',
          endPlaceholder: '结束时间',
          type: 'datetimerange',
          format: 'YYYY-MM-DD HH:mm:ss',
          valueFormat: 'YYYY-MM-DD HH:mm:ss',
          clearable: true,
          defaultTime: [new Date(2000, 1, 1, 0, 0, 0), new Date(2000, 2, 1, 23, 59, 59)],
        },
        rules: [
          { required: true, message: '请选择资格预审起止时间', trigger: 'change' },
          {
            validator: (rule, value, callback) => {
              if (!value && value.length < 1) {
                callback();
              }
              if (form.value.registerTime && new Date(value[1]).getTime() < new Date(form.value.registerTime[1]).getTime()) {
                callback(new Error('资格预审截止时间不能小于报名截止时间'));
              } else {
                callback();
              }
            },
            trigger: 'change',
          },
        ],
        show: (form) => form.inviteMethod === 'PUBLICITY' && form.preQualification === 1 && !pageType.value,
        colProps: { span: 12 },
      },
      {
        label: '资格预审起止时间',
        prop: 'preReviewTime',
        type: '',
        rules: [
          { required: true, message: '请选择资格预审起止时间' },
        ],
        colProps: { span: 12 },
        show: (form) => form.inviteMethod === 'PUBLICITY' && form.preQualification && !!pageType.value,
        render: (row) => (
          <span>{row.preReviewStartTime} 至 {row.preReviewEndTime} </span>
        )
      },
    ]
  })

  // 项目小组成员
  const memberColumns = computed(() => {
    const columns = [
      {
        label: '角色',
        prop: 'role',
        minWidth: 160,
        type: '',
        rules: [
          { required: true, message: '请选择角色', trigger: 'change' },
        ],
        colProps: { span: 24 },
        headerRender: ({ column }) => {
          return (
            <span>
              <i style="color: #FF3B30">*</i> {column.label}
            </span>
          );
        },
        render: (row) => {
          return pageType.value && !row.ifEdit ? memberOptions.find(i => i.value === row.role)?.label : (
            <el-select
              v-model={row.role}
              onChange={value => changeRole(value, row)}
            >
              {memberOptions.map(item => (
                <el-option key={item.value} label={item.label} value={item.value} />
              ))}
            </el-select>
          )
        }
      },
      {
        label: '姓名',
        prop: 'userId',
        minWidth: 160,
        type: '',
        rules: [
          { required: true, message: '请选择姓名', trigger: 'change' },
        ],
        colProps: { span: 24 },
        headerRender: ({ column }) => {
          return (
            <span>
              <i style="color: #FF3B30">*</i> {column.label}
            </span>
          );
        },
        render: (row) => {
          return pageType.value && !row.ifEdit ? userList.value?.find(i => i.value === row.userId)?.label : (
            <el-select
              v-model={row.userId}
              onChange={value => changeUser(value, row)}
            >
              {userList.value.map(item => (
                <el-option key={item.value} label={item.label} value={item.value} />
              ))}
            </el-select>
          )
        }
      },
      {
        label: '联系电话',
        prop: 'contactPhone',
        minWidth: 160,
        type: pageType.value ? '' : 'input',
        rules: [
          { required: true, message: '请输入联系电话', trigger: 'blur' },
          { validator: rule.validatePhone, trigger: 'blur' },
        ],
        colProps: { span: 24 },
        headerRender: ({ column }) => {
          return (
            <span>
              <i style="color: #FF3B30">*</i> {column.label}
            </span>
          );
        },
        render: (row) => {
          return pageType.value && !row.ifEdit ? row.contactPhone : (
            <el-input v-model={row.contactPhone} placeholder="请输入联系电话"></el-input>
          )
        }
      },
      {
        label: '所属组织',
        prop: 'deptName',
        minWidth: 160,
        type: '',
        colProps: { span: 24 },
        render: (row) => {
          return pageType.value && form.value.id && !row.ifEdit ? (userList.value.find(i => i.value === row.userId)?.deptName) : (
            <el-input
              v-model={row.deptName}
              disabled={true}
              placeholder="选择用户自动带入"
            ></el-input>
          );
        },
      },
    ]
    if(!pageType.value || partialEdit.value) {
      columns.push({
        label: '操作',
        prop: 'action',
        width: 100,
        fixed: 'right',
      })
    }
    return columns;
  })

  // 标段信息
  const lotColumns = computed(() => {
    return [
      {
        prop: 'lotType',
        label: '标段类型',
        type: 'radio',
        enums: [
          { label: '单标段采购', value: 0 },
          { label: '多标段采购', value: 1, disabled: ['ZJWT', 'JJCG', 'KSXJ'].includes(form.value.sourcingType) },
        ],
        attrs: {
          disabled: !!pageType.value || !form.value.sourcingType,
          onChange: () => handleChangeLotType(),
        },
        rules: [
          { required: true, message: '请选择标段类型', trigger: 'blur' },
        ],
        colProps: { span: 24 },
        show: () => !pageType.value,
      },
      {
        prop: 'lotType',
        label: '标段类型',
        type: '',
        enums: [
          { label: '单标段采购', value: 0 },
          { label: '多标段采购', value: 1 },
        ],
        rules: [
          { required: true, message: '请选择标段类型', trigger: 'blur' },
        ],
        colProps: { span: 24 },
        show: () => !!pageType.value,
        render: (row) => {
          return row.lotType ? '多标段采购' : '单标段采购'
        }
      }
    ]
  })
  // 标段表格信息
  const lotTableColumns = computed(() => {
    const columns = [
      {
        label: '标段序号',
        prop: 'index',
        width: 90,
        render: ({ $index }) => $index + 1,
      },
      {
        label: '标段名称',
        prop: 'sectionName',
        type: pageType.value ? '' : 'input',
        rules: [
          { required: true, message: '请输入标段名称', trigger: 'blur' },
        ],
        colProps: { span: 24 },
        headerRender: ({ column }) => {
          return (
            <span>
              <i style="color: #FF3B30">*</i> {column.label}
            </span>
          );
        }
      },
      {
        label: '标段编号',
        prop: 'sectionCode',
        type: pageType.value ? '' : 'input',
        rules: [
          { required: true, message: '请输入标段编号', trigger: 'blur' },
        ],
        colProps: { span: 24 },
        headerRender: ({ column }) => {
          return (
            <span>
              <i style="color: #FF3B30">*</i> {column.label}
            </span>
          );
        }
      },
    ]
    if(!pageType.value) {
      columns.push(
        {
          label: '操作',
          prop: 'action',
          width: 100,
          fixed: 'right',
        }
      )
    }
    return columns;
  })

  // 项目附件
  const projectColumns = computed(() => {
    return [
      {
        prop: 'originOrderNumber',
        label: '项目附件',
        colProps: { span: 24 },
        render: (row) => {
          return (
            <FileList
              modelValue={row.attachment}
              isView={!!pageType.value}
              onUpdate:modelValue={(fileList) => {
                row.attachment = fileList;
              }}
            />
          )
        }
      },
    ]

  })

  // 选择物料
  const chooseMaterial = () => {
    chooseMaterialRef.value.show();
  };
  // 选择计划物料
  const choosePlanMaterial = () => {
    choosePlanMaterialRef.value.show();
  };

  // 处理选择采购计划确认
  const handleChoosePlanConfirm = (data) => {
    if (!Array.isArray(data) || data.length === 0) return;
    const firstServiceTypeId = data[0].serviceTypeId;
    const firstPlanRecruitMethod = data[0].planRecruitMethod;
    const firstProcurementMethod = data[0].procurementMethod;
    const isAllSame = data.every(
      item => item.serviceTypeId === firstServiceTypeId && item.planRecruitMethod === firstPlanRecruitMethod && item.procurementMethod === firstProcurementMethod
    );
    if (!isAllSame) {
      ElMessage.warning('请选择采购业务类型、寻源方式、采购方式相同的计划');
      return;
    }
    selectedPlans.value = data;
    // 一次遍历收集去重
    const planId = new Set();
    const planCodeSet = new Set();
    const demandDeptIdSet = new Set();
    const procurementMethodSet = new Set();
    const buyerDeptIdSet = new Set();
    data.forEach(item => {
      planId.add(item.id);
      planCodeSet.add(item.planCode);
      demandDeptIdSet.add(item.applyDeptId);
      procurementMethodSet.add(item.procurementMethod);
      buyerDeptIdSet.add(item.procurementDeptId);
    });
    form.value.planId = Array.from(planId).join(',');
    form.value.planCode = Array.from(planCodeSet).join(',');
    form.value.purchaseDeptId = Array.from(demandDeptIdSet).join(',');
    form.value.sourcingMethod = firstProcurementMethod;
    form.value.sourcingType = firstPlanRecruitMethod;
    form.value.serviceTypeId = firstServiceTypeId;
    form.value.agencyOrgId = data[0].baseAgentDeptId;
    let agency = agentOptions.value.find(i => i.value === data[0].baseAgentDeptId);
    form.value.agencyOrgName = agency?.label;
    agencyDisabled.value = !!form.value.agencyOrgId;
    form.value.buyerDeptId = Array.from(buyerDeptIdSet).length > 1 ? '' : Array.from(buyerDeptIdSet).join(',');
    if (['XJCG', 'JJCG', 'ZB', 'JZTP'].includes(form.value.sourcingType)) {
      form.value.inviteMethod = 'PUBLICITY';
      form.value.preQualification = 1;
    } else {
      form.value.inviteMethod = 'INVITE';
      form.value.preQualification = 0;
    }
    getDynamicFieldsByPlanId(form.value.planId);
  };

  // 处理选择物料确认
  const handleChooseMaterialConfirm = (data) => {
    let existingItems = [];
    let currentList = form.value.sections[Number(activeName.value)].projectItems;
    existingItems = currentList.filter((i) => data.map(j => j.materialCode)?.includes(i.materialCode));
    console.log(existingItems, 'existingItems')
    data.forEach((item) => {
      const existingItem = currentList.find((i) => i.materialCode === item.materialCode);
      if (!existingItem) {
        let data = {};
        formFields.value.forEach((i) => {
          if (i.key === 'specModel') {
            data[i.key] = item.spec || '';
          } else {
            data[i.key] = item[i.key] || '';
          }
        });
        currentList.push(data);
      }
    });
    console.log(data, 'datadata')
    existingItems.length && ElMessage.warning(`物料 ${existingItems.map(i => i.materialName)?.join(',')} 已存在`);
  };

  // 处理选择计划物料确认
  const handleChoosePlanMaterialConfirm = (data) => {
    let existingItems = [];
    let currentList = form.value.sections[Number(activeName.value)].projectItems;
    existingItems = currentList.filter((i) => data.map(j => j.requireNo)?.includes(i.requireNo));
    data.forEach((item) => {
      const existingItem = currentList.find((i) => i.requireNo === item.requireNo);
      if (!existingItem) {
        let data = {};
        formFields.value.forEach((i) => {
          if (i.key === 'specModel') {
            data[i.key] = item.spec || '';
          } else {
            data[i.key] = item[i.key] || '';
          }
        });
        currentList.push(data);
      }
    });
    existingItems.length && ElMessage.warning(`物料 ${existingItems.map(i => i.materialName)?.join(',')} 已存在`);
  };

  // 选择招标代理机构
  const handleChangeAgency = (val) => {
    let agency = agentOptions.value.find(i => i.value === val);
    form.value.agencyOrgName = agency?.label;
  }

  // 获取采购业务类型
  const getTypeOptions = async () => {
    try {
      const { data } = await getBaseServiceType();
      typeOptions.value = data.map(i => {
        return {
          label: i.typeName,
          value: i.id
        }
      })
    } catch (error) {
      console.error('获取采购业务类型失败:', error);
    }
  }

  // 获取代理机构
  const getAgentOptions = async () => {
    try {
      const { data } = await getAgentListApi();
      agentOptions.value = data.map((i) => {
        return {
          label: i.agentName,
          value: i.id,
        };
      });
    } catch (error) {
      console.error('代理机构获取失败:', error);
    }
  };

  // 获取支付方式
  const getPaymentMethodOptions = async () => {
    try {
      const { data } = await getPaymentMethodListApi({statusList: ['ENABLED']}, { current: 1, size: 1000 });
      paymentMethodOptions.value = data.records.map((i) => {
        return {
          label: i.methodName,
          value: i.id,
        };
      });
    } catch (error) {
      console.error('支付方式获取失败:', error);
    }
  };

  // 获取账期时间
  const getPaymentPeriodOptions = async (val, row, ifInit) => {
    if(form.value.paymentInfoList.some(i => i !== row && i.paymentMethodId && i.paymentMethodId == val)) {
      ElMessage.warning('支付方式不能重复选择，请重新选择！');
      row.paymentMethodId = '';
      row.paymentPeriodOptions = [];
      row.paymentPeriodId = '';
      return;
    }
    try {
      const { data } = await getPaymentPeriodListApi(val);
      row.paymentPeriodOptions = data.basePaymentMethodPeriodList.map((i) => {
        return {
          label: i.periodName,
          value: i.id,
        };
      });
       console.log(row.paymentPeriodOptions, 'row.paymentPeriodOptions')
      !ifInit ? row.paymentPeriodId = (row.paymentPeriodOptions && row.paymentPeriodOptions.length === 1) ? row.paymentPeriodOptions[0].value : '' : null;
    } catch (error) {
      console.error('获取账期时间获取失败:', error);
    }
  };

  // 获取用户列表
  const getUserList = async () => {
    try {
      const { data } = await getUserListApi();
      userList.value = data.records.map(i => {
        return {
          label: i.name,
          value: i.userId,
          deptId: i.deptId,
          deptName: i.deptName,
          phone: i.phone,
        }
      })
      console.log(userList, 'userList')
    } catch (error) {
      console.error('获取用户失败:', error);
    }
  }

  // 修改角色
  const changeRole = (val, row) => {
    if (val === 'PROJECT_LEADER') {
      row.userId = userInfos.value.user.userId;
      row.deptId = userInfos.value.user.deptId;
      row.contactPhone = userInfos.value.user.phone;
      row.deptName = getDeptName(row.deptId);
    }
  }

  // 修改用户
  const changeUser = (val, row) => {
    let obj = userList.value.find(i => i.value === val);
    row.deptName = obj?.deptName;
    row.deptId = obj?.deptId;
    row.contactPhone = obj?.phone;
  }

  // 根据部门id获取部门名称
  const getDeptName = (val) => {
    // 递归查找树结构
    function findDeptName(tree, id) {
      for (const node of tree) {
        if (node.id === id) {
          return node.name;
        }
        if (node.children && node.children.length) {
          const found = findDeptName(node.children, id);
          if (found) return found;
        }
      }
      return undefined;
    }
    return findDeptName(treeDeptData.value, val);
  }


  // 滚动到指定区域
  const scrollToSection = (sectionId) => {
    const targetElement = document.getElementById(`section-${sectionId}`);
    // 修改：使用正确的滚动容器
    const biddingContainer = document.querySelector('.content-area') || document.querySelector('.proposal-detail-container');
    if (targetElement && biddingContainer) {
      // 立即更新 activeAnchor，但延迟更新滑块位置以减少顿挫感
      activeAnchor.value = sectionId;

      // 使用 requestAnimationFrame 延迟更新滑块位置
      requestAnimationFrame(() => {
        updateSliderPosition();
      });

      // 计算目标元素相对于滚动容器的位置
      const containerRect = biddingContainer.getBoundingClientRect();
      const targetRect = targetElement.getBoundingClientRect();
      const scrollTop = biddingContainer.scrollTop;

      // 计算需要滚动到的位置
      const offsetTop = targetRect.top - containerRect.top + scrollTop - 20; // 减去20px间距

      // 滚动容器而不是页面
      biddingContainer.scrollTo({
        top: offsetTop,
        behavior: 'smooth',
      });
    } else {
      // 如果找不到正确的滚动容器，使用页面滚动作为备选方案
      if (targetElement) {
        targetElement.scrollIntoView({
          behavior: 'smooth',
          block: 'start'
        });
      }
    }
  }

  // 计算滑块位置
  const updateSliderPosition = () => {
    nextTick(() => {
      const activeItem = document.querySelector('.nav-item.active');

      if (activeItem) {
        const relativeTop = activeItem.offsetTop;

        // 使用 requestAnimationFrame 确保动画流畅
        requestAnimationFrame(() => {
          sliderStyle.value = {
            transform: `translateY(${relativeTop}px)`,
            height: `16px`,
            opacity: 1,
          };
        });

        // 首次计算完成后显示滑块
        if (!isSliderReady.value) {
          isSliderReady.value = true;
        }
      } else {
        // 如果找不到活跃元素，尝试根据 activeAnchor 手动计算位置
        const navItems = document.querySelectorAll('.nav-item');
        const activeIndex = anchorList.findIndex(anchor => anchor.id === activeAnchor.value);

        if (activeIndex >= 0 && navItems[activeIndex]) {
          const relativeTop = navItems[activeIndex].offsetTop;

          // 使用 requestAnimationFrame 确保动画流畅
          requestAnimationFrame(() => {
            sliderStyle.value = {
              transform: `translateY(${relativeTop}px)`,
              height: `16px`,
              opacity: 1,
            };
          });

          // 设置活跃状态 - 使用更高效的方式
          navItems.forEach((item, index) => {
            if (index === activeIndex) {
              item.classList.add('active');
            } else {
              item.classList.remove('active');
            }
          });

          if (!isSliderReady.value) {
            isSliderReady.value = true;
          }
        }
      }
    });
  }

  // 防抖函数
  const debounce = (func, wait) => {
    let timeout;
    return function executedFunction(...args) {
      const later = () => {
        clearTimeout(timeout);
        func(...args);
      };
      clearTimeout(timeout);
      timeout = setTimeout(later, wait);
    };
  };

  // 处理滚动事件，更新活跃锚点
  const handleScroll = debounce(() => {
    // 修改：使用正确的滚动容器
    const biddingContainer = document.querySelector('.content-area') || document.querySelector('.proposal-detail-container');
    if (!biddingContainer) return;

    const scrollTop = biddingContainer.scrollTop;
    const containerRect = biddingContainer.getBoundingClientRect();

    const sections = anchorList
      .map((anchor) => {
        const element = document.getElementById(`section-${anchor.id}`);
        if (!element) return null;

        const elementRect = element.getBoundingClientRect();
        // 计算相对于滚动容器的位置
        const relativeTop = elementRect.top - containerRect.top + scrollTop;

        return {
          id: anchor.id,
          element,
          offsetTop: relativeTop,
        };
      })
      .filter((item) => item !== null);

    // 找到当前滚动位置对应的区域
    for (let i = sections.length - 1; i >= 0; i--) {
      const section = sections[i];
      if (section && section.offsetTop <= scrollTop + 100) {
        if (activeAnchor.value !== section.id) {
          activeAnchor.value = section.id;
          // 使用 requestAnimationFrame 延迟更新滑块位置
          requestAnimationFrame(() => {
            updateSliderPosition();
          });
        }
        break;
      }
    }
  }, 16); // 约60fps的防抖时间

  function handlePaymentMethodChange(value, row) {
    row.paymentMethod = value;
  }

  // 根据district查找完整的省市区信息
  function getAddressByDistrict(districtCode) {
    if (!districtCode) return '';

    for (const province of cityList.value) {
      if (province.districts) {
        for (const city of province.districts) {
          if (city.districts) {
            const districtItem = city.districts.find(item => item.adcode === districtCode);
            if (districtItem) {
              return `${province.name} / ${city.name} / ${districtItem.name}`;
            }
          }
        }
      }
    }
    return '';
  }

  // 获取详情
  const getDetail = async () => {
    try {
      if (!route?.query?.projectCode) return;
      await getCityTree();
      const { data } = await getProposalDetail(route.query.projectCode);
      await fetchDynamicFields(data.serviceTypeId, data.sourcingType);
      let { projectSectionList, projectMemberList, projectPaymentList, projectAttachmentList, relationPlanCodeList, province, city, district, purchaseDeptId, ...rest } = data;
      // 根据district查找完整的省市区信息
      const carrierLocation = getAddressByDistrict(district);
      projectSectionList = projectSectionList.map(i => {
        let { projectItemList, ...rest } = i;
        let projectItems = projectItemList.map(j => {
          j.projectFieldValueList?.forEach(k => {
            if (k.fieldType == 'ATTACH') {
              j[k.fieldCode] = JSON.parse(k.fieldValue || []);
            } else {
              j[k.fieldCode] = k.fieldValue;
            }
          })
          let regions = j.usageLocationRegion?.split('/') || [];
          let district = regions.length > 1 ? regions[regions.length -1] : '';
          if(/^\d+$/.test(regions[regions.length -1])) {
            j.usageLocationRegion = getAddressByDistrict(district);
          } else {
            j.usageLocationRegion = j.usageLocationRegion?.slice(10, j.usageLocationRegion.length) || '-';
          }
          j.indicators = [{label: j.qualityIndicatorName, value: j.qualityIndicatorId}];
          return j;
        })
        return {
          ...rest,
          projectItems: projectItems,
        }
      })
      form.value = {
        sections: projectSectionList,
        projectMembers: projectMemberList,
        paymentInfoList: projectPaymentList,
        lotType: projectSectionList.length > 1 ? 1 : 0,
        quoteTime: [rest.quoteStartTime, rest.quoteEndTime],
        registerTime: [rest.registerStartTime, rest.registerEndTime],
        preReviewTime: [rest.preReviewStartTime, rest.preReviewEndTime],
        tenderFeePayTime: [rest.tenderFeePayStartTime, rest.tenderFeePayEndTime],
        tenderFileGainTime: [rest.tenderFileGainStartTime, rest.tenderFileGainEndTime],
        tenderClarifyTime: [rest.tenderClarifyStartTime, rest.tenderClarifyEndTime],
        attachment: projectAttachmentList?.map(i => { return { fileName: i.fileName, name: i.fileName, fileUrl: i.filePath, url: i.filePath } }) || [],
        carrierLocation: pageType.value ? carrierLocation : [province, city, district],
        planCode: relationPlanCodeList?.join(',') || '',
        relationPlanCodeList,
        purchaseDeptId: purchaseDeptId?.split(',') || [],
        ...rest,
      };
      form.value.paymentInfoList.forEach(i => {
        getPaymentPeriodOptions(i.paymentMethodId, i, true);
      })
      form.value.projectMembers.forEach(i => {
        i.deptName = userList.value.find(j => j.value === i.userId)?.deptName;
      })
    } catch (error) {
      console.error('获取详情失败:', error);
    }
  };

  const getPlanDetailInfo = async () => {
    try {
      const { data } = await getPlanDetail(route?.query?.planCode);
      handleChoosePlanConfirm([data]);
    } catch (error) {
      console.error('获取计划详情失败:', error);
    }
  }

  const handleChangeLotType = () => {
    let sections = deepClone(form.value.sections);
    form.value.sections = [{ projectItems: [] }];
    form.value.sections = [{ sectionName: '', sectionCode: '', projectItems: sections[0].projectItems }]
    activeName.value = '0';
  }

  // 返回采购计划页
  const goBack = () => {
    router.push('/purchasing/projectProposal/index');
  };

  // 在组件挂载时获取动态字段
  onMounted(async () => {
    await getUserList();
    if (route?.query?.projectCode) {
      getDetail();
    } else {
      getCityTree();
      form.value = {
        lotType: 0,
        paymentInfoList: [{ paymentMethodId: '', paymentPeriodId: '', paymentRemark: '' }],
        projectMembers: [{ role: 'PROJECT_LEADER', userId: '', contactPhone: '' }],
        sections: [{ projectItems: [] }],
      };
      nextTick(() => {
        formRef?.value?.elForm?.clearValidate();
        projectTimeRef?.value?.elForm?.clearValidate();
        attachRef?.value?.elForm?.clearValidate();
        lotRef?.value?.elForm?.clearValidate();
        formRefs[`0_paymentMethodIdRef`]?.elForm?.clearValidate();
      });
    }
    if(route?.query?.planId && route?.query?.planCode) {
      form.value.planId = route?.query?.planId;
      form.value.planCode = route?.query?.planCode;
      getPlanDetailInfo()
      getDynamicFieldsByPlanId(form.value.planId);
    }
    form.value.projectMembers[0].userId = userInfos.value.user.userId;
    form.value.projectMembers[0].deptName = getDeptName(userInfos.value.user.deptId);
    form.value.projectMembers[0].contactPhone = userInfos.value.user.phone;
    getTypeOptions();
    getAgentOptions();
    getPaymentMethodOptions();
  })

  return {
    form,
    basicColumns,
    payColumns,
    projectTimeColumns,
    memberColumns,
    lotColumns,
    lotTableColumns,
    projectColumns,
    loading,
    choosePlanRef,
    activeAnchor,
    sliderStyle,
    isSliderReady,
    activeCollapse,
    pageType,
    partialEdit,
    chooseMaterialRef,
    choosePlanMaterialRef,
    selectedRow,
    activeName,
    typeOptions,
    tableColumns,
    mergedTableFormConfig,
    dynamicFields,
    formFields,
    chooseMaterial,
    choosePlanMaterial,
    scrollToSection,
    updateSliderPosition,
    handleScroll,
    handleChoosePlanConfirm,
    handleChooseMaterialConfirm,
    handleChoosePlanMaterialConfirm,
    handlePaymentMethodChange,
    goBack,
  }
}
