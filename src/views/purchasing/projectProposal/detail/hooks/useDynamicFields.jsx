import { ref, computed, nextTick } from 'vue';
import { getDynamicFields } from '@/api/purchasing/config';
import { fixedFieldNames, fixedFieldNamesNoEdit } from '@/views/purchasing/constant';
import FileList from '@/views/purchasing/plan/detail/components/useFileList.jsx';
import { planMaterialListApi } from '@/api/purchasing/proposal';
import { fetchList as getBaseUsageLocation } from '@/api/lowcode/base-usage-location/index';
import { fetchList as getBaseQualityIndicator } from '@/api/lowcode/base-quality-indicator/index';
import { calculateFieldExpression } from '@/utils/safeCalculator';

export function useDynamicFields({ form, selectedRow, typeOptions, pageType, indicatorDetailRef, cityList}) {

  // 动态字段配置
  const dynamicFields = ref([]);
  const usageLocations = ref([]);
  const isUpdatingFields = ref(false); // 添加更新状态标记
  const updateQueue = ref([]); // 添加更新队列
  
  // 安全的更新dynamicFields的方法
  const safeUpdateDynamicFields = async (newFields) => {
    if (isUpdatingFields.value) {
      // 如果正在更新，将更新任务加入队列
      updateQueue.value.push(newFields);
      return;
    }
    
    try {
      isUpdatingFields.value = true;
      
      // 添加延迟，确保组件完全挂载
      await new Promise(resolve => setTimeout(resolve, 50));
      await nextTick();
      
      dynamicFields.value = newFields;
      
      // 处理队列中的其他更新任务
      while (updateQueue.value.length > 0) {
        const queuedFields = updateQueue.value.shift();
        await new Promise(resolve => setTimeout(resolve, 50));
        await nextTick();
        dynamicFields.value = queuedFields;
      }
    } catch (error) {
      console.error('更新动态字段失败:', error);
    } finally {
      isUpdatingFields.value = false;
    }
  };
  
  // 从计划详情获取动态字段
  const getDynamicFieldsByPlanId = async (planId) => {
    if (!planId) return;
    
    try {
      const res = await planMaterialListApi({ ids: planId.split(',') });
      
      form.value.sections[0].projectItems = res.data.map(i => {
        let { baseServiceTypeFieldList, ...rest } = i;
        let dynamicFieldsValues = {};
        baseServiceTypeFieldList?.forEach(j => {
          if (j.fieldType === 'ATTACH') {
            dynamicFieldsValues[j.fieldCode] = JSON.parse(j.fieldValue || []);
          } else {
            dynamicFieldsValues[j.fieldCode] = j.fieldValue;
          }
        })
        let regions = rest.usageLocationRegion?.split('/') || [];
        let district = regions.length > 1 ? regions[regions.length -1] : '';
        if(/^\d+$/.test(regions[regions.length -1])) {
          rest.usageLocationRegion = getAddressByDistrict(district);
        } else {
          rest.usageLocationRegion = rest.usageLocationRegion?.slice(10, rest.usageLocationRegion.length) || '-';
        }
        rest.indicators = [{label: rest.qualityIndicatorName, value: rest.qualityIndicatorId}];
        return {
          ...dynamicFieldsValues,
          ...rest
        }
      });
      
      // 获取需求牧场下拉数据
      const usageLocationRes = await getBaseUsageLocation({statusList: 'ENABLED'}, {page: 1, size: 1000});
      usageLocations.value = usageLocationRes.data.records.map(i => {
        return {
          label: i.locationName,
          value: i.id,
          ...i,
        }
      })
      
      // 使用安全的更新方法
      const filteredFields = Array.isArray(res.data[0].baseServiceTypeFieldList) && res.data[0].baseServiceTypeFieldList.length && res.data[0].baseServiceTypeFieldList.map(i => {
        if (fixedFieldNames.includes(i.fieldName)) {
          i.fieldName === '需求牧场' ? i.enumValues = JSON.stringify(usageLocations.value) : null;
        }
        return i;
      }).filter(i => i.applyToProject === 'YES');
      
      await safeUpdateDynamicFields(filteredFields);
      console.log(form.value, 'form.value')
    } catch (error) {
      console.error('获取计划详情失败:', error);
    }
  };

  // 根据district查找完整的省市区信息
  const getAddressByDistrict = (districtCode) => {
    if (!districtCode) return '';

    for (const province of cityList.value) {
      if (province.districts) {
        for (const city of province.districts) {
          if (city.districts) {
            const districtItem = city.districts.find(item => item.adcode === districtCode);
            if (districtItem) {
              return `${province.name} / ${city.name} / ${districtItem.name}`;
            }
          }
        }
      }
    }
    return '';
  }

  // 修改需求牧场填充所在区域和详细地址
  const changeUsageLocation = (val, row) => {
    let current = usageLocations.value.find(i => i.value === val) || {};
    row['usageLocationRegion'] = getAddressByDistrict(current.district);
    row['usageLocationAddress'] = current.address;
  }

  // 获取动态字段
  async function fetchDynamicFields(serviceTypeId, buyWay) {
    form.value.serviceTypeName = typeOptions.value.find(i => i.value === serviceTypeId)?.label || '';
    try {
      // 获取需求牧场下拉数据
      try {
        const usageLocationRes = await getBaseUsageLocation({statusList: 'ENABLED'}, {page: 1, size: 1000});
        usageLocations.value = usageLocationRes.data.records.map(i => {
          return {
            label: i.locationName,
            value: i.id,
            ...i,
          }
        })
      } catch(error) {
        console.error('获取需求牧场下拉数据失败:', error);
      }
      
      const { data } = await getDynamicFields(serviceTypeId, buyWay);
      // 只赋值一次，保证响应式
      const filteredFields = (data.baseServiceTypeFieldList || []).map(i => {
        if (fixedFieldNames.includes(i.fieldName)) {
          i.fieldName === '需求牧场' ? i.enumValues = JSON.stringify(usageLocations.value) : null;
        }
        return i;
      }).filter(i => i.applyToProject === 'YES');
      
      // 使用安全的更新方法
      await safeUpdateDynamicFields(filteredFields);
      form.value.list = [];
      selectedRow.value = [];
    } catch (error) {
      console.error('获取动态字段失败:', error);
    }
  }

  // 解析计算规则并计算结果
  function calculate({ defaultValue, fieldCode }, row) {
    if (pageType?.value) return null;
    if (!defaultValue || !row || Object.keys(row).length === 0) {
      row[fieldCode] = '';
      return '';
    }

    try {
      // 使用安全的字段表达式计算器
      const result = calculateFieldExpression(defaultValue, row, {
        decimals: 2,
        defaultValue: 0,
        debug: false, // 可以根据需要开启调试
        returnEmptyOnError: true
      });

      // 更新row对象中的值
      row[fieldCode] = result;
      return result;

    } catch (error) {
      console.error('计算规则解析错误:', error, '表达式:', defaultValue, '字段:', fieldCode);
      row[fieldCode] = '';
      return '';
    }
  }

  // 查看质量指标明细
  const handleViewIndicator = (row) => {
    indicatorDetailRef && indicatorDetailRef.value.show(row)
  }

  // 获取质量指标数据
  const getQualityIndicator = async (row) =>{
    // 获取质量指标-通过物料筛选
    const indicatorsRes = await getBaseQualityIndicator({statusList: ['ENABLED'], materialCode: row.materialCode}, {page: 1, size: 1000});
    row.indicators = (indicatorsRes.data.records || []).map(i => {
      return {
        label: i.indicatorName,
        value: i.id
      }
    });
  }

  // 根据动态字段生成表格列
  function generateDynamicColumns(fields) {
    if (!Array.isArray(fields) || fields.length === 0) return [];
    return fields?.map((field) => ({
      prop: field.fieldCode,
      label: field.rename || field.fieldName,
      width: ['质量标准', '所在区域'].includes(field.fieldName) ? 200 : 'auto',
      headerRender: field.planIsRequired === '1'
        ? ({ column }) => (
          <span>
            <i style="color: #FF3B30">*</i> {column.label}
          </span>
        )
        : undefined,
    }));
  }

  // 根据动态字段生成表格内表单配置
  function generateDynamicFormConfig(fields) {
    const config = {};
    fields.forEach((field) => {
      const formItem = {
        prop: field.fieldCode,
        label: field.rename || field.fieldName,
        rules: field.planIsRequired === '1' ? [{ required: true, message: `${field.rename || field.fieldName}不能为空` }] : [],
        colProps: { span: 24 },
      };
      switch (field.fieldType) {
        case 'TEXT':
          formItem.type = pageType?.value ? '' : 'input';
          formItem.rules.push({
            max: Number(field.fieldLength),
            message: `${field.rename || field.fieldName}长度不能超过${field.fieldLength}个字符`,
          });
          formItem.attrs = {
            disabled: fixedFieldNamesNoEdit.includes(field.fieldName),
            placeholder: field.fieldDesc || '请输入',
          }
          break;
        case 'NUM':
          formItem.type = pageType?.value ? '' : 'input';
          formItem.rules.push(
            {
              pattern: /^\d+(\.\d+)?$/,
              message: '请输入数字',
            },
            {
              validator: (rule, value, callback) => {
                if (value && value.toString().length > field.fieldLength) {
                  callback(new Error(`${field.rename || field.fieldName}总长度不能超过${field.fieldLength}个字符`));
                } else {
                  callback();
                }
              },
              trigger: 'blur'
            }
          );
          break;
        case 'NUM_CALC':
          formItem.type = pageType?.value ? '' : 'input';
          // 优化：避免在render函数中创建新的computed
          formItem.render = (row) => {
            // 直接计算结果，避免创建computed
            const calculatedValue = pageType?.value ? row[field.fieldCode] : calculate(field, row);
            return (
              <p>{calculatedValue}</p>
            )
          }
          break;
        case 'ENUM':
          formItem.type = pageType?.value ? '' : 'select';
          formItem.enums = field.enumValues && JSON.parse(field.enumValues)?.map((item) => ({
            label: item.label,
            value: item.value,
          })) || [];
          if (pageType.value && formItem.label !== '质量标准') {
            formItem.render = (row) => {
              let options = field.enumValues && JSON.parse(field.enumValues);
              let name = options.find(i => i.value === row[field.fieldCode])?.label || '-';
              return (
                <p>{name}</p>
              )
            }
          }
          if (formItem.label === '需求牧场' && !pageType.value) {
            formItem.render = (row) => {
              return (
                <el-select
                  v-model={row[formItem.prop]}
                  onChange={value => changeUsageLocation(value, row, fields)}
                >
                  {field.enumValues && JSON.parse(field.enumValues).map(item => (
                    <el-option key={item.value} label={item.label} value={item.value} />
                  ))}
                </el-select>
              )
            }
          }
          if (formItem.label === '质量标准') {
            formItem.render = (row) => {
              return pageType.value ? (
                <span style='cursor: pointer;color: #0069FF' onClick={ () => handleViewIndicator(row) }>{row.qualityIndicatorName}</span>
              ) : (
                <div>
                  <el-select
                    v-model={row[formItem.prop]}
                    style='width:80% !important'
                    onClick={ () => getQualityIndicator(row) }
                    onChange={ (val) => {
                      row.qualityIndicatorId = val;
                      row.qualityIndicatorName = row.indicators.find(i => i.value === val)?.label || '';
                    } }
                  >
                    {(row.indicators || []).map(item => (
                      <el-option key={item.value} label={item.label} value={item.value} />
                    ))}
                  </el-select>
                  <el-button 
                    style='width:calc(20%-5px);margin: 0 0 5px 5px' 
                    type="text" 
                    onClick={ () => handleViewIndicator(row) }
                    disabled={!row.qualityIndicatorId}
                  >查看</el-button>
                </div>
                
              )
            }
          }
          break;
        case 'LINK_FORM':
          formItem.type = pageType?.value ? '' : 'select';
          formItem.enums = field.enumValues && JSON.parse(field.enumValues)?.map((item) => ({
            label: item.label,
            value: item.value,
          })) || [];
          if (pageType?.value) {
            formItem.render = (row) => {
              let options = field.enumValues && JSON.parse(field.enumValues);
              let name = options.find(i => i.value === row[field.fieldCode])?.label || '-';
              return (
                <p>{name}</p>
              )
            }
          }
          break;
        case 'ATTACH':
          formItem.type = pageType?.value ? '' : '';
          formItem.render = (row) => {
            return (
              <FileList
                modelValue={row[field.fieldCode]}
                isView={!!pageType?.value}
                onUpdate:modelValue={(fileList) => {
                  row[field.fieldCode] = fileList;
                }}
              />
            );
          };
          break;
        default:
          formItem.type = '';
      }
      config[field.fieldCode] = [formItem];
    });
    return config;
  }

  // 合并固定字段和动态字段的表格列
  const tableColumns = computed(() => {
    // 添加防抖，避免频繁计算
    if (isUpdatingFields.value) {
      // 返回默认的空列配置，避免渲染错误
      return pageType?.value ? [] : [
        {
          type: 'selection',
          width: 52,
          fixed: 'left',
        },
        {
          prop: 'action',
          label: '操作',
          width: 80,
          fixed: 'right',
        }
      ];
    }
    
    const dynamicCols = generateDynamicColumns(dynamicFields.value);
    let columns = [...dynamicCols,];
    if (!pageType?.value) {
      columns.unshift({
        type: 'selection',
        width: 52,
        fixed: 'left',
      });
      columns.push({
        prop: 'action',
        label: '操作',
        width: 80,
        fixed: 'right',
      });
    }
    return columns;
  });

  // 动态表单字段key集合
  const formFields = computed(() => {
    // 添加防抖，避免频繁计算
    if (isUpdatingFields.value) return [];
    
    if (!Array.isArray(dynamicFields.value) || dynamicFields.value.length === 0) return [];
    return dynamicFields.value.map(field => ({
      key: field.fieldCode,
      ...field,
    }));
  });

  // 合并表单配置
  const mergedTableFormConfig = computed(() => {
    // 添加防抖，避免频繁计算
    if (isUpdatingFields.value) return {};
    
    const config = {};
    dynamicFields.value.forEach(field => {
      config[field.fieldCode] = generateDynamicFormConfig([field])[field.fieldCode];
    });
    return config;
  });

  return {
    tableColumns,
    mergedTableFormConfig,
    formFields,
    dynamicFields,
    fetchDynamicFields,
    getDynamicFieldsByPlanId
  }
}

