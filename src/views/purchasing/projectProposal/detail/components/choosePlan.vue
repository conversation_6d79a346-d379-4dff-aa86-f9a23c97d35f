<template>
  <yun-dialog
    v-model="visible"
    size="large"
    title="选择采购计划"
    :confirm-button-text="'确定'"
    :cancel-button-text="'取消'"
    :show-cancel-button="true"
    :confirm-button-handler="handleConfirm"
  >
    <div
      class="table-content"
      v-if="show"
    >
      <yun-pro-table
        ref="tableRef"
        v-model:pagination="pagination"
        v-model:searchData="searchForm"
        v-model:selected="selectData"
        auto-height
        :table-props="{ 
          rowKey: 'id',
				  showTableSetting: true,
			  }"
        :search-fields="searchFields"
        :table-columns="tableColumns"
        :remote-method="remoteMethod"
        :layout="'whole'"
        @reset="initTable"
        :global-selection="true"
        :selection-key="'id'"
      />
    </div>
  </yun-dialog>
</template>

<script setup lang="jsx">
import { ref } from 'vue';
import { ElMessage } from 'yun-design';
import { planListApi } from '@/api/purchasing/plan';
import { useDict } from '@/hooks/dict';

const emit = defineEmits(['confirm']);

const visible = ref(false);
const tableRef = ref();
const selectData = ref([]);
const getDict = computed(() => (str) => {
  if (!str) return [];
  const value = useDict(str)?.[str]?.value;
  return value;
});

// 表格相关
const pagination = ref({
  page: 1,
  size: 10,
  total: 0,
});

const searchForm = ref({
  statusList: ['FINISH', 'EFFECT'],
});

const searchFields = [
  {
    label: '计划编号',
    prop: 'planCode',
    component: 'el-input',
    placeholder: '请输入物料编码',
  },
  {
    label: '计划名称',
    prop: 'planName',
    component: 'el-input',
    placeholder: '请输入物料名称',
  },
  {
    label: '采购方式',
    prop: 'procurementMethodList',
    component: 'el-select',
    subComponent: 'el-option',
    attrs: {
      multiple: true,
      filterable: true,
      clearable: true,
    },
    enums: getDict.value('procurement_method') || [],
  },
  {
    label: '计划寻源方式',
    prop: 'planRecruitMethodList',
    component: 'el-select',
    subComponent: 'el-option',
    attrs: {
      multiple: true,
      filterable: true,
      clearable: true,
    },
    enums: getDict.value('plan_recruit_method') || [],
  },
];

const tableColumns = [
  {
    type: 'selection',
    width: 50,
  },
  {
    label: '序号',
    prop: 'index',
    type: 'index',
  },
  {
    label: '采购计划编号',
    prop: 'planCode',
    width: 120,
  },
  {
    label: '采购计划名称',
    prop: 'planName',
    minWidth: 120,
  },
  {
    label: '采购业务类型',
    prop: 'serviceTypeName',
    width: 120,
  },
  {
    label: '申请日期',
    prop: 'applyDate',
  },
  {
    label: '申请部门',
    prop: 'applyDeptName',
  },
  {
    label: '申请人',
    prop: 'applicantName',
  },
  {
    label: '采购方式',
    prop: 'procurementMethod',
    minWidth: 150,
    formatter(row) {
      const value = row['procurementMethod']?.split(',') || [];
      const options = getDict.value('procurement_method') || [];
      if (Array.isArray(value)) {
        return value.map((item) => options?.find((opt) => opt.value == item)?.label).join('、');
      }
      return options?.find((item) => item.value == value)?.label || '-';
    },
  },
  {
    label: '计划寻源方式',
    prop: 'planRecruitMethod',
    minWidth: 150,
    formatter(row) {
      const value = row['planRecruitMethod'];
      const options = getDict.value('plan_recruit_method') || [];
      if (Array.isArray(value)) {
        return value.map((item) => options?.find((opt) => opt.value == item)?.label).join('、');
      }
      return options?.find((item) => item.value == value)?.label || '-';
    },
  },
  {
    label: '单据状态',
    prop: 'status',
    formatter(row) {
      const value = row['status'];
      const options = getDict.value('plan_status') || [];
      return options?.find((item) => item.value == value)?.label || '-';
    },
  },
];

const remoteMethod = async ({ searchData, pagination }) => {
  const { data } = await planListApi(
    {
      ...searchData,
    },
    { current: pagination?.page, size: pagination?.size }
  );
  return data;
};

const handleConfirm = () => {
  if (!selectData.value || selectData.value.length === 0) {
    ElMessage.warning('请选择采购计划');
    return;
  }
  visible.value = false;
  emit('confirm', selectData.value);
};

const show = () => {
  visible.value = true;
  tableRef?.value?.tableRef?.clearSelection();
  selectData.value = [];
};

const initTable = () => {
  pagination.value.page = 1;
  searchForm.value.materialCategoryId = '';
  tableRef?.value?.getData();
};

defineExpose({
  show,
});
</script>

<style lang="scss" scoped>
.table-content {
  height: 500px;
  :deep(.yun-pro-table) {
    height: 500px;
  }
}
</style>
