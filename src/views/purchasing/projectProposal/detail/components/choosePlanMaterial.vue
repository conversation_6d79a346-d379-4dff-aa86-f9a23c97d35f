<template>
  <yun-dialog
    v-model="visible"
    size="large"
    title="选择计划物料"
    :confirm-button-text="'确定'"
    :cancel-button-text="'取消'"
    :show-cancel-button="true"
    :confirm-button-handler="handleConfirm"
  >
    <div
      class="table-content"
      v-if="show"
    >
      <yun-pro-table
        ref="tableRef"
        v-model:pagination="pagination"
        v-model:searchData="searchForm"
        v-model:selected="selectData"
        auto-height
        :table-props="{ rowKey: 'id', showTableSetting: true }"
        :search-fields="searchFields"
        :table-columns="tableColumns"
        :remote-method="remoteMethod"
        :layout="'whole'"
        @reset="initTable"
        :global-selection="true"
        :selection-key="'id'"
      />
    </div>
  </yun-dialog>
</template>

<script setup lang="jsx">
import { ref } from 'vue';
import { ElMessage } from 'yun-design';
import { planMaterialListApi } from '@/api/purchasing/proposal';
import { useDict } from '@/hooks/dict';

const emit = defineEmits(['confirm']);

const props = defineProps({
  planId: String,
});

const visible = ref(false);
const tableRef = ref();
const selectData = ref([]);
const getDict = computed(() => (str) => {
  if (!str) return [];
  const value = useDict(str)?.[str]?.value;
  return value;
});

// 表格相关
const pagination = ref({
  page: 1,
  size: 10,
  total: 0,
});

const searchForm = ref({});

const searchFields = [
  {
    label: '计划编号/名称',
    prop: 'searchContent',
    component: 'el-input',
    placeholder: '请输入计划编号/名称',
  },
  {
    label: '需求行号',
    prop: 'requireNo',
    component: 'el-input',
    placeholder: '请输入需求行号',
  },
  {
    label: '物料编码/名称',
    prop: 'materialSearchContent',
    component: 'el-input',
    placeholder: '请输入物料编码/名称',
  },
];

const tableColumns = [
  {
    type: 'selection',
    width: 50,
  },
  {
    label: '序号',
    prop: 'index',
    type: 'index',
  },
  {
    label: '计划编号',
    prop: 'planCode',
    width: 120,
  },
  {
    label: '计划名称',
    prop: 'planName',
    minWidth: 120,
  },
  {
    label: '需求行号',
    prop: 'requireNo',
    width: 120,
  },
  {
    label: '申请日期',
    prop: 'applyDate',
  },
  {
    label: '物料编码',
    prop: 'materialCode',
    width: 120,
  },
  {
    label: '物料名称',
    prop: 'materialName',
    minWidth: 120,
  },
  {
    label: '规格型号',
    prop: 'specModel',
    width: 120,
  },
  {
    label: '单位',
    prop: 'unit',
    width: 80,
  },
];

const remoteMethod = async ({ searchData, pagination }) => {
  const { data } = await planMaterialListApi(
    {
      ...searchData,
      ids: props.planId?.split(','),
    },
    { current: pagination?.page, size: pagination?.size }
  );
  return data;
};

const handleConfirm = () => {
  if (!selectData.value || selectData.value.length === 0) {
    ElMessage.warning('请选择计划物料');
    return;
  }
  visible.value = false;
  emit('confirm', selectData.value);
};

const show = () => {
  visible.value = true;
  tableRef?.value?.tableRef?.clearSelection();
  selectData.value = [];
};

const initTable = () => {
  pagination.value.page = 1;
  searchForm.value.materialCategoryId = '';
  treeRef.value?.setCurrentKey(null);
  tableRef?.value?.getData();
};

defineExpose({
  show,
});
</script>

<style lang="scss" scoped>
.table-content {
  height: 500px;
  :deep(.yun-pro-table) {
    height: 500px;
  }
}
</style>
