// 折叠面板公共样式
// Collapse Panel Common Styles

.proposal-detail-container {
	.content {
		.content-section {
			margin-top: 7px;
			.el-collapse  {
				border: none !important;
			}
			:deep(.section-collapse) {
				border: none !important;
				border-radius: 0 !important;
				.el-collapse-item {
					padding: 20px;
					background: #fff;
				}

				.el-collapse-item__header {
					align-items: baseline;
					background-color: #fff !important;
					height: auto !important;
					padding: 0 !important;
					line-height: 32px !important;
					color: var(--Color-Text-text-color-primary, #1d2129);
					font-family: 'PingFang SC';
					font-size: 16px;
					font-style: normal;
					font-weight: 600;
					line-height: 24px;
				}
			}
			.el-collapse-item__wrap {
				border-bottom: none !important;
			}
			// 隐藏默认的箭头图标
			.el-collapse-item__arrow {
				display: none !important;
			}
			:deep(.first-collapse) {
				.el-collapse-item__wrap {
					border-bottom: 1px solid #ebeef5 !important;
				}
			}

			:deep(.section-collapse.team-section) {
				.el-collapse-item__wrap {
					border: none !important;
				}
			}

			.el-collapse-item__content {
				padding: 0 0 10px 15px !important;
				border-bottom: 1px solid #E4E7ED;
			}
			.section-collapse1 {
				.el-collapse-item__content {
					border-bottom: none !important;
				}
			}
			// 自定义标题样式
			.collapse-title {
				width: auto;
				display: flex;
				align-items: center;
				gap: 8px;
				padding: 0 !important;
				min-height: auto !important;
				font-size: 16px;
				.collapse-icon {
					transition: transform 0.3s ease;
					color: #1d2129;
					font-size: 12px;
				}
			}

			// 当折叠面板展开时，旋转图标
			.el-collapse-item.is-active {
				.collapse-title .collapse-icon {
					color: #1d2129;
					transform: rotate(90deg);
				}
			}
			.form-content {
				margin-top: 12px;
			}
			.el-card__body {
				padding: 0 !important;
			}
			// 可编辑表格样式
			.table-content {
				height: 220px;
				.pro_table {
					.yun-pro-form {
						background: transparent;
						padding: 0;
						margin-bottom: 0px;
						border-radius: 0px;
					}
				}
				:deep(.el-form-item__label) {
					display: none !important;
				}
				tr {
					&:hover {
						background-color: #fff !important;
						td {
							background-color: #fff !important;
						}
					}
					td {
						border-bottom: 1px solid #ebeef5;
						padding: 6px 0;
					}
				}
			}
			// 可编辑表格-详情态样式
			.table-content1 {
				th.el-table__cell {
					background-color: var(--Color-Fill-fill-color-light, #f5f7fa) !important;
					padding: 6px 0 !important;
					border-bottom: 1px solid var(--Color-Border-border-color-light, #ebeef5) !important;

					.cell {
						color: var(---el-text-color-regular, #505762) !important;
						font-family: 'PingFang SC' !important;
						font-size: 14px !important;
						font-style: normal !important;
						font-weight: 600 !important;
					}
				}
			}
		}
	}
}

