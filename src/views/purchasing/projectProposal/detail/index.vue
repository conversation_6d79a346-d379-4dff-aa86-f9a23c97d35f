<template>
  <div
    class="proposal-detail-container"
    :class="proposalDetailContainerClass"
  >
    <div class="content">
      <!-- 左侧锚点导航 -->
      <div class="anchor-navigation">
        <ul class="nav-list">
          <!-- 高亮滑块 -->
          <div
            v-show="isSliderReady"
            class="nav-slider"
            :style="{
              height: sliderStyle.height,
              transform: sliderStyle.transform,
              top: '4px',
            }"
          ></div>
          <li
            v-for="anchor in anchorList"
            :key="anchor.id"
            class="nav-item"
            :class="{ active: activeAnchor === anchor.id }"
            @click="scrollToSection(anchor.id)"
          >
            {{ anchor.label }}
          </li>
        </ul>
      </div>
      <!-- 右侧内容区域 -->
      <div class="content-area">
        <div
          :id="'section-' + 'basic'"
          class="content-section"
        >
          <el-collapse
            v-model="activeCollapse.basic"
            class="section-collapse first-collapse"
            expand-icon-position="left"
          >
            <el-collapse-item
              title="基础信息"
              name="basic"
            >
              <template #title>
                <span class="collapse-title">
                  <el-icon class="collapse-icon"><CaretRight /></el-icon>
                  基础信息
                </span>
              </template>
              <div class="form-content">
                <yun-pro-form
                  :customClass="pageType ? 'form-detail' : ''"
                  ref="formRef"
                  v-model:form="form"
                  :columns="basicColumns"
                  :form-props="{ labelWidth: '110px', labelPosition: 'left' }"
                />
              </div>
            </el-collapse-item>
          </el-collapse>
        </div>
        <div
          :id="'section-' + 'timeLimit'"
          class="content-section"
        >
          <el-collapse
            v-model="activeCollapse.timeLimit"
            class="section-collapse"
            expand-icon-position="left"
          >
            <el-collapse-item
              title="项目时间限制"
              name="timeLimit"
            >
              <template #title>
                <span class="collapse-title">
                  <el-icon class="collapse-icon"><CaretRight /></el-icon>
                  项目时间限制
                </span>
              </template>
              <div class="form-content">
                <yun-pro-form
                  :customClass="pageType ? 'form-detail' : ''"
                  ref="projectTimeRef"
                  v-model:form="form"
                  :columns="projectTimeColumns.value"
                  :form-props="{ labelWidth: '150px', labelPosition: 'left' }"
                />
              </div>
            </el-collapse-item>
          </el-collapse>
        </div>
        <div
          :id="'section-' + 'payment'"
          class="content-section"
        >
          <el-collapse
            v-model="activeCollapse.payment"
            class="section-collapse"
            expand-icon-position="left"
          >
            <el-collapse-item name="payment">
              <template #title>
                <span class="collapse-title">
                  <el-icon class="collapse-icon"><CaretRight /></el-icon>
                  支付信息
                </span>
              </template>
              <div :class="['table-content', { 'table-content1': !!pageType }]">
                <yun-pro-table
                  :table-columns="payColumns"
                  :table-data="form.paymentInfoList"
                  :auto-height="true"
                  :table-props="{
                    stripe: false,
				            showTableSetting: true,
                  }"
                >
                  <template
                    v-for="field in payColumns.filter((i) => i.prop !== 'action')"
                    :key="field.prop"
                    #[`t_${field.prop}`]="{ row }"
                  >
                    <yun-pro-form
                      :customClass="pageType ? 'form-detail' : ''"
                      :ref="
                        (el) => {
                          if (el && form.paymentInfoList) {
                            const rowIndex = form.paymentInfoList.findIndex((item) => item === row);
                            if (rowIndex !== -1) {
                              formRefs[`${rowIndex}_${field.prop}Ref`] = el;
                            }
                          }
                        }
                      "
                      :form="row"
                      :columns="[field]"
                    />
                  </template>
                  <template #t_action="scope">
                    <el-button
                      type="text"
                      :disabled="!!pageType"
                      @click="handleAdd('paymentInfoList')"
                      class="add-btn"
                    >
                      新增
                    </el-button>
                    <el-button
                      @click="handleDel(scope.$index, 'paymentInfoList')"
                      type="text"
                      class="delete-btn"
                      :disabled="!!pageType"
                    >
                      删除
                    </el-button>
                  </template>
                </yun-pro-table>
              </div>
            </el-collapse-item>
          </el-collapse>
        </div>
        <div
          :id="'section-' + 'team'"
          class="content-section"
        >
          <el-collapse
            v-model="activeCollapse.team"
            class="section-collapse team-section"
            expand-icon-position="left"
          >
            <el-collapse-item
              title="项目小组成员"
              name="team"
            >
              <template #title>
                <span class="collapse-title">
                  <el-icon class="collapse-icon"><CaretRight /></el-icon>
                  项目小组成员
                </span>
              </template>
              <div :class="['table-content', { 'table-content1': !!pageType && !partialEdit }]">
                <yun-pro-table
                  :table-columns="memberColumns"
                  :table-data="form.projectMembers"
                  :auto-height="true"
                  :table-props="{
                    stripe: false,
				            showTableSetting: true,
                  }"
                >
                  <template
                    v-for="field in memberColumns.filter((i) => i.prop !== 'action')"
                    :key="field.prop"
                    #[`t_${field.prop}`]="{ row }"
                  >
                    <yun-pro-form
                      :customClass="pageType ? 'form-detail' : ''"
                      :ref="
                        (el) => {
                          if (el && form.projectMembers) {
                            const rowIndex = form.projectMembers.findIndex((item) => item === row);
                            if (rowIndex !== -1) {
                              formRefs[`${rowIndex}_${field.prop}Ref`] = el;
                            }
                          }
                        }
                      "
                      :form="row"
                      :columns="[field]"
                    />
                  </template>
                  <template #t_action="scope">
                    <el-button
                      type="text"
                      @click="handleAdd('projectMembers')"
                      :disabled="!!pageType && !partialEdit"
                      class="add-btn"
                    >
                      增加
                    </el-button>
                    <el-button
                      @click="handleDel(scope.$index, 'projectMembers')"
                      type="text"
                      class="delete-btn"
                      :disabled="(form.projectMembers.length === 1 || !!pageType) && (form.projectMembers.length === 1 || !partialEdit)"
                    >
                      删除
                    </el-button>
                  </template>
                </yun-pro-table>
              </div>
            </el-collapse-item>
          </el-collapse>
        </div>
        <div
          :id="'section-' + 'target'"
          class="content-section"
        >
          <el-collapse
            v-model="activeCollapse.target"
            class="section-collapse"
            expand-icon-position="left"
          >
            <el-collapse-item
              title="标段信息"
              name="target"
            >
              <template #title>
                <span class="collapse-title">
                  <el-icon class="collapse-icon"><CaretRight /></el-icon>
                  标段信息
                </span>
              </template>
              <div class="form-content">
                <yun-pro-form
                  :customClass="pageType ? 'form-detail' : ''"
                  ref="lotRef"
                  v-model:form="form"
                  :columns="lotColumns"
                  :form-props="{ labelWidth: '110px', labelPosition: 'left' }"
                ></yun-pro-form>
              </div>
              <div :class="['table-content', { 'table-content1': !!pageType }]" v-if="['XJCG', 'JZTP', 'ZB'].includes(form.sourcingType) && form.lotType !== 0">
                <yun-pro-table
                  :table-columns="lotTableColumns"
                  :table-data="form.sections"
                  :auto-height="true"
                  :table-props="{
                    stripe: false,
				            showTableSetting: true,
                  }"
                >
                  <template
                    v-for="field in lotTableColumns.filter((i) => !['action', 'index'].includes(i.prop))"
                    :key="field.prop"
                    #[`t_${field.prop}`]="{ row }"
                  >
                    <yun-pro-form
                  :customClass="pageType ? 'form-detail' : ''"
                      :ref="
                        (el) => {
                          if (el && form.sections) {
                            const rowIndex = form.sections.findIndex((item) => item === row);
                            if (rowIndex !== -1) {
                              formRefs[`${rowIndex}_${field.prop}Ref`] = el;
                            }
                          }
                        }
                      "
                      :form="row"
                      :columns="[field]"
                    />
                  </template>
                  <template #t_action="scope">
                    <el-button
                      type="text"
                      @click="handleAdd('sections')"
                      class="add-btn"
                      :disabled="!!pageType"
                    >
                      增加
                    </el-button>
                    <el-button
                      @click="handleDel(scope.$index, 'sections')"
                      type="text"
                      class="delete-btn"
                      :disabled="form.sections.length < 3 || !!pageType"
                    >
                      删除
                    </el-button>
                  </template>
                </yun-pro-table>
              </div>
            </el-collapse-item>
          </el-collapse>
        </div>
        <div
          :id="'section-' + 'list'"
          class="content-section"
        >
          <el-collapse
            v-model="activeCollapse.list"
            class="section-collapse"
            expand-icon-position="left"
          >
            <el-collapse-item
              title="项目清单"
              name="list"
            >
              <template #title>
                <span class="collapse-title">
                  <el-icon class="collapse-icon"><CaretRight /></el-icon>
                  项目清单
                </span>
              </template>
              <div :class="['list-tabs', { 'single-tab': !form.lotType }]">
                <div class="toolbar">
                  <el-button
                    @click.stop="choosePlanMaterial"
                    :disabled="!dynamicFields.length || !form.planId"
                    v-if="!pageType"
                    >选择计划物料</el-button
                  >
                  <el-button
                    @click.stop="chooseMaterial"
                    :disabled="!dynamicFields.length"
                    v-if="!pageType"
                    >选择物料</el-button
                  >
                  <el-button
                    @click.stop="batchImport"
                    :disabled="!dynamicFields.length"
                    v-if="!pageType"
                    >批量导入</el-button
                  >
                  <el-button
                    @click.stop="handleExport"
                    :disabled="!dynamicFields.length || !!props.mode"
                    >导出</el-button
                  >
                  <el-button
                    @click.stop="batchDelete(null)"
                    :disabled="!selectedRow.length"
                    v-if="!pageType"
                    >批量删除</el-button
                  >
                </div>
                <el-tabs
                  v-model="activeName"
                  class="proposal-tabs"
                >
                  <el-tab-pane
                    v-for="(item, index) in form.sections"
                    :key="index"
                    :label="`标段${index + 1}`"
                    :name="index.toString()"
                  >
                    <div :class="['table-content', { 'table-content1': !!pageType }]">
                      <yun-pro-table
                        v-model:selected="selectedRow"
                        :table-columns="tableColumns"
                        :table-data="item.projectItems"
                        :selection-key="'id'"
                        :auto-height="true"
                        :table-props="{
                          stripe: false,
				                  showTableSetting: true,
                        }"
                      >
                        <template
                          v-for="field in formFields"
                          :key="field.key"
                          #[`t_${field.key}`]="{ row }"
                        >
                          <yun-pro-form
                            :customClass="pageType ? 'form-detail' : ''"
                            :ref="
                              (el) => {
                                if (el && item.projectItems) {
                                  const rowIndex = item.projectItems.findIndex((item) => item === row);
                                  if (rowIndex !== -1) {
                                    formRefs[`${rowIndex}_${field.key}Ref`] = el;
                                  }
                                }
                              }
                            "
                            :form="row"
                            :columns="mergedTableFormConfig[field.key]"
                          />
                        </template>
                        <template #t_action="scope">
                          <el-button
                            @click="batchDelete(scope.row, scope.$index)"
                            type="text"
                            class="delete-btn delete-btn1"
                            :disabled="!!pageType"
                          >
                            删除
                          </el-button>
                        </template>
                      </yun-pro-table>
                    </div>
                  </el-tab-pane>
                </el-tabs>
              </div>
            </el-collapse-item>
          </el-collapse>
        </div>
        <div
          :id="'section-' + 'attachments'"
          class="content-section"
        >
          <el-collapse
            v-model="activeCollapse.attachments"
            class="section-collapse section-collapse1"
            expand-icon-position="left"
          >
            <el-collapse-item
              title="项目附件"
              name="attachments"
            >
              <template #title>
                <span class="collapse-title">
                  <el-icon class="collapse-icon"><CaretRight /></el-icon>
                  项目附件
                </span>
              </template>
              <div class="form-content">
                <yun-pro-form
                  :customClass="pageType ? 'form-detail' : ''"
                  ref="attachRef"
                  v-model:form="form"
                  :columns="projectColumns"
                  :form-props="{ labelWidth: '110px', labelPosition: 'left' }"
                ></yun-pro-form>
              </div>
            </el-collapse-item>
          </el-collapse>
        </div>
        <div class="proposal-detail-footer" v-if="!props.mode">
          <el-button
            @click="handleSubmit"
            type="primary"
            :loading="loading"
            v-if="!pageType || partialEdit"
            >提交审核</el-button
          >
          <el-button @click="handleCancel" v-if="pageType !== 'biddingView'">{{ pageType && !partialEdit ? '返回' : '取消' }}</el-button>
        </div>
      </div>
    </div>
    <ChoosePlan
      ref="choosePlanRef"
      @confirm="handleChoosePlanConfirm"
    />
    <ChooseMaterial
      ref="chooseMaterialRef"
      @confirm="handleChooseMaterialConfirm"
    />
    <ChoosePlanMaterial
      :planId="form.planId"
      ref="choosePlanMaterialRef"
      @confirm="handleChoosePlanMaterialConfirm"
    />
    <yun-import
      v-model="importVisible"
      dialog-tips="温馨提示:单次最多只能导入1000条数据"
      upload-tips="只能上传.xls,.xlsx文件"
      :download-function="handleDownTemplate"
      :upload-function="handleImport"
      :template-urls="templateUrls"
    />
    <IndicatorDetail ref="indicatorDetailRef" />
  </div>
</template>

<script setup lang="jsx">
import { useTable } from './hooks/index.jsx';
import { YunDialog } from '@ylz-material/dialog';
import { useExcel } from '@/views/purchasing/plan/detail/hooks/useExcel.jsx';
import { anchorList } from '../constant.js';
import { CaretRight } from '@element-plus/icons-vue';
import { ElMessage } from 'yun-design';
import { onMounted, nextTick, onUnmounted, computed } from 'vue';
import ChoosePlan from './components/choosePlan.vue';
import ChoosePlanMaterial from './components/choosePlanMaterial.vue';
import ChooseMaterial from '@/views/purchasing/plan/detail/components/chooseMaterial.vue';
import { procurementProposalAdd } from '@/api/purchasing/proposal';
import IndicatorDetail from '@/views/purchasing/plan/detail/components/indicatorDetail.vue';

const props = defineProps({
  mode: {
    type: String,
    default: '',
  },
  pageType: {
    type: String,
    default: '',
  }
});

const indicatorDetailRef = ref();
const formRef = ref();
const projectTimeRef = ref();
const attachRef = ref();
const lotRef = ref();
const formRefs = reactive({});

const {
  form,
  basicColumns,
  payColumns,
  projectTimeColumns,
  memberColumns,
  lotColumns,
  lotTableColumns,
  projectColumns,
  loading,
  choosePlanRef,
  activeAnchor,
  isSliderReady,
  sliderStyle,
  activeCollapse,
  scrollToSection,
  updateSliderPosition,
  handleScroll,
  handleChoosePlanConfirm,
  handleChooseMaterialConfirm,
  pageType,
  partialEdit,
  chooseMaterialRef,
  chooseMaterial,
  selectedRow,
  activeName,
  tableColumns,
  mergedTableFormConfig,
  dynamicFields,
  formFields,
  choosePlanMaterialRef,
  handleChoosePlanMaterialConfirm,
  choosePlanMaterial,
  goBack,
} = useTable(props, indicatorDetailRef, formRef, projectTimeRef, attachRef, lotRef, formRefs);

const importVisible = ref(false);
const templateUrls = ref([
  {
    label: '模板下载',
    value: '',
  },
]);

const { downloadExcelTemplate, importExcelFile, exportMultiSheetToExcel, exportToExcel } = useExcel();

const proposalDetailContainerClass = computed(() => {
  if (pageType.value === 'biddingView') {
    return 'proposal-detail-bidding-view';
  }
  if (props.mode === 'audit') {
    return 'proposal-detail-audit-view';
  }
  // view 或 undefined
  return 'proposal-detail-view';
});

const handleAdd = (type) => {
  if (type === 'sections') {
    form.value[type].push({ projectItems: [] });
  } else if (type === 'projectMembers') {
    form.value[type].push({ role: 'PROJECT_MEMBER', userId: '', contactPhone: '', ifEdit: partialEdit.value });
  } else {
    form.value[type].push({});
  }
};
const handleDel = (index, type) => {
  form.value[type].splice(index, 1);
};
const handleCancel = () => {
  if (!pageType.value) {
		YunDialog.confirm('确定取消当前操作?', '提示', {
			type: 'warning',
			confirmButtonText: '确定',
			cancelButtonText: '取消',
			confirmButtonHandler: (done) => {
				done();
				goBack();
			},
		});
	} else {
		goBack();
	}
};
const handleSubmit = async () => {
  const errors = [];
  const validatePromises = [
    // 基础表单校验
    formRef?.value?.elForm?.validate().catch(() => {
      errors.push('基础信息填写有误');
    }),
    projectTimeRef?.value?.elForm?.validate().catch(() => {
      errors.push('项目时间限制填写有误');
    }),
    // 支付信息表格内表单校验
    ...form.value.paymentInfoList.map((item, index) => {
      return Promise.all(
        payColumns.value.map((field) => {
          const refKey = `${index}_${field.prop}Ref`;
          const formRef = formRefs[refKey];
          if (field.prop !== 'action') {
            if (!formRef) {
              // errors.push(`第${index + 1}行${field.label}表单引用不存在`);
              return Promise.resolve();
            }
            return formRef.elForm?.validate().catch(() => {
              errors.push(`第${index + 1}行${field.label}填写有误`);
            });
          }
        })
      );
    }),
    // 项目小组成员表格内表单校验
    ...form.value.projectMembers.map((item, index) => {
      return Promise.all(
        memberColumns.value.map((field) => {
          const refKey = `${index}_${field.prop}Ref`;
          const formRef = formRefs[refKey];
          if (field.prop !== 'action') {
            if (!formRef) {
              // errors.push(`第${index + 1}行${field.label}表单引用不存在`);
              return Promise.resolve();
            }
            return formRef.elForm?.validate().catch(() => {
              errors.push(`第${index + 1}行${field.label}填写有误`);
            });
          }
        })
      );
    }),
    // 标段信息表格内表单校验
    ...form.value.sections.map((item, index) => {
      return Promise.all(
        lotTableColumns.value.map((field) => {
          const refKey = `${index}_${field.prop}Ref`;
          const formRef = formRefs[refKey];
          if (!['index', 'action'].includes(field.prop)) {
            if (!formRef) {
              // errors.push(`第${index + 1}行${field.label}表单引用不存在`);
              return Promise.resolve();
            }
            return formRef.elForm?.validate().catch(() => {
              errors.push(`第${index + 1}行${field.label}填写有误`);
            });
          }
        })
      );
    }),
    // 标段信息-项目清单表格内表单校验
    ...form.value.sections.flatMap((section, sectionIndex) => {
      return section.projectItems.map((row, rowIndex) => {
        return Promise.all(
          formFields.value.map((field) => {
            const refKey = `${rowIndex}_${field.key}Ref`;
            const formRef = formRefs[refKey];
            if (field.prop !== 'action') {
              if (!formRef) {
                // errors.push(`标段${sectionIndex + 1}，项目清单第${rowIndex + 1}行${field.label}表单引用不存在`);
                return Promise.resolve();
              }
              return formRef.elForm?.validate().catch(() => {
                errors.push(`标段${sectionIndex + 1}，项目清单第${rowIndex + 1}行${field.label}填写有误`);
              });
            }
          })
        );
      });
    }),
  ];
  await Promise.all(validatePromises);
  console.log(errors, 'errorserrorserrors');
  if (errors.length > 0) {
    ElMessage.error('存在表单校验失败'); // 只提示第一个错误，也可以合并所有错误一起提示
    return;
  }
  await handleConfirm();
};

// 提取时间区间到params
function extractRangeToParams(params, formValue) {
  const timeRangeFields = ['quoteTime', 'registerTime', 'preReviewTime', 'tenderFeePayTime', 'tenderFileGainTime', 'tenderClarifyTime'];

  timeRangeFields.forEach((field) => {
    const value = formValue[field];
    if (value && Array.isArray(value) && value.length > 1) {
      // 去除末尾的'Time'再拼接
      const base = field.slice(0, -4);
      params[`${base}StartTime`] = value[0];
      params[`${base}EndTime`] = value[1];
      delete params[field];
    }
  });
}

const handleConfirm = async () => {
  let { sections, purchaseDeptId, preQualification, attachment, ...rest } = form.value;
  // 每个section单独处理
  const sectionList = (sections || []).map((section) => {
    const transformedItems = (section.projectItems || []).map((item) => ({
      dynamicFieldList: dynamicFields.value.map((configItem) => {
        let fieldValue = '';
        if (configItem.fieldType === 'ATTACH') {
          fieldValue = JSON.stringify(item[configItem.fieldCode] || '');
        } else {
          fieldValue = item[configItem.fieldCode] || '';
        }
        return {
          code: configItem.fieldCode,
          value: fieldValue,
        };
      }),
      requireNo: item.requireNo,
      planId: item.planId,
    }));
    return {
      sectionName: section.sectionName,
      sectionCode: section.sectionCode,
      projectItems: transformedItems,
    };
  });
  let params = {
    ...rest,
    preQualification: preQualification,
    purchaseDeptId: typeof purchaseDeptId === 'string' ? purchaseDeptId : purchaseDeptId?.join(',') || '',
    sections: sectionList,
    attachmentList:
      attachment?.map((i) => {
        return { fileName: i.fileName, filePath: i.url };
      }) || [],
  };
  // 统一处理所有区间时间
  extractRangeToParams(params, form.value);
  loading.value = true;
  try {
    await procurementProposalAdd(params);
    ElMessage.success(`${params.id ? '更新' : '新增'}采购项目成功`);
    window.history.back();
    loading.value = false;
  } catch (error) {
    loading.value = false;
    console.error('采购项目新增失败:', error);
  }
};

// 批量导入
const batchImport = () => {
  importVisible.value = true;
};

// 下载导入模板
const handleDownTemplate = async () => {
  downloadExcelTemplate(dynamicFields.value);
};

// 处理导入
const handleImport = async (files) => {
  if (files.length === 0) {
    ElMessage({ type: 'warning', message: '请先上传文件' });
    return;
  }
  try {
    const rows = await importExcelFile(files[0].raw, dynamicFields.value);
    form.value.sections[Number(activeName.value)].projectItems = [...form.value.sections[Number(activeName.value)].projectItems, ...rows];
    ElMessage.success('导入成功');
    importVisible.value = false;
  } catch (error) {
    ElMessage.error(error.message || '导入失败');
  }
};

// 处理导出
const handleExport = async () => {
  if (!form.value.sections || form.value.sections.length === 0) {
    ElMessage.warning('没有可导出的数据');
    return;
  }
  try {
    let blob = null;
    if(form.value.lotType) {
      // 组装每个标段sheet
      const sheets = form.value.sections.map((section, idx) => ({
        sheetName: section.sectionName || `标段${idx + 1}`,
        data: section.projectItems || [],
        fields: dynamicFields.value, // 如有每个标段不同字段可自行调整
      }));
      blob = await exportMultiSheetToExcel(sheets);
    } else {
      blob = await exportToExcel(form.value.sections[0]?.projectItems || [], dynamicFields.value);
    }
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `${form.value.serviceTypeName}-项目清单.xlsx`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    window.URL.revokeObjectURL(url);
  } catch (error) {
    ElMessage.error('导出失败');
  }
};

// 批量删除
const batchDelete = (row, index) => {
  YunDialog.confirm('确定删除所选物料?', '提示', {
    type: 'info',
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    confirmButtonHandler: async (done) => {
      let currentList = form.value.sections[activeName.value].projectItems;
      if (row) {
        currentList.splice(index, 1);
      } else {
        form.value.sections[activeName.value].projectItems = currentList.filter((item) => !selectedRow.value.some((selected) => selected.materialCode === item.materialCode));
      }
      ElMessage.success('删除成功');
      done();
    },
  });
};

// 组件挂载后初始化滑块和滚动监听器
onMounted(() => {
  nextTick(() => {
    // 监听容器滚动事件
    const biddingContainer = document.querySelector('.content-area') || document.querySelector('.proposal-detail-container');
    if (biddingContainer) {
      biddingContainer.addEventListener('scroll', handleScroll);
    }
    // 延迟初始化滑块位置，确保DOM完全渲染
    const initSlider = () => {
      updateSliderPosition();
      // 如果滑块还没有准备好，继续重试
      if (!isSliderReady.value) {
        setTimeout(initSlider, 100);
      }
    };
    // 延迟初始化，确保DOM完全渲染
    setTimeout(initSlider, 100);
  });
});

// 组件卸载后清理滚动监听器
onUnmounted(() => {
  // 清理滚动监听器
  const biddingContainer = document.querySelector('.content-area') || document.querySelector('.proposal-detail-container');
  if (biddingContainer) {
    biddingContainer.removeEventListener('scroll', handleScroll);
  }
});
</script>

<style lang="scss">
@import './style/collapse-panel.scss';
</style>

<style lang="scss" scoped>
.proposal-detail-container {
  .content {
    display: flex;
    background: #fff;
    gap: 20px;
    padding: 20px;
    // 左侧锚点导航
    .anchor-navigation {
      width: 120px; // 恢复固定宽度
      flex-shrink: 0; // 防止在空间不足时被压缩
      position: sticky;
      border-left: 1px solid #e6eaf0;
      top: 20px;
      height: fit-content;
      .nav-list {
        list-style: none;
        padding: 0;
        margin: 0;
        position: relative; // 为滑块定位提供上下文

        // 高亮滑块
        .nav-slider {
          position: absolute;
          left: -1px;
          width: 1px;
          height: 16px;
          background-color: #0069ff;
          transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1), opacity 0.3s ease;
          will-change: transform;
          z-index: 1;
          // 添加硬件加速
          transform: translateZ(0);
          backface-visibility: hidden;
        }

        .nav-item {
          padding: 2px 12px;
          cursor: pointer;
          border-radius: 4px;
          transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
          color: var(--Light-Light-el-text-color-primary, #1c2026);
          font-family: 'PingFang SC';
          font-size: 12px;
          font-style: normal;
          font-weight: 400;
          line-height: 20px;
          position: relative; // 确保层级关系
          // 添加硬件加速
          transform: translateZ(0);
          backface-visibility: hidden;

          &.active {
            color: var(--Color-Primary-color-primary, #0069ff);
            font-family: 'PingFang SC';
            font-size: 12px;
            font-style: normal;
            font-weight: 600;
            line-height: 20px;
          }
        }
      }
    }
    // 右侧内容区域
    .content-area {
      flex: 1;
      min-width: 0; // 关键！防止内容溢出，确保能够收缩
      // 添加滚动相关样式
      overflow-y: auto;
      max-height: calc(100vh - 120px); // 设置最大高度，确保可以滚动
      .table-content {
        :deep(.el-form-item__label) {
          display: none !important;
        }
        :deep(.yun-pro-form-item__render) {
          > span {
            width: 100%;
            display: inline-block;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
          }
        }
      }
      .el-table {
        width: 100%;
        height: 200px;
        .el-empty__image {
          width: 50px !important;
        }
        .delete-btn {
          font-weight: 400px;
          color: #FF3B30;
        }
        .delete-btn1 {
          font-weight: 760px;
        }
        .add-btn {
          font-weight: 400px;
          color: #0069FF;
        }
        .el-button.is-disabled {
          color: var(--el-button-disabled-text-color) !important;
        }
      }
      .list-tabs {
        position: relative;
        .toolbar {
          position: absolute;
          right: 0;
          z-index: 1000;
        }
        .proposal-tabs {
          margin-bottom: 20px;
        }
        :deep(.el-tabs__nav-wrap::after) {
          height: 0 !important;
        }
      }
      .single-tab {
        :deep(.el-tabs__nav-scroll) {
          visibility: hidden;
        }
      }
      .form-detail {
        :deep(.yun-pro-form-item ){
          margin: 5px 0 !important;
          .el-form-item {
            margin-bottom: 0px;
          }
        }
      }
      .proposal-detail-footer {
        padding: 20px;
        background: #fff;
        margin: 0 0 50px 0;
      }
    }
  }
}
.proposal-detail-view {
  padding: 20px;
}
.proposal-detail-audit-view {
  height: 650px;
  padding: 20px;
  .content {
    height: 100%;
  }
}
.proposal-detail-bidding-view {
  padding: 0;
  .content {
    border-radius: 6px;
  }
}
</style>
