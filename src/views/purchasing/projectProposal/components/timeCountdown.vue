<template>
  <div>
    <div>{{ time || '-' }}</div>
    <div style="color:#FF3B30;font-size:12px;" v-if="time">{{ countdown }}</div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue';

const props = defineProps<{ time: string }>();
const now = ref(Date.now());
let timer: number | undefined | '';

onMounted(() => {
  timer = window.setInterval(() => {
    now.value = Date.now();
  }, 1000);
});
onUnmounted(() => {
  if (timer) clearInterval(timer);
});

const target = computed(() => new Date(props.time).getTime());
const countdown = computed(() => {
  const diff = target.value - now.value;
  if (!props.time || isNaN(target.value)) return '-';
  if (diff <= 0) return '已截止';
  const d = Math.floor(diff / (1000 * 60 * 60 * 24));
  const h = Math.floor((diff / (1000 * 60 * 60)) % 24);
  const m = Math.floor((diff / (1000 * 60)) % 60);
  // const s = Math.floor((diff / 1000) % 60);
  // return `剩余${d ? d + '天' : ''}${h}小时${m}分${s}秒`;
  return `剩余${d ? d + '天' : ''}${h}小时${m}分`;
});
</script> 