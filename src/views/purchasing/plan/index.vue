<template>
	<div class="plan-container">
		<yun-pro-table
			ref="tableRef"
			v-model:pagination="state.page"
			v-model:searchData="state.searchData"
			:search-fields="searchFields"
			:layout="'whole'"
			:auto-height="true"
			:table-columns="tableColumns"
			:remote-method="remoteMethod"
			:table-props="tablePropsObj"
		>
			<template #tableHeaderLeft>
				<el-button
					type="primary"
					@click="toDetail()"
				>
					新增采购计划
				</el-button>
			</template>
			<template #t_action="scope">
				<yun-rest limit="3">
					<el-button
						type="text"
						@click="toDetail(scope.row, 'view')"
						v-if="scope.row.status !== 'TO_PROJECT'"
					>
						查看
					</el-button>
					<el-button
						@click="toDetail(scope.row)"
						type="text"
						v-if="!['APPROVE', 'APPROVING'].includes(scope.row.approveStatus)"
					>
						编辑
					</el-button>
					<el-button
						@click="handleDel(scope.row, refreshTable)"
						type="text"
						v-if="!['APPROVE', 'APPROVING'].includes(scope.row.approveStatus)"
					>
						删除
					</el-button>
					<el-button
						@click="handleCancelApproval(scope.row, refreshTable)"
						type="text"
						v-if="['APPROVING'].includes(scope.row.approveStatus)"
					>
						撤销审批
					</el-button>
					<el-button
						@click="toProposal(scope.row)"
						type="text"
						v-if="scope.row.status === 'EFFECT'"
					>
						去立项
					</el-button>
					<el-button
						@click="handleTerminate(scope.row, refreshTable)"
						type="text"
						v-if="['EFFECT', 'TO_PROJECT'].includes(scope.row.status) && scope.row.approveStatus !== 'APPROVING'"
					>
						作废
					</el-button>
					<el-button
						@click="handleDelegate(scope.row)"
						type="text"
						v-if="['EFFECT', 'TO_PROJECT'].includes(scope.row.status) && (scope.row.procurementMethod && scope.row.procurementMethod.includes('WTCG')) && scope.row.approveStatus !== 'APPROVING'"
					>
						委托采购
					</el-button>
				</yun-rest>
			</template>
		</yun-pro-table>
		<DelegatePurchase
			@refreshTable="refreshTable"
			ref="delegatePurchaseRef"
		/>
	</div>
</template>

<script setup name="purchasingPlan" lang="ts">
import { ref, reactive } from 'vue';
import { planListApi } from '@/api/purchasing/plan.ts';
import useOptions from './hooks/useOptions.jsx';
import useHandler from './hooks/useHandler.js';
import DelegatePurchase from './components/delegatePurchase.vue';

const delegatePurchaseRef = ref();
const { toDetail, toProposal, handleCancelApproval, handleDel, handleDelegate, handleTerminate } = useHandler(delegatePurchaseRef);
const { searchFields, tableColumns, tablePropsObj } = useOptions();

const state = reactive({
	page: {
		total: 0, // 总页数
		page: 1, // 当前页数
		size: 20, // 每页显示多少条
	},
	searchData: {},
	loading: false,
});
const tableRef = ref();

const remoteMethod = async ({ searchData, pagination }) => {
	const { data } = await planListApi(
		{
			...searchData,
		},
		{ current: pagination?.page, size: pagination?.size }
	);
	return data;
};

const refreshTable = () => {
	state.page.page = 1;
	tableRef.value?.getData?.();
};
</script>

<style lang="scss" scoped>
.plan-container {
	width: 100%;
	height: calc(100vh - 88px);
	:deep(.el-form .el-form-item:last-of-type) {
		margin-bottom: 24px !important;
	}
}
</style>
