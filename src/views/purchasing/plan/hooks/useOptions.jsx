import { computed } from 'vue';
import { useDict } from '@/hooks/dict';

const getDict = computed(() => (str) => {
  if (!str) return [];
  const value = useDict(str)?.[str]?.value;
  return value;
});

export default function useOptions() {
  const searchFields = computed(() => {
    return [
      {
        label: '采购计划编号',
        prop: 'planCode',
        component: 'el-input',
      },
      {
        label: '采购计划名称',
        prop: 'planName',
        component: 'el-input',
      },
      {
        label: '申请人',
        prop: 'applicantName',
        component: 'el-input',
      },
      {
        label: '单据状态',
        prop: 'statusList',
        component: 'el-select',
        subComponent: 'el-option',
        attrs: {
          multiple: true,
          filterable: true,
        },
        enums: getDict.value('plan_status') || [],
      },
      {
        label: '计划寻源方式',
        prop: 'planRecruitMethodList',
        component: 'el-select',
        subComponent: 'el-option',
        attrs: {
          multiple: true,
          filterable: true,
          clearable: true,
        },
        enums: getDict.value('plan_recruit_method') || [],
      },
      {
        label: '申请部门',
        prop: 'applyDeptName',
        component: 'el-input',
      },
      {
        label: '采购方式',
        prop: 'procurementMethodList',
        component: 'el-select',
        subComponent: 'el-option',
        attrs: {
          multiple: true,
          filterable: true,
          clearable: true,
        },
        enums: getDict.value('procurement_method') || [],
      },
    ]
  })
  const tableColumns = [
    {
      label: '序号',
      prop: 'index',
      type: 'index',
    },
    {
      label: '计划编号',
      prop: 'planCode',
      minWidth: 175,
    },
    {
      label: '计划名称',
      prop: 'planName',
    },
    {
      label: '采购业务类型',
      prop: 'serviceTypeName',
      minWidth: 150,
    },
    {
      label: '申请日期',
      prop: 'applyDate',
    },
    {
      label: '申请部门',
      prop: 'applyDeptName',
      
    },
    {
      label: '申请人',
      prop: 'applicantName',
    },
    {
      label: '申请人电话',
      prop: 'applicantPhone',
      minWidth: 140,
    },
    {
      label: '采购部门',
      prop: 'procurementDeptName',
    },
    {
      label: '计划寻源方式',
      prop: 'planRecruitMethod',
      minWidth: 150,
      formatter(row){
        const value = row['planRecruitMethod'];
        const options = getDict.value('plan_recruit_method') || [];
        if (Array.isArray(value)) {
          return value.map((item) => options?.find((opt) => opt.value == item)?.label).join('、');
        }
        return options?.find((item) => item.value == value)?.label || '-';
      }
    },
    {
      label: '期望到货日期',
      prop: 'periodStartDate',
      minWidth: 150,
    },
    {
      label: '采购方式',
      prop: 'procurementMethod',
      minWidth: 150,
      formatter(row){
        const value = row['procurementMethod'];
        const options = getDict.value('procurement_method') || [];
        return options?.find((item) => item.value == value)?.label || '-';
      }
    },
    {
      label: '资金来源',
      prop: 'fundSource',
    },
    {
      label: '预算金额',
      prop: 'budgetAmount',
    },
    {
      label: '计划来源',
      prop: 'source',
      formatter(row){
        const value = row['source'];
        const options = getDict.value('plan_source') || [];
        if (Array.isArray(value)) {
          return value.map((item) => options?.find((opt) => opt.value == item)?.label).join('、');
        }
        return options?.find((item) => item.value == value)?.label || '-';
      }
    },
    {
      label: '创建人',
      prop: 'createBy',
    },
    {
      label: '创建时间',
      prop: 'createTime',
      minWidth: 165,
    },
    {
      label: '单据状态',
      prop: 'status',
      formatter(row){
        const value = row['status'];
        const options = getDict.value('plan_status') || [];
        return options?.find((item) => item.value == value)?.label || '-';
      }
    },
    {
      label: '审批状态',
      prop: 'approveStatus',
      formatter(row){
        const value = row['approveStatus'];
        const options = getDict.value('plan_approve_status') || [];
        return options?.find((item) => item.value == value)?.label || '-';
      }
    },
    {
      label: '当前审批人',
      prop: 'createBy',
    },
    // {
    //   label: '采购中数量',
    //   prop: 'createBy',
    //   minWidth: 150
    // },
    {
      label: '操作',
      prop: 'action',
      width: '200px',
      fixed: 'right',
    },
  ];

  const tablePropsObj = computed(() => {
    return {
      // 去除斑马纹
      stripe: false,
      // 设置表头样式
      headerCellStyle: {
        backgroundColor: '#f5f7fa',
        color: '#303133',
        fontWeight: 'bold',
        height: '40px',
      },
      // 设置单元格样式
      cellStyle: {
        padding: '0',
        height: '40px',
        'vertical-align': 'middle',
      },
      // 设置行样式
      rowStyle: {
        height: '40px',
      },
      // 设置表格行高
      rowHeight: 40,
      showTableSetting: true,
    };
  })

  return {
    searchFields,
    tableColumns,
    tablePropsObj,
  };
}