import { useRouter } from 'vue-router';
import { ElMessage, ElMessageBox } from 'yun-design';
import { procurementPlanRemove, terminatePlan, cancelApprovePlan } from '@/api/purchasing/plan.ts';

export default (delegatePurchaseRef) => {
  const router = useRouter();
  // 查看、编辑、新增
  const toDetail = (row, pageType) => {
    let pageTypeName = '';
    if(pageType) {
      pageTypeName = '查看';
    } else {
      if(row?.id) {
        pageTypeName = '编辑';
      } else {
        pageTypeName = '新增';
      }
    }
    router.push({
      path: '/purchasing/plan/detail/index',
      query: {
        planCode: row?.planCode,
        pageType,
        tagsViewName: `${pageTypeName}采购计划`
      }
    });
  };
  // 去立项
  const toProposal = (row) => {
    router.push({
      path: '/purchasing/projectProposal/detail/index',
      query: {
        planId: row?.id,
        planCode: row?.planCode,
        tagsViewName: '新增项目'
      }
    });
  };
  // 委托采购
  const handleDelegate = (row) => {
    if (!delegatePurchaseRef?.value) {
      ElMessage.error('委托采购组件未正确初始化');
      return;
    }
    delegatePurchaseRef.value.show(row);
  };
  // 作废
  const handleTerminate = (row, refreshTable) => {
    ElMessageBox.confirm('确定要作废该采购计划吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })
      .then(async () => {
        await terminatePlan({ bizType: 'SRM_PROCUREMENT_PLAN_CANCEL', bizKey: row.planCode, args: [row.planCode, row.planCode] });
        refreshTable?.();
        ElMessage({
          type: 'success',
          message: '作废成功！',
        });
      })
      .catch(() => {});
  };
  // 撤销审批
  const handleCancelApproval = (row, refreshTable) => {
    ElMessageBox.confirm('确定要撤销审批吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })
      .then(() => {
        cancelApprovePlan({bizType: row.status === 'EFFECT' ? 'SRM_PROCUREMENT_PLAN_CANCEL' : 'SRM_PROCUREMENT_PLAN', bizKey: row.planCode}).then(() => {
          refreshTable?.();
          ElMessage({
            type: 'success',
            message: '撤销审批成功！',
          });
        });
      })
      .catch(() => {});
  };
  // 删除
  const handleDel = (item, refreshTable) => {
    ElMessageBox.confirm('确定要删除采购计划', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })
      .then(() => {
        procurementPlanRemove([item.id]).then(() => {
          refreshTable?.();
          ElMessage({
            type: 'success',
            message: '删除成功！',
          });
        });
      })
      .catch(() => {
        ElMessage({
          type: 'info',
          message: '删除失败',
        });
      });
  };

  return {
    toDetail,
    toProposal,
    handleCancelApproval,
    handleDel,
    handleDelegate,
    handleTerminate,
  };
};