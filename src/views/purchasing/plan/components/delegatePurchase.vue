<template>
	<yun-dialog
		v-model="visible"
		width="540px"
		title="委托采购"
		:confirm-button-text="'确定'"
		:cancel-button-text="'取消'"
		:show-cancel-button="true"
		:confirm-button-handler="handleConfirm"
	>
		<h1>委托采购后，将自动全部转项目，由招投标公司维护项目信息，执行采购</h1>
		<yun-pro-form
			ref="formRef"
			v-model:form="form"
			:columns="basicColumns"
			:form-props="{ labelWidth: '120px', labelPosition: 'left' }"
		/>
	</yun-dialog>
</template>
<script setup lang="jsx">
import { ref, computed } from 'vue';
import { getAgentListApi, delegatePurchase } from '@/api/purchasing/plan';
import { ElMessage } from 'yun-design';

const visible = ref(false);
const formRef = ref();
const form = ref({
	baseAgentDeptId: '',
});
const agentOptions = ref([]);
const emit = defineEmits(['refreshTable']);

// 获取代理机构
const getAgentOptions = async () => {
	try {
		const { data } = await getAgentListApi();
		agentOptions.value = data.map((i) => {
			return {
				label: i.agentName,
				value: i.id,
			};
		});
	} catch (error) {
		console.error('代理机构获取失败:', error);
	}
};

// 基础表单配置
const basicColumns = computed(() => [
	{
		label: '招标代理机构',
		prop: 'baseAgentDeptId',
		type: 'select',
		rules: [{ required: true, message: '请选择', trigger: 'blur' }],
		enums: agentOptions.value,
		colProps: { span: 24 },
	},
]);

const handleConfirm = async () => {
	try {
		await delegatePurchase(form.value);
		ElMessage.success('委托采购成功');
		visible.value = false;
		emit('refreshTable');
	} catch (error) {
		console.error('委托采购失败:', error);
	}
};

const show = async (row) => {
	await getAgentOptions();
	form.value = { ...row };
	visible.value = true;
};

defineExpose({ show });
</script>

<style lang="scss" scoped>
h1 {
	margin-bottom: 20px;
}
</style>
