import { ref, computed, watch, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import dayjs from 'dayjs';
import { rule } from '/@/utils/validate';
import { deptTree } from '@/api/admin/dept';
import FileList from '@/views/purchasing/plan/detail/components/useFileList.jsx';
import { useDict } from '@/hooks/dict';
import { getBaseServiceType, getDynamicFields } from '@/api/purchasing/config';
import { getCityList, getPlanDetail, getUserListApi } from '@/api/purchasing/plan';
import { fixedFieldNames, fixedFieldNamesNoEdit } from '@/views/purchasing/constant';
import { useUserInfo } from '/@/stores/userInfo';
import { storeToRefs } from 'pinia';
import { fetchList as getBaseUsageLocation } from '@/api/lowcode/base-usage-location/index';
import { fetchList as getBaseQualityIndicator } from '@/api/lowcode/base-quality-indicator/index';
import { calculateFieldExpression } from '@/utils/safeCalculator';

// 用户信息
const stores = useUserInfo();
const { userInfos } = storeToRefs(stores);

// 字典数据
const getDict = computed(() => (str) => {
  if (!str) return [];
  const value = useDict(str)?.[str]?.value;
  return value;
});

export const useTable = (indicatorDetailRef, props) => {
  const route = useRoute();
  const router = useRouter();
  const selectedRow = ref([]);
  const pageType = computed(() => route?.query?.pageType || props?.pageType); // 区分新增、编辑或详情

  const loading = ref(false)

  const typeOptions = ref([])
  const userList = ref([])

  const usageLocations = ref([])

  // 初始化表单数据
  const form = ref({
    planName: '',
    serviceTypeId: '',
    serviceTypeName: '',
    applyDate: null,
    applyDeptId: '',
    applicant: '',
    applicantPhone: '',
    procurementDeptId: '',
    planRecruitMethod: '',
    periodStartDate: '',
    procurementMethod: null,
    fundSource: '',
    budgetAmount: '',
    applyReason: '',
    shippingAddress: '',
    supplierRequirements: '',
    remarks: '',
    attachment: [],
    list: [],
  });

  form.value.applyDeptId = userInfos.value.user.deptId;
  form.value.applicant = userInfos.value.user.userId;
  form.value.deptId = userInfos.value.user.deptId;
  form.value.applicantPhone = userInfos.value.user.phone;
  form.value.applyDate = dayjs().format('YYYY-MM-DD');

  // 部门相关
  const deptTreeRef = ref(null);
  const treeDeptData = ref([]);
  const filterText = ref('');
  const getFlatTree = (list, subKey = 'children') =>
    list?.reduce((result, item) => {
      result.push(item);
      if (item[subKey]) {
        result.push(...getFlatTree(item[subKey]));
      }
      return result;
    }, []);
  const flatDept = computed(() => getFlatTree(treeDeptData.value));
  const checkDeptId = (form, field, row) => {
    form[`${field}Id`] = row.id;
    form[`${field}Name`] = row.name;
    if (field === 'applyDept') {
      getUserList();
      form.applicant = '';
    }
  };
  const filterNode = (value, data) => {
    if (!value) return true;
    return data.name.indexOf(value) !== -1;
  };
  const getTree = () => {
    deptTree().then((response) => {
      treeDeptData.value = response.data;
    });
  };
  getTree();
  watch(
    () => filterText.value,
    (val) => {
      deptTreeRef?.value?.filter(val);
    },
  );

  // 基础信息
  const basicColumns = computed(() => {
    return [
      {
        prop: 'planName',
        label: '计划名称',
        type: pageType.value ? '' : 'input',
        rules: [
          { required: true, message: '计划名称不能为空', trigger: 'blur' },
          { max: 30, message: '长度在30个字符内' },
        ],
        colProps: { span: 12 },
      },
      {
        prop: 'serviceTypeId',
        label: '采购业务类型',
        type: pageType.value ? '' : 'select',
        enums: typeOptions.value,
        rules: [
          { required: true, message: '请选择采购业务类型', trigger: 'blur' },
        ],
        colProps: { span: 12 },
        attrs: {
          onChange: (val) => {
            form.value.planRecruitMethod && fetchDynamicFields(val, form.value.planRecruitMethod);
          },
        },
        show: () => !pageType.value
      },
      {
        prop: 'serviceTypeName',
        label: '采购业务类型',
        type: '',
        rules: [
          { required: true, message: '请选择采购业务类型', trigger: 'blur' },
        ],
        colProps: { span: 12 },
        show: () => pageType.value
      },
      {
        prop: 'applyDate',
        label: '申请日期',
        type: pageType.value ? '' : 'date-picker',
        attrs: {
          placeholder: '请选择预计申请日期',
          type: 'date',
          format: 'YYYY-MM-DD',
          valueFormat: 'YYYY-MM-DD',
          clearable: false,
          disabledDate: (time) => time.getTime() > new Date().getTime(),
        },
        rules: [
          { required: true, message: '请选择申请日期', trigger: 'blur' },
          {
            validator: (rule, value, callback) => {
              if (new Date(value).getTime() > new Date(form.value.expectedDeliveryDate).getTime()) {
                callback(new Error('申请日期不能大于期望到货日期'));
              } else {
                callback();
              }
            },
            trigger: 'change',
          },
        ],
        colProps: { span: 12 },
      },
      {
        prop: 'applyDeptId',
        label: '申请单位',
        rules: [
          { required: true, message: '申请单位不能为空', trigger: 'blur' },
        ],
        colProps: { span: 12 },
        render: (form) => {
          return pageType.value ? form.applyDeptName : (
            <el-select
              v-model={form.applyDeptId}
              style={{ width: '100%' }}
              placeholder={'请选择申请单位'}
            >
              <div>
                <el-input
                  v-model={filterText.value}
                  style={{
                    marginBottom: '10px',
                    marginLeft: '10px',
                    marginRight: '10px',
                    boxSizing: 'border-box',
                    width: 'calc(100% - 20px)',
                  }}
                />
                <el-tree
                  ref={deptTreeRef}
                  check-strictly
                  highlight-current
                  data={treeDeptData.value}
                  props={{ label: 'name', value: 'id' }}
                  filter-node-method={filterNode}
                  onNode-click={(...rest) => checkDeptId(form, 'applyDept', ...rest)}
                />
              </div>
              {(flatDept.value || []).map((item) => (
                <el-option class='hide' style={{ display: 'none' }} key={item.id} label={item.name} value={item.id}></el-option>
              ))}
            </el-select>
          );
        },
      },
      {
        prop: 'applicant',
        label: '申请人',
        type: pageType.value ? '' : 'select',
        rules: [
          { required: true, message: '申请人不能为空', trigger: 'blur' },
        ],
        enums: userList.value,
        colProps: { span: 12 },
        show: () => !pageType.value,
      },
      {
        prop: 'applicantName',
        label: '申请人',
        type: '',
        rules: [
          { required: true, message: '申请人不能为空', trigger: 'blur' },
        ],
        colProps: { span: 12 },
        show: () => pageType.value,
      },
      {
        prop: 'applicantPhone',
        label: '申请人电话',
        type: pageType.value ? '' : 'input',
        rules: [
          { required: true, message: '申请人电话不能为空', trigger: 'blur' },
          { validator: rule.validatePhone, trigger: 'blur' },
        ],
        colProps: { span: 12 },
      },
      {
        prop: 'procurementDeptId',
        label: '采购部门',
        rules: [
          { required: true, message: '采购部门不能为空', trigger: 'blur' },
        ],
        render: (form) => {
          return pageType.value ? form.procurementDeptName : (
            <el-select
              v-model={form.procurementDeptId}
              style={{ width: '100%' }}
              placeholder={'请选择采购部门'}
              clearable
            >
              <div>
                <el-input
                  v-model={filterText.value}
                  style={{
                    marginBottom: '10px',
                    marginLeft: '10px',
                    marginRight: '10px',
                    boxSizing: 'border-box',
                    width: 'calc(100% - 20px)',
                  }}
                />
                <el-tree
                  ref={deptTreeRef}
                  check-strictly
                  highlight-current
                  data={treeDeptData.value}
                  props={{ label: 'name', value: 'id' }}
                  filter-node-method={filterNode}
                  onNode-click={(...rest) => checkDeptId(form, 'procurementDept', ...rest)}
                />
              </div>
              {(flatDept.value || []).map((item) => (
                <el-option class='hide' style={{ display: 'none' }} key={item.id} label={item.name} value={item.id}></el-option>
              ))}
            </el-select>
          );
        },
        colProps: { span: 12 },
      },
      {
        prop: 'planRecruitMethod',
        label: '计划寻源方式',
        type: pageType.value ? '' : 'select',
        enums: getDict.value('plan_recruit_method'),
        colProps: { span: 12 },
        attrs: {
          clearable: true,
          onChange: (val) => {
            form.value.serviceTypeId && fetchDynamicFields(form.value.serviceTypeId, val);
          },
        },
        rules: [
          { required: true, message: '请选择计划寻源方式', trigger: 'blur' },
        ],
        show: () => !pageType.value
      },
      {
        prop: 'planRecruitMethod',
        label: '计划寻源方式',
        type: '',
        colProps: { span: 12 },
        show: () => pageType.value,
        render: (row) => {
          let planRecruitMethod = getDict.value('plan_recruit_method');
          let obj = planRecruitMethod.find(i => i.value === row.planRecruitMethod);
          return (<span>{obj?.label || '-'}</span>)
        },

        rules: [
          { required: true, message: '请选择计划寻源方式', trigger: 'blur' },
        ]
      },
      {
        prop: 'periodStartDate',
        label: '期望到货日期',
        type: pageType.value ? '' : 'date-picker',
        attrs: {
          placeholder: '请选择期望到货日期',
          type: 'date',
          format: 'YYYY-MM-DD',
          valueFormat: 'YYYY-MM-DD',
          clearable: true,
          disabledDate: (time) => time.getTime() < new Date(form.value.applyDate),
        },
        rules: [
          {
            validator: (rule, value, callback) => {
              if (new Date(value).getTime() < new Date(form.value.applyDate).getTime()) {
                callback(new Error('期望到货日期不能小于申请日期'));
              } else {
                callback();
              }
            },
            trigger: 'change',
          },
        ],
        colProps: { span: 12 },
      },
      {
        prop: 'procurementMethod',
        label: '采购方式',
        type: pageType.value ? '' : 'select',
        enums: getDict.value('procurement_method'),
        colProps: { span: 12 },
        rules: [
          { required: true, message: '请选择采购方式', trigger: 'change' },
        ],
        attrs: {
          clearable: true,
        },
        show: () => !pageType.value
      },
      {
        prop: 'procurementMethod',
        label: '采购方式',
        type: '',
        colProps: { span: 12 },
        rules: [
          { required: true, message: '请选择采购方式', trigger: 'change' },
        ],
        show: () => pageType.value,
        render: (row) => {
          let procurementMethod = getDict.value('procurement_method');
          let obj = procurementMethod.find(j => j.value === row.procurementMethod);
          return (<span>{obj?.label || '-'}</span>)
        }
      },
      {
        prop: 'fundSource',
        label: '资金来源',
        type: pageType.value ? '' : 'input',
        rules: [
          { max: 30, message: '长度在30个字符内' },
        ],
        colProps: { span: 12 },
        attrs: {
          clearable: true,
        }
      },
      {
        prop: 'budgetAmount',
        label: '预算金额（元）',
        type: pageType.value ? '' : 'input',
        rules: [
          {
            validator: (rule, value, callback) => {
              if (!value) {
                callback();
                return;
              }
              if (value.length > 30) {
                callback(new Error('数值总长度不能超过30位'));
                return;
              }
              if (!/^\d+(\.\d+)?$/.test(value)) {
                callback(new Error('请输入正确的数字格式'));
                return;
              }
              callback();
            },
            trigger: 'blur'
          }
        ],
        colProps: { span: 12 },
        attrs: {
          clearable: true,
        }
      },
      {
        prop: 'applyReason',
        label: '申请理由',
        type: pageType.value ? '' : 'input',
        rules: [
          { max: 100, message: '长度在100个字符内' },
        ],
        colProps: { span: 12 },
        attrs: {
          clearable: true,
        }
      },
      {
        prop: 'shippingAddress',
        label: '收货地址',
        type: pageType.value ? '' : 'input',
        rules: [
          { max: 100, message: '长度在100个字符内' },
        ],
        colProps: { span: 12 },
        attrs: {
          clearable: true,
        }
      },
      {
        prop: 'supplierRequirements',
        label: '供应商要求',
        type: pageType.value ? '' : 'input',
        rules: [
          { max: 100, message: '长度在100个字符内' },
        ],
        colProps: { span: 12 },
        attrs: {
          clearable: true,
        }
      },
    ];
  });

  // 备注
  const remarkColumns = [
    {
      prop: 'remarks',
      label: '备注',
      type: pageType.value ? '' : 'input',
      rules: [
        { max: 100, message: '长度在100个字符内' },
      ],
      colProps: { span: 24 },
    },
    {
      prop: 'attachment',
      label: '附件',
      colProps: { span: 24 },
      render: (row) => {
        return (
          <FileList
            modelValue={row.attachment}
            isView={!!pageType.value}
            onUpdate:modelValue={(fileList) => {
              row.attachment = fileList;
            }}
          />
        );
      }
    },
  ];

  // 省市区相关
  const cityList = ref([]);
  const getCityTree = () => {
    return getCityList().then((response) => {
      cityList.value = response.districts[0].districts;
    });
  };

  // 根据district查找完整的省市区信息
  const getAddressByDistrict = (districtCode) => {
    if (!districtCode) return '';

    for (const province of cityList.value) {
      if (province.districts) {
        for (const city of province.districts) {
          if (city.districts) {
            const districtItem = city.districts.find(item => item.adcode === districtCode);
            if (districtItem) {
              return `${province.name} / ${city.name} / ${districtItem.name}`;
            }
          }
        }
      }
    }
    return '';
  }

  // 修改需求牧场填充所在区域和详细地址
  const changeUsageLocation = (val, row) => {
    let current = usageLocations.value.find(i => i.value === val) || {};
    row['usageLocationRegion'] = getAddressByDistrict(current.district);
    row['usageLocationAddress'] = current.address;
  }

  // 动态字段配置
  const dynamicFields = ref([]);

  // 获取动态字段
  const fetchDynamicFields = async (serviceTypeId, buyWay) => {
    form.value.serviceTypeName = typeOptions.value.find(i => i.value === serviceTypeId)?.label || '';
    try {
      // 获取需求牧场下拉数据
      const usageLocationRes = await getBaseUsageLocation({statusList: 'ENABLED'}, {page: 1, size: 1000});
      usageLocations.value = usageLocationRes.data.records.map(i => {
        return {
          label: i.locationName,
          value: i.id,
          ...i,
        }
      })
      const { data } = await getDynamicFields(serviceTypeId, buyWay);
      dynamicFields.value = data.baseServiceTypeFieldList.map(i => {
        if (fixedFieldNames.includes(i.fieldName)) {
          i.fieldName === '需求牧场' ? i.enumValues = JSON.stringify(usageLocations.value) : null;
        }
        return i;
      });
      dynamicFields.value = dynamicFields.value.filter(i => i.applyToPlan === 'YES');
      form.value.config = dynamicFields.value;
      form.value.list = [];
      selectedRow.value = [];
      console.log(dynamicFields, 'dynamicFields')
    } catch (error) {
      console.error('获取动态字段失败:', error);
    }
  };

  // 获取采购业务类型
  const getTypeOptions = async () => {
    try {
      const { data } = await getBaseServiceType();
      typeOptions.value = data.map(i => {
        return {
          label: i.typeName,
          value: i.id
        }
      })
    } catch (error) {
      console.error('获取采购业务类型失败:', error);
    }
  }

  // 获取申请人列表
  const getUserList = async () => {
    try {
      const { data } = await getUserListApi(form.value.applyDeptId ? [form.value.applyDeptId] : []);
      userList.value = data.map(i => {
        return {
          label: i.username,
          value: i.userId
        }
      })
    } catch (error) {
      console.error('获取采购业务类型失败:', error);
    }
  }

  // 查看质量标准明细
  const handleViewIndicator = (row) => {
    indicatorDetailRef && indicatorDetailRef.value.show(row)
  }

  // 获取质量标准数据
  const getQualityIndicator = async (row) =>{
    // 获取质量标准-通过物料筛选
    const indicatorsRes = await getBaseQualityIndicator({statusList: ['ENABLED'], materialCode: row.materialCode}, {page: 1, size: 1000});
    row.indicators = (indicatorsRes.data.records || []).map(i => {
      return {
        label: i.indicatorName,
        value: i.id
      }
    });
  }

  // 根据动态字段生成表格列
  const generateDynamicColumns = (fields) => {
    return fields.map((field) => ({
      prop: field.fieldCode,
      label: field.rename || field.fieldName,
      width: ['质量标准', '所在区域'].includes(field.fieldName) ? 200 : 'auto',
      headerRender: field.planIsRequired === '1'
        ? ({ column }) => {
          return (
            <span>
              <i style="color: #FF3B30">*</i> {column.label}
            </span>
          );
        }
        : undefined,
    }));
  };

  // 根据动态字段生成表格内表单配置
  const generateDynamicFormConfig = (fields) => {
    const config = {};
    fields.forEach((field) => {
      const formItem = {
        prop: field.fieldCode,
        label: field.rename || field.fieldName,
        rules: field.planIsRequired === '1' ? [{ required: true, message: `${field.rename || field.fieldName}不能为空` }] : [],
        colProps: { span: 24 },
      };

      // 根据字段类型设置不同的表单项类型和校验规则
      switch (field.fieldType) {
        case 'TEXT':
          formItem.type = pageType.value ? '' : 'input';
          formItem.rules.push({
            max: Number(field.fieldLength),
            message: `${field.rename || field.fieldName}长度不能超过${field.fieldLength}个字符`,
          });
          formItem.attrs = {
            disabled: fixedFieldNamesNoEdit.includes(field.fieldName),
            placeholder: field.fieldDesc || '请输入',
          }
          break;
        case 'NUM':
          formItem.type = pageType.value ? '' : 'input';
          formItem.rules.push(
            {
              pattern: /^\d+(\.\d+)?$/,
              message: '请输入数字',
            },
            {
              validator: (rule, value, callback) => {
                if (value && value.toString().length > field.fieldLength) {
                  callback(new Error(`${field.rename || field.fieldName}总长度不能超过${field.fieldLength}个字符`));
                } else {
                  callback();
                }
              },
              trigger: 'blur'
            }
          );
          break;
        case 'NUM_CALC':
          formItem.type = pageType.value ? '' : 'input';
          formItem.render = (row) => {
            const calculatedValue = computed(() => calculate(field, row));
            return (
              <p>{pageType.value ? row[field.fieldCode] : calculatedValue.value}</p>
            )
          }
          break;
        case 'ENUM':
          formItem.type = pageType.value ? '' : 'select';
          formItem.enums = field.enumValues && JSON.parse(field.enumValues)?.map((item) => ({
            label: item.label,
            value: item.value,
          })) || [];
          if (pageType.value && formItem.label !== '质量标准') {
            formItem.render = (row) => {
              let options = field.enumValues && JSON.parse(field.enumValues);
              let name = options.find(i => i.value === row[field.fieldCode])?.label || '-';
              return (
                <p>{name}</p>
              )
            }
          }
          if (formItem.label === '需求牧场' && !pageType.value) {
            formItem.render = (row) => {
              return (
                <el-select
                  v-model={row[formItem.prop]}
                  onChange={value => changeUsageLocation(value, row, fields)}
                >
                  {field.enumValues && JSON.parse(field.enumValues).map(item => (
                    <el-option key={item.value} label={item.label} value={item.value} />
                  ))}
                </el-select>
              )
            }
          }
          if (formItem.label === '质量标准') {
            formItem.render = (row) => {
              return pageType.value ? (
                <span style='cursor: pointer;color: #0069FF' onClick={ () => handleViewIndicator(row) }>{row.qualityIndicatorName}</span>
              ) : (
                <div>
                  <el-select
                    v-model={row[formItem.prop]}
                    style='width:80%'
                    onClick={ () => getQualityIndicator(row) }
                  >
                    {(row.indicators || []).map(item => (
                      <el-option key={item.value} label={item.label} value={item.value} />
                    ))}
                  </el-select>
                  <el-button 
                    style='width:calc(20%-5px);margin: 0 0 5px 5px' 
                    type="text" 
                    onClick={ () => handleViewIndicator(row) }
                    disabled={!row.qualityIndicatorId}
                  >查看</el-button>
                </div>
                
              )
            }
          }
          break;
        case 'LINK_FORM': 
          formItem.type = pageType.value ? '' : 'select';
          formItem.enums = field.enumValues && JSON.parse(field.enumValues)?.map((item) => ({
            label: item.label,
            value: item.value,
          })) || [];
          if (pageType.value) {
            formItem.render = (row) => {
              let options = field.enumValues && JSON.parse(field.enumValues);
              let name = options.find(i => i.value === row[field.fieldCode])?.label || '-';
              return (
                <p>{name}</p>
              )
            }
          }
          break;
        case 'ATTACH':
          formItem.type = pageType.value ? '' : '';
          formItem.render = (row) => {
            return (
              <FileList
                modelValue={row[field.fieldCode]}
                isView={!!pageType.value}
                onUpdate:modelValue={(fileList) => {
                  row[field.fieldCode] = fileList;
                }}
              />
            );
          };
          break;
        default:
          formItem.type = '';
      }

      config[field.fieldCode] = [formItem];
    });
    return config;
  };

  // 合并固定字段和动态字段的表格列
  const tableColumns = computed(() => {
    const dynamicCols = generateDynamicColumns(dynamicFields.value);
    let columns = [
      {
        type: 'selection',
        width: 52,
        fixed: 'left',
      },
      ...dynamicCols,
    ]
    !pageType.value ? columns.push({
      prop: 'action',
      label: '操作',
      width: 80,
      fixed: 'right'
    }) : null
    return columns;
  });

  // 合并固定字段和动态字段的表单配置
  const mergedTableFormConfig = computed(() => {
    const dynamicConfig = generateDynamicFormConfig(dynamicFields.value);
    console.log(dynamicConfig, 'dynamicConfigdynamicConfigdynamicConfig')
    return {
      ...dynamicConfig,
    };
  });

  // 根据合并后的表单配置生成formFields
  const formFields = computed(() => {
    const config = mergedTableFormConfig.value;
    return Object.entries(config).map(([key, value]) => ({
      key,
      label: value[0]?.label || key,
    }));
  });

  // 获取详情
  const getDetail = async () => {
    try {
      await getCityTree();
      const { data } = await getPlanDetail(route?.query?.planCode);
      // 获取需求牧场下拉数据
      const usageLocationRes = await getBaseUsageLocation({statusList: 'ENABLED'}, {page: 1, size: 1000});
      usageLocations.value = usageLocationRes.data.records.map(i => {
        return {
          label: i.locationName,
          value: i.id,
          ...i,
        }
      })
      const { baseServiceTypeEntity, srmProcurementPlanDetailList, attachment, ...rest } = data;
      const { baseServiceTypeFieldList } = baseServiceTypeEntity;
      const config = Array.isArray(baseServiceTypeFieldList) && baseServiceTypeFieldList.length && baseServiceTypeFieldList.map(i => {
        if (fixedFieldNames.includes(i.fieldName)) {
          i.fieldName === '需求牧场' ? i.enumValues = JSON.stringify(usageLocations.value) : null;
        }
        return i;
      });
      form.value = { ...rest };
      form.value.attachment = attachment ? JSON.parse(attachment || []) : [];
      form.value.list = srmProcurementPlanDetailList.map(i => {
        let { srmProcurementPlanFieldValueList, ...rest } = i;
        let dynamicFieldsValues = {};
        srmProcurementPlanFieldValueList?.forEach(j => {
          if (j.fieldType === 'ATTACH') {
            dynamicFieldsValues[j.fieldCode] = JSON.parse(j.fieldValue || []);
          } else {
            dynamicFieldsValues[j.fieldCode] = j.fieldValue;
          }
        })
        let regions = rest.usageLocationRegion?.split('/') || [];
        let district = regions.length > 1 ? regions[regions.length -1] : '';
        if(/^\d+$/.test(regions[regions.length -1])) {
          rest.usageLocationRegion = getAddressByDistrict(district);
        } else {
          rest.usageLocationRegion = rest.usageLocationRegion?.slice(10, rest.usageLocationRegion.length) || '-';
        }
        rest.indicators = [{label: rest.qualityIndicatorName, value: rest.qualityIndicatorId}];
        return {
          ...dynamicFieldsValues,
          ...rest,
        }
      });
      dynamicFields.value = config.filter(i => i.applyToPlan === 'YES');
      form.value.config = dynamicFields.value;
      console.log(form.value.list, 'form.value.listform.value.list')
    } catch (error) {
      console.error('获取详情失败:', error);
    }
  }

  /**
   * 解析计算规则并计算结果
   * @param {object} field - 字段配置对象
   * @param {object} row - 当前行数据
   * @returns {number|string} - 计算结果
   */
  const calculate = ({ defaultValue, fieldCode }, row) => {
    if (pageType.value) return null;
    // 检查row是否为空对象或无效值
    if (!defaultValue || !row || Object.keys(row).length === 0) {
      row[fieldCode] = '';
      return '';
    }

    try {
      // 使用安全的字段表达式计算器
      const result = calculateFieldExpression(defaultValue, row, {
        decimals: 2,
        defaultValue: 0,
        debug: false, // 可以根据需要开启调试
        returnEmptyOnError: true
      });

      // 更新row对象中的值
      row[fieldCode] = result;
      return result;

    } catch (error) {
      console.error('计算规则解析错误:', error, '表达式:', defaultValue, '字段:', fieldCode);
      row[fieldCode] = '';
      return '';
    }
  };

  // 返回采购计划页
  const goBack = () => {
    router.push('/purchasing/plan/index');
  };

  // 在组件挂载时获取动态字段
  onMounted(() => {
    getUserList();
    getTypeOptions();
    if (route?.query?.planCode) {
      getDetail();
    } else {
      getCityTree();
    }
  });

  return {
    form,
    pageType,
    selectedRow,
    basicColumns,
    remarkColumns,
    loading,
    tableColumns,
    mergedTableFormConfig,
    formFields,
    dynamicFields,
    goBack,
  }
}
