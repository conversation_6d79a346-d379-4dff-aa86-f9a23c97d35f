import { ElMessage } from 'yun-design';
import ExcelJS from 'exceljs';

export function useExcel() {
  async function generateExcelTemplate(fields) {
    try {
      const workbook = new ExcelJS.Workbook();
      const worksheet = workbook.addWorksheet('导入模板');

      // 生成表头（带红色*）
      const headerRow = worksheet.getRow(1);
      fields.forEach((field, idx) => {
        if (field.planIsRequired === '1') {
          headerRow.getCell(idx + 1).value = {
            richText: [
              { text: '*', font: { color: { argb: 'FFFF0000' }, bold: true } },
              { text: field.fieldName, font: { color: { argb: 'FF000000' } } },
            ],
          };
        } else {
          headerRow.getCell(idx + 1).value = field.fieldName;
        }
      });
      headerRow.commit();

      // 设置列宽
      worksheet.columns = fields.map(() => ({ width: 20 }));

      // 枚举下拉数据处理
      fields.forEach((field, colIdx) => {
        if (field.fieldType === 'ENUM') {
          let enumOptions = [];
          try {
            enumOptions = JSON.parse(field.enumValues || '[]');
          } catch (e) {
            enumOptions = [];
          }
          if (Array.isArray(enumOptions) && enumOptions.length > 0) {
            const labels = enumOptions.map((opt) => (opt.label || '').replace(/,/g, '，'));
            const dropList = labels.join(',');
            const colLetter = getExcelColLetter(colIdx);

            if (dropList.length <= 255) {
              // 普通方式
              worksheet.dataValidations.add(`${colLetter}2:${colLetter}1000`, {
                type: 'list',
                allowBlank: true,
                formulae: [`"${dropList}"`],
                showErrorMessage: true,
                errorTitle: '输入错误',
                error: '请从下拉列表中选择',
                showInputMessage: true,
                promptTitle: '选择提示',
                prompt: '请从下拉列表中选择',
              });
            } else {
              // 超长，写入隐藏sheet
              let hiddenSheet = workbook.getWorksheet('下拉选项');
              if (!hiddenSheet) {
                hiddenSheet = workbook.addWorksheet('下拉选项', { state: 'veryHidden' });
              }
              // 每个ENUM字段一列，防止冲突
              const startRow = 1;
              const startCol = colIdx + 1;
              labels.forEach((label, i) => {
                hiddenSheet.getCell(i + startRow, startCol).value = label;
              });
              const endRow = labels.length;
              const hiddenColLetter = getExcelColLetter(colIdx);
              worksheet.dataValidations.add(`${colLetter}2:${colLetter}1000`, {
                type: 'list',
                allowBlank: true,
                formulae: [`=下拉选项!$${hiddenColLetter}$${startRow}:$${hiddenColLetter}$${endRow}`],
                showErrorMessage: true,
                errorTitle: '输入错误',
                error: '请从下拉列表中选择',
                showInputMessage: true,
                promptTitle: '选择提示',
                prompt: '请从下拉列表中选择',
              });
            }
          }
        }
      });
      // 导出
      const buffer = await workbook.xlsx.writeBuffer();
      return new Blob([buffer], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
    } catch (error) {
      console.error('生成Excel模板失败:', error);
      ElMessage.error('生成Excel模板失败');
      return null;
    }
  }
  // 工具函数：支持A~ZZ等多列
  function getExcelColLetter(colIdx) {
    let s = '';
    colIdx++;
    while (colIdx > 0) {
      let m = (colIdx - 1) % 26;
      s = String.fromCharCode(65 + m) + s;
      colIdx = Math.floor((colIdx - m) / 26);
    }
    return s;
  }

  async function downloadExcelTemplate(fields) {
    const blob = await generateExcelTemplate(fields);
    if (!blob) return;
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = '物料导入模板.xlsx';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    window.URL.revokeObjectURL(url);
  }

  // 导入Excel文件
  async function importExcelFile(file, fields) {
    try {
      const workbook = new ExcelJS.Workbook();
      await workbook.xlsx.load(await file.arrayBuffer());
      const worksheet = workbook.getWorksheet(1);

      if (!worksheet) {
        throw new Error('未找到工作表');
      }

      // 获取表头
      const headerRow = worksheet.getRow(1);
      const headers = headerRow.values.slice(1); // 去掉第一个空值

      // 验证表头是否匹配
      const fieldNames = fields.map((field) => field.fieldName);
      const isValidHeader = headers.every((header, index) => {
        // 处理带红色*的表头
        const headerText = typeof header === 'object' ? header.richText[1].text : header;
        return headerText === fieldNames[index];
      });

      if (!isValidHeader) {
        throw new Error('导入的Excel文件格式不正确，请使用正确的模板');
      }

      // 读取数据行
      const rows = [];
      worksheet.eachRow((row, rowNumber) => {
        if (rowNumber > 1) {
          // 跳过表头
          const rowData = {};
          row.eachCell((cell, colNumber) => {
            const field = fields[colNumber - 1];
            if (field) {
              // fieldCode 作为键
              const key = field.fieldCode;
              if (field.fieldType === 'ENUM' && field.enumValues) {
                const enumValues = typeof field.enumValues === 'string' ? JSON.parse(field.enumValues) : field.enumValues;
                rowData[key] = enumValues.find((i) => i.label === cell.value)?.value || cell.value;
              } else {
                rowData[key] = cell.value;
              }
            }
          });
          if (Object.keys(rowData).length > 0) {
            rows.push(rowData);
          }
        }
      });

      return rows;
    } catch (error) {
      console.error('导入Excel文件失败:', error);
      throw error;
    }
  }

  // 导出数据到Excel
  async function exportToExcel(data, fields) {
    try {
      const workbook = new ExcelJS.Workbook();
      const worksheet = workbook.addWorksheet('数据导出');

      // 生成表头
      const headerRow = worksheet.getRow(1);
      fields.forEach((field, idx) => {
        headerRow.getCell(idx + 1).value = field.fieldName;
      });

      // 设置表头样式
      headerRow.font = { bold: true };
      headerRow.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: 'FFE0E0E0' },
      };

      // 添加数据行
      data.forEach((row, rowIndex) => {
        const dataRow = worksheet.getRow(rowIndex + 2);
        fields.forEach((field, colIndex) => {
          const key = field.fieldCode;
          let cellValue = row[key];

          // 处理枚举值
          if (field.fieldType === 'ENUM' && field.enumValues) {
            try {
              const enumValues = typeof field.enumValues === 'string' ? JSON.parse(field.enumValues) : field.enumValues;
              cellValue = enumValues.find((item) => item.value === cellValue)?.label || cellValue;
              if (field.fieldName === '质量标准') {
                dataRow.getCell(colIndex + 1).value = row.qualityIndicatorName;
              } else {
                dataRow.getCell(colIndex + 1).value = cellValue;
              }
              return;
            } catch (e) {
              console.error('解析枚举值失败:', e);
            }
          }

          // 处理附件
          if (field.fieldType === 'ATTACH' && cellValue) {
            try {
              // 解析附件JSON字符串
              const attachments = typeof cellValue === 'string' ? JSON.parse(cellValue) : cellValue;

              if (Array.isArray(attachments) && attachments.length > 0) {
                const cell = dataRow.getCell(colIndex + 1);

                // 设置第一个附件
                const firstAttachment = attachments[0];
                cell.value = {
                  text: firstAttachment.name || '查看附件',
                  hyperlink: import.meta.env.VITE_ADMIN_PROXY_PATH + '/api' + firstAttachment.url,
                };

                // 如果有多个附件，添加注释
                if (attachments.length > 1) {
                  const noteTexts = [
                    { text: '其他附件：\n' },
                    ...attachments.slice(1).map((attachment) => ({
                      text: `${attachment.name || '查看附件'}\n`,
                      hyperlink: import.meta.env.VITE_ADMIN_PROXY_PATH + '/api' + attachment.url,
                    })),
                  ];

                  cell.note = {
                    texts: noteTexts,
                  };
                }

                cell.font = {
                  color: { argb: 'FF0000FF' },
                  underline: true,
                };
              } else {
                dataRow.getCell(colIndex + 1).value = cellValue;
              }
            } catch (e) {
              console.error('解析附件数据失败:', e);
              dataRow.getCell(colIndex + 1).value = cellValue;
            }
            return;
          } else {
            dataRow.getCell(colIndex + 1).value = cellValue;
          }
        });
      });

      // 设置列宽
      worksheet.columns = fields.map(() => ({ width: 20 }));

      // 添加边框
      worksheet.eachRow((row, rowNumber) => {
        row.eachCell((cell) => {
          cell.border = {
            top: { style: 'thin' },
            left: { style: 'thin' },
            bottom: { style: 'thin' },
            right: { style: 'thin' },
          };
        });
      });

      // 导出
      const buffer = await workbook.xlsx.writeBuffer();
      return new Blob([buffer], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
    } catch (error) {
      console.error('导出Excel失败:', error);
      throw error;
    }
  }

  // 导出多sheet数据到Excel
  async function exportMultiSheetToExcel(sheets) {
    try {
      const workbook = new ExcelJS.Workbook();
      for (const sheet of sheets) {
        const worksheet = workbook.addWorksheet(sheet.sheetName || 'Sheet');
        // 生成表头
        const headerRow = worksheet.getRow(1);
        sheet.fields.forEach((field, idx) => {
          headerRow.getCell(idx + 1).value = field.fieldName;
        });
        headerRow.font = { bold: true };
        headerRow.fill = {
          type: 'pattern',
          pattern: 'solid',
          fgColor: { argb: 'FFE0E0E0' },
        };
        // 添加数据行
        sheet.data.forEach((row, rowIndex) => {
          const dataRow = worksheet.getRow(rowIndex + 2);
          sheet.fields.forEach((field, colIndex) => {
            const key = field.fieldCode;
            let cellValue = row[key];
            // 处理枚举值
            if (field.fieldType === 'ENUM' && field.enumValues) {
              try {
                const enumValues = typeof field.enumValues === 'string' ? JSON.parse(field.enumValues) : field.enumValues;
                cellValue = enumValues.find((item) => item.value === cellValue)?.label || cellValue;
                if (field.fieldName === '质量标准') {
                  dataRow.getCell(colIndex + 1).value = row.qualityIndicatorName;
                } else {
                  dataRow.getCell(colIndex + 1).value = cellValue;
                }
                return;
              } catch (e) {
                dataRow.getCell(colIndex + 1).value = cellValue;
              }
            }
            // 处理附件
            else if (field.fieldType === 'ATTACH' && cellValue) {
              try {
                const attachments = typeof cellValue === 'string' ? JSON.parse(cellValue) : cellValue;
                if (Array.isArray(attachments) && attachments.length > 0) {
                  const cell = dataRow.getCell(colIndex + 1);
                  const firstAttachment = attachments[0];
                  cell.value = {
                    text: firstAttachment.name || '查看附件',
                    hyperlink: import.meta.env.VITE_ADMIN_PROXY_PATH + '/api' + firstAttachment.url,
                  };
                  if (attachments.length > 1) {
                    const noteTexts = [
                      { text: '其他附件：\n' },
                      ...attachments.slice(1).map((attachment) => ({
                        text: `${attachment.name || '查看附件'}\n`,
                        hyperlink: import.meta.env.VITE_ADMIN_PROXY_PATH + '/api' + attachment.url,
                      })),
                    ];
                    cell.note = { texts: noteTexts };
                  }
                  cell.font = {
                    color: { argb: 'FF0000FF' },
                    underline: true,
                  };
                } else {
                  dataRow.getCell(colIndex + 1).value = cellValue;
                }
              } catch (e) {
                dataRow.getCell(colIndex + 1).value = cellValue;
              }
              return;
            } else {
              dataRow.getCell(colIndex + 1).value = cellValue;
            }
          });
        });
        worksheet.columns = sheet.fields.map(() => ({ width: 20 }));
        worksheet.eachRow((row) => {
          row.eachCell((cell) => {
            cell.border = {
              top: { style: 'thin' },
              left: { style: 'thin' },
              bottom: { style: 'thin' },
              right: { style: 'thin' },
            };
          });
        });
      }
      const buffer = await workbook.xlsx.writeBuffer();
      return new Blob([buffer], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
    } catch (error) {
      console.error('多sheet导出Excel失败:', error);
      throw error;
    }
  }

  return {
    generateExcelTemplate,
    downloadExcelTemplate,
    importExcelFile,
    exportToExcel,
    exportMultiSheetToExcel,
  };
}
