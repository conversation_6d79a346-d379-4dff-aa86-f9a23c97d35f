<template>
	<div class="plan-detail-container">
		<el-collapse
			v-model="activeCollapse.basic"
			class="section-collapse first-collapse"
			expand-icon-position="left"
		>
			<el-collapse-item
				title="基础信息"
				name="basic"
			>
				<template #title>
					<span class="collapse-title">
						<el-icon class="collapse-icon"><CaretRight /></el-icon>
						基础信息
					</span>
				</template>
				<div class="form-content">
					<yun-pro-form
						ref="formRef"
						v-model:form="form"
						:columns="basicColumns"
						:form-props="{ labelWidth: '110px', labelPosition: 'right' }"
					/>
				</div>
			</el-collapse-item>
		</el-collapse>
		<el-collapse
			v-model="activeCollapse.demand"
			class="section-collapse"
			expand-icon-position="left"
		>
			<el-collapse-item
				title="采购物资需求"
				name="demand"
			>
				<template #title>
					<span class="collapse-title">
						<el-icon class="collapse-icon"><CaretRight /></el-icon>
						采购物资需求
					</span>
					<div class="toolbar">
						<el-button
							@click.stop="chooseMaterial"
							:disabled="pageType || !dynamicFields.length"
							>选择物料</el-button
						>
						<el-button
							@click.stop="batchImport"
							:disabled="pageType || !dynamicFields.length"
							>批量导入</el-button
						>
						<el-button
							@click.stop="handleExport"
							:disabled="!dynamicFields.length || !!props.mode"
							>导出</el-button
						>
						<el-button
							@click.stop="batchDelete(null)"
							:disabled="!selectedRow.length || pageType"
							>批量删除</el-button
						>
					</div>
				</template>
				<div class="table-content">
					<yun-pro-table
						v-model:selected="selectedRow"
						:table-columns="tableColumns"
						:table-data="form.list"
						:selection-key="'id'"
						:auto-height="true"
						:table-props="{
							stripe: false,
							showTableSetting: true,
						}"
					>
						<template
							v-for="field in formFields"
							:key="field.key"
							#[`t_${field.key}`]="{ row }"
						>
							<yun-pro-form
								:ref="
									(el) => {
										if (el && form.list) {
											const rowIndex = form.list.findIndex((item) => item === row);
											if (rowIndex !== -1) {
												formRefs[`${rowIndex}_${field.key}Ref`] = el;
											}
										}
									}
								"
								:form="row"
								:columns="mergedTableFormConfig[field.key]"
							/>
						</template>
						<template #t_action="scope">
							<el-button
								@click="batchDelete(scope.row, scope.$index)"
								type="text"
								class="delete-btn"
							>
								删除
							</el-button>
						</template>
					</yun-pro-table>
				</div>
			</el-collapse-item>
		</el-collapse>
		<el-collapse
			v-model="activeCollapse.remark"
			class="section-collapse"
			expand-icon-position="left"
		>
			<el-collapse-item
				title="备注"
				name="remark"
			>
				<template #title>
					<span class="collapse-title">
						<el-icon class="collapse-icon"><CaretRight /></el-icon>
						备注
					</span>
				</template>
				<div class="form-content">
					<yun-pro-form
						ref="formRef"
						v-model:form="form"
						:columns="remarkColumns"
						:form-props="{ labelWidth: '110px', labelPosition: 'right' }"
					/>
				</div>
			</el-collapse-item>
		</el-collapse>
		<ChooseMaterial
			ref="chooseMaterialRef"
			@confirm="handleChooseMaterialConfirm"
		/>
		<yun-import
			v-model="importVisible"
			dialog-tips="温馨提示:单次最多只能导入1000条数据"
			upload-tips="只能上传.xls,.xlsx文件"
			:download-function="handleDownTemplate"
			:upload-function="handleImport"
			:template-urls="templateUrls"
		/>
		<IndicatorDetail ref="indicatorDetailRef" />
		<div class="plan-detail-footer" v-if="!props.mode">
			<el-button
				@click="handleSubmit"
				type="primary"
				:loading="loading"
				v-if="!pageType"
				>提交审核</el-button
			>
			<el-button @click="handleCancel">{{ pageType ? '返回' : '取消' }}</el-button>
		</div>
	</div>
</template>

<script setup lang="jsx">
import ChooseMaterial from './components/chooseMaterial.vue';
import { YunDialog } from '@ylz-material/dialog';
import { useTable } from './hooks/index.jsx';
import { useExcel } from './hooks/useExcel.jsx';
import { ElMessage } from 'yun-design';
import { procurementPlanAdd } from '@/api/purchasing/plan';
import { cloneDeep } from 'lodash';
import IndicatorDetail from './components/indicatorDetail.vue';

const props = defineProps({
  mode: {
    type: String,
    default: '',
  },
  pageType: {
    type: String,
    default: '',
  }
});

const activeCollapse = ref({
	basic: ['basic'],
	demand: ['demand'],
	remark: ['remark'],
});
const indicatorDetailRef = ref();

const { form, pageType, selectedRow, basicColumns, remarkColumns, loading, tableColumns, mergedTableFormConfig, formFields, dynamicFields, goBack } =
	useTable(indicatorDetailRef, props);

const { downloadExcelTemplate, importExcelFile, exportToExcel } = useExcel();

const formRef = ref();
const chooseMaterialRef = ref();
const importVisible = ref(false);
const formRefs = reactive({});
const templateUrls = ref([
	{
		label: '模板下载',
		value: '',
	},
]);

// 选择物料
const chooseMaterial = () => {
	chooseMaterialRef.value.show();
};

// 处理选择物料确认
const handleChooseMaterialConfirm = (data) => {
	let existingItems = [];
	existingItems = form.value.list.filter((i) => data.map(j => j.materialCode)?.includes(i.materialCode));
	data.forEach(async (item) => {
		const existingItem = form.value.list.find((i) => i.materialCode === item.materialCode);
		if (!existingItem) {
			let data = {};
			formFields.value.forEach((i) => {
				if (i.key === 'specModel') {
					data[i.key] = item.spec || '';
				} else {
					data[i.key] = item[i.key] || '';
				}
			});
			form.value.list.push(data);
		}
	});
	existingItems.length && ElMessage.warning(`物料 ${existingItems.map(i => i.materialName)?.join(',')} 已存在`);
};

// 批量导入
const batchImport = () => {
	importVisible.value = true;
};

// 下载导入模板
const handleDownTemplate = async () => {
	downloadExcelTemplate(dynamicFields.value);
};

// 处理导入
const handleImport = async (files) => {
	if (files.length === 0) {
		ElMessage({ type: 'warning', message: '请先上传文件' });
		return;
	}
	try {
		const rows = await importExcelFile(files[0].raw, dynamicFields.value);
		form.value.list = [...form.value.list, ...rows];
		ElMessage.success('导入成功');
		importVisible.value = false;
	} catch (error) {
		ElMessage.error(error.message || '导入失败');
	}
};

// 处理导出
const handleExport = async () => {
	if (!form.value.list || form.value.list.length === 0) {
		ElMessage.warning('没有可导出的数据');
		return;
	}
	try {
		const blob = await exportToExcel(form.value.list, dynamicFields.value);
		const url = window.URL.createObjectURL(blob);
		const link = document.createElement('a');
		link.href = url;
		link.download = `${form.value.serviceTypeName}-采购计划清单.xlsx`;
		document.body.appendChild(link);
		link.click();
		document.body.removeChild(link);
		window.URL.revokeObjectURL(url);
	} catch (error) {
		ElMessage.error('导出失败');
	}
};

// 批量删除
const batchDelete = (row, index) => {
	YunDialog.confirm('确定删除所选物料?', '提示', {
		type: 'info',
		confirmButtonText: '确定',
		cancelButtonText: '取消',
		confirmButtonHandler: async (done) => {
			if (row) {
				form.value.list.splice(index, 1);
			} else {
				form.value.list = form.value.list.filter((item) => !selectedRow.value.some((selected) => selected.materialCode === item.materialCode));
			}
			ElMessage.success('删除成功');
			done();
		},
	});
};

// 提交
const handleSubmit = async () => {
	try {
		const validatePromises = [
			// 基础表单校验
			formRef?.value?.elForm?.validate().catch(() => {
				throw new Error('基础信息填写有误');
			}),
			// 表格内表单校验
			...form.value.list.map((item, index) => {
				return Promise.all(
					formFields.value?.map((field) => {
						const refKey = `${index}_${field.key}Ref`;
						const formRef = formRefs[refKey];
						console.log('获取表单引用:', refKey, formRef);

						if (!formRef) {
							throw new Error(`第${index + 1}行${field.label}表单引用不存在`);
						}

						return formRef.elForm?.validate().catch(() => {
							throw new Error(`第${index + 1}行${field.label}填写有误`);
						});
					})
				);
			}),
		];

		await Promise.all(validatePromises);
		await handleConfirm();
	} catch (error) {
		ElMessage.error(error.message || '表单校验失败，请检查填写内容');
		return;
	} finally {
		loading.value = false;
	}
};

const handleConfirm = async () => {
	let { list, config, buyWay, attachment, ...rest } = form.value;
	const transformedList = list.map((item) => {
		const newItem = {
			data: config?.map((configItem) => {
				let fieldValue = '';
				if (configItem.fieldType === 'ATTACH') {
					fieldValue = JSON.stringify(item[configItem.fieldCode] || '');
				} else {
					fieldValue = item[configItem.fieldCode] || '';
				}
				return {
					code: configItem.fieldCode,
					value: fieldValue,
				};
			}),
		};
		return newItem;
	});
	const configArray = Object.values(cloneDeep(config));
	let params = {
		...rest,
		buyWay: buyWay ? buyWay : 'NULL',
		config: configArray,
		attachment: JSON.stringify(attachment || ''),
		srmProcurementPlanFieldValueList: transformedList,
	};
	loading.value = true;
	try {
		await procurementPlanAdd(params);
		ElMessage.success(`${params.id ? '更新' : '新增'}采购计划成功`);
		window.history.back();
	} catch (error) {
		loading.value = false;
		console.error('采购计划新增失败:', error);
	}
	// console.log('转换后的数据：', JSON.stringify(params, null, 2));
};

// 取消
const handleCancel = () => {
	if (!pageType.value) {
		YunDialog.confirm('确定取消当前操作?', '提示', {
			type: 'warning',
			confirmButtonText: '确定',
			cancelButtonText: '取消',
			confirmButtonHandler: (done) => {
				done();
				goBack();
			},
		});
	} else {
		goBack();
	}
};

const { watchFieldChanges } = useTable();

// 在表格数据变化时设置监听器
watch(
	() => form.value.list,
	(newData) => {
		if (newData && newData.length > 0) {
			newData.forEach((row) => {
				// 遍历所有字段配置
				formFields.value.forEach((field) => {
					if (field.defaultValue && field.defaultValue.includes('@')) {
						// 如果字段有计算规则，设置监听器
						watchFieldChanges(row, field.defaultValue, field.fieldCode);
					}
				});
			});
		}
	},
	{ immediate: true, deep: true }
);
</script>

<style lang="scss" scoped>
@import './style/collapse-panel.scss';
.plan-detail-container {
	padding: 20px;
	.toolbar {
		margin-left: 24px;
	}
	.table-content {
		:deep(.el-card__body) {
			padding: 0 !important;
		}
	}
	.form-content {
		margin-top: 12px;
	}
	.table-content {
		height: 220px;
		.pro_table {
			:deep(th) {
				background-color: #f0f2f5 !important;
			}
			:deep(tr) {
				&:hover {
					background-color: #fff !important;
					td {
						background-color: #fff !important;
					}
				}
				td {
					border-bottom: 1px solid #ebeef5;
					padding: 6px 0;
				}
			}
			.delete-btn {
				color: #ff3b30;
			}
			.yun-pro-form {
				background: transparent;
				padding: 0;
				margin-bottom: 0px;
				border-radius: 0px;
			}
		}
		:deep(.el-form-item__label) {
			display: none !important;
		}
		:deep(.yun-pro-form-item__render) {
			> span {
				width: 100%;
				display: inline-block;
				white-space: nowrap;
				overflow: hidden;
				text-overflow: ellipsis;
			}
		}
	}
	.plan-detail-footer {
		padding: 20px;
		background: #fff;
		border-top: 1px solid #e4e7ed;
		margin: 0 0 50px 0;
	}
}
</style>
