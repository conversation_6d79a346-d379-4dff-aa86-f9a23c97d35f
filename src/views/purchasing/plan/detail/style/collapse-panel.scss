// 折叠面板公共样式
// Collapse Panel Common Styles

.plan-detail-container {
	:deep(.section-collapse) {
		border: none !important;
		border-radius: 0 !important;
		.el-collapse-item {
			padding: 20px;
			background: #fff;
		}

		.el-collapse-item__header {
			align-items: baseline;
			background-color: #fff !important;
			height: auto !important;
			padding: 0 !important;
			line-height: 32px !important;
			color: var(--Color-Text-text-color-primary, #1d2129);
			font-family: 'PingFang SC';
			font-size: 16px;
			font-style: normal;
			font-weight: 600;
			line-height: 24px;
			border-bottom: none !important;
		}

		// 隐藏默认的箭头图标
		.el-collapse-item__arrow {
			display: none;
		}

		.el-collapse-item__wrap {
			border-bottom: none !important;
		}
	}
	:deep(.first-collapse) {
		.el-collapse-item__wrap {
			border-bottom: 1px solid #ebeef5 !important;
		}
	}

	:deep(.section-collapse.team-section) {
		.el-collapse-item__wrap {
			border: none !important;
		}
	}

	:deep(.el-collapse-item__content) {
		padding: 0 !important;
	}

	// 自定义标题样式
	.collapse-title {
		width: auto;
		display: flex;
		align-items: center;
		gap: 8px;
		padding: 0 !important;
		min-height: auto !important;

		.collapse-icon {
			transition: transform 0.3s ease;
			color: #1d2129;
			font-size: 12px;
		}
	}

	// 当折叠面板展开时，旋转图标
	:deep(.el-collapse-item.is-active) {
		.collapse-title .collapse-icon {
			color: #1d2129;
			transform: rotate(90deg);
		}
	}
}
