<template>
	<yun-dialog
		v-model="visible"
		size="large"
		title="查看指标详情"
		:confirm-button-text="'确定'"
		:cancel-button-text="'取消'"
    :show-cancel-button="false"
    :show-confirm-button="false"
	>
    <Detail :groups="processedGroups" :data="form"/>
  </yun-dialog>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import Detail from '@/components/Detail/index.vue';
import { getObj, getAttribute } from '@/api/lowcode/base-quality-indicator/index';

interface FormData {
  [key: string]: any;
}

// 静态生成的分组配置
const processedGroups = [
  {
    title: "基础信息",
    columns: [
      {
        prop: 'materialName',
        label: '选择物料',
        type: 'text',
        span: 1.5,
      },
      {
        prop: 'indicatorName',
        label: '指标名称',
        type: 'text',
        span: 1.5,
      },
    ]
  },
  {
    title: '质量指标',
    columns: [
      {
        prop: 'baseQualityIndicatorAttributeValue',
        label: '',
        type: 'table',
        attrs: {
          showIndex: true,
          columns: [
            {
              prop: 'attributeName',
              label: '质量属性',
              type: 'text',
            },
            {
              prop: 'valueContent',
              label: '属性值内容',
              type: 'text'
            },
            {
              prop: 'rejectionStandard',
              label: '拒收标准',
              type: 'text'
            },
            {
              prop: 'penaltyStandard',
              label: '扣款标准',
              type: 'text'
            }
          ]
        }
      }
    ]
  }
];

const visible = ref(false);
const form = ref<FormData>({});

async function show(row: FormData) {
  visible.value = true;
  if(row.qualityIndicatorId) {
    const res = await getObj(row.qualityIndicatorId);
    const { data } = await getAttribute({});
    res.data.baseQualityIndicatorAttributeValueList.forEach(i => {
      i.attributeName = data.find(j => j.id === i.attributeId)?.attributeName;
    })
    form.value = res.data;
    form.value.materialName = row.materialName;
  }
}

defineExpose({
  show,
});
</script>