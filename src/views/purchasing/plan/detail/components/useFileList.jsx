import { computed, watch } from 'vue';
import Upload from '@/components/Upload/index.vue';

export default {
    name: 'FileList',
    components: {
        Upload
    },
    props: {
        modelValue: {
            type: [String, Array],
            default: () => []
        },
        isView: {
            type: <PERSON>olean,
            default: false
        },
        // 文件类型限制
        accept: {
            type: String,
            default: '.jpg,.jpeg,.png,.pdf,.xls,.xlsx,.doc,.docx'
        },
        // 文件个数限制
        limit: {
            type: Number,
            default: 5
        },
        // 上传提示文本
        tips: {
            type: String,
            default: '目前支持图片、pdf、excel、word文件,最多上传5个附件'
        }
    },
    emits: ['update:modelValue'],
    setup(props, { emit }) {
        const fileList = computed(() => {
            let list = [];
            try {
                const fileData = props.modelValue;
                // 如果是数组，直接使用
                if (Array.isArray(fileData)) {
                    return fileData;
                }
                // 如果是字符串，解析成数组
                if (fileData && fileData !== '""' && fileData !== '"' && fileData.trim()) {
                    list = fileData.split(',')
                        .filter(url => url && url.trim())
                        .map(url => url.trim());
                }
            } catch (error) {
                console.error('文件数据解析错误:', error);
                list = [];
            }
            return list;
        });

        // 监听 modelValue 的变化
        watch(() => props.modelValue, (newVal) => {
            if (newVal) {
                //console.log(newVal, 'newValnewValnewValnewVal');
            }
        }, { immediate: true });

        const handleUpdate = (_, fileList) => {
            emit('update:modelValue', fileList);
        };

        return {
            fileList,
            handleUpdate
        };
    },
    render() {
        if (this.isView) {
            if (!this.fileList.length) {
                return <span class="text-gray-400">-</span>;
            }

            return (
                <div class="flex flex-col gap-1">
                    {this.fileList.map((file, index) => (
                        <a
                            key={index}
                            href={import.meta.env.VITE_ADMIN_PROXY_PATH + '/api' + file.url}
                            target="_blank"
                            class="text-primary hover:text-primary-focus truncate mt-[8px]"
                            title={file.name}
                        >
                            {file.name}
                        </a>
                    ))}
                </div>
            );
        }

        return (
            <Upload
                class="value"
                is-classify
                modelValue={this.modelValue}
                tips={this.tips}
                accept={this.accept}
                limit={this.limit}
                onUpdate:modelValue={this.handleUpdate}
                type="simple"
            />
        );
    }
}; 