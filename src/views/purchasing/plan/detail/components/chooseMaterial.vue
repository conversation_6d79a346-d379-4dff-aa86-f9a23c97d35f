<template>
	<yun-dialog
		v-model="visible"
		size="large"
		title="选择物资"
		:confirm-button-text="'确定'"
		:cancel-button-text="'取消'"
		:show-cancel-button="true"
		:confirm-button-handler="handleConfirm"
	>
		<div class="choose-material">
      <div class="category-tree">
				<el-tree
					ref="treeRef"
					:data="categoryTree"
					:props="{
						label: 'categoryName',
						children: 'children',
					}"
					node-key="id"
					highlight-current
					@node-click="handleNodeClick"
				/>
			</div>
			<div
				class="table-content"
				v-if="show"
			>
				<yun-pro-table
					ref="tableRef"
					v-model:pagination="pagination"
					v-model:searchData="searchForm"
					v-model:selected="selectData"
					auto-height
					:table-props="{
						rowKey: 'id',
						showTableSetting: true,
					}"
					:search-fields="searchFields"
					:table-columns="tableColumns"
					:remote-method="remoteMethod"
					:layout="'whole'"
					@reset="initTable"
					:global-selection="true"
					:selection-key="'id'"
					:search-props="{
						showCollapseBtn: false,
					}"
				/>
			</div>
		</div>
	</yun-dialog>
</template>

<script setup lang="jsx">
import { ref, onMounted } from 'vue';
import { ElMessage } from 'yun-design';
import { getMaterialCategory, getMaterialPage } from '@/api/purchasing/plan';

const emit = defineEmits(['confirm']);

const visible = ref(false);
const treeRef = ref();
const tableRef = ref();
const categoryTree = ref([]);
const selectData = ref([]);

// 获取物料分类树
const getCategoryTree = async () => {
	try {
		const { data } = await getMaterialCategory('/api/material/categories');
		categoryTree.value = data;
	} catch (error) {
		ElMessage.error('获取物料分类失败');
	}
};

// 处理树节点点击
const handleNodeClick = (data) => {
	pagination.value.page = 1;
	// 更新搜索表单中的分类ID
	searchForm.value.materialCategoryId = data.id;
	// 重新加载表格数据
	tableRef.value?.getData();
};

// 表格相关
const pagination = ref({
	page: 1,
	size: 10,
	total: 0,
});

const searchForm = ref({
	materialCategoryId: '',
	materialName: '',
	materialCode: '',
});

const searchFields = [
	{
		label: '物料编码',
		prop: 'materialCode',
		type: 'input',
		placeholder: '请输入物料编码',
	},
	{
		label: '物料名称',
		prop: 'materialName',
		type: 'input',
		placeholder: '请输入物料名称',
	},
];

const tableColumns = [
	{
		type: 'selection',
		width: 50,
	},
	{
		label: '物料编码',
		prop: 'materialCode',
		width: 120,
	},
	{
		label: '物料名称',
		prop: 'materialName',
		minWidth: 120,
	},
	{
		label: '规格型号',
		prop: 'spec',
		width: 120,
	},
	{
		label: '单位',
		prop: 'unit',
		width: 80,
	},
];

const remoteMethod = async ({ searchData, pagination }) => {
	const { data } = await getMaterialPage(
		{
			...searchData,
		},
		{ current: pagination?.page, size: pagination?.size }
	);
	return data;
};

const handleConfirm = () => {
	if (!selectData.value || selectData.value.length === 0) {
		ElMessage.warning('请选择物料');
		return;
	}
	visible.value = false;
	emit('confirm', selectData.value);
};

const show = () => {
	visible.value = true;
	tableRef?.value?.tableRef?.clearSelection();
	selectData.value = [];
};

const initTable = () => {
	pagination.value.page = 1;
	searchForm.value.materialCategoryId = '';
	treeRef.value?.setCurrentKey(null);
	tableRef?.value?.getData();
};

onMounted(() => {
	getCategoryTree();
});

defineExpose({
	show,
});
</script>

<style lang="scss" scoped>
.choose-material {
	display: flex;
	gap: 20px;
	.category-tree {
		width: 200px;
		border-right: 1px solid #e4e7ed;
		padding-right: 20px;
		overflow-y: auto;

		:deep(.el-tree) {
			.el-tree-node__content {
				height: 32px;
			}
		}
	}

	.table-content {
		height: 500px;
		flex: 1;
		:deep(.yun-pro-table) {
			height: 100%;
			display: flex;
			flex-direction: column;

			.el-table {
				flex: 1;
				overflow: hidden;
			}
		}
	}
}
</style>
