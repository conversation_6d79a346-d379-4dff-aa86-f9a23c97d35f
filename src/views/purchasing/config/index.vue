<template>
	<div class="config-container">
		<yun-pro-table
			ref="tableRef"
			v-model:pagination="state.page"
			v-model:searchData="state.searchData"
			:search-fields="searchFields"
			:layout="'whole'"
			:auto-height="true"
			:table-columns="tableColumns"
			:remote-method="remoteMethod"
			:table-props="{ showTableSetting: true }"
		>
			<template #tableHeaderLeft>
				<el-button
					type="primary"
					@click="toAdd(null)"
				>
					新建业务类型
				</el-button>
			</template>
			<template #t_status="scope">
				<span>{{ scope.row.status === 'ENABLED' ? '启用' : '禁用' }}</span>
			</template>
			<template #t_action="scope">
				<el-button
					type="text"
					@click="toAdd(scope.row, 'view')"
				>
					查看
				</el-button>
				<el-button
					type="text"
					@click="toAdd(scope.row)"
				>
					编辑
				</el-button>
				<el-button
					type="text"
					@click="toConfig(scope.row)"
				>
					配置动态表单
				</el-button>
				<el-button
					type="text"
					@click="handleModifyStatus(scope.row)"
				>
					{{ scope.row.status === 'DISABLED' ? '启用' : '禁用' }}
				</el-button>
				<el-button
					type="text"
					@click="handleDelete(scope.row)"
				>
					删除
				</el-button>
			</template>
		</yun-pro-table>
		<EditType
			ref="editTypeRef"
			@confirm="tableRef.getData()"
		/>
		<Detail ref="detailRef" />
	</div>
</template>

<script setup name="purchasingConfig" lang="jsx">
import EditType from './components/editType.vue';
import Detail from './detail/index.vue';
import useOptions from './hooks/useOption';
import useHandler from './hooks/useHandler';
import { baseServiceTypeListApi } from '@/api/purchasing/config.ts';

const tableRef = ref();
const editTypeRef = ref();
const detailRef = ref();

const state = reactive({
	page: {
		total: 0, // 总页数
		page: 1, // 当前页数
		size: 20, // 每页显示多少条
	},
	searchData: {
		status: 'ENABLED',
	},
	loading: false,
});

const { searchFields, tableColumns } = useOptions();
const { toConfig, toAdd, handleModifyStatus, handleDelete } = useHandler({
	tableRef,
	editTypeRef,
	detailRef,
	state,
});

const remoteMethod = async ({ searchData, pagination }) => {
	const { data } = await baseServiceTypeListApi(
		{
			...searchData,
		},
		{ current: pagination.page, size: pagination.size }
	);
	return data;
};
</script>

<style lang="scss" scoped>
.config-container {
	width: 100%;
	height: calc(100vh - 88px);
}
</style>
