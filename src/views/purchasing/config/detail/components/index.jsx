import { ref, computed, watch } from 'vue';
import { useDict } from '@/hooks/dict';
import EnumTable from './EnumTable.vue';
import CalcRuleInput from './CalcRuleInput.vue';

// 获取字典数据
const dictData = useDict('dynamic_form_type');
const formTypeOptions = computed(() => {
  return dictData.dynamic_form_type?.value || [];
});

// 数值限制选项
const valueLimitOptions = [
  { label: '10字符', value: '10' },
  { label: '20字符', value: '20' },
  { label: '30字符', value: '30' },
  { label: '50字符', value: '50' },
  { label: '100字符', value: '100' },
  { label: '200字符', value: '200' },
  { label: '无限制', value: '' },
];

// 表单配置
export const useFormConfig = () => {
  const form = ref({
    fieldName: '',
    fieldType: 'TEXT',
    fieldLength: null,
    fieldDesc: '',
    defaultValue: '',
    enumValues: [{}], // 用于存储枚举值列表
    link: '',
    showFieldSelect: false,
    fieldSearch: '',
    filteredFields: [],
  });
  form.enumValues = form.value.fieldType === 'ENUM' ? [{}] : [];

  const basicColumns = computed(() => {
    return [
      {
        label: '字段名称',
        prop: 'fieldName',
        type: 'input',
        rules: [{ required: true, message: '请输入字段名称', trigger: 'blur' }],
        colProps: { span: 24 },
        rules: [{ max: 20, message: '长度在20个字符内' }],
        attrs: {
          disabled: !!form.value.disabled
        }
      },
      {
        label: '字段类型',
        prop: 'fieldType',
        type: 'select',
        rules: [{ required: true, message: '请选择字段类型', trigger: 'change' }],
        enums: formTypeOptions.value,
        colProps: { span: 24 },
      },
      {
        label: '数值限制',
        prop: 'fieldLength',
        type: 'select',
        rules: [{ required: true, message: '请选择数值限制', trigger: 'change' }],
        enums: valueLimitOptions,
        colProps: { span: 24 },
        show: (form) => {
          return ['TEXT', 'NUM'].includes(form.fieldType);
        },
      },
      {
        label: '计算规则',
        prop: 'defaultValue',
        type: 'input',
        rules: [{ required: true, message: '请输入计算规则', trigger: 'blur' }],
        colProps: { span: 24 },
        show: (form) => {
          return form.fieldType === 'NUM_CALC';
        },
        render: (form) => {
          return (
            <CalcRuleInput
              v-model={form.defaultValue}
              fieldList={form.fieldList || []}
            />
          );
        }
      },
      {
        label: '输入链接表单',
        prop: 'backupValue',
        type: 'input',
        rules: [{ required: true, message: '请输入链接表单', trigger: 'blur' }],
        colProps: { span: 24 },
        show: (form) => {
          return ['LINK_FORM'].includes(form.fieldType);
        },
        attrs: { placeholder: '请输入链接表单的URL或标识' },
      },
      {
        label: '枚举值',
        prop: 'enumValues',
        type: 'custom',
        colProps: { span: 24 },
        show: (form) => {
          return ['ENUM'].includes(form.fieldType);
        },
        render: (form) => {
          return (
            <EnumTable
              v-model={form.enumValues}
              onAdd-row={() => {
                form.enumValues.push({ label: '', value: '' });
              }}
            />
          );
        }
      },
      {
        label: '字段说明',
        prop: 'fieldDesc',
        type: 'input',
        colProps: { span: 24 },
        attrs: { type: 'textarea' },
        rules: [{ max: 50, message: '长度在50个字符内' }],
      }
    ]
  });

  // 监听字典数据变化
  watch(
    () => dictData.dynamic_form_type?.value,
    (newVal) => {
      if (newVal) {
        basicColumns.value[1].enums = newVal;
      }
    },
    { immediate: true }
  );

  return {
    form,
    basicColumns,
  };
};