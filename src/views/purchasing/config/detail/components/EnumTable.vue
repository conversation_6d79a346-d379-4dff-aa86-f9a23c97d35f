<template>
	<div class="enum-table">
		<el-table
			:data="modelValue"
			border
			size="small"
		>
			<el-table-column
				type="index"
				label="序号"
				width="50"
				align="center"
			/>
			<el-table-column
				label="枚举名称"
				prop="label"
				min-width="80"
			>
				<template #default="{ row }">
					<el-input
						v-model="row.label"
						placeholder="请输入枚举名称"
						size="small"
						maxlength="20"
					/>
				</template>
			</el-table-column>
			<el-table-column
				label="枚举值"
				prop="value"
				min-width="80"
			>
				<template #default="{ row }">
					<el-input
						v-model="row.value"
						placeholder="请输入枚举值"
						size="small"
						maxlength="20"
					/>
				</template>
			</el-table-column>
			<el-table-column
				label="操作"
				width="120"
				align="center"
			>
				<template #default="{ $index }">
					<div class="flex justify-center gap-2">
						<el-button
							type="text"
							@click="handleAdd"
							>+</el-button
						>
						<el-button
							type="text delete"
							@click="handleDelete($index)"
							v-if="$index > 0"
							>-</el-button
						>
					</div>
				</template>
			</el-table-column>
		</el-table>
	</div>
</template>

<script setup lang="ts">
interface Props {
	modelValue: Array<{ label: string; value: string }>;
}

interface Emits {
	(e: 'update:modelValue', value: Array<{ label: string; value: string }>): void;
	(e: 'add-row'): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

const handleAdd = () => {
	emit('add-row');
};

const handleDelete = (index: number) => {
	const newValue = [...props.modelValue];
	newValue.splice(index, 1);
	emit('update:modelValue', newValue);
};
</script>

<style lang="scss" scoped>
.enum-table {
	:deep(.el-input__wrapper) {
		box-shadow: none;
	}
	:deep(.el-button) {
		font-size: 20px;
		font-weight: bold;
	}
	:deep(.el-button.delete) {
		font-size: 22px;
		color: #ff3b30;
	}
}
</style>
