<template>
  <yun-dialog
    v-model="visible"
    width="800px"
    :title="`${title}字段`"
    :confirm-button-text="'确定'"
    :cancel-button-text="'取消'"
    :show-cancel-button="true"
    :confirm-button-handler="handleConfirm"
  >
    <yun-pro-form
      ref="formRef"
      v-model:form="form"
      :columns="basicColumns"
      :form-props="{ labelWidth: '120px', labelPosition: 'left' }"
    />
  </yun-dialog>
</template>
<script setup lang="jsx">
import { ref } from 'vue';
import { useFormConfig } from './index';

const emit = defineEmits(['add-row']);
const visible = ref(false);
const formRef = ref();
const title = ref('新建');
const { form, basicColumns } = useFormConfig();

const handleConfirm = async () => {
  let result = await formRef.value?.elForm?.validate();
  if (result) {
    emit('add-row', form);
  }
};

const show = (row, fieldList, show = true) => {
  if(!show) {
    visible.value = show;
    return;
  }
  if (row) {
    form.value = { ...row };
    form.value.disabled = row.fieldName === '单价';
    title.value = '编辑';
  } else {
    form.value = {
      fieldName: '',
      fieldType: 'TEXT',
      fieldLength: null,
      fieldDesc: '',
      defaultValue: '',
      enumValues: [{}], // 用于存储枚举值列表
      link: '',
      showFieldSelect: false,
      fieldSearch: '',
      filteredFields: [],
    };
    title.value = '新建';
  }
  form.value.fieldList = fieldList;
  formRef.value?.elForm?.resetFields();
  visible.value = show;
};

defineExpose({ show });
</script>
