<template>
	<div class="calc-rule-input">
		<el-input
			v-model="inputValue"
			:placeholder="placeholder"
			@keydown="handleKeydown"
			@focus="handleFocus"
			@blur="handleBlur"
		>
			<template #suffix>
				<el-dropdown
					trigger="click"
					@command="insertOperator"
				>
					<el-button
						type="primary"
						link
					>
						运算符
						<el-icon class="el-icon--right">
							<arrow-down />
						</el-icon>
					</el-button>
					<template #dropdown>
						<el-dropdown-menu>
							<el-dropdown-item command="+">加号 (+)</el-dropdown-item>
							<el-dropdown-item command="-">减号 (-)</el-dropdown-item>
							<el-dropdown-item command="*">乘号 (×)</el-dropdown-item>
							<el-dropdown-item command="/">除号 (÷)</el-dropdown-item>
							<el-dropdown-item command="(">左括号 (</el-dropdown-item>
							<el-dropdown-item command=")">右括号 )</el-dropdown-item>
						</el-dropdown-menu>
					</template>
				</el-dropdown>
			</template>
		</el-input>
		<el-popover
			placement="bottom-start"
			trigger="manual"
			v-model:visible="showFieldSelect"
			width="300"
			:offset="0"
			popper-class="field-select-popover"
		>
			<template #reference>
				<div class="field-select-trigger"></div>
			</template>
			<div class="field-select">
				<el-input
					v-model="fieldSearch"
					placeholder="搜索字段"
					clearable
					@input="handleFieldSearch"
				/>
				<el-scrollbar height="200px">
					<div class="field-list">
						<div
							v-for="field in filteredFields"
							:key="field.fieldCode"
							class="field-item"
							@click="handleFieldSelect(field)"
						>
							{{ field.fieldCode }} - {{ field.fieldName }}
						</div>
					</div>
				</el-scrollbar>
			</div>
		</el-popover>
	</div>
</template>

<script setup lang="jsx">
import { ref, computed, watch, nextTick } from 'vue';
import { ArrowDown } from '@element-plus/icons-vue';

const props = defineProps({
	modelValue: {
		type: [String, Number],
		default: '',
	},
	fieldList: {
		type: Array,
		default: () => [],
	},
	placeholder: {
		type: String,
		default: '请输入计算规则，输入@可选择已有字段',
	},
});

const emit = defineEmits(['update:modelValue']);

const showFieldSelect = ref(false);
const fieldSearch = ref('');
const cursorPosition = ref(0);
const inputValue = ref(props.modelValue);
const isOperatorSelected = ref(false);

// 监听modelValue的变化
watch(
	() => props.modelValue,
	(newVal) => {
		inputValue.value = newVal;
	}
);

// 监听inputValue的变化
watch(inputValue, (newVal) => {
	emit('update:modelValue', newVal);
	// 检查是否包含@符号，并且不是在选择运算符后
	if (newVal.includes('@') && !isOperatorSelected.value) {
		showFieldSelect.value = true;
	} else {
		showFieldSelect.value = false;
	}
});

// 监听 fieldList 的变化
watch(() => props.fieldList, (newVal) => {
	console.log('CalcRuleInput接收到的fieldList:', newVal);
}, { immediate: true });

const filteredFields = computed(() => {
	console.log('计算filteredFields时的fieldList:', props.fieldList);
	if (!fieldSearch.value) {
		return props.fieldList;
	}
	return props.fieldList.filter(
		(field) =>
			field.fieldCode.toLowerCase().includes(fieldSearch.value.toLowerCase()) ||
			field.fieldName.toLowerCase().includes(fieldSearch.value.toLowerCase())
	);
});

const handleFocus = (e) => {
	// 保存当前光标位置
	cursorPosition.value = e.target.selectionStart || 0;
};

const handleBlur = () => {
	// 延迟关闭下拉框，以便能够点击字段
	setTimeout(() => {
		showFieldSelect.value = false;
		fieldSearch.value = '';
	}, 200);
};

const handleKeydown = (e) => {
	if (e.key === '@') {
		e.preventDefault();
		const input = e.target;
		cursorPosition.value = input.selectionStart || 0;
		const currentValue = inputValue.value || '';
		const newValue = currentValue.slice(0, cursorPosition.value) + '@' + currentValue.slice(cursorPosition.value);
		inputValue.value = newValue;
		setTimeout(() => {
			input.setSelectionRange(cursorPosition.value + 1, cursorPosition.value + 1);
		}, 0);
		// 强制显示下拉框
		showFieldSelect.value = true;
		isOperatorSelected.value = false;
	}
};

const handleFieldSearch = () => {
	// 搜索时保持下拉框显示
	showFieldSelect.value = true;
};

const handleFieldSelect = (field) => {
	const currentValue = inputValue.value || '';
	const input = document.querySelector('.calc-rule-input input');
	if (!input) return;

	// 获取当前光标位置
	const cursorPos = input.selectionStart || 0;

	// 找到最后一个@的位置
	const lastAtIndex = currentValue.lastIndexOf('@');
	if (lastAtIndex !== -1) {
		// 在最后一个@的位置插入字段，并删除多余的@
		const newValue = currentValue.slice(0, lastAtIndex) + `@${field.fieldCode}` + currentValue.slice(cursorPos);
		inputValue.value = newValue;

		// 设置光标位置到插入的字段后面
		setTimeout(() => {
			input.setSelectionRange(lastAtIndex + field.fieldCode.length + 1, lastAtIndex + field.fieldCode.length + 1);
		}, 0);
	}

	// 关闭下拉框并清空搜索
	showFieldSelect.value = false;
	fieldSearch.value = '';
};

const insertOperator = (operator) => {
	const currentValue = inputValue.value || '';
	const input = document.querySelector('.calc-rule-input input');
	if (!input) return;

	const cursorPos = input.selectionStart || 0;
	const newValue = currentValue.slice(0, cursorPos) + operator + currentValue.slice(cursorPos);
	inputValue.value = newValue;

	// 设置光标位置到运算符后面
	setTimeout(() => {
		input.setSelectionRange(cursorPos + 1, cursorPos + 1);
	}, 0);

	// 标记为选择了运算符
	isOperatorSelected.value = true;
	// 确保在选择运算符后不显示字段选择弹窗
	showFieldSelect.value = false;
};
</script>

<style lang="scss" scoped>
.calc-rule-input {
	position: relative;
	width: 100%;

	:deep(.el-input__wrapper) {
		padding-right: 80px;
	}
}

.field-select {
	.field-list {
		margin-top: 8px;
	}

	.field-item {
		padding: 8px 12px;
		cursor: pointer;
		transition: all 0.3s;
		border-radius: 4px;

		&:hover {
			background-color: var(--el-color-primary-light-9);
		}
	}
}

:deep(.field-select-popover) {
	padding: 0;
	margin-top: 4px;
}
</style>
