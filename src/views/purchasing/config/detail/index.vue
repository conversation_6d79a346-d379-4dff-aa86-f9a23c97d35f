<template>
  <yun-drawer
    v-model="visible"
    destroy-on-close
    modal
    custom-class="form-drawer"
    close-on-click-modal
    :confirm-button-disabled="loading"
    title="配置动态表单"
    size="90%"
    @close="visible = false"
    @confirm="handleConfirm"
  >
    <div class="config-detail">
      <h1>基础信息</h1>
      <yun-pro-form
        ref="formRef"
        v-model:form="form"
        :columns="basicColumns"
        :form-props="{ labelWidth: '110px', labelPosition: 'right' }"
      />
      <h1>子表单配置</h1>
      <div class="table-content">
        <yun-pro-table
          :table-columns="tableColumns"
          :table-data="form.baseServiceTypeFieldList"
          :selection-key="'id'"
          :auto-height="true"
          :table-props="{
            stripe: false,
				    showTableSetting: true,
          }"
          @selectedChange="(val) => (selectedRows = val)"
        >
          <template #tableHeaderLeft></template>
          <template #tableHeaderRight>
            <el-button
              type="primary"
              @click="handleBatchDelete()"
              :disabled="!selectedRows.length"
            >
              批量删除
            </el-button>
            <el-button
              type="primary"
              @click="handleEditField(null, -1)"
            >
              新建字段
            </el-button>
          </template>
          <template #t_rename="scope">
            <el-input
              v-model="scope.row.rename"
              :maxlength="30"
              placeholder="请输入名称"
              size="small"
              style="margin-top: 5px"
              :disabled="fixedFieldNames.includes(scope.row.fieldName)"
            />
          </template>
          <template #t_plan="scope">
            <el-select
              v-model="scope.row.planIsRequired"
              size="small"
              style="width: 70px; margin: 5px 5px 0 0"
              :disabled="(fixedFieldNames.filter(i => !fixedFieldNamesCanEdit.includes(i)).includes(scope.row.fieldName)) || quotationSpecific.includes(scope.row.fieldName)"
            >
              <el-option
                v-for="item in requiredOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
            <el-switch
              v-model="scope.row.applyToPlanEdit"
              active-text="可编辑"
              inactive-text="不可编辑"
              active-value="YES"
              inactive-value="NO"
              :disabled="(fixedFieldNames.filter(i => !fixedFieldNamesCanEdit.includes(i)).includes(scope.row.fieldName)) || quotationSpecific.includes(scope.row.fieldName)"
            />
            <el-switch
              v-model="scope.row.applyToPlan"
              active-text="显示"
              inactive-text="隐藏"
              class="custom-switch"
              active-value="YES"
              inactive-value="NO"
              :disabled="(fixedFieldNames.filter(i => !fixedFieldNamesCanEdit.includes(i)).includes(scope.row.fieldName)) || quotationSpecific.includes(scope.row.fieldName)"
            />
          </template>
          <template #t_proposal="scope">
            <el-select
              v-model="scope.row.projectIsRequired"
              size="small"
              style="width: 70px; margin-right: 5px"
              :disabled="(fixedFieldNames.filter(i => !fixedFieldNamesCanEdit.includes(i)).includes(scope.row.fieldName)) || quotationSpecific.includes(scope.row.fieldName)"
            >
              <el-option
                v-for="item in requiredOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
            <el-switch
              v-model="scope.row.applyToProjectEdit"
              active-text="可编辑"
              inactive-text="不可编辑"
              :disabled="(fixedFieldNames.filter(i => !fixedFieldNamesCanEdit.includes(i)).includes(scope.row.fieldName)) || quotationSpecific.includes(scope.row.fieldName)"
              active-value="YES"
              inactive-value="NO"
            />
            <el-switch
              v-model="scope.row.applyToProject"
              active-text="显示"
              inactive-text="隐藏"
              class="custom-switch"
              :disabled="(fixedFieldNames.filter(i => !fixedFieldNamesCanEdit.includes(i)).includes(scope.row.fieldName)) || quotationSpecific.includes(scope.row.fieldName)"
              active-value="YES"
              inactive-value="NO"
            />
          </template>
          <template #t_quotation="scope">
            <el-select
              v-model="scope.row.quoteIsRequired"
              size="small"
              style="width: 70px; margin-right: 5px"
              :disabled="(fixedFieldNames.filter(i => !fixedFieldNamesCanEdit.includes(i)).includes(scope.row.fieldName))"
            >
              <el-option
                v-for="item in requiredOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
            <el-switch
              v-model="scope.row.applyToQuoteEdit"
              active-text="可编辑"
              inactive-text="不可编辑"
              :disabled="(fixedFieldNames.filter(i => !fixedFieldNamesCanEdit.includes(i)).includes(scope.row.fieldName))"
              active-value="YES"
              inactive-value="NO"
            />
            <el-switch
              v-model="scope.row.applyToQuote"
              active-text="显示"
              inactive-text="隐藏"
              class="custom-switch"
              :disabled="(fixedFieldNames.filter(i => !fixedFieldNamesCanEdit.includes(i)).includes(scope.row.fieldName))"
              active-value="YES"
              inactive-value="NO"
            />
            <el-switch
              v-model="scope.row.payIsManyType"
              active-text="多支付方式报价"
              inactive-text="多支付方式报价"
              class="custom-switch1"
              :disabled="fixedFieldNames.includes(scope.row.fieldName)"
              active-value="1"
              inactive-value="0"
            />
          </template>
          <template #t_action="scope">
            <el-button
              @click="handleMove(scope.row, scope.$index, 'up')"
              type="text"
              v-if="scope.$index > 0"
            >
              上移
            </el-button>
            <el-button
              @click="handleMove(scope.row, scope.$index, 'down')"
              type="text"
              v-if="scope.$index < form.baseServiceTypeFieldList.length - 1"
            >
              下移
            </el-button>
            <el-button
              @click="handleEditField(scope.row, scope.$index)"
              type="text"
              :disabled="fixedFieldNames.includes(scope.row.fieldName) && scope.row.fieldName !== '单价'"
            >
              编辑
            </el-button>
          </template>
        </yun-pro-table>
      </div>
      <EditField
        ref="editFieldRef"
        :onAdd-row="(form) => handleField(form)"
      />
    </div>
  </yun-drawer>
</template>

<script setup lang="jsx">
import { ElMessage } from 'yun-design';
import EditField from './components/editField.vue';
import { useTable } from './hooks/index.jsx';
import { saveFieldConfigApi, getConfigDetail, getFieldIfUsed } from '@/api/purchasing/config';
import { useUserInfo } from '/@/stores/userInfo';
import { fixedFieldNames, fixedFieldNamesCanEdit, quotationSpecific } from '@/views/purchasing/constant';
import { cloneDeep } from 'lodash';

const { form, basicColumns, tableColumns, requiredOptions, fixedFields } = useTable();
const editFieldRef = ref();
const formRef = ref();
const currentIndex = ref(-1);
const stores = useUserInfo();
const { userInfos } = storeToRefs(stores);
const selectedRows = ref([]);

const handleField = (row) => {
  if (currentIndex.value !== -1) {
    // 编辑时保留原有行的所有字段，只更新row中提供的字段
    form.value.baseServiceTypeFieldList[currentIndex.value] = {
      ...form.value.baseServiceTypeFieldList[currentIndex.value],
      ...row.value,
    };
  } else {
    if(form.value.baseServiceTypeFieldList.some(i => i.fieldName === row.value.fieldName)) {
      ElMessage.warning('字段名称不能重复');
      return;
    }
    form.value.baseServiceTypeFieldList.push({
      ...row.value,
      planIsRequired: '0',
      projectIsRequired: '0',
      quoteIsRequired: '0',
      applyToPlanEdit: 'YES',
      applyToProjectEdit: 'YES',
      applyToQuoteEdit: 'YES',
      applyToPlan: 'YES',
      applyToProject: 'YES',
      applyToQuote: 'YES',
      mockId: `mock_${Date.now()}_${Math.random().toString(36).substring(2, 8)}`,
    });
  }
  editFieldRef.value.show({}, [], false);
};
const visible = ref(false);
const loading = ref(false);

const handleBatchDelete = async () => {
  if (!selectedRows.value?.length) return;
  const ids = selectedRows.value.map((i) => i.id).filter(Boolean);
  const mockIds = selectedRows.value.map((i) => i.mockId).filter(Boolean);
  let usedIds = [];
  // 检查字段是否被引用
  if (ids.length) {
    try {
      const { data } = await getFieldIfUsed(ids.join(','));
      usedIds = data || [];
    } catch (error) {
      console.error('获取字段被引用情况失败:', error);
      return;
    }
  }
  form.value.baseServiceTypeFieldList = form.value.baseServiceTypeFieldList.filter((item) => {
    // 如果是已使用的字段，保留
    if (usedIds.includes(item.id)) {
      return true;
    }
    // 如果是未使用的字段或新建未保存字段，删除
    return !ids.includes(item.id) && !mockIds.includes(item.mockId);
  });
  const usedIdNames = form.value.baseServiceTypeFieldList.filter((i) => usedIds.includes(i.id)).map((i) => i.fieldName);
  const hasDeletedFields = ids.length > usedIds.length || mockIds.length > 0;
  const message = usedIds.length ? `${usedIdNames.join(',')}已被引用，不允许删除${hasDeletedFields ? '，其余字段删除成功' : ''}` : '字段删除成功';
  ElMessage[usedIds.length ? 'warning' : 'success'](message);
};
const handleMove = (row, index, direction) => {
  if (!form.value?.baseServiceTypeFieldList) {
    return;
  }

  const list = form.value.baseServiceTypeFieldList;
  if (direction === 'up' && index > 0) {
    const temp = list[index - 1];
    list[index - 1] = row;
    list[index] = temp;
  } else if (direction === 'down' && index < list.length - 1) {
    const temp = list[index + 1];
    list[index + 1] = row;
    list[index] = temp;
  }
};
const handleEditField = (row, index) => {
  currentIndex.value = index !== -1 ? index : -1;
  editFieldRef.value.show(row, form.value.baseServiceTypeFieldList);
};
const show = (row) => {
  visible.value = true;
  if (row?.id) {
    getDetail(row?.id);
  }
};
const handleConfirm = async () => {
  let params = cloneDeep(form.value);
  let result = await formRef.value.elForm.validate();
  if (result) {
    loading.value = true;
    params.deptId = userInfos.value.user.deptId;
    params.buyWay = params.buyWay;
    params.baseServiceTypeFieldList.forEach((i, index) => {
      i.sort = index + 1;
      i.buyWay = form.value.buyWay;
      i.enumValues = JSON.stringify(i.enumValues);
      delete i.filteredFields;
      delete i.fieldList;
      if(!fixedFieldNames.includes(i.fieldName)) {
        delete i.fieldCode;
      }
    });
    try {
      saveFieldConfigApi(params)
        .then(() => {
          loading.value = false;
          visible.value = false;
          ElMessage.success('配置保存成功');
        })
        .catch((error) => {
          loading.value = false;
          console.error('保存配置失败:', error);
        });
    } catch (error) {
      console.error('保存配置失败:', error);
    }
  }
};
const getDetail = async (id) => {
  try {
    const { data } = await getConfigDetail(id);
    let baseServiceTypeFieldList = data.baseServiceTypeFieldList;
    baseServiceTypeFieldList.forEach((i) => {
      i.enumValues = i.enumValues ? JSON.parse(i.enumValues) : null;
    });
    form.value = {
      id: data?.id || '',
      typeName: data?.typeName || '',
      buyWay: baseServiceTypeFieldList[0]?.buyWay,
      baseServiceTypeFieldList: baseServiceTypeFieldList.length ? baseServiceTypeFieldList : [...fixedFields.value],
    };
    console.log(form.value, 'baseServiceTypeFieldListbaseServiceTypeFieldListbaseServiceTypeFieldList');
    formRef.value?.elForm?.resetFields();
  } catch (error) {
    console.error('获取详情失败:', error);
  }
};

defineExpose({
  show,
});
</script>

<style lang="scss" scoped>
.config-detail {
  padding: 0 20px;
  h1 {
    font-size: 18px;
    font-weight: bold;
  }
  .yun-pro-form {
    border-bottom: 1px solid #e4e7ed;
    margin-bottom: 20px;
    margin-top: 10px;
  }
  :deep(.el-card__body) {
    padding: 0 !important;
  }
  .table-content {
    height: 670px;
    :deep(.el-switch) {
      .el-switch__label {
        display: none;
      }
      .is-active {
        display: inline-block;
        position: absolute;
        right: 24px;
        color: #fff;
      }
      .el-switch__label--left {
        color: #fff;
        left: 24px;
        z-index: 2;
      }
      .el-switch__core {
        width: 85px !important;
      }
    }
    :deep(.custom-switch) {
      margin-left: 5px;
      .el-switch__core {
        width: 60px !important;
      }
    }
    :deep(.custom-switch1) {
      margin-left: 5px;
      .el-switch__core {
        width: 130px !important;
      }
    }
  }
  .delete-btn {
    color: #f56c6c;
    &:hover {
      color: #d9534f;
    }
  }
}
</style>
