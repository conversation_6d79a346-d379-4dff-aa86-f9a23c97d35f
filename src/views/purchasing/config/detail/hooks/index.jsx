import { ref, computed, watch } from "vue";
import { useDict } from '@/hooks/dict';
import { fixedFieldNames } from '@/views/purchasing/constant';

// 获取字典数据
const dictData = useDict('plan_recruit_method');
const sourceTypeOptions = computed(() => {
  return dictData.plan_recruit_method?.value || [];
})

export const useTable = () => {
  const fixedFields = ref([
    {
      fieldName: '需求行号',
      fieldCode: 'requireNo',
      fieldType: 'TEXT',
      fieldDesc: '系统自动生成',
      fieldLength: 50,
      rename: '',
      plan: '采购计划子表',
      proposal: '采购立项子表',
      quotation: '采购报价子表',
      planIsRequired: '0',
      projectIsRequired: '0',
      quoteIsRequired: '0',
      applyToPlanEdit: 'NO',
      applyToProjectEdit: 'NO',
      applyToQuoteEdit: 'NO',
      applyToPlan: 'YES',
      applyToProject: 'YES',
      applyToQuote: 'YES',
      payIsManyType: '0',
    },
    {
      fieldName: '物料编码',
      fieldCode: 'materialCode',
      fieldType: 'TEXT',
      fieldDesc: '选择后带出',
      fieldLength: 100,
      rename: '',
      plan: '采购计划子表',
      proposal: '采购立项子表',
      quotation: '采购报价子表',
      planIsRequired: '1',
      projectIsRequired: '1',
      quoteIsRequired: '1',
      applyToPlanEdit: 'NO',
      applyToProjectEdit: 'NO',
      applyToQuoteEdit: 'NO',
      applyToPlan: 'YES',
      applyToProject: 'YES',
      applyToQuote: 'YES',
      payIsManyType: '0',
    },
    {
      fieldName: '物料名称',
      fieldCode: 'materialName',
      fieldType: 'TEXT',
      fieldDesc: '选择后带出',
      fieldLength: 100,
      rename: '',
      plan: '采购计划子表',
      proposal: '采购立项子表',
      quotation: '采购报价子表',
      planIsRequired: '1',
      projectIsRequired: '1',
      quoteIsRequired: '1',
      applyToPlanEdit: 'NO',
      applyToProjectEdit: 'NO',
      applyToQuoteEdit: 'NO',
      applyToPlan: 'YES',
      applyToProject: 'YES',
      applyToQuote: 'YES',
      payIsManyType: '0',
    },
    {
      fieldName: '规格型号',
      fieldCode: 'specModel',
      fieldType: 'TEXT',
      fieldDesc: '选择后带出',
      fieldLength: 50,
      rename: '',
      plan: '采购计划子表',
      proposal: '采购立项子表',
      quotation: '采购报价子表',
      planIsRequired: '0',
      projectIsRequired: '0',
      quoteIsRequired: '0',
      applyToPlanEdit: 'NO',
      applyToProjectEdit: 'NO',
      applyToQuoteEdit: 'NO',
      applyToPlan: 'YES',
      applyToProject: 'YES',
      applyToQuote: 'YES',
      payIsManyType: '0',
    },
    {
      fieldName: '单位',
      fieldCode: 'unit',
      fieldType: 'TEXT',
      fieldDesc: '选择后带出',
      fieldLength: 30,
      rename: '',
      plan: '采购计划子表',
      proposal: '采购立项子表',
      quotation: '采购报价子表',
      planIsRequired: '0',
      projectIsRequired: '0',
      quoteIsRequired: '0',
      applyToPlanEdit: 'NO',
      applyToProjectEdit: 'NO',
      applyToQuoteEdit: 'NO',
      applyToPlan: 'YES',
      applyToProject: 'YES',
      applyToQuote: 'YES',
      payIsManyType: '0',
    },
    {
      fieldName: '需求数量',
      fieldCode: 'requiredQuantity',
      fieldType: 'NUM',
      fieldDesc: '请填写',
      fieldLength: 30,
      rename: '',
      plan: '采购计划子表',
      proposal: '采购立项子表',
      quotation: '采购报价子表',
      planIsRequired: '1',
      projectIsRequired: '1',
      quoteIsRequired: '1',
      applyToPlanEdit: 'YES',
      applyToProjectEdit: 'YES',
      applyToQuoteEdit: 'YES',
      applyToPlan: 'YES',
      applyToProject: 'YES',
      applyToQuote: 'YES',
      payIsManyType: '0',
    },
    {
      fieldName: '需求牧场',
      fieldCode: 'usageLocationId',
      fieldType: 'ENUM',
      fieldDesc: '请填写',
      fieldLength: 30,
      rename: '',
      plan: '采购计划子表',
      proposal: '采购立项子表',
      quotation: '采购报价子表',
      planIsRequired: '1',
      projectIsRequired: '1',
      quoteIsRequired: '1',
      applyToPlanEdit: 'YES',
      applyToProjectEdit: 'YES',
      applyToQuoteEdit: 'YES',
      applyToPlan: 'YES',
      applyToProject: 'YES',
      applyToQuote: 'YES',
      payIsManyType: '0',
      enumValues: [],
    },
    {
      fieldName: '所在区域',
      fieldCode: 'usageLocationRegion',
      fieldType: 'TEXT',
      fieldDesc: '选择后带出',
      fieldLength: 100,
      rename: '',
      plan: '采购计划子表',
      proposal: '采购立项子表',
      quotation: '采购报价子表',
      planIsRequired: '0',
      projectIsRequired: '0',
      quoteIsRequired: '0',
      applyToPlanEdit: 'NO',
      applyToProjectEdit: 'NO',
      applyToQuoteEdit: 'NO',
      applyToPlan: 'YES',
      applyToProject: 'YES',
      applyToQuote: 'YES',
      payIsManyType: '0',
    },
    {
      fieldName: '详细地址',
      fieldCode: 'usageLocationAddress',
      fieldType: 'TEXT',
      fieldDesc: '选择后带出',
      fieldLength: 200,
      rename: '',
      plan: '采购计划子表',
      proposal: '采购立项子表',
      quotation: '采购报价子表',
      planIsRequired: '0',
      projectIsRequired: '0',
      quoteIsRequired: '0',
      applyToPlanEdit: 'NO',
      applyToProjectEdit: 'NO',
      applyToQuoteEdit: 'NO',
      applyToPlan: 'YES',
      applyToProject: 'YES',
      applyToQuote: 'YES',
      payIsManyType: '0',
    },
    {
      fieldName: '质量标准',
      fieldCode: 'qualityIndicatorId',
      fieldType: 'ENUM',
      fieldDesc: '请填写',
      fieldLength: 30,
      rename: '',
      plan: '采购计划子表',
      proposal: '采购立项子表',
      quotation: '采购报价子表',
      planIsRequired: '1',
      projectIsRequired: '1',
      quoteIsRequired: '1',
      applyToPlanEdit: 'YES',
      applyToProjectEdit: 'YES',
      applyToQuoteEdit: 'YES',
      applyToPlan: 'YES',
      applyToProject: 'YES',
      applyToQuote: 'YES',
      payIsManyType: '0',
      enumValues: [],
    },
    {
      fieldName: '可供数量',
      fieldCode: 'availableQuantity',
      fieldType: 'NUM',
      fieldDesc: '请填写',
      fieldLength: 30,
      rename: '',
      plan: '采购计划子表',
      proposal: '采购立项子表',
      quotation: '采购报价子表',
      planIsRequired: '0',
      projectIsRequired: '0',
      quoteIsRequired: '1',
      applyToPlanEdit: 'NO',
      applyToProjectEdit: 'NO',
      applyToQuoteEdit: 'YES',
      applyToPlan: 'NO',
      applyToProject: 'NO',
      applyToQuote: 'YES',
      payIsManyType: '0',
    },
    {
      fieldName: '单价',
      fieldCode: 'quotePrice',
      fieldType: 'NUM',
      fieldDesc: '请填写',
      fieldLength: 30,
      rename: '',
      plan: '采购计划子表',
      proposal: '采购立项子表',
      quotation: '采购报价子表',
      planIsRequired: '0',
      projectIsRequired: '0',
      quoteIsRequired: '1',
      applyToPlanEdit: 'NO',
      applyToProjectEdit: 'NO',
      applyToQuoteEdit: 'YES',
      applyToPlan: 'NO',
      applyToProject: 'NO',
      applyToQuote: 'YES',
      payIsManyType: '1',
    },
    {
      fieldName: '总价',
      fieldCode: 'quoteAmount',
      fieldType: 'NUM_CALC',
      fieldDesc: '计算得出',
      fieldLength: 100,
      defaultValue: "@availableQuantity*@quotePrice",
      rename: '',
      plan: '采购计划子表',
      proposal: '采购立项子表',
      quotation: '采购报价子表',
      planIsRequired: '0',
      projectIsRequired: '0',
      quoteIsRequired: '1',
      applyToPlanEdit: 'NO',
      applyToProjectEdit: 'NO',
      applyToQuoteEdit: 'YES',
      applyToPlan: 'NO',
      applyToProject: 'NO',
      applyToQuote: 'YES',
      payIsManyType: '1',
    }
  ])

  // 初始化表单数据
  const form = ref({
    typeName: '',
    buyWay: '',
    baseServiceTypeFieldList: [...fixedFields.value],
  })

  const requiredOptions = ref([
    {
      label: '必填',
      value: '1'
    },
    {
      label: '非必填',
      value: '0'
    }
  ])

  // 基础信息
  const basicColumns = computed(() => {
    return [
      {
        prop: 'typeName',
        label: '业务类型',
        type: '',
      },
      {
        prop: 'buyWay',
        label: '寻源方式',
        type: 'select',
        enums: sourceTypeOptions.value,
        rules: [
          { required: true, message: '寻源方式不能为空', trigger: 'change' },
        ]
      },
    ]
  })

  // 子表单配置表头
  const tableColumns = computed(() => {
    return [
      {
        type: 'selection',
        width: 52,
        fixed: 'left',
        selectable: (row) => {
          return (
            !fixedFieldNames.includes(row.fieldName)
          );
        },
      },
      {
        prop: 'indexed',
        type: 'indexed',
        width: 50,
        label: '序号',
        dragSort: false,
        render: ({ $index }) => $index + 1,
        align: 'center',
      },
      {
        label: '字段名称',
        prop: 'fieldName',
        minWidth: 90,
      },
      {
        label: '字段说明',
        prop: 'fieldDesc',
        minWidth: 80,
      },
      {
        label: '重命名',
        prop: 'rename',
        minWidth: 80,
      },
      {
        label: '采购计划子表',
        prop: 'plan',
        width: '260px',
      },
      {
        label: '采购立项子表',
        prop: 'proposal',
        width: '260px',
      },
      {
        label: '采购报价子表',
        prop: 'quotation',
        width: '400px',
      },
      {
        label: '操作',
        prop: 'action',
        width: 140,
        fixed: 'right',
      },
    ]
  })

  // 监听字典数据变化
  watch(
    () => dictData.plan_recruit_method?.value,
    (newVal) => {
      if (newVal) {
        basicColumns.value[1].enums = newVal;
      }
    },
    { immediate: true }
  );

  return {
    form,
    basicColumns,
    tableColumns,
    requiredOptions,
    fixedFields,
  }
}