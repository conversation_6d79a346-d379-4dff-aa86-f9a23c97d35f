<template>
	<yun-dialog
		v-model="visible"
		width="540px"
		:title="`${title}业务类型`"
		:confirm-button-text="'确定'"
		:cancel-button-text="'取消'"
		:show-cancel-button="!pageType"
		:show-confirm-button="!pageType"
		:confirm-button-handler="handleConfirm"
	>
		<yun-pro-form
			ref="formRef"
			v-model:form="form"
			:columns="basicColumns"
			:form-props="{ labelWidth: '120px', labelPosition: 'left' }"
		/>
	</yun-dialog>
</template>
<script setup lang="jsx">
import { ref, computed } from 'vue';
import { addBaseServiceType, updateBaseServiceType } from '@/api/purchasing/config.ts';
import { useUserInfo } from '/@/stores/userInfo';
import { ElMessage } from 'yun-design';

// 定义变量内容
const stores = useUserInfo();
const { userInfos } = storeToRefs(stores);

const emit = defineEmits(['confirm']);

const visible = ref(false);
const formRef = ref();
const form = ref({
	typeName: '',
	description: '',
});
const title = ref('新建');
const pageType = ref('');
const basicColumns = computed(() => [
	{
		label: '业务类型名称',
		prop: 'typeName',
		type: pageType.value ? '' : 'input',
		rules: [
			{ required: true, message: '请输入业务类型名称', trigger: 'blur' },
			{ max: 30, message: '长度在30个字符内' },
		],
		colProps: { span: 24 },
	},
	{
		label: '业务类型说明',
		prop: 'description',
		type: pageType.value ? '' : 'input',
		colProps: { span: 24 },
		rules: [{ max: 50, message: '长度在50个字符内' }],
	},
]);
const handleConfirm = async () => {
	let result = await formRef.value?.elForm?.validate();
	if (result) {
		form.value.deptId = userInfos.value.user.deptId;
		if (form.value.id) {
			await updateBaseServiceType(form.value);
		} else {
			await addBaseServiceType(form.value);
		}
		ElMessage.success(`${title.value}业务类型成功`);
		visible.value = false;
		emit('confirm');
	}
};
const show = (data, type) => {
	if (data) {
		form.value = { ...data };
		title.value = type ? '查看' : '编辑';
	} else {
		form.value = {
			typeName: '',
			description: '',
		};
		title.value = '新建';
	}
	pageType.value = type;
	visible.value = true;
	formRef.value?.elForm?.resetFields();
};

defineExpose({ show });
</script>
