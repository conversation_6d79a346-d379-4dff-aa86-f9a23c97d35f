import { updateBaseServiceType, removeBaseServiceType } from '@/api/purchasing/config';
import { ElMessageBox, ElMessage } from 'yun-design';

export default ({ tableRef, editTypeRef, detailRef, state }) => {
  // 配置动态表单
  const toConfig = (row, type) => {
    detailRef.value.show(row);
  };
  // 新增业务类型
  const toAdd = (row, type) => {
    editTypeRef.value.show(row, type);
  };
  // 修改状态
  const handleModifyStatus = (row) => {
    ElMessageBox.confirm(`确定要${row.status === 'ENABLED' ? '禁用' : '启用'}该业务类型吗？`, '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })
      .then(() => {
        // 调用修改状态接口
        updateBaseServiceType({ ...row, status: row.status === 'ENABLED' ? 'DISABLED' : 'ENABLED' })
          .then(() => {
            ElMessage.success(`业务类型${row.status === 'ENABLED' ? '禁用' : '启用'}成功`)
            refreshTable(); // 使用refreshTable方法刷新表格
          })
          .catch((error) => {
            console.log(error);
          });
      })
      .catch(() => {
        console.log("取消");
      });
  };
  // 删除
  const handleDelete = (row) => {
    ElMessageBox.confirm('确定要删除该业务类型吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })
      .then(() => {
        // 调用删除接口
        removeBaseServiceType([row.id])
          .then(() => {
            refreshTable(); // 使用refreshTable方法刷新表格
            ElMessage.success('业务类型删除成功');
          })
          .catch((error) => {
            console.log(error)
          });
      })
      .catch(() => {
        console.log("取消");
      });
  };

  // 刷新表格数据
  const refreshTable = () => {
    if (state) {
      state.page.page = 1;
    }
    tableRef.value?.getData?.();
  };

  return {
    toConfig,
    toAdd,
    handleModifyStatus,
    handleDelete,
    refreshTable,
  };
}