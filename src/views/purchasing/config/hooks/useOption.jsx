export default function useOptions() {
  const searchFields = [
    {
      label: '业务类型名称',
      prop: 'typeName',
      component: 'el-input',
    },
    {
      label: '状态',
      prop: 'status',
      component: 'el-select',
      options: [
        { label: '启用', value: 'ENABLED' },
        { label: '禁用', value: 'DISABLED' },
      ],
    },
  ]
  const tableColumns = [
    {
      label: '序号',
      prop: 'index',
      type: 'index',
    },
    {
      label: '业务类型名称',
      prop: 'typeName',
    },
    {
      label: '业务类型说明',
      prop: 'description',
    },
    {
      label: '状态',
      prop: 'status',
    },
    {
      label: '创建人',
      prop: 'createBy',
    },
    {
      label: '创建时间',
      prop: 'createTime',
    },
    {
      label: '修改人',
      prop: 'updateBy',
    },
    {
      label: '修改时间',
      prop: 'updateTime',
    },
    {
      label: '操作',
      prop: 'action',
      width: '280px',
      fixed: 'right',
    },
  ]

  return {
    searchFields,
    tableColumns
  }
}